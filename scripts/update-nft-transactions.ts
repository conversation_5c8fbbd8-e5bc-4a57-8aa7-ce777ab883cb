require('dotenv').config();
import { createConnection } from 'mysql2/promise';
import { v4 as uuidv4 } from 'uuid';

async function updateNFTTransactions(orderId: string) {
  const connection = await createConnection({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE
  });

  try {
    // Bắt đầu transaction
    await connection.beginTransaction();

    try {
      // 1. <PERSON><PERSON><PERSON> thông tin cần thiết
      const [orders]: any = await connection.query(`
        SELECT 
          o.*, 
          n.currentPrice, 
          n.gasFee, 
          n.type,
          w.id as walletId,
          t1.id as usdtTokenId,
          t2.id as wmTokenId
        FROM nft_orders o
        JOIN nfts n ON o.nftId = n.id
        JOIN wallets w ON w.userId = o.traderId
        JOIN tokens t1 ON t1.symbol = 'USDT'
        JOIN tokens t2 ON t2.symbol = 'WM'
        WHERE o.id = ?
      `, [orderId]);

      const order = orders[0];
      if (!order) {
        throw new Error(`Order not found: ${orderId}`);
      }

      console.log('Found order:', order); // Log để kiểm tra dữ liệu

      const {
        traderId,
        walletId,
        currentPrice,
        gasFee,
        type,
        usdtTokenId,
        wmTokenId
      } = order;

      // 2. Tạo giao dịch USDT
      await connection.query(`
        INSERT INTO transactions 
        (id, userId, walletId, type, status, amount, tokenId, reference, note, createdAt, updatedAt)
        VALUES (?, ?, ?, 'NFT_PURCHASE', 'COMPLETED', ?, ?, ?, ?, NOW(), NOW())
      `, [
        uuidv4(),
        traderId,
        walletId,
        currentPrice,
        usdtTokenId,
        orderId,
        `Buy NFT ${type}`
      ]);

      // 3. Tạo giao dịch WM
      await connection.query(`
        INSERT INTO transactions 
        (id, userId, walletId, type, status, amount, tokenId, reference, note, createdAt, updatedAt)
        VALUES (?, ?, ?, 'NFT_PURCHASE', 'COMPLETED', ?, ?, ?, ?, NOW(), NOW())
      `, [
        uuidv4(),
        traderId,
        walletId,
        gasFee,
        wmTokenId,
        orderId,
        `Gas fee for buy NFT ${type}`
      ]);



      // 4. Cập nhật số dư
      await connection.query(`
        UPDATE wallets
        SET usdtBalance = usdtBalance - ?
        WHERE id = ?
      `, [currentPrice, walletId]);

      await connection.query(`
        UPDATE wallets
        SET wmBalance = wmBalance - ?
        WHERE id = ?
      `, [gasFee, walletId]);

      await connection.query(`
        UPDATE token_balances
        SET availableBalance = availableBalance - ?, totalBalance = totalBalance - ?
        WHERE userId = ? AND tokenId = ?
      `, [currentPrice, currentPrice, traderId, usdtTokenId]);

      await connection.query(`
        UPDATE token_balances
        SET availableBalance = availableBalance - ?, totalBalance = totalBalance - ?
        WHERE userId = ? AND tokenId = ?
      `, [gasFee, gasFee, traderId, wmTokenId]);

      // Commit transaction nếu mọi thứ OK
      await connection.commit();
      console.log(`Successfully updated transactions for order: ${orderId}`);

    } catch (error) {
      // Rollback nếu có lỗi
      await connection.rollback();
      throw error;
    }

  } catch (error) {
    console.error('Error:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

// Chạy script
updateNFTTransactions('a9f17edb-f95c-47ce-b4c7-89b75333c52d')
  .then(() => console.log('Done'))
  .catch(console.error);