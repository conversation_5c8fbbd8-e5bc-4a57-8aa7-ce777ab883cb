-- <PERSON><PERSON><PERSON> truy vấn để kiểm tra chi tiết giao dịch của một người dùng cụ thể
-- Thay thế USER_ID bằng ID người dùng cần kiểm tra

-- L<PERSON>y thông tin người dùng
SELECT id, username, wallet, web3WalletId FROM users WHERE id = 'USER_ID';

-- L<PERSON>y thông tin ví
SELECT * FROM wallets WHERE userId = 'USER_ID';

-- Lấy thông tin số dư token
SELECT * FROM token_balances WHERE userId = 'USER_ID';

-- <PERSON><PERSON><PERSON> tất cả giao dịch USDT của người dùng
SELECT 
    t.id,
    t.type,
    t.amount,
    t.status,
    t.note,
    t.createdAt,
    tk.symbol AS token_symbol
FROM 
    transactions t
JOIN 
    tokens tk ON t.tokenId = tk.id
WHERE 
    t.userId = 'USER_ID'
    AND tk.symbol = 'USDT'
    AND t.status = 'COMPLETED'
ORDER BY 
    t.createdAt;

-- T<PERSON>h tổng theo loại giao dịch
SELECT 
    t.type,
    COUNT(*) AS transaction_count,
    SUM(t.amount) AS total_amount
FROM 
    transactions t
JOIN 
    tokens tk ON t.tokenId = tk.id
WHERE 
    t.userId = 'USER_ID'
    AND tk.symbol = 'USDT'
    AND t.status = 'COMPLETED'
GROUP BY 
    t.type
ORDER BY 
    SUM(t.amount) DESC;

-- Tính số dư lý thuyết
WITH TransactionSummary AS (
    SELECT 
        -- Tổng tiền nạp
        SUM(CASE WHEN t.type = 'DEPOSIT' THEN t.amount ELSE 0 END) AS deposit_total,
        
        -- Tổng tiền rút
        SUM(CASE WHEN t.type = 'WITHDRAWAL' THEN t.amount ELSE 0 END) AS withdrawal_total,
        
        -- Tổng lãi từ staking
        SUM(CASE WHEN t.type = 'GLOBAL_MINING_COSHARE_INTEREST' THEN t.amount ELSE 0 END) AS interest_total,
        
        -- Tổng hoa hồng trực tiếp
        SUM(CASE WHEN t.type = 'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT' THEN t.amount ELSE 0 END) AS direct_commission_total,
        
        -- Tổng hoa hồng matching
        SUM(CASE WHEN t.type = 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING' THEN t.amount ELSE 0 END) AS matching_commission_total,
        
        -- Tổng hoa hồng lợi nhuận
        SUM(CASE WHEN t.type = 'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING' THEN t.amount ELSE 0 END) AS profit_commission_total,
        
        -- Tổng hoa hồng rank
        SUM(CASE WHEN t.type = 'GLOBAL_MINING_COSHARE_COMMISSION_RANK' THEN t.amount ELSE 0 END) AS rank_commission_total,
        
        -- Tổng hoa hồng cổ đông
        SUM(CASE WHEN t.type = 'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER' THEN t.amount ELSE 0 END) AS shareholder_commission_total,
        
        -- Tổng chi tiêu mua NFT
        SUM(CASE WHEN t.type = 'NFT_PURCHASE' THEN t.amount ELSE 0 END) AS nft_purchase_total,
        
        -- Tổng thu từ bán NFT
        SUM(CASE WHEN t.type = 'NFT_SALE' THEN t.amount ELSE 0 END) AS nft_sale_total,
        
        -- Tổng hoa hồng hệ thống
        SUM(CASE WHEN t.type = 'SYSTEM_COMMISSION' THEN t.amount ELSE 0 END) AS system_commission_total
    FROM 
        transactions t
    JOIN 
        tokens tk ON t.tokenId = tk.id
    WHERE 
        t.userId = 'USER_ID'
        AND tk.symbol = 'USDT'
        AND t.status = 'COMPLETED'
)
SELECT 
    deposit_total,
    withdrawal_total,
    interest_total,
    direct_commission_total,
    matching_commission_total,
    profit_commission_total,
    rank_commission_total,
    shareholder_commission_total,
    nft_purchase_total,
    nft_sale_total,
    system_commission_total,
    
    -- Tính tổng thu nhập
    (
        COALESCE(deposit_total, 0) + 
        COALESCE(interest_total, 0) + 
        COALESCE(direct_commission_total, 0) + 
        COALESCE(matching_commission_total, 0) + 
        COALESCE(profit_commission_total, 0) + 
        COALESCE(rank_commission_total, 0) + 
        COALESCE(shareholder_commission_total, 0) + 
        COALESCE(nft_sale_total, 0) + 
        COALESCE(system_commission_total, 0)
    ) AS total_income,
    
    -- Tính tổng chi tiêu
    (
        COALESCE(withdrawal_total, 0) + 
        COALESCE(nft_purchase_total, 0)
    ) AS total_expense,
    
    -- Tính số dư lý thuyết
    (
        COALESCE(deposit_total, 0) + 
        COALESCE(interest_total, 0) + 
        COALESCE(direct_commission_total, 0) + 
        COALESCE(matching_commission_total, 0) + 
        COALESCE(profit_commission_total, 0) + 
        COALESCE(rank_commission_total, 0) + 
        COALESCE(shareholder_commission_total, 0) + 
        COALESCE(nft_sale_total, 0) + 
        COALESCE(system_commission_total, 0) - 
        COALESCE(withdrawal_total, 0) - 
        COALESCE(nft_purchase_total, 0)
    ) AS calculated_balance,
    
    -- Lấy số dư thực tế từ wallets
    (SELECT usdtBalance FROM wallets WHERE userId = 'USER_ID') AS wallet_balance,
    
    -- Lấy số dư thực tế từ token_balances
    (SELECT availableBalance FROM token_balances WHERE userId = 'USER_ID' AND tokenId = (SELECT id FROM tokens WHERE symbol = 'USDT')) AS token_balance,
    
    -- Tính chênh lệch
    (
        (
            COALESCE(deposit_total, 0) + 
            COALESCE(interest_total, 0) + 
            COALESCE(direct_commission_total, 0) + 
            COALESCE(matching_commission_total, 0) + 
            COALESCE(profit_commission_total, 0) + 
            COALESCE(rank_commission_total, 0) + 
            COALESCE(shareholder_commission_total, 0) + 
            COALESCE(nft_sale_total, 0) + 
            COALESCE(system_commission_total, 0) - 
            COALESCE(withdrawal_total, 0) - 
            COALESCE(nft_purchase_total, 0)
        ) - (SELECT usdtBalance FROM wallets WHERE userId = 'USER_ID')
    ) AS wallet_difference
FROM 
    TransactionSummary;
