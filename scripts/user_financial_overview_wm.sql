CREATE TABLE user_financial_overview_wm (
    -- <PERSON><PERSON><PERSON><PERSON>, trù<PERSON> với userId từ cả hai bảng
                                         userId VARCHAR(255) NOT NULL PRIMARY KEY, -- !! <PERSON>uan trọng: <PERSON><PERSON><PERSON> bảo kiểu dữ liệu này khớp với userId trong các bảng gốc

    -- <PERSON><PERSON><PERSON> cột từ user_transaction_summary_wm (giữ nguyên kiểu dữ liệu)
                                         update_profile_bonus_num INT NOT NULL DEFAULT 0,
                                         update_profile_bonus_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         deposit_num INT NOT NULL DEFAULT 0,
                                         deposit_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         withdrawal_num INT NOT NULL DEFAULT 0,
                                         withdrawal_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         transfer_num INT NOT NULL DEFAULT 0,
                                         transfer_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         mining_num INT NOT NULL DEFAULT 0,
                                         mining_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         referral_bonus_num INT NOT NULL DEFAULT 0,
                                         referral_bonus_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         referral_bonus_mining_num INT NOT NULL DEFAULT 0,
                                         referral_bonus_mining_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         task_reward_num INT NOT NULL DEFAULT 0,
                                         task_reward_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         kyc_verify_bonus_num INT NOT NULL DEFAULT 0,
                                         kyc_verify_bonus_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         kyc_verify_commission_num INT NOT NULL DEFAULT 0,
                                         kyc_verify_commission_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         kyc_purchase_num INT NOT NULL DEFAULT 0,
                                         kyc_purchase_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         lightning_bolt_purchase_num INT NOT NULL DEFAULT 0,
                                         lightning_bolt_purchase_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         priority_position_purchase_num INT NOT NULL DEFAULT 0,
                                         priority_position_purchase_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         nft_purchase_num INT NOT NULL DEFAULT 0,
                                         nft_purchase_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         nft_withdrawal_num INT NOT NULL DEFAULT 0,
                                         nft_withdrawal_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         nft_sale_num INT NOT NULL DEFAULT 0,
                                         nft_sale_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         sale_bonus_num INT NOT NULL DEFAULT 0,
                                         sale_bonus_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         direct_commission_num INT NOT NULL DEFAULT 0,
                                         direct_commission_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         ranking_commission_num INT NOT NULL DEFAULT 0,
                                         ranking_commission_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         co_shareholder_bonus_num INT NOT NULL DEFAULT 0,
                                         co_shareholder_bonus_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         gas_fee_num INT NOT NULL DEFAULT 0,
                                         gas_fee_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         scheduling_fee_num INT NOT NULL DEFAULT 0,
                                         scheduling_fee_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         system_commission_num INT NOT NULL DEFAULT 0,
                                         system_commission_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,
                                         summary_last_updated_at TIMESTAMP NULL, -- Đổi tên cột last_updated_at từ summary để rõ ràng hơn

    -- Các cột từ wallets (trừ userId)
                                         address VARCHAR(255) NULL, -- Hoặc kiểu dữ liệu phù hợp cho địa chỉ ví, cho phép NULL nếu có thể
                                         wmBalance DECIMAL(18, 8) NOT NULL DEFAULT 0.00, -- !! Quan trọng: Đảm bảo kiểu dữ liệu khớp với wallets
                                         wmLockedBalance DECIMAL(18, 8) NOT NULL DEFAULT 0.00, -- !! Quan trọng: Đảm bảo kiểu dữ liệu khớp với wallets
                                         usdtBalance DECIMAL(18, 8) NOT NULL DEFAULT 0.00, -- !! Quan trọng: Đảm bảo kiểu dữ liệu khớp với wallets

    -- (Tùy chọn) Cột metadata cho bảng mới này
                                         overview_generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO user_financial_overview_wm (
    userId,
    -- Các cột từ user_transaction_summary_wm
    update_profile_bonus_num, update_profile_bonus_amount, deposit_num, deposit_amount,
    withdrawal_num, withdrawal_amount, transfer_num, transfer_amount, mining_num, mining_amount,
    referral_bonus_num, referral_bonus_amount, referral_bonus_mining_num, referral_bonus_mining_amount,
    task_reward_num, task_reward_amount, kyc_verify_bonus_num, kyc_verify_bonus_amount,
    kyc_verify_commission_num, kyc_verify_commission_amount, kyc_purchase_num, kyc_purchase_amount,
    lightning_bolt_purchase_num, lightning_bolt_purchase_amount, priority_position_purchase_num, priority_position_purchase_amount,
    nft_purchase_num, nft_purchase_amount, nft_withdrawal_num, nft_withdrawal_amount,
    nft_sale_num, nft_sale_amount, sale_bonus_num, sale_bonus_amount, direct_commission_num, direct_commission_amount,
    ranking_commission_num, ranking_commission_amount, co_shareholder_bonus_num, co_shareholder_bonus_amount,
    gas_fee_num, gas_fee_amount, scheduling_fee_num, scheduling_fee_amount,
    system_commission_num, system_commission_amount, summary_last_updated_at,
    -- Các cột từ wallets
    address, wmBalance, wmLockedBalance, usdtBalance
    -- Không cần liệt kê overview_generated_at nếu nó có DEFAULT
)
SELECT
    uts.userId, -- Lấy userId từ một trong hai bảng (chúng giống nhau sau khi join)
    -- Các cột từ user_transaction_summary_wm (sử dụng alias uts)
    uts.update_profile_bonus_num, uts.update_profile_bonus_amount, uts.deposit_num, uts.deposit_amount,
    uts.withdrawal_num, uts.withdrawal_amount, uts.transfer_num, uts.transfer_amount, uts.mining_num, uts.mining_amount,
    uts.referral_bonus_num, uts.referral_bonus_amount, uts.referral_bonus_mining_num, uts.referral_bonus_mining_amount,
    uts.task_reward_num, uts.task_reward_amount, uts.kyc_verify_bonus_num, uts.kyc_verify_bonus_amount,
    uts.kyc_verify_commission_num, uts.kyc_verify_commission_amount, uts.kyc_purchase_num, uts.kyc_purchase_amount,
    uts.lightning_bolt_purchase_num, uts.lightning_bolt_purchase_amount, uts.priority_position_purchase_num, uts.priority_position_purchase_amount,
    uts.nft_purchase_num, uts.nft_purchase_amount, uts.nft_withdrawal_num, uts.nft_withdrawal_amount,
    uts.nft_sale_num, uts.nft_sale_amount, uts.sale_bonus_num, uts.sale_bonus_amount, uts.direct_commission_num, uts.direct_commission_amount,
    uts.ranking_commission_num, uts.ranking_commission_amount, uts.co_shareholder_bonus_num, uts.co_shareholder_bonus_amount,
    uts.gas_fee_num, uts.gas_fee_amount, uts.scheduling_fee_num, uts.scheduling_fee_amount,
    uts.system_commission_num, uts.system_commission_amount, uts.last_updated_at, -- Lấy cột gốc từ uts
    -- Các cột từ wallets (sử dụng alias w)
    w.address, w.wmBalance, w.wmLockedBalance, w.usdtBalance
FROM
    user_transaction_summary_wm uts -- Đặt alias cho bảng summary
        INNER JOIN
    wallets w ON uts.userId = w.userId; -- Điều kiện join là userId phải khớp nhau

-- Nếu bạn muốn bao gồm cả những user chỉ có trong summary hoặc chỉ có trong wallets,
-- bạn có thể thay INNER JOIN bằng LEFT JOIN, RIGHT JOIN hoặc FULL OUTER JOIN (tùy CSDL hỗ trợ)
-- và xử lý các giá trị NULL tương ứng (ví dụ dùng COALESCE(uts.userId, w.userId) AS userId).

-- Nếu bảng user_financial_overview đã có dữ liệu và bạn muốn cập nhật thay vì chỉ insert mới (upsert):
-- MySQL/MariaDB: Thêm `ON DUPLICATE KEY UPDATE cột1 = VALUES(cột1), cột2 = VALUES(cột2), ...` vào cuối lệnh INSERT.
-- PostgreSQL: Dùng `INSERT ... ON CONFLICT (userId) DO UPDATE SET cột1 = EXCLUDED.cột1, ...`.
-- SQL Server: Dùng `MERGE`.