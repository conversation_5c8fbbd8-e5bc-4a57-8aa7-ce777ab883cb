SELECT * FROM users WHERE users.wallet = '******************************************';
SELECT * FROM wallets WHERE wallets.userId = 'a3c0be6f-2d82-40d9-8b48-f12ac4808ae7';
SELECT * FROM token_balances WHERE token_balances.userId = 'a3c0be6f-2d82-40d9-8b48-f12ac4808ae7';
SELECT * FROM nfts WHERE nfts.owner_id = 'a3c0be6f-2d82-40d9-8b48-f12ac4808ae7';
SELECT * FROM nft_orders WHERE nft_orders.traderId = 'a3c0be6f-2d82-40d9-8b48-f12ac4808ae7' ORDER BY nft_orders.createdAt DESC;
SELECT * FROM transactions WHERE transactions.userId = 'a3c0be6f-2d82-40d9-8b48-f12ac4808ae7' AND transactions.type = 'DEPOSIT' ORDER BY transactions.createdAt DESC;
SELECT * FROM transactions WHERE transactions.userId = 'a3c0be6f-2d82-40d9-8b48-f12ac4808ae7' AND transactions.type = 'WITHDRAWAL' ORDER BY transactions.createdAt DESC;
SELECT transactions.type, transactions.tokenId, SUM(transactions.amount) FROM transactions WHERE transactions.userId = 'a3c0be6f-2d82-40d9-8b48-f12ac4808ae7' GROUP BY transactions.type, transactions.tokenId ORDER BY transactions.tokenId;
SELECT * FROM transactions WHERE transactions.userId = 'a3c0be6f-2d82-40d9-8b48-f12ac4808ae7' ORDER BY transactions.createdAt DESC;