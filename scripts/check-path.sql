WITH RECURSIVE ReferralPathBuilder (
    id, referredBy, calculated_path
) AS (
    SELECT id, referredBy, CAST(NULL AS CHAR(10000))
    FROM users WHERE referredBy IS NULL
    UNION ALL
    SELECT c.id, c.referredBy,
           CASE WHEN p.calculated_path IS NULL THEN CAST(p.id AS CHAR(10000))
                ELSE CAST(CONCAT(p.calculated_path, '.', p.id) AS CHAR(10000))
           END
    FROM users c INNER JOIN ReferralPathBuilder p ON c.referredBy = p.id
)
SELECT
    u.id AS user_id,
    u.path AS actual_path_in_db,
    rpb.calculated_path,
    CASE WHEN u.path <=> (rpb.calculated_path COLLATE utf8mb4_0900_ai_ci) -- Đ<PERSON>m bảo collation đúng
         THEN 'Khớp'
         ELSE 'Không Khớp'
    END AS comparison_status
FROM users u
LEFT JOIN ReferralPathBuilder rpb ON u.id = rpb.id
WHERE NOT (u.path <=> (rpb.calculated_path COLLATE utf8mb4_0900_ai_ci)); -- <PERSON><PERSON><PERSON> ra cái không khớp