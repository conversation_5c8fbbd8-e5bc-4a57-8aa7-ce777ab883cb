require('dotenv').config(); // Đọc file .env
const mysql = require('mysql2/promise');

// Thông tin kết nối từ .env
const dbConfig = {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT, 10),
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
};

// Hàm rebuild path cho một username và toàn bộ con cháu
async function rebuildUserTree(username) {
    let connection;

    try {
        // Kết nối MySQL
        connection = await mysql.createConnection(dbConfig);
        console.log('Kết nối thành công!');

        // Tìm user theo username
        const [userRows] = await connection.execute(
            'SELECT id, username, referredBy, path FROM users WHERE username = ?',
            [username]
        );
        
        const user = userRows[0];
        if (!user) {
            console.error(`<PERSON>hông tìm thấy user với username: ${username}`);
            return;
        }

        console.log(`<PERSON><PERSON> tìm thấy user: ${user.username} (ID: ${user.id})`);

        // Hàm cập nhật path cho từng user
        async function updateUserPath(userId, visited = new Set()) {
            if (visited.has(userId)) {
                return; // Tránh vòng lặp vô hạn
            }
            visited.add(userId);

            // Lấy thông tin user
            const [userRows] = await connection.execute(
                'SELECT id, referredBy, path FROM users WHERE id = ?',
                [userId]
            );
            const user = userRows[0];
            if (!user) {
                return; // User không tồn tại
            }

            const referredBy = user.referredBy;
            let newPath = '';

            if (referredBy) {
                // Lấy thông tin referrer
                const [referrerRows] = await connection.execute(
                    'SELECT id, path FROM users WHERE id = ?',
                    [referredBy]
                );
                const referrer = referrerRows[0];

                if (referrer) {
                    newPath = referrer.path ? `${referrer.path}.${referrer.id}` : referrer.id.toString();
                }
            }

            // Cập nhật path
            await connection.execute(
                'UPDATE users SET path = ? WHERE id = ?',
                [newPath, userId]
            );
            console.log(`Đã cập nhật path cho user ${userId}: ${newPath}`);

            // Cập nhật các user con
            const [children] = await connection.execute(
                'SELECT id FROM users WHERE referredBy = ?',
                [userId]
            );

            console.log(`Tìm thấy ${children.length} user con cần cập nhật`);

            // Cập nhật path cho từng user con
            for (const child of children) {
                await updateUserPath(child.id, visited);
            }
        }

        // Bắt đầu cập nhật từ user được chỉ định
        await updateUserPath(user.id);

        console.log('Hoàn tất cập nhật path!');

        // In thống kê
        const [stats] = await connection.execute(
            'WITH RECURSIVE user_tree AS (' +
            '  SELECT id, username, path FROM users WHERE id = ? ' +
            '  UNION ALL ' +
            '  SELECT u.id, u.username, u.path FROM users u ' +
            '  INNER JOIN user_tree ut ON u.referredBy = ut.id' +
            ') ' +
            'SELECT COUNT(*) as total FROM user_tree',
            [user.id]
        );

        console.log(`\nThống kê:`);
        console.log(`- Tổng số user đã cập nhật: ${stats[0].total}`);

    } catch (error) {
        console.error('Lỗi:', error.message);
    } finally {
        if (connection) {
            await connection.end();
            console.log('Đã đóng kết nối.');
        }
    }
}

// Lấy username từ command line argument
const username = process.argv[2];
if (!username) {
    console.error('Vui lòng cung cấp username. Ví dụ: node rebuild-user-tree.js username123');
    process.exit(1);
}

// Chạy script
rebuildUserTree(username); 