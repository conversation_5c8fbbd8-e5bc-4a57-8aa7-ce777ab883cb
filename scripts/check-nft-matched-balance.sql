-- <PERSON><PERSON><PERSON> t<PERSON>y vấn để kiểm tra số dư từ các giao dịch NFT đã khớp (MATCHED)
-- Tính toán số tiền từ các giao dịch NFT đã khớp và so sánh với số dư thực tế

-- 1. <PERSON><PERSON><PERSON> tổng tiền từ các đơn mua NFT đã khớp (MATCHED)
WITH MatchedBuyOrders AS (
    SELECT 
        no.traderId,
        SUM(no.nftPrice) AS total_matched_buy_amount
    FROM 
        nft_orders no
    WHERE 
        no.status = 'MATCHED' 
        AND no.orderType = 'BUY'
    GROUP BY 
        no.traderId
),

-- 2. <PERSON><PERSON>h tổng tiền từ các đơn bán NFT đã khớp (MATCHED)
MatchedSellOrders AS (
    SELECT 
        no.traderId,
        SUM(no.nftPrice) AS total_matched_sell_amount
    FROM 
        nft_orders no
    WHERE 
        no.status = 'MATCHED' 
        AND no.orderType = 'SELL'
    GROUP BY 
        no.traderId
),

-- 3. <PERSON><PERSON><PERSON> tổng tiền từ các giao dịch NFT_PURCHASE và NFT_SALE
NftTransactions AS (
    SELECT 
        t.userId,
        SUM(CASE WHEN t.type = 'NFT_PURCHASE' THEN t.amount ELSE 0 END) AS total_nft_purchase,
        SUM(CASE WHEN t.type = 'NFT_SALE' THEN t.amount ELSE 0 END) AS total_nft_sale
    FROM 
        transactions t
    WHERE 
        t.type IN ('NFT_PURCHASE', 'NFT_SALE')
        AND t.status = 'COMPLETED'
    GROUP BY 
        t.userId
)

-- 4. Kết hợp và so sánh các kết quả
SELECT 
    u.id AS userId,
    u.username,
    u.wallet AS walletAddress,
    
    -- Số tiền từ các đơn mua NFT đã khớp
    COALESCE(mbo.total_matched_buy_amount, 0) AS matched_buy_amount,
    
    -- Số tiền từ các đơn bán NFT đã khớp
    COALESCE(mso.total_matched_sell_amount, 0) AS matched_sell_amount,
    
    -- Số tiền từ các giao dịch NFT_PURCHASE
    COALESCE(nt.total_nft_purchase, 0) AS nft_purchase_amount,
    
    -- Số tiền từ các giao dịch NFT_SALE
    COALESCE(nt.total_nft_sale, 0) AS nft_sale_amount,
    
    -- Chênh lệch giữa đơn mua đã khớp và giao dịch NFT_PURCHASE
    COALESCE(mbo.total_matched_buy_amount, 0) - COALESCE(nt.total_nft_purchase, 0) AS buy_difference,
    
    -- Chênh lệch giữa đơn bán đã khớp và giao dịch NFT_SALE
    COALESCE(mso.total_matched_sell_amount, 0) - COALESCE(nt.total_nft_sale, 0) AS sell_difference
FROM 
    users u
LEFT JOIN MatchedBuyOrders mbo ON u.id = mbo.traderId
LEFT JOIN MatchedSellOrders mso ON u.id = mso.traderId
LEFT JOIN NftTransactions nt ON u.id = nt.userId
WHERE 
    -- Lọc những người dùng có giao dịch NFT
    (mbo.traderId IS NOT NULL OR mso.traderId IS NOT NULL OR nt.userId IS NOT NULL)
    -- Lọc những người dùng có chênh lệch
    AND (
        ABS(COALESCE(mbo.total_matched_buy_amount, 0) - COALESCE(nt.total_nft_purchase, 0)) > 0.001
        OR ABS(COALESCE(mso.total_matched_sell_amount, 0) - COALESCE(nt.total_nft_sale, 0)) > 0.001
    )
ORDER BY 
    ABS(COALESCE(mbo.total_matched_buy_amount, 0) - COALESCE(nt.total_nft_purchase, 0)) + 
    ABS(COALESCE(mso.total_matched_sell_amount, 0) - COALESCE(nt.total_nft_sale, 0)) DESC;

-- Kiểm tra chi tiết cho một người dùng cụ thể (thay thế USER_ID bằng ID người dùng cần kiểm tra)
-- SELECT * FROM nft_orders 
-- WHERE traderId = 'USER_ID' 
-- AND status = 'MATCHED'
-- ORDER BY createdAt;
