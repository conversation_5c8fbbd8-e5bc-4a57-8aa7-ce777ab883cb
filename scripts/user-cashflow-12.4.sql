-- <PERSON><PERSON>n MySQL 8.0+ hoặc các hệ quản trị CSDL khác hỗ trợ Recursive CTEs
SET SESSION cte_max_recursion_depth = 10000; -- <PERSON><PERSON><PERSON> bảo giới hạn đệ quy đủ lớn

WITH RECURSIVE BaseData AS (
    -- B<PERSON>ớc 0: <PERSON><PERSON><PERSON> bị dữ liệu gốc và thêm số thứ tự
    SELECT
        t.id,
        t.userId,
        DATE(t.createdAt) AS createdDate,
        t.createdAt,
        t.type,
        t.amount, -- <PERSON><PERSON><PERSON> gốc là DECIMAL(18, 8)
        CASE WHEN t.type IN(
            'DEPOSIT', 'KYC_VERIFY_COMMISSION', 'NFT_SALE', 'DIRECT_COMMISSION', 'RANKING_COMMISSION',
            'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST',
            'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLO<PERSON>L_MINING_COSHARE_COMMISSION_MATCHING',
            'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK',
            'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER'
        ) THEN t.amount ELSE 0 END AS original_cash_in,
        CASE WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') -- Đã bao gồm ở đây
             THEN -ABS(t.amount) ELSE 0 END AS original_cash_out,
        CASE WHEN t.type = 'NFT_PURCHASE' THEN ABS(t.amount) ELSE 0 END AS potential_purchase_value,
        CASE
            WHEN t.type = 'NFT_SALE' THEN ROUND(ABS(t.amount) / 1.025, 8)
            ELSE 0
        END AS base_purchase_value_equivalent,
        ROW_NUMBER() OVER (PARTITION BY t.userId ORDER BY t.createdAt ASC, t.id ASC) as rn
    FROM
        transactions t
    WHERE
        t.status = 'COMPLETED'
        AND t.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5'
        AND t.userId = '69463326-5a20-40b6-8ec8-da1d5093b3fe' -- <<<<<<<<<< THAY USER ID Ở ĐÂY
),
-- CTE này tính toán original_cash_total để JOIN lại ở cuối
OriginalCalculations AS (
     SELECT
        t.id,
        t.userId,
        -- Tính cash_total gốc (theo logic ban đầu user cung cấp)
        SUM(
            CASE
                WHEN t.type IN(
                    'DEPOSIT', 'KYC_VERIFY_COMMISSION', 'NFT_SALE', 'DIRECT_COMMISSION', 'RANKING_COMMISSION',
                    'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST',
                    'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING',
                    'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK',
                    'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER'
                ) THEN t.amount
                WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') -- Đã bao gồm ở đây
                THEN -ABS(t.amount)
                ELSE 0
            END
        ) OVER(
            PARTITION BY t.userId ORDER BY t.createdAt ASC, t.id ASC
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS original_cash_total,
        -- Lấy original_cash_in/out để hiển thị lại
        CASE WHEN t.type IN( 'DEPOSIT', 'KYC_VERIFY_COMMISSION', 'NFT_SALE', 'DIRECT_COMMISSION', 'RANKING_COMMISSION', 'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST', 'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK', 'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER' ) THEN t.amount ELSE 0 END AS original_cash_in,
        CASE WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') THEN -ABS(t.amount) ELSE 0 END AS original_cash_out -- Đã bao gồm ở đây
    FROM
        transactions t
    WHERE
        t.status = 'COMPLETED'
        AND t.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5'
        AND t.userId = '69463326-5a20-40b6-8ec8-da1d5093b3fe' -- <<<<<<<<<< THAY USER ID Ở ĐÂY NỮA
),
AdjustedFlowSimulation (
    id, userId, createdDate, createdAt, type, amount,
    original_cash_in_base, original_cash_out_base,
    potential_purchase_value, base_purchase_value_equivalent, rn,
    transaction_validity_status,
    valid_purchase_value,
    adjusted_cash_in,
    adjusted_cash_out,
    cumulative_valid_purchase_value,
    cumulative_sale_equivalent_value,
    adjusted_cash_total
) AS (
    -- ================= ANCHOR MEMBER (rn=1) =================
    SELECT
        bd.id, bd.userId, bd.createdDate, bd.createdAt, bd.type, bd.amount,
        bd.original_cash_in, bd.original_cash_out,
        bd.potential_purchase_value, bd.base_purchase_value_equivalent, bd.rn,
        -- 1.1: Status - Bổ sung GLOBAL_MINING_COSHARE
        CASE
            WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID_OUTFLOW_INSUFFICIENT_FUNDS'
            WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID_SALE_NO_VALID_PURCHASE_AVAILABLE'
            ELSE 'NORMAL'
        END AS transaction_validity_status,
        -- 1.2: Valid Purchase Value - Logic kiểm tra outflow bên trong cần cập nhật
        CAST(CASE
            WHEN bd.type = 'NFT_PURCHASE' AND
                 (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE
            THEN bd.potential_purchase_value
            ELSE 0.0
        END AS DECIMAL(65, 8)) AS valid_purchase_value,
        -- 1.3: Adjusted Cash In - Logic kiểm tra outflow bên trong cần cập nhật
        CAST(CASE
            WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE
            THEN bd.original_cash_in ELSE 0.0
        END AS DECIMAL(65, 8)) AS adjusted_cash_in,
        -- 1.4: Adjusted Cash Out - Logic kiểm tra outflow bên trong cần cập nhật
        CAST(CASE
            WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE
            THEN bd.original_cash_out ELSE 0.0
        END AS DECIMAL(65, 8)) AS adjusted_cash_out,
        -- 1.5: Cumulative Valid Purchase Value - Logic kiểm tra outflow bên trong cần cập nhật
        CAST(CASE
            WHEN bd.type = 'NFT_PURCHASE' AND
                 (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE
            THEN bd.potential_purchase_value
            ELSE 0.0
        END AS DECIMAL(65, 8)) AS cumulative_valid_purchase_value,
        -- 1.6: Cumulative Sale Equivalent Value (Không thay đổi logic này vì chỉ liên quan đến NFT_SALE)
        CAST(CASE
             WHEN bd.type = 'NFT_SALE' AND
                  (CASE WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
             THEN bd.base_purchase_value_equivalent
             ELSE 0.0
        END AS DECIMAL(65, 8)) AS cumulative_sale_equivalent_value,
        -- 1.7: Adjusted Cash Total - Logic kiểm tra outflow bên trong cần cập nhật
        CAST(
           (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_in ELSE 0.0 END) -- Đã thêm GLOBAL_MINING_COSHARE
         + (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_out ELSE 0.0 END) -- Đã thêm GLOBAL_MINING_COSHARE
          AS DECIMAL(65, 8)) AS adjusted_cash_total
    FROM BaseData bd
    WHERE bd.rn = 1

    UNION ALL

    -- ================= RECURSIVE MEMBER (rn > 1) =================
    SELECT
        bd.id, bd.userId, bd.createdDate, bd.createdAt, bd.type, bd.amount,
        bd.original_cash_in, bd.original_cash_out,
        bd.potential_purchase_value, bd.base_purchase_value_equivalent, bd.rn,
        -- 2.1: Status - Bổ sung GLOBAL_MINING_COSHARE
        CASE
            WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID_OUTFLOW_INSUFFICIENT_FUNDS'
            WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID_SALE_NO_VALID_PURCHASE_AVAILABLE'
            ELSE 'NORMAL'
        END AS transaction_validity_status,
        -- 2.2: Valid Purchase Value - Logic kiểm tra outflow bên trong cần cập nhật
        CASE
            WHEN bd.type = 'NFT_PURCHASE' AND
                 (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE
            THEN bd.potential_purchase_value
            ELSE 0.0
        END AS valid_purchase_value,
        -- 2.3: Adjusted Cash In - Logic kiểm tra outflow và sale bên trong cần cập nhật
        CASE
            WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE vào check outflow
            THEN bd.original_cash_in ELSE 0.0
        END AS adjusted_cash_in,
        -- 2.4: Adjusted Cash Out - Logic kiểm tra outflow và sale bên trong cần cập nhật
        CASE
            WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE vào check outflow
            THEN bd.original_cash_out ELSE 0.0
        END AS adjusted_cash_out,
        -- 2.5: Cumulative Valid Purchase Value - Logic kiểm tra outflow bên trong cần cập nhật
        prev.cumulative_valid_purchase_value +
        (CASE WHEN bd.type = 'NFT_PURCHASE' AND (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.potential_purchase_value ELSE 0.0 END) -- Đã thêm GLOBAL_MINING_COSHARE
        AS cumulative_valid_purchase_value,
        -- 2.6: Cumulative Sale Equivalent Value (Không thay đổi logic này)
        prev.cumulative_sale_equivalent_value +
        (CASE WHEN bd.type = 'NFT_SALE' AND (CASE WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.base_purchase_value_equivalent ELSE 0.0 END)
        AS cumulative_sale_equivalent_value,
        -- 2.7: Adjusted Cash Total - Logic kiểm tra outflow và sale bên trong cần cập nhật
        prev.adjusted_cash_total
         + (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_in ELSE 0.0 END) -- Đã thêm GLOBAL_MINING_COSHARE
         + (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_out ELSE 0.0 END) -- Đã thêm GLOBAL_MINING_COSHARE
        AS adjusted_cash_total
    FROM BaseData bd
    INNER JOIN AdjustedFlowSimulation prev ON bd.userId = prev.userId AND bd.rn = prev.rn + 1
),
-- *** THAY ĐỔI CTE NÀY ***
-- Bước bổ sung: Tìm tổng bán cuối cùng VÀ tính luôn trạng thái purchase_processing_status
FinalMarking AS (
    SELECT
        afs.*,
        -- Tìm giá trị cumulative_sale_equivalent_value lớn nhất (tức là giá trị cuối cùng) cho user
        MAX(afs.cumulative_sale_equivalent_value) OVER (PARTITION BY afs.userId) as final_cumulative_sale_equivalent,
        -- Tính luôn cột trạng thái ở đây
        CASE
            WHEN afs.type = 'NFT_PURCHASE'
             AND afs.transaction_validity_status = 'NORMAL'
             -- So sánh với giá trị MAX OVER được tính ngay trong CTE này
             AND ROUND(afs.cumulative_valid_purchase_value, 8) > ROUND(MAX(afs.cumulative_sale_equivalent_value) OVER (PARTITION BY afs.userId), 8)
            THEN 'UNPROCESSED'
            ELSE NULL
        END AS purchase_processing_status
    FROM AdjustedFlowSimulation afs
)
-- Final Select: Kết hợp với original_cash_total và sắp xếp
SELECT
    fm.id, fm.userId, fm.createdDate, fm.createdAt, fm.type, fm.amount,
    oc.original_cash_in, oc.original_cash_out, oc.original_cash_total,
    fm.transaction_validity_status,
    fm.adjusted_cash_in,
    fm.adjusted_cash_out,
    fm.adjusted_cash_total,
    fm.purchase_processing_status -- Chọn cột đã được tính trong FinalMarking
FROM FinalMarking fm
JOIN OriginalCalculations oc ON fm.id = oc.id
ORDER BY fm.userId, fm.rn;