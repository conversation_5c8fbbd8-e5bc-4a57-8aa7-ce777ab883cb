SELECT
    t.userId,
    DATE(t.createdAt) AS createdDate,
    t.createdAt,
    t.type,
    t.amount,
    -- Vẫn hiển thị amount gốc để đối chiếu
    -- cash_in: Chỉ hiển thị amount dương cho loại Nạp/Thưởng
    CASE WHEN t.type IN(
        'DEPOSIT',
        'KYC_VERIFY_COMMISSION',
        'NFT_SALE',
        'DIRECT_COMMISSION',
        'RANKING_COMMISSION',
        'CO_SHAREHOLDER_BONUS',
        'SYSTEM_COMMISSION'
    ) THEN t.amount -- G<PERSON><PERSON> định các loại này amount luôn dương
    ELSE 0
END AS cash_in,
-- cash_out: Luô<PERSON> hiển thị giá trị âm (hoặc 0) cho loại Rút/Mua
CASE WHEN t.type IN(
'NFT_PURCHASE',
'WITHDRAWAL'
) THEN - ABS(t.amount) -- *** SỬA: Đ<PERSON><PERSON> bảo luôn âm hoặc 0 ***
ELSE 0
END AS cash_out,
-- cash_total: <PERSON><PERSON><PERSON> tổng lũ<PERSON> kế, đả<PERSON> bả<PERSON>/<PERSON><PERSON> luôn trừ tiền
SUM(
    CASE
    -- Các loại cộng tiền (giả định amount dương)
    WHEN t.type IN(
        'DEPOSIT',
        'KYC_VERIFY_COMMISSION',
        'NFT_SALE',
        'DIRECT_COMMISSION',
        'RANKING_COMMISSION',
        'CO_SHAREHOLDER_BONUS',
        'SYSTEM_COMMISSION'
    ) THEN t.amount
    -- Các loại trừ tiền (luôn trừ đi giá trị tuyệt đối)
    WHEN t.type IN(
    'NFT_PURCHASE',
    'WITHDRAWAL'
    ) THEN - ABS(t.amount) -- *** SỬA: Luôn trừ đi giá trị tuyệt đối ***
    -- Các loại khác không ảnh hưởng (ví dụ: KYC_PURCHASE nếu bị loại)
    ELSE 0
END
) OVER(
    PARTITION BY t.userId
ORDER BY
    t.createdAt ASC,
    t.id ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
) AS cash_total
FROM
    transactions t
WHERE
    t.status = 'COMPLETED' -- Trạng thái hoàn thành
    AND t.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5' -- Tiền USDT
    AND t.userId = 'c1f7ec9b-bdde-4951-b48f-6113c69da840'
ORDER BY
    t.userId,
    t.createdAt ASC,
    t.id ASC;