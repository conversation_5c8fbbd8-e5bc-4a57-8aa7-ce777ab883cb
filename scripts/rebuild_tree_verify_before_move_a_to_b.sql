-- === SCRIPT KIỂM TRA TRƯỚC KHI CHUYỂN NODE ===
-- <PERSON><PERSON><PERSON> này giúp xác nhận thông tin trước khi thực hiện việc chuyển node F4892AC9 sang làm con của 947D7C1D

-- === ĐỊNH NGHĨA BIẾN ===
SET @user_to_move_code = 'F4892AC9';
SET @new_parent_code = '947D7C1D';

-- Lấy ID thực từ referralCode
SELECT id INTO @user_to_move_id
FROM users
WHERE referralCode = @user_to_move_code;

SELECT id INTO @new_parent_id
FROM users
WHERE referralCode = @new_parent_code;

-- === PHẦN 1: KIỂM TRA SỰ TỒN TẠI CỦA CÁC NODE ===
SELECT 'KIỂM TRA SỰ TỒN TẠI CỦA CÁC NODE' AS 'BƯỚC 1';

-- Kiểm tra node cần di chuyển
SELECT
    id,
    username,
    referralCode,
    referredBy AS 'cha_hiện_tại',
    path AS 'đường_dẫn_hiện_tại',
    CASE
        WHEN referralCode = @user_to_move_code THEN 'Node cần di chuyển'
        ELSE 'KHÔNG ĐÚNG - Kiểm tra lại referralCode'
    END AS 'Trạng thái'
FROM users
WHERE referralCode = @user_to_move_code;

-- Kiểm tra node cha mới
SELECT
    id,
    username,
    referralCode,
    referredBy AS 'cha_hiện_tại',
    path AS 'đường_dẫn_hiện_tại',
    CASE
        WHEN referralCode = @new_parent_code THEN 'Node cha mới'
        ELSE 'KHÔNG ĐÚNG - Kiểm tra lại referralCode'
    END AS 'Trạng thái'
FROM users
WHERE referralCode = @new_parent_code;

-- === PHẦN 2: KIỂM TRA CẤU TRÚC CÂY HIỆN TẠI ===
SELECT 'KIỂM TRA CẤU TRÚC CÂY HIỆN TẠI' AS 'BƯỚC 2';

-- Lấy thông tin cha hiện tại của node cần di chuyển
SELECT
    u.id,
    u.username,
    u.referralCode,
    u.path AS 'đường_dẫn_hiện_tại'
FROM users u
JOIN users node ON node.referredBy = u.id
WHERE node.id = @user_to_move_id;

-- Đếm số lượng con trực tiếp của node cần di chuyển
SELECT
    COUNT(*) AS 'Số_con_trực_tiếp_của_node_cần_di_chuyển'
FROM users
WHERE referredBy = @user_to_move_id;

-- Đếm tổng số con cháu của node cần di chuyển
SELECT
    COUNT(*) AS 'Tổng_số_con_cháu_của_node_cần_di_chuyển'
FROM users
WHERE path LIKE CONCAT('%', @user_to_move_id, '%');

-- === PHẦN 3: MÔ PHỎNG KẾT QUẢ SAU KHI CHUYỂN ĐỔI ===
SELECT 'MÔ PHỎNG KẾT QUẢ SAU KHI CHUYỂN ĐỔI' AS 'BƯỚC 3';

-- Lấy path hiện tại của node cần di chuyển
SELECT path INTO @old_path_of_moved_user
FROM users
WHERE id = @user_to_move_id;

-- Hiển thị thông tin node cần di chuyển
SELECT @user_to_move_id AS 'Node_cần_di_chuyển', @old_path_of_moved_user AS 'Path_hiện_tại';

-- Lấy path của node cha mới
SELECT path INTO @new_parent_path
FROM users
WHERE id = @new_parent_id;

-- Hiển thị thông tin node cha mới
SELECT @new_parent_id AS 'Node_cha_mới', @new_parent_path AS 'Path_hiện_tại';

-- Tính path mới cho node được di chuyển
SET @new_path_for_moved_user = IF(@new_parent_path IS NULL,
                                  @new_parent_id,
                                  CONCAT(@new_parent_path, '.', @new_parent_id));

-- Hiển thị thông tin path trước và sau khi chuyển đổi
SELECT
    @user_to_move_id AS 'ID_node_cần_di_chuyển',
    @old_path_of_moved_user AS 'Path_cũ',
    @new_path_for_moved_user AS 'Path_mới_dự_kiến';

-- Tính tiền tố path cũ và mới của cây con
SET @old_subtree_path_prefix = IF(@old_path_of_moved_user IS NULL,
                                   @user_to_move_id,
                                   CONCAT(@old_path_of_moved_user, '.', @user_to_move_id));
SET @new_subtree_path_prefix = CONCAT(@new_path_for_moved_user, '.', @user_to_move_id);

-- Hiển thị thông tin tiền tố path của cây con trước và sau khi chuyển đổi
SELECT
    @old_subtree_path_prefix AS 'Tiền_tố_path_cũ_của_cây_con',
    @new_subtree_path_prefix AS 'Tiền_tố_path_mới_của_cây_con';

-- === PHẦN 4: KIỂM TRA CON CHÁU SẼ BỊ ẢNH HƯỞNG ===
SELECT 'KIỂM TRA CON CHÁU SẼ BỊ ẢNH HƯỞNG' AS 'BƯỚC 4';

-- Hiển thị danh sách con trực tiếp của node cần di chuyển
SELECT
    id,
    username,
    referralCode,
    path AS 'path_hiện_tại',
    CONCAT(@new_subtree_path_prefix, SUBSTRING(path, LENGTH(@old_subtree_path_prefix) + 1)) AS 'path_mới_dự_kiến'
FROM users
WHERE referredBy = @user_to_move_id
LIMIT 100000;

-- Hiển thị danh sách con cháu của node cần di chuyển (giới hạn 10 bản ghi)
SELECT
    id,
    username,
    referralCode,
    path AS 'path_hiện_tại',
    CONCAT(@new_subtree_path_prefix, SUBSTRING(path, LENGTH(@old_subtree_path_prefix) + 1)) AS 'path_mới_dự_kiến'
FROM users
WHERE path LIKE CONCAT(@old_subtree_path_prefix, '.%')
LIMIT 100000;

-- === PHẦN 5: KIỂM TRA TÍNH HỢP LỆ CỦA VIỆC CHUYỂN ĐỔI ===
SELECT 'KIỂM TRA TÍNH HỢP LỆ CỦA VIỆC CHUYỂN ĐỔI' AS 'BƯỚC 5';

-- Kiểm tra xem node cha mới có phải là con cháu của node cần di chuyển không
-- (Nếu có thì sẽ tạo ra vòng lặp vô hạn trong cây)
SELECT
    CASE
        WHEN EXISTS (
            SELECT 1 FROM users
            WHERE id = @new_parent_id
            AND path LIKE CONCAT('%', @user_to_move_id, '%')
        )
        THEN 'CẢNH BÁO: Node cha mới là con cháu của node cần di chuyển. KHÔNG THỂ THỰC HIỆN CHUYỂN ĐỔI!'
        ELSE 'OK: Node cha mới không phải là con cháu của node cần di chuyển. Có thể thực hiện chuyển đổi.'
    END AS 'Kiểm_tra_vòng_lặp';

-- Kiểm tra độ dài path sau khi chuyển đổi
-- (Nếu quá dài có thể gây ra vấn đề với giới hạn độ dài của cột path)
SELECT
    CASE
        WHEN LENGTH(@new_subtree_path_prefix) > 100000
        THEN 'CẢNH BÁO: Path mới có thể quá dài. Kiểm tra giới hạn độ dài của cột path trong bảng users.'
        ELSE 'OK: Độ dài path mới nằm trong giới hạn cho phép.'
    END AS 'Kiểm_tra_độ_dài_path';

-- === KẾT LUẬN ===
SELECT
    'Nếu tất cả các kiểm tra trên đều OK, bạn có thể thực hiện script chuyển đổi: scripts/move-F4892AC9-to-947D7C1D.sql'
    AS 'KẾT LUẬN';
