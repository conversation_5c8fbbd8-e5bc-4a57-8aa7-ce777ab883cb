require('dotenv').config(); // Đọc file .env
const mysql = require('mysql2/promise');

// Thông tin kết nối từ .env
const dbConfig = {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT, 10),
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
};

// Hàm kết nối và cập nhật path
async function updatePaths() {
    let connection;

    try {
        // Kết nối MySQL
        connection = await mysql.createConnection(dbConfig);
        console.log('Kết nối thành công!');

        // Hàm cập nhật path cho từng user
        async function updateUserPath(userId, visited = new Set()) {
            if (visited.has(userId)) {
                return; // Tránh vòng lặp vô hạn
            }
            visited.add(userId);

            // L<PERSON>y thông tin user
            const [userRows] = await connection.execute(
                'SELECT id, referredBy, path FROM users WHERE id = ?',
                [userId]
            );
            const user = userRows[0];
            if (!user) {
                return; // User không tồn tại
            }

            const referredBy = user.referredBy;
            let newPath;

            if (referredBy){
                // Lấy thông tin referrer
                const [referrerRows] = await connection.execute(
                    'SELECT id, path FROM users WHERE id = ?',
                    [referredBy]
                );
                const referrer = referrerRows[0];

                if (referrer) {
                    // Logic ternary: referrer.path ? `${referrer.path}.${referrer.id}` : referrer.id
                    newPath = referrer.path ? `${referrer.path}.${referrer.id}` : referrer.id.toString();
                }
            }

            // Cập nhật path nếu thay đổi
            if (newPath !== undefined) {
                if (newPath !== user.path) {
                    await connection.execute(
                        'UPDATE users SET path = ? WHERE id = ?',
                        [newPath, userId]
                    );
                    console.log(`Đã cập nhật path cho user ${userId}: ${newPath}`);
                }
            }

            // Cập nhật các user con
            const [children] = await connection.execute(
                'SELECT id FROM users WHERE referredBy = ?',
                [userId]
            );

            for (const child of children) {
                await updateUserPath(child.id, visited);
            }
        }

        // Bắt đầu từ các user root (không có referredBy)
        const [rootUsers] = await connection.execute(
            'SELECT id FROM users WHERE isRoot = 1'
        );

        for (const rootUser of rootUsers) {
            await updateUserPath(rootUser.id);
        }

        console.log('Hoàn tất cập nhật path!');
    } catch (error) {
        console.error('Lỗi:', error.message);
    } finally {
        if (connection) {
            await connection.end();
            console.log('Đã đóng kết nối.');
        }
    }
}

// Chạy script
updatePaths();