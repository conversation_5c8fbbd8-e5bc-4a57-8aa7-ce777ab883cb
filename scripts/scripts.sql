-- 1. Get all transactions for a token (USDT)
SELECT 
    t.userId, 
    t.tokenId, 
    t.deposit_total,
    t.system_commission_total,
    t.withdrawal_total,
    t.nft_purchase_total,
    t.adjusted_amount,
    w.usdtBalance,
    tb.availableBalance,
    tb.totalBalance
FROM (
    SELECT 
        transactions.userId, 
        transactions.tokenId, 
        SUM(CASE WHEN transactions.type = 'DEPOSIT' THEN transactions.amount ELSE 0 END) AS deposit_total,
        SUM(CASE WHEN transactions.type = 'SYSTEM_COMMISSION' THEN transactions.amount ELSE 0 END) AS system_commission_total,
        SUM(CASE WHEN transactions.type = 'WITHDRAWAL' THEN transactions.amount ELSE 0 END) AS withdrawal_total,
        SUM(CASE WHEN transactions.type = 'NFT_PURCHASE' THEN transactions.amount ELSE 0 END) AS nft_purchase_total,
        SUM(CASE 
            WHEN transactions.type IN ('DEPOSIT', 'SYSTEM_COMMISSION', 'WITHDRAWAL') THEN transactions.amount 
            WHEN transactions.type = 'NFT_PURCHASE' THEN -transactions.amount 
            ELSE 0 
        END) AS adjusted_amount
    FROM 
        transactions 
    WHERE 
        transactions.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5' 
        AND transactions.type IN ('SYSTEM_COMMISSION', 'DEPOSIT', 'WITHDRAWAL', 'NFT_PURCHASE') 
    GROUP BY 
        transactions.userId, 
        transactions.tokenId
) t
LEFT JOIN wallets w ON t.userId = w.userId
LEFT JOIN token_balances tb ON t.userId = tb.userId AND t.tokenId = tb.tokenId;

-- 2. Cập nhật đồng bộ balance USDT cho wallets
UPDATE wallets w
INNER JOIN (
    SELECT 
        transactions.userId, 
        transactions.tokenId, 
        SUM(CASE 
            WHEN transactions.type IN ('DEPOSIT', 'SYSTEM_COMMISSION', 'WITHDRAWAL') THEN transactions.amount 
            WHEN transactions.type = 'NFT_PURCHASE' THEN -transactions.amount 
            ELSE 0 
        END) AS adjusted_amount
    FROM 
        transactions 
    WHERE 
        transactions.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5' 
        AND transactions.type IN ('SYSTEM_COMMISSION', 'DEPOSIT', 'WITHDRAWAL', 'NFT_PURCHASE') 
    GROUP BY 
        transactions.userId, 
        transactions.tokenId
) at ON w.userId = at.userId
SET w.usdtBalance = at.adjusted_amount
WHERE at.adjusted_amount >= 0;

-- 3. Cập nhật đồng bộ balance USDT cho token_balances
WITH AdjustedTransactions AS (
    SELECT 
        transactions.userId, 
        transactions.tokenId, 
        SUM(CASE 
            WHEN transactions.type IN ('DEPOSIT', 'SYSTEM_COMMISSION', 'WITHDRAWAL') THEN transactions.amount 
            WHEN transactions.type = 'NFT_PURCHASE' THEN -transactions.amount 
            ELSE 0 
        END) AS adjusted_amount
    FROM 
        transactions 
    WHERE 
        transactions.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5' 
        AND transactions.type IN ('SYSTEM_COMMISSION', 'DEPOSIT', 'WITHDRAWAL', 'NFT_PURCHASE') 
    GROUP BY 
        transactions.userId, 
        transactions.tokenId
)
UPDATE token_balances tb
INNER JOIN AdjustedTransactions at ON tb.userId = at.userId AND tb.tokenId = at.tokenId
SET tb.availableBalance = at.adjusted_amount,
    tb.totalBalance = at.adjusted_amount
WHERE at.adjusted_amount >= 0;

-- 4. Cập nhật đồng bộ balance USDT cho wallets adjusted_amount < 0 về 0
WITH AdjustedTransactions AS (
    SELECT 
        transactions.userId, 
        transactions.tokenId, 
        SUM(CASE 
            WHEN transactions.type IN ('DEPOSIT', 'SYSTEM_COMMISSION', 'WITHDRAWAL') THEN transactions.amount 
            WHEN transactions.type = 'NFT_PURCHASE' THEN -transactions.amount 
            ELSE 0 
        END) AS adjusted_amount
    FROM 
        transactions 
    WHERE 
        transactions.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5' 
        AND transactions.type IN ('SYSTEM_COMMISSION', 'DEPOSIT', 'WITHDRAWAL', 'NFT_PURCHASE') 
    GROUP BY 
        transactions.userId, 
        transactions.tokenId
)
UPDATE wallets w
INNER JOIN AdjustedTransactions at ON w.userId = at.userId
SET w.usdtBalance = 0
WHERE at.adjusted_amount < 0;

-- 5. Cập nhật đồng bộ balance USDT cho token_balances adjusted_amount < 0 về 0
WITH AdjustedTransactions AS (
    SELECT 
        transactions.userId, 
        transactions.tokenId, 
        SUM(CASE 
            WHEN transactions.type IN ('DEPOSIT', 'SYSTEM_COMMISSION', 'WITHDRAWAL') THEN transactions.amount 
            WHEN transactions.type = 'NFT_PURCHASE' THEN -transactions.amount 
            ELSE 0 
        END) AS adjusted_amount
    FROM 
        transactions 
    WHERE 
        transactions.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5' 
        AND transactions.type IN ('SYSTEM_COMMISSION', 'DEPOSIT', 'WITHDRAWAL', 'NFT_PURCHASE') 
    GROUP BY 
        transactions.userId, 
        transactions.tokenId
)
UPDATE token_balances tb
INNER JOIN AdjustedTransactions at ON tb.userId = at.userId AND tb.tokenId = at.tokenId
SET tb.availableBalance = 0,
    tb.totalBalance = 0
WHERE at.adjusted_amount < 0;


-- CÂU LỆNH TỔNG HỢP CHO CÁC LOẠI GIAO DỊCH - USDT
-- 1. Cập nhật đồng bộ balance USDT cho wallets
WITH AdjustedTransactions AS (
    SELECT 
        transactions.userId, 
        transactions.tokenId, 
        SUM(CASE 
            WHEN transactions.type IN ('DEPOSIT', 'SYSTEM_COMMISSION', 'WITHDRAWAL') THEN transactions.amount 
            WHEN transactions.type = 'NFT_PURCHASE' THEN -transactions.amount 
            ELSE 0 
        END) AS adjusted_amount
    FROM 
        transactions 
    WHERE 
        transactions.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5' 
        AND transactions.type IN ('SYSTEM_COMMISSION', 'DEPOSIT', 'WITHDRAWAL', 'NFT_PURCHASE') 
    GROUP BY 
        transactions.userId, 
        transactions.tokenId
)
UPDATE wallets w
INNER JOIN AdjustedTransactions at ON w.userId = at.userId
SET w.usdtBalance = CASE 
    WHEN at.adjusted_amount >= 0 THEN at.adjusted_amount 
    ELSE 0 
END;


-- 2. Cập nhật đồng bộ balance USDT cho token_balances
WITH AdjustedTransactions AS (
    SELECT 
        transactions.userId, 
        transactions.tokenId, 
        SUM(CASE 
            WHEN transactions.type IN ('DEPOSIT', 'SYSTEM_COMMISSION', 'WITHDRAWAL') THEN transactions.amount 
            WHEN transactions.type = 'NFT_PURCHASE' THEN -transactions.amount 
            ELSE 0 
        END) AS adjusted_amount
    FROM 
        transactions 
    WHERE 
        transactions.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5' 
        AND transactions.type IN ('SYSTEM_COMMISSION', 'DEPOSIT', 'WITHDRAWAL', 'NFT_PURCHASE') 
    GROUP BY 
        transactions.userId, 
        transactions.tokenId
)
UPDATE token_balances tb
INNER JOIN AdjustedTransactions at ON tb.userId = at.userId AND tb.tokenId = at.tokenId
SET tb.availableBalance = CASE 
    WHEN at.adjusted_amount >= 0 THEN at.adjusted_amount 
    ELSE 0 
END,
    tb.totalBalance = CASE 
    WHEN at.adjusted_amount >= 0 THEN at.adjusted_amount 
    ELSE 0 
END;


-- 3. Cập nhật đồng bộ balance USDT cho wallets không có giao dịch về 0
WITH AdjustedTransactions AS (
    SELECT 
        transactions.userId, 
        transactions.tokenId, 
        SUM(CASE 
            WHEN transactions.type IN ('DEPOSIT', 'SYSTEM_COMMISSION', 'WITHDRAWAL') THEN transactions.amount 
            WHEN transactions.type = 'NFT_PURCHASE' THEN -transactions.amount 
            ELSE 0 
        END) AS adjusted_amount
    FROM 
        transactions 
    WHERE 
        transactions.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5' 
        AND transactions.type IN ('SYSTEM_COMMISSION', 'DEPOSIT', 'WITHDRAWAL', 'NFT_PURCHASE') 
    GROUP BY 
        transactions.userId, 
        transactions.tokenId
)
UPDATE wallets w
LEFT JOIN AdjustedTransactions at ON w.userId = at.userId
SET w.usdtBalance = 0
WHERE at.userId IS NULL;


-- 4. Cập nhật đồng bộ balance USDT cho token_balances không có giao dịch về 0
WITH AdjustedTransactions AS (
    SELECT 
        transactions.userId, 
        transactions.tokenId, 
        SUM(CASE 
            WHEN transactions.type IN ('DEPOSIT', 'SYSTEM_COMMISSION', 'WITHDRAWAL') THEN transactions.amount 
            WHEN transactions.type = 'NFT_PURCHASE' THEN -transactions.amount 
            ELSE 0 
        END) AS adjusted_amount
    FROM 
        transactions 
    WHERE 
        transactions.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5' 
        AND transactions.type IN ('SYSTEM_COMMISSION', 'DEPOSIT', 'WITHDRAWAL', 'NFT_PURCHASE') 
    GROUP BY 
        transactions.userId, 
        transactions.tokenId
)
UPDATE token_balances tb
LEFT JOIN AdjustedTransactions at ON tb.userId = at.userId AND tb.tokenId = at.tokenId
SET tb.availableBalance = 0,
    tb.totalBalance = 0
WHERE at.userId IS NULL;


# 01.04.2025

-- Tạo bảng tạm để lưu kết quả
CREATE TEMPORARY TABLE temp_orders AS
WITH RECURSIVE numbers AS (
  SELECT 1 AS n
  UNION ALL
  SELECT n + 1 FROM numbers WHERE n < (SELECT MAX(quantity) FROM nft_orders WHERE status = 'PENDING')
)
SELECT 
  UUID() as new_id,
  o.orderType,
  o.traderId,
  o.nftType,
  o.nftId,
  o.nftPrice,
  1 as quantity,  -- Set quantity = 1
  o.soldQuantity,
  o.remainingQuantity,
  o.tradeSessions,
  o.status,
  o.sessionType,
  o.sessionId,
  o.gasFee,
  o.gasFeePercentage,
  o.sessionTime,
  o.matchedAt,
  o.completedAt,
  o.sellerPaid,
  o.isTransferred,
  o.createdAt,
  o.updatedAt,
  o.isScheduled,
  o.isBuy,
  o.isPriorityBuy
FROM nft_orders o
CROSS JOIN numbers n
WHERE o.status = 'PENDING' 
AND o.quantity > 1
AND n.n < o.quantity;

-- Update bản ghi gốc
UPDATE nft_orders 
SET quantity = 1,
    remainingQuantity = 1
WHERE status = 'PENDING' AND quantity > 1;

-- Insert các bản ghi mới
INSERT INTO nft_orders (
  id,
  orderType,
  traderId,
  nftType,
  nftId,
  nftPrice,
  quantity,
  soldQuantity,
  remainingQuantity,
  tradeSessions,
  status,
  sessionType,
  sessionId,
  gasFee,
  gasFeePercentage,
  sessionTime,
  matchedAt,
  completedAt,
  sellerPaid,
  isTransferred,
  createdAt,
  updatedAt,
  isScheduled,
  isBuy,
  isPriorityBuy
)
SELECT 
  new_id,
  orderType,
  traderId,
  nftType,
  nftId,
  nftPrice,
  quantity,
  soldQuantity,
  quantity as remainingQuantity,  -- Set remainingQuantity = quantity (1)
  tradeSessions,
  status,
  sessionType,
  sessionId,
  gasFee,
  gasFeePercentage,
  sessionTime,
  matchedAt,
  completedAt,
  sellerPaid,
  isTransferred,
  createdAt,
  updatedAt,
  isScheduled,
  isBuy,
  isPriorityBuy
FROM temp_orders;

-- Xóa bảng tạm
DROP TEMPORARY TABLE IF EXISTS temp_orders;

UPDATE nft_orders SET nft_orders.sessionId = 'c528dc54-d36a-4991-bbe3-6f76665f2e7b' WHERE nft_orders.status = 'PENDING' AND nft_orders.orderType = 'BUY';


-- Câu lện báo cáo tổng kết phiên
SELECT
    nft_orders.orderType,
    nft_orders.nftType,
    nft_orders.status,
    SUM(quantity),
    SUM(quantity * nftPrice) AS totalVolume
FROM
    `nft_orders`
WHERE
    nft_orders.sessionId = 'a9d6caef-cb72-4aff-a802-796b16a2b358'
GROUP BY
    nft_orders.orderType,
    nft_orders.nftType,
    nft_orders.status
ORDER BY
    nft_orders.orderType DESC;


SELECT * FROM users WHERE users.wallet = '******************************************';
SELECT * FROM wallets WHERE wallets.userId = '6cf5bbdb-61a7-4468-9281-1f7fe248f20d';
SELECT * FROM token_balances WHERE token_balances.userId = '6cf5bbdb-61a7-4468-9281-1f7fe248f20d';
SELECT * FROM nft_orders WHERE nft_orders.traderId = '6cf5bbdb-61a7-4468-9281-1f7fe248f20d' ORDER BY nft_orders.createdAt DESC;
SELECT * FROM nfts WHERE nfts.owner_id = '6cf5bbdb-61a7-4468-9281-1f7fe248f20d' ORDER BY nfts.createdAt DESC;
SELECT * FROM transactions WHERE transactions.userId = '6cf5bbdb-61a7-4468-9281-1f7fe248f20d' ORDER BY transactions.createdAt DESC;