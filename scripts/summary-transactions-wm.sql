CREATE TABLE user_transaction_summary_wm
(
    -- <PERSON><PERSON><PERSON> định danh người dùng, nên dùng kiểu dữ liệu phù hợp với cột userId gốc
    -- VARCHAR(255), UUID, INT, BIGINT tùy thuộc vào hệ thống của bạn
    userId                            VARCHAR(255)   NOT NULL PRIMARY KEY,

    -- Cặp cột (s<PERSON>, tổng tiền) cho mỗi loại giao dịch
    update_profile_bonus_num          INT            NOT NULL DEFAULT 0,
    update_profile_bonus_amount       DECIMAL(18, 8) NOT NULL DEFAULT 0.00, -- <PERSON><PERSON><PERSON><PERSON> chỉnh độ chính xác (precision, scale) nếu cần

    deposit_num                       INT            NOT NULL DEFAULT 0,
    deposit_amount                    DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    withdrawal_num                    INT            NOT NULL DEFAULT 0,
    withdrawal_amount                 DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    transfer_num                      INT            NOT NULL DEFAULT 0,
    transfer_amount                   DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    mining_num                        INT            NOT NULL DEFAULT 0,
    mining_amount                     DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    referral_bonus_num                INT            NOT NULL DEFAULT 0,
    referral_bonus_amount             DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    referral_bonus_mining_num         INT            NOT NULL DEFAULT 0,
    referral_bonus_mining_amount      DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    task_reward_num                   INT            NOT NULL DEFAULT 0,
    task_reward_amount                DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    kyc_verify_bonus_num              INT            NOT NULL DEFAULT 0,
    kyc_verify_bonus_amount           DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    kyc_verify_commission_num         INT            NOT NULL DEFAULT 0,
    kyc_verify_commission_amount      DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    kyc_purchase_num                  INT            NOT NULL DEFAULT 0,
    kyc_purchase_amount               DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    lightning_bolt_purchase_num       INT            NOT NULL DEFAULT 0,
    lightning_bolt_purchase_amount    DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    priority_position_purchase_num    INT            NOT NULL DEFAULT 0,
    priority_position_purchase_amount DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    nft_purchase_num                  INT            NOT NULL DEFAULT 0,
    nft_purchase_amount               DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    nft_withdrawal_num                INT            NOT NULL DEFAULT 0,
    nft_withdrawal_amount             DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    nft_sale_num                      INT            NOT NULL DEFAULT 0,
    nft_sale_amount                   DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    sale_bonus_num                    INT            NOT NULL DEFAULT 0,
    sale_bonus_amount                 DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    direct_commission_num             INT            NOT NULL DEFAULT 0,
    direct_commission_amount          DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    ranking_commission_num            INT            NOT NULL DEFAULT 0,
    ranking_commission_amount         DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    co_shareholder_bonus_num          INT            NOT NULL DEFAULT 0,
    co_shareholder_bonus_amount       DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    gas_fee_num                       INT            NOT NULL DEFAULT 0,
    gas_fee_amount                    DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    scheduling_fee_num                INT            NOT NULL DEFAULT 0,
    scheduling_fee_amount             DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    system_commission_num             INT            NOT NULL DEFAULT 0,
    system_commission_amount          DECIMAL(18, 8) NOT NULL DEFAULT 0.00,

    -- (Tùy chọn) Thêm các cột metadata nếu cần
    last_updated_at                   TIMESTAMP               DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

INSERT INTO user_transaction_summary_wm (
    userId,
    update_profile_bonus_num, update_profile_bonus_amount,
    deposit_num, deposit_amount,
    withdrawal_num, withdrawal_amount,
    transfer_num, transfer_amount,
    mining_num, mining_amount,
    referral_bonus_num, referral_bonus_amount,
    referral_bonus_mining_num, referral_bonus_mining_amount,
    task_reward_num, task_reward_amount,
    kyc_verify_bonus_num, kyc_verify_bonus_amount,
    kyc_verify_commission_num, kyc_verify_commission_amount,
    kyc_purchase_num, kyc_purchase_amount,
    lightning_bolt_purchase_num, lightning_bolt_purchase_amount,
    priority_position_purchase_num, priority_position_purchase_amount,
    nft_purchase_num, nft_purchase_amount,
    nft_withdrawal_num, nft_withdrawal_amount,
    nft_sale_num, nft_sale_amount,
    sale_bonus_num, sale_bonus_amount,
    direct_commission_num, direct_commission_amount,
    ranking_commission_num, ranking_commission_amount,
    co_shareholder_bonus_num, co_shareholder_bonus_amount,
    gas_fee_num, gas_fee_amount,
    scheduling_fee_num, scheduling_fee_amount,
    system_commission_num, system_commission_amount
    -- Không cần thêm last_updated_at ở đây nếu nó có DEFAULT
)
SELECT
    transactions.userId,

    -- Sao chép các biểu thức COUNT(CASE...) và SUM(CASE...) từ câu truy vấn pivot vào đây
    COUNT(CASE WHEN transactions.type = 'UPDATE_PROFILE_BONUS' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'UPDATE_PROFILE_BONUS' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'DEPOSIT' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'DEPOSIT' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'WITHDRAWAL' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'WITHDRAWAL' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'TRANSFER' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'TRANSFER' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'MINING' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'MINING' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'REFERRAL_BONUS' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'REFERRAL_BONUS' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'REFERRAL_BONUS_MINING' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'REFERRAL_BONUS_MINING' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'TASK_REWARD' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'TASK_REWARD' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'KYC_VERIFY_BONUS' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'KYC_VERIFY_BONUS' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'KYC_VERIFY_COMMISSION' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'KYC_VERIFY_COMMISSION' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'KYC_PURCHASE' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'KYC_PURCHASE' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'LIGHTNING_BOLT_PURCHASE' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'LIGHTNING_BOLT_PURCHASE' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'PRIORITY_POSITION_PURCHASE' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'PRIORITY_POSITION_PURCHASE' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'NFT_PURCHASE' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'NFT_PURCHASE' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'NFT_WITHDRAWAL' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'NFT_WITHDRAWAL' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'NFT_SALE' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'NFT_SALE' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'SALE_BONUS' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'SALE_BONUS' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'DIRECT_COMMISSION' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'DIRECT_COMMISSION' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'RANKING_COMMISSION' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'RANKING_COMMISSION' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'CO_SHAREHOLDER_BONUS' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'CO_SHAREHOLDER_BONUS' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'GAS_FEE' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'GAS_FEE' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'SCHEDULING_FEE' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'SCHEDULING_FEE' THEN amount ELSE 0 END),

    COUNT(CASE WHEN transactions.type = 'SYSTEM_COMMISSION' THEN 1 END),
    SUM(CASE WHEN transactions.type = 'SYSTEM_COMMISSION' THEN amount ELSE 0 END)
FROM
    transactions
WHERE
    transactions.tokenId = 'f74d2210-d158-405b-bf9a-65a1c46f406c' AND transactions.status = 'COMPLETED'
GROUP BY
    transactions.userId; -- Nhóm theo userId để SELECT hoạt động đúng

-- Nếu bạn muốn cập nhật dữ liệu đã có thay vì chỉ insert mới (upsert):
-- MySQL/MariaDB: Thêm `ON DUPLICATE KEY UPDATE` vào cuối lệnh INSERT
-- PostgreSQL: Dùng `INSERT ... ON CONFLICT ... DO UPDATE`
-- SQL Server: Dùng `MERGE`