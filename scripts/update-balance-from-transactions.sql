-- <PERSON><PERSON><PERSON> lệnh SQL để cập nhật số dư USDT trong wallets và token_balances
-- dựa trên lịch sử giao dịch

-- 1. T<PERSON><PERSON> bảng tạm để lưu số dư tính toán từ giao dịch
CREATE TEMPORARY TABLE temp_calculated_balances AS
WITH TransactionSummary AS (
    SELECT 
        transactions.userId,
        -- Tổng tiền nạp
        SUM(CASE WHEN transactions.type = 'DEPOSIT' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS deposit_total,
        
        -- Tổng tiền rút
        SUM(CASE WHEN transactions.type = 'WITHDRAWAL' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS withdrawal_total,
        
        -- Tổng lãi từ staking
        SUM(CASE WHEN transactions.type = 'GLOBAL_MINING_COSHARE_INTEREST' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS interest_total,
        
        -- Tổng hoa hồng trực tiếp
        SUM(CASE WHEN transactions.type = 'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS direct_commission_total,
        
        -- Tổng hoa hồng matching
        SUM(CASE WHEN transactions.type = 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS matching_commission_total,
        
        -- Tổng hoa hồng lợi nhuận
        SUM(CASE WHEN transactions.type = 'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS profit_commission_total,
        
        -- Tổng hoa hồng rank
        SUM(CASE WHEN transactions.type = 'GLOBAL_MINING_COSHARE_COMMISSION_RANK' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS rank_commission_total,
        
        -- Tổng hoa hồng cổ đông
        SUM(CASE WHEN transactions.type = 'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS shareholder_commission_total,
        
        -- Tổng chi tiêu mua NFT
        SUM(CASE WHEN transactions.type = 'NFT_PURCHASE' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS nft_purchase_total,
        
        -- Tổng thu từ bán NFT
        SUM(CASE WHEN transactions.type = 'NFT_SALE' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS nft_sale_total,
        
        -- Tổng hoa hồng hệ thống
        SUM(CASE WHEN transactions.type = 'SYSTEM_COMMISSION' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS system_commission_total
    FROM 
        transactions
    WHERE 
        transactions.tokenId = (SELECT id FROM tokens WHERE symbol = 'USDT')
        AND transactions.status = 'COMPLETED'
    GROUP BY 
        transactions.userId
)
SELECT 
    ts.userId,
    -- Tính số dư lý thuyết
    GREATEST(
        (
            COALESCE(ts.deposit_total, 0) + 
            COALESCE(ts.interest_total, 0) + 
            COALESCE(ts.direct_commission_total, 0) + 
            COALESCE(ts.matching_commission_total, 0) + 
            COALESCE(ts.profit_commission_total, 0) + 
            COALESCE(ts.rank_commission_total, 0) + 
            COALESCE(ts.shareholder_commission_total, 0) + 
            COALESCE(ts.nft_sale_total, 0) + 
            COALESCE(ts.system_commission_total, 0) - 
            COALESCE(ts.withdrawal_total, 0) - 
            COALESCE(ts.nft_purchase_total, 0)
        ), 0
    ) AS calculated_balance
FROM 
    TransactionSummary ts;

-- 2. Cập nhật số dư trong bảng wallets
UPDATE wallets w
JOIN temp_calculated_balances tcb ON w.userId = tcb.userId
SET w.usdtBalance = tcb.calculated_balance;

-- 3. Cập nhật số dư trong bảng token_balances
UPDATE token_balances tb
JOIN temp_calculated_balances tcb ON tb.userId = tcb.userId
SET 
    tb.availableBalance = tcb.calculated_balance,
    tb.totalBalance = tcb.calculated_balance
WHERE 
    tb.tokenId = (SELECT id FROM tokens WHERE symbol = 'USDT');

-- 4. Xóa bảng tạm
DROP TEMPORARY TABLE IF EXISTS temp_calculated_balances;

-- 5. Kiểm tra kết quả sau khi cập nhật
SELECT 
    u.id AS userId,
    u.username,
    u.wallet AS walletAddress,
    w.usdtBalance AS wallet_balance,
    tb.availableBalance AS token_balance
FROM 
    users u
JOIN wallets w ON u.id = w.userId
JOIN token_balances tb ON u.id = tb.userId AND tb.tokenId = (SELECT id FROM tokens WHERE symbol = 'USDT')
WHERE 
    w.usdtBalance > 0 OR tb.availableBalance > 0
ORDER BY 
    w.usdtBalance DESC;
