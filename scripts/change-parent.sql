-- === BẮT ĐẦU GIAO DỊCH ===
START TRANSACTION;

-- === ĐỊNH NGHĨA BIẾN ===
SET @user_to_move_id = '19025A6C';
SET @new_parent_id = '5D080DB9';

-- === LẤY THÔNG TIN HIỆN TẠI ===
-- Lấy path hiện tại của người dùng cần di chuyển
SELECT path INTO @old_path_of_moved_user
FROM users
WHERE id = @user_to_move_id;

-- Lấy path của người cha mới
SELECT path INTO @new_parent_path
FROM users
WHERE id = @new_parent_id;

-- === TÍNH TOÁN PATH MỚI ===
-- Tính path mới cho người dùng được di chuyển (19025A6C)
-- Nếu cha mới là root (path=NULL), path mới = id cha mới
-- Nếu cha mới không phải root, path mới = path cha mới + '.' + id cha mới
SET @new_path_for_moved_user = IF(@new_parent_path IS NULL,
                                  @new_parent_id,
                                  CONCAT(@new_parent_path, '.', @new_parent_id));

-- Tính tiền tố path CŨ của cây con (dùng để TÌM các con cháu)
-- Path của con cháu bắt đầu bằng path_cũ_của_cha.id_cha
SET @old_subtree_path_prefix = IF(@old_path_of_moved_user IS NULL,
                                   @user_to_move_id,
                                   CONCAT(@old_path_of_moved_user, '.', @user_to_move_id));
SET @old_subtree_path_prefix_like = CONCAT(@old_subtree_path_prefix, '.%'); -- Dùng cho LIKE

-- Tính tiền tố path MỚI của cây con (dùng để THAY THẾ)
-- Path mới của con cháu sẽ bắt đầu bằng path_mới_của_cha.id_cha
SET @new_subtree_path_prefix = CONCAT(@new_path_for_moved_user, '.', @user_to_move_id);

-- === CẬP NHẬT DỮ LIỆU ===

-- Bước 1 & 4 (trong mô tả của bạn): Cập nhật cho chính người dùng được di chuyển
UPDATE users
SET
    referredBy = @new_parent_id,
    path = @new_path_for_moved_user
WHERE
    id = @user_to_move_id;

-- Bước 3 (trong mô tả của bạn): Cập nhật path cho TẤT CẢ con cháu trong cây
UPDATE users
SET
    path = CONCAT(@new_subtree_path_prefix, SUBSTRING(path, LENGTH(@old_subtree_path_prefix) + 1))
WHERE
    -- Chỉ cập nhật các bản ghi có path bắt đầu bằng tiền tố cũ của cây con
    -- Ví dụ: Nếu old_subtree_path_prefix = '1.2.3', thì tìm các path như '1.2.3.4', '1.2.3.5.6', ...
    path LIKE @old_subtree_path_prefix_like;

-- === KẾT THÚC GIAO DỊCH ===
-- Kiểm tra lại kết quả nếu cần trước khi COMMIT
-- SELECT id, referredBy, path FROM users WHERE id = @user_to_move_id;
-- SELECT id, referredBy, path FROM users WHERE path LIKE CONCAT(@new_subtree_path_prefix, '.%') LIMIT 10;

-- Nếu mọi thứ ổn thoả
COMMIT;

-- Nếu có lỗi xảy ra, chạy lệnh này để hoàn tác
-- ROLLBACK;