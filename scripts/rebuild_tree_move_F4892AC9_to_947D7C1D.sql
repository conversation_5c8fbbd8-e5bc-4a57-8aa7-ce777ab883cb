-- === BẮT ĐẦU GIAO DỊCH AN TOÀN ===
START TRANSACTION;

-- Tắt chế độ safe update mode
SET SQL_SAFE_UPDATES = 0;

-- === ĐỊNH NGHĨA BIẾN ===
SET @user_to_move_code = 'F4892AC9';
SET @new_parent_code = '947D7C1D';

-- Lấy ID thực từ referralCode
SELECT id INTO @user_to_move_id
FROM users
WHERE referralCode = @user_to_move_code;

SELECT id INTO @new_parent_id
FROM users
WHERE referralCode = @new_parent_code;

-- === LẤY THÔNG TIN HIỆN TẠI ===
-- Lấy path hiện tại của người dùng cần di chuyển
SELECT path INTO @old_path_of_moved_user
FROM users
WHERE id = @user_to_move_id;

-- Lấy path của người cha mới
SELECT path INTO @new_parent_path
FROM users
WHERE id = @new_parent_id;

-- === HIỂN THỊ THÔNG TIN TRƯỚC KHI CẬP NHẬT ===
SELECT 'THÔNG TIN TRƯỚC KHI CẬP NHẬT' AS 'BƯỚC 1';
-- (<PERSON><PERSON><PERSON> câu SELECT hiển thị thông tin như cũ)
SELECT id, referralCode, referredBy AS 'cha_hiện_tại', path AS 'đường_dẫn_hiện_tại' FROM users WHERE id = @user_to_move_id;
SELECT id, referralCode, referredBy, path AS 'đường_dẫn_hiện_tại' FROM users WHERE id = @new_parent_id;
SELECT COUNT(*) AS 'Số_con_trực_tiếp' FROM users WHERE referredBy = @user_to_move_id;
-- Tính path gốc của con cháu (path của cha + '.' + id cha)
SET @old_children_base_path = CONCAT(IFNULL(CONCAT(@old_path_of_moved_user, '.'), ''), @user_to_move_id);
SELECT COUNT(*) AS 'Số_con_cháu_theo_path' FROM users WHERE path LIKE CONCAT(@old_children_base_path, '.%');


-- === TÍNH TOÁN PATH MỚI ===
-- Path mới cho người dùng được di chuyển
SET @new_path_for_moved_user = IF(@new_parent_path IS NULL,
                                  @new_parent_id,
                                  CONCAT(@new_parent_path, '.', @new_parent_id));

-- Path mới cho con TRỰC TIẾP của người dùng được di chuyển
SET @new_path_for_direct_children = CONCAT(@new_path_for_moved_user, '.', @user_to_move_id);

-- === KIỂM TRA TRƯỚC KHI CẬP NHẬT ===
-- (Các câu SELECT kiểm tra như cũ, có thể thêm kiểm tra mô phỏng cho con trực tiếp)
SELECT 'KIỂM TRA TRƯỚC KHI CẬP NHẬT' AS 'BƯỚC 2';
-- ...

-- === CẬP NHẬT DỮ LIỆU (ĐÚNG THỨ TỰ) ===
SELECT 'BẮT ĐẦU CẬP NHẬT DỮ LIỆU' AS 'BƯỚC 3';

-- Bước 1: Cập nhật path cho CON CHÁU SÂU HƠN (grandchildren+) TRƯỚC NHẤT (Nếu có)
-- Path mới = path mới của con trực tiếp + '.' + phần còn lại của path cũ
-- Phần đầu path cũ của con cháu sâu hơn = path cũ của con trực tiếp = @old_children_base_path
UPDATE users
SET
    path = CONCAT(@new_path_for_direct_children, '.',
                 SUBSTRING(path, LENGTH(@old_children_base_path) + 2)) -- +2 để bỏ qua dấu '.' sau ID cha cũ
WHERE
    path LIKE CONCAT(@old_children_base_path, '.%') -- Tìm con cháu sâu hơn (có thêm dấu '.' sau ID cha)
    AND path <> CONCAT(@new_path_for_direct_children, '.', SUBSTRING(path, LENGTH(@old_children_base_path) + 2));

-- Bước 2: Cập nhật path cho CON TRỰC TIẾP TIẾP THEO
-- Path mới của con trực tiếp = path mới của cha nó + '.' + ID của cha nó
UPDATE users
SET
    path = @new_path_for_direct_children
WHERE
    referredBy = @user_to_move_id -- Điều kiện chính xác để tìm con trực tiếp
    AND path <> @new_path_for_direct_children; -- Tránh update không cần thiết

-- Bước 3: Cập nhật cho chính người dùng được di chuyển CUỐI CÙNG
UPDATE users
SET
    referredBy = @new_parent_id,
    path = @new_path_for_moved_user
WHERE
    id = @user_to_move_id
    AND (referredBy IS NULL OR referredBy <> @new_parent_id OR path IS NULL OR path <> @new_path_for_moved_user); -- Đảm bảo update nếu có thay đổi hoặc là NULL


-- === KIỂM TRA KẾT QUẢ ===
SELECT 'KIỂM TRA KẾT QUẢ SAU KHI CẬP NHẬT' AS 'BƯỚC 4';

-- Kiểm tra node đã được di chuyển
SELECT id, referralCode, referredBy AS 'cha_mới', path AS 'đường_dẫn_mới' FROM users WHERE id = @user_to_move_id;

-- Kiểm tra một số con trực tiếp đã được cập nhật path
SELECT id, referralCode, referredBy, path AS 'đường_dẫn_mới' FROM users WHERE referredBy = @user_to_move_id LIMIT 10;

-- Kiểm tra một số con cháu sâu hơn đã được cập nhật path (nếu có)
SELECT id, referralCode, referredBy, path AS 'đường_dẫn_mới' FROM users WHERE path LIKE CONCAT(@new_path_for_direct_children, '.%') LIMIT 10;

-- Bật lại chế độ safe update mode
SET SQL_SAFE_UPDATES = 1;

-- === KẾT THÚC GIAO DỊCH ===
-- !!! KIỂM TRA KỸ LƯỠNG KẾT QUẢ TRƯỚC KHI COMMIT !!!

-- Nếu chắc chắn đúng:
COMMIT;

-- Nếu có lỗi hoặc sai sót:
-- ROLLBACK;