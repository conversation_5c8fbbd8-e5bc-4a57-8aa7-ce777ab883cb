-- <PERSON><PERSON>n MySQL 8.0+ hoặc các hệ quản trị CSDL khác hỗ trợ Recursive CTEs
SET SESSION cte_max_recursion_depth = 10000;

WITH RECURSIVE BaseData AS (
    -- Bước 0: <PERSON><PERSON><PERSON> bị dữ liệu gốc và thêm số thứ tự
    SELECT
        t.id,
        t.userId,
        DATE(t.createdAt) AS createdDate,
        t.createdAt,
        t.type,
        t.amount,
        CASE WHEN t.type IN(
            'DEPOSIT', 'KYC_VERIFY_COMMISSION', 'NFT_SALE', 'DIRECT_COMMISSION', 'RANKING_COMMISSION',
            'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST',
            'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING',
            'GLO<PERSON>L_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK',
            'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER'
        ) THEN t.amount ELSE 0 END AS original_cash_in,
        CASE WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE')
             THEN -ABS(t.amount) ELSE 0 END AS original_cash_out,
        CASE WHEN t.type = 'NFT_PURCHASE' THEN ABS(t.amount) ELSE 0 END AS potential_purchase_value,
        CASE
            WHEN t.type = 'NFT_SALE' THEN
                CASE ABS(t.amount)
                    WHEN 102.5  THEN 100.0 WHEN 205.0  THEN 200.0 WHEN 256.25 THEN 250.0
                    WHEN 512.5  THEN 500.0 WHEN 1025.0 THEN 1000.0 ELSE 0
                END
            ELSE 0
        END AS base_purchase_value_equivalent,
        ROW_NUMBER() OVER (PARTITION BY t.userId ORDER BY t.createdAt ASC, t.id ASC) as rn
    FROM
        transactions t
    WHERE
        t.status = 'COMPLETED'
        AND t.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5'
        AND t.userId = '97e4ba41-a933-4bb6-a50e-2a96d82a2fa6'
),
-- CTE này tính toán original_cash_total để JOIN lại ở cuối
OriginalCalculations AS (
     SELECT
        t.id,
        t.userId,
        -- Tính cash_total gốc (theo logic ban đầu user cung cấp)
        SUM(
            CASE
                WHEN t.type IN(
                    'DEPOSIT', 'KYC_VERIFY_COMMISSION', 'NFT_SALE', 'DIRECT_COMMISSION', 'RANKING_COMMISSION',
                    'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST',
                    'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING',
                    'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK',
                    'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER'
                ) THEN t.amount
                WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE')
                THEN -ABS(t.amount)
                ELSE 0
            END
        ) OVER(
            PARTITION BY t.userId ORDER BY t.createdAt ASC, t.id ASC
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS original_cash_total,
        -- Lấy original_cash_in/out để hiển thị lại
        CASE WHEN t.type IN( 'DEPOSIT', 'KYC_VERIFY_COMMISSION', 'NFT_SALE', 'DIRECT_COMMISSION', 'RANKING_COMMISSION', 'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST', 'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK', 'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER' ) THEN t.amount ELSE 0 END AS original_cash_in,
        CASE WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') THEN -ABS(t.amount) ELSE 0 END AS original_cash_out
    FROM
        transactions t
    WHERE
        t.status = 'COMPLETED'
        AND t.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5'
        AND t.userId = '97e4ba41-a933-4bb6-a50e-2a96d82a2fa6'
),
AdjustedFlowSimulation (
    id, userId, createdDate, createdAt, type, amount,
    original_cash_in_base, original_cash_out_base, -- Đổi tên để tránh trùng lặp
    potential_purchase_value, base_purchase_value_equivalent, rn,
    transaction_validity_status,
    valid_purchase_value,
    adjusted_cash_in,
    adjusted_cash_out,
    cumulative_valid_purchase_value,
    cumulative_sale_equivalent_value,
    adjusted_cash_total
) AS (
    -- ================= ANCHOR MEMBER (rn=1) =================
    SELECT
        bd.id, bd.userId, bd.createdDate, bd.createdAt, bd.type, bd.amount,
        bd.original_cash_in, bd.original_cash_out, -- Giữ tên gốc trong BaseData
        bd.potential_purchase_value, bd.base_purchase_value_equivalent, bd.rn,
        -- 1.1: Status
        CASE
            WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (0 + bd.original_cash_out < 0) THEN 'INVALID_OUTFLOW_INSUFFICIENT_FUNDS'
            WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID_SALE_NO_VALID_PURCHASE_AVAILABLE'
            ELSE 'NORMAL'
        END AS transaction_validity_status,
        -- 1.2: Valid Purchase Value
        CASE
            WHEN bd.type = 'NFT_PURCHASE' AND
                 (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (0 + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
            THEN bd.potential_purchase_value
            ELSE 0.0
        END AS valid_purchase_value,
        -- 1.3: Adjusted Cash In
        CASE
            WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (0 + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
            THEN bd.original_cash_in ELSE 0.0
        END AS adjusted_cash_in,
        -- 1.4: Adjusted Cash Out
        CASE
            WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (0 + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
            THEN bd.original_cash_out ELSE 0.0
        END AS adjusted_cash_out,
        -- 1.5: Cumulative Valid Purchase Value
        CASE
            WHEN bd.type = 'NFT_PURCHASE' AND
                 (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (0 + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
            THEN bd.potential_purchase_value
            ELSE 0.0
        END AS cumulative_valid_purchase_value,
        -- 1.6: Cumulative Sale Equivalent Value
        CASE
             WHEN bd.type = 'NFT_SALE' AND
                  (CASE WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
             THEN bd.base_purchase_value_equivalent
             ELSE 0.0
        END AS cumulative_sale_equivalent_value,
        -- 1.7: Adjusted Cash Total
         (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (0 + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_in ELSE 0.0 END)
       + (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (0 + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_out ELSE 0.0 END)
        AS adjusted_cash_total
    FROM BaseData bd
    WHERE bd.rn = 1

    UNION ALL

    -- ================= RECURSIVE MEMBER (rn > 1) =================
    SELECT
        bd.id, bd.userId, bd.createdDate, bd.createdAt, bd.type, bd.amount,
        bd.original_cash_in, bd.original_cash_out, -- Giữ tên gốc trong BaseData
        bd.potential_purchase_value, bd.base_purchase_value_equivalent, bd.rn,
        -- 2.1: Status
        CASE
            WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID_OUTFLOW_INSUFFICIENT_FUNDS'
            WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID_SALE_NO_VALID_PURCHASE_AVAILABLE'
            ELSE 'NORMAL'
        END AS transaction_validity_status,
        -- 2.2: Valid Purchase Value
        CASE
            WHEN bd.type = 'NFT_PURCHASE' AND
                 (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
            THEN bd.potential_purchase_value
            ELSE 0.0
        END AS valid_purchase_value,
        -- 2.3: Adjusted Cash In
        CASE
            WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
            THEN bd.original_cash_in ELSE 0.0
        END AS adjusted_cash_in,
        -- 2.4: Adjusted Cash Out
        CASE
            WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
            THEN bd.original_cash_out ELSE 0.0
        END AS adjusted_cash_out,
        -- 2.5: Cumulative Valid Purchase Value
        prev.cumulative_valid_purchase_value +
        (CASE WHEN bd.type = 'NFT_PURCHASE' AND (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.potential_purchase_value ELSE 0.0 END)
        AS cumulative_valid_purchase_value,
        -- 2.6: Cumulative Sale Equivalent Value
        prev.cumulative_sale_equivalent_value +
        (CASE WHEN bd.type = 'NFT_SALE' AND (CASE WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.base_purchase_value_equivalent ELSE 0.0 END)
        AS cumulative_sale_equivalent_value,
        -- 2.7: Adjusted Cash Total
        prev.adjusted_cash_total
         + (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_in ELSE 0.0 END)
         + (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_out ELSE 0.0 END)
        AS adjusted_cash_total
    FROM BaseData bd
    INNER JOIN AdjustedFlowSimulation prev ON bd.userId = prev.userId AND bd.rn = prev.rn + 1 -- Join với dòng trước đó
)
-- Final Select: Kết hợp với original_cash_total và sắp xếp
SELECT
    afs.id, afs.userId, afs.createdDate, afs.createdAt, afs.type, afs.amount,
    -- Lấy original_cash_in/out/total từ CTE `OriginalCalculations`
    oc.original_cash_in,
    oc.original_cash_out,
    oc.original_cash_total,
    -- Các cột từ RCTE
    afs.transaction_validity_status,
    afs.adjusted_cash_in,
    afs.adjusted_cash_out,
    afs.adjusted_cash_total
    -- , afs.cumulative_valid_purchase_value, afs.cumulative_sale_equivalent_value -- Debug
FROM AdjustedFlowSimulation afs
-- *** ĐÃ SỬA JOIN TẠI ĐÂY ***
JOIN OriginalCalculations oc ON afs.id = oc.id -- Join lại để lấy original_cash_total và các cột original khác
ORDER BY afs.userId, afs.rn;