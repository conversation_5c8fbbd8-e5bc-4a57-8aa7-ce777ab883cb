SELECT
    t.id,
    t.userId,
    DAT<PERSON>(t.createdAt) AS createdDate,
    t.createdAt,
    t.type,
    t.amount,
    -- Vẫn hiển thị amount gốc để đối chiếu
    -- cash_in: Chỉ hiển thị amount dương cho loại Nạp/Thưởng
    CASE WHEN t.type IN(
        'DEPOSIT',
        'KYC_VERIFY_COMMISSION',
        'NFT_SALE',
        'DIRECT_COMMISSION',
        'RANKING_COMMISSION',
        'CO_SHAREHOLDER_BONUS',
        'SYSTEM_COMMISSION',
        -- STACKING
        'GLOBAL_MINING_COSHARE_INTEREST',
        'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT',
        'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING',
        'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING',
        'GLOBAL_MINING_COSHARE_COMMISSION_RANK',
        'GLO<PERSON>L_MINING_COSHARE_COMMISSION_SHAREHOLDER'
    ) THEN t.amount -- <PERSON><PERSON><PERSON> định các loại này amount luôn dương
    ELSE 0
END AS cash_in,
-- cash_out: Luôn hiển thị giá trị âm (hoặc 0) cho loại Rút/Mua
CASE WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') THEN - ABS(t.amount) -- *** SỬA: Đảm bảo luôn âm hoặc 0 ***
ELSE 0
END AS cash_out,
-- cash_total: Tính tổng lũy kế, đảm bảo Rút/Mua luôn trừ tiền
SUM(
    CASE
    -- Các loại cộng tiền (giả định amount dương)
    WHEN t.type IN(
        'DEPOSIT',
        'KYC_VERIFY_COMMISSION',
        'NFT_SALE',
        'DIRECT_COMMISSION',
        'RANKING_COMMISSION',
        'CO_SHAREHOLDER_BONUS',
        'SYSTEM_COMMISSION',
        -- STACKING
        'GLOBAL_MINING_COSHARE_INTEREST',
        'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT',
        'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING',
        'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING',
        'GLOBAL_MINING_COSHARE_COMMISSION_RANK',
        'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER'
    ) THEN t.amount
    -- Các loại trừ tiền (luôn trừ đi giá trị tuyệt đối)
    WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') THEN - ABS(t.amount) -- *** SỬA: Luôn trừ đi giá trị tuyệt đối ***
    -- Các loại khác không ảnh hưởng (ví dụ: KYC_PURCHASE nếu bị loại)
    ELSE 0
END
) OVER(
    PARTITION BY t.userId
ORDER BY
    t.createdAt ASC,
    t.id ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
) AS cash_total
FROM
    transactions t
WHERE
    t.status = 'COMPLETED' -- Trạng thái hoàn thành
    AND t.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5' -- Tiền USDT
    AND t.userId = '1a8d8404-2c4c-4735-a759-05c2be595f72'
ORDER BY
    t.userId,
    t.createdAt ASC,
    t.id ASC;