-- Cần MySQL 8.0+ hoặc các hệ quản trị CSDL khác hỗ trợ Recursive CTEs
SET SESSION cte_max_recursion_depth = 10000;

-- *** ĐỊNH NGHĨA USER ID MỘT LẦN Ở ĐÂY ***
SET @target_user_id = '4c22299c-907d-431f-ad81-b8284cacfc52'; -- <<<<<<<<<< THAY USER ID DUY NHẤT Ở ĐÂY

WITH FinalAdjustedData AS ( -- Đặt toàn bộ truy vấn Phiên bản 12.4 vào CTE này
WITH RECURSIVE BaseData AS (
    -- Bước 0: <PERSON><PERSON><PERSON> bị dữ liệu gốc và thêm số thứ tự
    SELECT
        t.id,
        t.userId,
        DATE(t.createdAt) AS createdDate,
        t.createdAt,
        t.type,
        t.amount, -- Ki<PERSON>u gốc là DECIMAL(18, 8)
        CASE WHEN t.type IN(
            'DEPOSIT', 'KYC_VERIFY_COMMISSION', 'NFT_SALE', 'DIRECT_COMMISSION', 'RANKING_COMMISSION',
            'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST',
            'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING',
            'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK',
            'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER'
        ) THEN t.amount ELSE 0 END AS original_cash_in,
        CASE WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') -- Đã bao gồm ở đây
             THEN -ABS(t.amount) ELSE 0 END AS original_cash_out,
        CASE WHEN t.type = 'NFT_PURCHASE' THEN ABS(t.amount) ELSE 0 END AS potential_purchase_value,
        CASE
            WHEN t.type = 'NFT_SALE' THEN ROUND(ABS(t.amount) / 1.025, 8)
            ELSE 0
        END AS base_purchase_value_equivalent,
        ROW_NUMBER() OVER (PARTITION BY t.userId ORDER BY t.createdAt ASC, t.id ASC) as rn
    FROM
        transactions t
    WHERE
        t.status = 'COMPLETED'
        AND t.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5'
        AND t.userId = @target_user_id
),
-- CTE này tính toán original_cash_total để JOIN lại ở cuối
OriginalCalculations AS (
     SELECT
        t.id,
        t.userId,
        -- Tính cash_total gốc (theo logic ban đầu user cung cấp)
        SUM(
            CASE
                WHEN t.type IN(
                    'DEPOSIT', 'KYC_VERIFY_COMMISSION', 'NFT_SALE', 'DIRECT_COMMISSION', 'RANKING_COMMISSION',
                    'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST',
                    'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING',
                    'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK',
                    'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER'
                ) THEN t.amount
                WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') -- Đã bao gồm ở đây
                THEN -ABS(t.amount)
                ELSE 0
            END
        ) OVER(
            PARTITION BY t.userId ORDER BY t.createdAt ASC, t.id ASC
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS original_cash_total,
        -- Lấy original_cash_in/out để hiển thị lại
        CASE WHEN t.type IN( 'DEPOSIT', 'KYC_VERIFY_COMMISSION', 'NFT_SALE', 'DIRECT_COMMISSION', 'RANKING_COMMISSION', 'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST', 'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK', 'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER' ) THEN t.amount ELSE 0 END AS original_cash_in,
        CASE WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') THEN -ABS(t.amount) ELSE 0 END AS original_cash_out -- Đã bao gồm ở đây
    FROM
        transactions t
    WHERE
        t.status = 'COMPLETED'
        AND t.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5'
        AND t.userId = @target_user_id
),
AdjustedFlowSimulation (
    id, userId, createdDate, createdAt, type, amount,
    original_cash_in_base, original_cash_out_base,
    potential_purchase_value, base_purchase_value_equivalent, rn,
    transaction_validity_status,
    valid_purchase_value,
    adjusted_cash_in,
    adjusted_cash_out,
    cumulative_valid_purchase_value,
    cumulative_sale_equivalent_value,
    adjusted_cash_total
) AS (
    -- ================= ANCHOR MEMBER (rn=1) =================
    SELECT
        bd.id, bd.userId, bd.createdDate, bd.createdAt, bd.type, bd.amount,
        bd.original_cash_in, bd.original_cash_out,
        bd.potential_purchase_value, bd.base_purchase_value_equivalent, bd.rn,
        -- 1.1: Status - Bổ sung GLOBAL_MINING_COSHARE
        CASE
            WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID_OUTFLOW_INSUFFICIENT_FUNDS'
            WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID_SALE_NO_VALID_PURCHASE_AVAILABLE'
            ELSE 'NORMAL'
        END AS transaction_validity_status,
        -- 1.2: Valid Purchase Value - Logic kiểm tra outflow bên trong cần cập nhật
        CAST(CASE
            WHEN bd.type = 'NFT_PURCHASE' AND
                 (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE
            THEN bd.potential_purchase_value
            ELSE 0.0
        END AS DECIMAL(65, 8)) AS valid_purchase_value,
        -- 1.3: Adjusted Cash In - Logic kiểm tra outflow bên trong cần cập nhật
        CAST(CASE
            WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE
            THEN bd.original_cash_in ELSE 0.0
        END AS DECIMAL(65, 8)) AS adjusted_cash_in,
        -- 1.4: Adjusted Cash Out - Logic kiểm tra outflow bên trong cần cập nhật
        CAST(CASE
            WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE
            THEN bd.original_cash_out ELSE 0.0
        END AS DECIMAL(65, 8)) AS adjusted_cash_out,
        -- 1.5: Cumulative Valid Purchase Value - Logic kiểm tra outflow bên trong cần cập nhật
        CAST(CASE
            WHEN bd.type = 'NFT_PURCHASE' AND
                 (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE
            THEN bd.potential_purchase_value
            ELSE 0.0
        END AS DECIMAL(65, 8)) AS cumulative_valid_purchase_value,
        -- 1.6: Cumulative Sale Equivalent Value (Không thay đổi logic này vì chỉ liên quan đến NFT_SALE)
        CAST(CASE
             WHEN bd.type = 'NFT_SALE' AND
                  (CASE WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
             THEN bd.base_purchase_value_equivalent
             ELSE 0.0
        END AS DECIMAL(65, 8)) AS cumulative_sale_equivalent_value,
        -- 1.7: Adjusted Cash Total - Logic kiểm tra outflow bên trong cần cập nhật
        CAST(
           (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_in ELSE 0.0 END) -- Đã thêm GLOBAL_MINING_COSHARE
         + (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_out ELSE 0.0 END) -- Đã thêm GLOBAL_MINING_COSHARE
          AS DECIMAL(65, 8)) AS adjusted_cash_total
    FROM BaseData bd
    WHERE bd.rn = 1

    UNION ALL

    -- ================= RECURSIVE MEMBER (rn > 1) =================
    SELECT
        bd.id, bd.userId, bd.createdDate, bd.createdAt, bd.type, bd.amount,
        bd.original_cash_in, bd.original_cash_out,
        bd.potential_purchase_value, bd.base_purchase_value_equivalent, bd.rn,
        -- 2.1: Status - Bổ sung GLOBAL_MINING_COSHARE
        CASE
            WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID_OUTFLOW_INSUFFICIENT_FUNDS'
            WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID_SALE_NO_VALID_PURCHASE_AVAILABLE'
            ELSE 'NORMAL'
        END AS transaction_validity_status,
        -- 2.2: Valid Purchase Value - Logic kiểm tra outflow bên trong cần cập nhật
        CASE
            WHEN bd.type = 'NFT_PURCHASE' AND
                 (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE
            THEN bd.potential_purchase_value
            ELSE 0.0
        END AS valid_purchase_value,
        -- 2.3: Adjusted Cash In - Logic kiểm tra outflow và sale bên trong cần cập nhật
        CASE
            WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE vào check outflow
            THEN bd.original_cash_in ELSE 0.0
        END AS adjusted_cash_in,
        -- 2.4: Adjusted Cash Out - Logic kiểm tra outflow và sale bên trong cần cập nhật
        CASE
            WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' -- Đã thêm GLOBAL_MINING_COSHARE vào check outflow
            THEN bd.original_cash_out ELSE 0.0
        END AS adjusted_cash_out,
        -- 2.5: Cumulative Valid Purchase Value - Logic kiểm tra outflow bên trong cần cập nhật
        prev.cumulative_valid_purchase_value +
        (CASE WHEN bd.type = 'NFT_PURCHASE' AND (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.potential_purchase_value ELSE 0.0 END) -- Đã thêm GLOBAL_MINING_COSHARE
        AS cumulative_valid_purchase_value,
        -- 2.6: Cumulative Sale Equivalent Value (Không thay đổi logic này)
        prev.cumulative_sale_equivalent_value +
        (CASE WHEN bd.type = 'NFT_SALE' AND (CASE WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.base_purchase_value_equivalent ELSE 0.0 END)
        AS cumulative_sale_equivalent_value,
        -- 2.7: Adjusted Cash Total - Logic kiểm tra outflow và sale bên trong cần cập nhật
        prev.adjusted_cash_total
         + (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_in ELSE 0.0 END) -- Đã thêm GLOBAL_MINING_COSHARE
         + (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_out ELSE 0.0 END) -- Đã thêm GLOBAL_MINING_COSHARE
        AS adjusted_cash_total
    FROM BaseData bd
    INNER JOIN AdjustedFlowSimulation prev ON bd.userId = prev.userId AND bd.rn = prev.rn + 1
),
-- *** THAY ĐỔI CTE NÀY ***
-- Bước bổ sung: Tìm tổng bán cuối cùng VÀ tính luôn trạng thái purchase_processing_status
FinalMarking AS (
    SELECT
        afs.*,
        -- Tìm giá trị cumulative_sale_equivalent_value lớn nhất (tức là giá trị cuối cùng) cho user
        MAX(afs.cumulative_sale_equivalent_value) OVER (PARTITION BY afs.userId) as final_cumulative_sale_equivalent,
        -- Tính luôn cột trạng thái ở đây
        CASE
            WHEN afs.type = 'NFT_PURCHASE'
             AND afs.transaction_validity_status = 'NORMAL'
             -- So sánh với giá trị MAX OVER được tính ngay trong CTE này
             AND ROUND(afs.cumulative_valid_purchase_value, 8) > ROUND(MAX(afs.cumulative_sale_equivalent_value) OVER (PARTITION BY afs.userId), 8)
            THEN 'UNPROCESSED'
            ELSE NULL
        END AS purchase_processing_status
    FROM AdjustedFlowSimulation afs
)
-- Final Select: Kết hợp với original_cash_total và sắp xếp
SELECT
    fm.id, fm.userId, fm.createdDate, fm.createdAt, fm.type, fm.amount,
    oc.original_cash_in, oc.original_cash_out, oc.original_cash_total,
    fm.transaction_validity_status,
    fm.adjusted_cash_in,
    fm.adjusted_cash_out,
    fm.adjusted_cash_total,
    fm.purchase_processing_status -- Chọn cột đã được tính trong FinalMarking
FROM FinalMarking fm
JOIN OriginalCalculations oc ON fm.id = oc.id
),
-- Bước cuối: Tổng hợp từ dữ liệu đã điều chỉnh
SummaryCalculations AS (
    SELECT
    userId,

    -- 1. Tổng Nạp
    SUM(CASE WHEN type = 'DEPOSIT' THEN 1 ELSE 0 END) AS deposit_count,
    SUM(CASE WHEN type = 'DEPOSIT' THEN adjusted_cash_in ELSE 0 END) AS deposit_total_amount,

    -- 2. Tổng Bán (Hợp Lệ)
    SUM(CASE WHEN type = 'NFT_SALE' AND transaction_validity_status = 'NORMAL' THEN 1 ELSE 0 END) AS valid_sale_count,
    SUM(CASE WHEN type = 'NFT_SALE' AND transaction_validity_status = 'NORMAL' THEN adjusted_cash_in ELSE 0 END) AS valid_sale_total_amount,

    -- 3. Tổng Mua (Hợp Lệ)
    SUM(CASE WHEN type = 'NFT_PURCHASE' AND transaction_validity_status = 'NORMAL' THEN 1 ELSE 0 END) AS valid_purchase_count,
    SUM(CASE WHEN type = 'NFT_PURCHASE' AND transaction_validity_status = 'NORMAL' THEN -adjusted_cash_out ELSE 0 END) AS valid_purchase_total_amount, -- Lấy giá trị dương

    -- 4. Lãi NFT THỰC TẾ
    SUM(CASE WHEN type = 'NFT_SALE' AND transaction_validity_status = 'NORMAL' THEN adjusted_cash_in ELSE 0 END) - SUM(CASE WHEN type = 'NFT_SALE' AND transaction_validity_status = 'NORMAL' THEN adjusted_cash_in / 1.025 ELSE 0 END) AS actual_nft_profit,

    -- 4b. Tổng giá trị mua hợp lệ CHƯA được bán
    (SUM(CASE WHEN type = 'NFT_PURCHASE' AND transaction_validity_status = 'NORMAL' THEN -adjusted_cash_out ELSE 0 END) - SUM(CASE WHEN type = 'NFT_SALE' AND transaction_validity_status = 'NORMAL' THEN adjusted_cash_in / 1.025 ELSE 0 END)) AS unprocessed_valid_purchase_value,

    -- 5. Tổng Rút
    SUM(CASE WHEN type = 'WITHDRAWAL' THEN 1 ELSE 0 END) AS actual_withdrawal_count, -- Đếm tất cả lệnh rút
    SUM(CASE WHEN type = 'WITHDRAWAL' THEN -original_cash_out ELSE 0 END) AS actual_withdrawal_total_amount, -- Tổng tiền rút thực tế (từ original_cash_out)
    SUM(CASE WHEN type = 'WITHDRAWAL' AND transaction_validity_status = 'NORMAL' THEN 1 ELSE 0 END) AS valid_withdrawal_count, -- Đếm lệnh rút hợp lệ
    SUM(CASE WHEN type = 'WITHDRAWAL' AND transaction_validity_status = 'NORMAL' THEN -adjusted_cash_out ELSE 0 END) AS valid_withdrawal_total_amount, -- Tổng tiền rút hợp lệ

    -- *** 5b. (Mới) Tổng GLOBAL_MINING_COSHARE (Hợp Lệ) ***
    SUM(CASE WHEN type = 'GLOBAL_MINING_COSHARE' AND transaction_validity_status = 'NORMAL' THEN 1 ELSE 0 END) AS valid_global_mining_coshare_count,
    SUM(CASE WHEN type = 'GLOBAL_MINING_COSHARE' AND transaction_validity_status = 'NORMAL' THEN -adjusted_cash_out ELSE 0 END) AS valid_global_mining_coshare_total_amount, -- Lấy giá trị dương

    -- 6. Tổng Hoa Hồng
    SUM(CASE WHEN type IN ('KYC_VERIFY_COMMISSION', 'DIRECT_COMMISSION', 'RANKING_COMMISSION', 'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST', 'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK', 'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER') THEN 1 ELSE 0 END) AS commission_count,
    SUM(CASE WHEN type IN ('KYC_VERIFY_COMMISSION', 'DIRECT_COMMISSION', 'RANKING_COMMISSION', 'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST', 'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK', 'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER') THEN adjusted_cash_in ELSE 0 END) AS commission_total_amount,

    -- 7. Số dư cuối cùng (Cách tính không đổi, vẫn bao gồm tất cả dòng tiền hợp lệ)
    SUM(adjusted_cash_in + adjusted_cash_out) AS final_adjusted_balance,

    -- *** 8. (Mới) Số dư cuối cùng SAU KHI trừ tất cả các lần RÚT THỰC TẾ ***
    (SUM(adjusted_cash_in + adjusted_cash_out) -- Lấy số dư cuối hợp lệ
     - (SUM(CASE WHEN type = 'WITHDRAWAL' THEN -original_cash_out ELSE 0 END) -- Trừ tổng rút thực tế
        - SUM(CASE WHEN type = 'WITHDRAWAL' AND transaction_validity_status = 'NORMAL' THEN -adjusted_cash_out ELSE 0 END)) -- Cộng lại phần rút hợp lệ đã bị trừ trong final_adjusted_balance
    ) AS balance_after_actual_withdrawals

FROM FinalAdjustedData
GROUP BY userId
),
-- *** THÊM CÁC CTE LẤY THÔNG TIN HIỆN TẠI ***
UserInfo AS (
    SELECT id, referralCode, wallet, isKycCompleted, name, phone, email
    FROM users
    WHERE id = @target_user_id
),
WalletInfo AS (
    SELECT userId, usdtBalance
    FROM wallets
    WHERE userId = @target_user_id
),
CurrentNfts AS (
    SELECT
        owner_id,
        COALESCE(SUM(currentPrice * quantity), 0) AS total_hold_nft_value,
        COALESCE(GROUP_CONCAT(CONCAT(type, ' (Qty: ', quantity, ')') ORDER BY type SEPARATOR ', '), 'Không có') AS owned_nft_summary
    FROM nfts
    WHERE owner_id = @target_user_id
    GROUP BY owner_id
),
PendingNftOrders AS (
    SELECT
        traderId,
        COUNT(*) AS pending_order_count,
        COALESCE(SUM(nftPrice * quantity), 0) AS total_pending_nft_price_value,
        COALESCE(GROUP_CONCAT(CONCAT(orderType, ' ', quantity, ' ', nftType) ORDER BY createdAt SEPARATOR '; '), 'Không có') AS pending_order_details
    FROM nft_orders
    WHERE traderId = @target_user_id
      AND status = 'PENDING'
    GROUP BY traderId
),
PendingWithdrawals AS (
    SELECT
        userId,
        COUNT(*) AS pending_withdrawal_count,
        COALESCE(SUM(ABS(amount)), 0) AS pending_withdrawal_amount -- Lấy tổng dương
    FROM transactions
    WHERE userId = @target_user_id
      AND type = 'WITHDRAWAL'
      AND status = 'PENDING'
    GROUP BY userId
)
-- *** THAY ĐỔI SELECT CUỐI CÙNG ĐỂ TẠO BÁO CÁO ***
SELECT
    CONCAT(
        'FINANCIAL AUDIT REPORT:\n\n',
        'Name: ', COALESCE(ui.name, 'N/A'), '\n',
        'Email: ', COALESCE(ui.email, 'N/A'), '\n',
        'Phone: ', COALESCE(ui.phone, 'N/A'), '\n',
        'Wallet (BNB): ', COALESCE(ui.wallet, 'N/A'), '\n',
        'Referral Code: ', COALESCE(ui.referralCode, 'N/A'), '\n',
        'KYC Status: ', IF(ui.isKycCompleted = 1, 'Completed', 'None'), '\n\n',
        'System Status:\n',
        'Wallet Balance (USDT): ', FORMAT(COALESCE(wi.usdtBalance, 0), 8), '\n',
        'NFT Holdings: ', COALESCE(cn.owned_nft_summary, 'None'), '\n',
        '   * Total Value (Current Price): ', FORMAT(COALESCE(cn.total_hold_nft_value, 0), 8), '\n',
        'Pending NFT Orders: ', COALESCE(pno.pending_order_details, 'None'), ' (', COALESCE(pno.pending_order_count, 0), ')\n',
        '   * Total Value (nftPrice): ', FORMAT(COALESCE(pno.total_pending_nft_price_value, 0), 8), '\n',
        'Pending Withdrawals: ', FORMAT(COALESCE(pw.pending_withdrawal_amount, 0), 8), ' (', COALESCE(pw.pending_withdrawal_count, 0), ')\n\n',
        'Audit Details:\n',
        '1. Deposits: ', COALESCE(cs.deposit_count, 0), ' txn, total ', FORMAT(COALESCE(cs.deposit_total_amount, 0), 8), '\n',
        '2. Withdrawals: ', COALESCE(cs.actual_withdrawal_count, 0), ' txn, total ', FORMAT(COALESCE(cs.actual_withdrawal_total_amount, 0), 8), '\n',
        '   (Valid: ', COALESCE(cs.valid_withdrawal_count, 0), ' txn, total ', FORMAT(COALESCE(cs.valid_withdrawal_total_amount, 0), 8), ')\n',
        '3. NFT Profit (Net): ', FORMAT(COALESCE(cs.actual_nft_profit, 0), 8), '\n',
        '   *(Valid ', COALESCE(cs.valid_sale_count, 0), ' SELL: ', FORMAT(COALESCE(cs.valid_sale_total_amount, 0), 8), ', BUY: ', FORMAT(COALESCE(cs.valid_sale_total_amount, 0) / 1.025, 8), ')*\n',
        '4. Commissions: ', COALESCE(cs.commission_count, 0), ' txn, total ', FORMAT(COALESCE(cs.commission_total_amount, 0), 8), '\n',
        '5. NFT Inventory (Cost): ', FORMAT(COALESCE(cs.unprocessed_valid_purchase_value, 0), 8), '\n',
        '   *(Valid ', COALESCE(cs.valid_purchase_count, 0), ' txn, Purchases: ', FORMAT(COALESCE(cs.valid_purchase_total_amount, 0), 8), ' - Sold: ', FORMAT(COALESCE(cs.valid_sale_total_amount, 0) / 1.025, 8), ')*\n',
        '6. GLOBAL_MINING_COSHARE: ', COALESCE(cs.valid_global_mining_coshare_count, 0), ' txn, total ', FORMAT(COALESCE(cs.valid_global_mining_coshare_total_amount, 0), 8), '\n',
        '7. Final Adj. Balance: ', FORMAT(COALESCE(cs.final_adjusted_balance,0), 8), '\n\n',
        'Reconciliation:\n',
        '- Dep + Profit + Comm - Valid W/D - Valid GMC = ', FORMAT(COALESCE(cs.deposit_total_amount,0)+COALESCE(cs.actual_nft_profit,0)+COALESCE(cs.commission_total_amount,0)-COALESCE(cs.valid_withdrawal_total_amount,0)-COALESCE(cs.valid_global_mining_coshare_total_amount,0), 8), '\n',
        '- Final Adj. Balance + NFT Inventory = ', FORMAT(COALESCE(cs.final_adjusted_balance,0)+COALESCE(cs.unprocessed_valid_purchase_value,0), 8), '\n\n',
        '-----------------------------------------\n',
        'CONCLUSION:\n',
        '- Final Adj. Balance + NFT Inventory (1) = ', FORMAT(COALESCE(cs.final_adjusted_balance,0)+COALESCE(cs.unprocessed_valid_purchase_value,0), 8), '\n',
        '- Invalid W/D (2) = ', FORMAT(COALESCE(cs.actual_withdrawal_total_amount,0)-COALESCE(cs.valid_withdrawal_total_amount,0), 8), '\n',
        '- NFT Holdings (3) = ', FORMAT(COALESCE(cn.total_hold_nft_value,0) + COALESCE(pno.total_pending_nft_price_value,0), 8), '\n\n',
        '- Pending W/D (4) = ', FORMAT(COALESCE(pw.pending_withdrawal_amount,0), 8), '\n',
        '- System Balance (5) = ', FORMAT(COALESCE(wi.usdtBalance,0), 8), '\n\n',
        '-----------------------------------------\n',
        'DISCREPANCY:\n',
        ' - Total (1) - (2) - (3) = ', FORMAT(COALESCE(cs.final_adjusted_balance,0)+COALESCE(cs.unprocessed_valid_purchase_value,0)-COALESCE(cs.actual_withdrawal_total_amount,0)+COALESCE(cs.valid_withdrawal_total_amount,0)-COALESCE(cn.total_hold_nft_value,0) - COALESCE(pno.total_pending_nft_price_value,0), 8), '\n',
        ' - Total (4) + (5) = ', FORMAT(COALESCE(pw.pending_withdrawal_amount,0)+COALESCE(wi.usdtBalance,0), 8), '\n\n'
    ) AS full_financial_report,
    FORMAT(COALESCE(cs.final_adjusted_balance,0)+COALESCE(cs.unprocessed_valid_purchase_value,0)-COALESCE(cs.actual_withdrawal_total_amount,0)+COALESCE(cs.valid_withdrawal_total_amount,0)-COALESCE(cn.total_hold_nft_value,0) - COALESCE(pno.total_pending_nft_price_value,0)-COALESCE(pw.pending_withdrawal_amount,0), 8) AS discrepancy,
    FORMAT(COALESCE(wi.usdtBalance,0), 8) AS system_balance
FROM
    SummaryCalculations cs -- Bảng tổng hợp chính
LEFT JOIN UserInfo ui ON cs.userId = ui.id
LEFT JOIN WalletInfo wi ON cs.userId = wi.userId
LEFT JOIN CurrentNfts cn ON cs.userId = cn.owner_id
LEFT JOIN PendingNftOrders pno ON cs.userId = pno.traderId
LEFT JOIN PendingWithdrawals pw ON cs.userId = pw.userId;