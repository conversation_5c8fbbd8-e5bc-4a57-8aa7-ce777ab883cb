-- <PERSON><PERSON><PERSON> truy vấn để tính số dư USDT của người dùng dựa trên lịch sử giao dịch
-- T<PERSON>h tổng (Nạp + lãi + hoa hồng) - rút = số dư lý thuyết
-- So sánh với số dư thực tế trong bảng wallets và token_balances

WITH TransactionSummary AS (
    SELECT 
        transactions.userId,
        -- Tổng tiền nạp
        SUM(CASE WHEN transactions.type = 'DEPOSIT' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS deposit_total,
        
        -- Tổng tiền rút
        SUM(CASE WHEN transactions.type = 'WITHDRAWAL' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS withdrawal_total,
        
        -- Tổng lãi từ staking
        SUM(CASE WHEN transactions.type = 'GLOBAL_MINING_COSHARE_INTEREST' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS interest_total,
        
        -- Tổng hoa hồng trực tiếp
        SUM(CASE WHEN transactions.type = 'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS direct_commission_total,
        
        -- Tổng hoa hồng matching
        SUM(CASE WHEN transactions.type = 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS matching_commission_total,
        
        -- Tổng hoa hồng lợi nhuận
        SUM(CASE WHEN transactions.type = 'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS profit_commission_total,
        
        -- Tổng hoa hồng rank
        SUM(CASE WHEN transactions.type = 'GLOBAL_MINING_COSHARE_COMMISSION_RANK' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS rank_commission_total,
        
        -- Tổng hoa hồng cổ đông
        SUM(CASE WHEN transactions.type = 'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS shareholder_commission_total,
        
        -- Tổng chi tiêu mua NFT
        SUM(CASE WHEN transactions.type = 'NFT_PURCHASE' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS nft_purchase_total,
        
        -- Tổng thu từ bán NFT
        SUM(CASE WHEN transactions.type = 'NFT_SALE' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS nft_sale_total,
        
        -- Tổng hoa hồng hệ thống
        SUM(CASE WHEN transactions.type = 'SYSTEM_COMMISSION' AND transactions.status = 'COMPLETED' THEN transactions.amount ELSE 0 END) AS system_commission_total
    FROM 
        transactions
    WHERE 
        transactions.tokenId = (SELECT id FROM tokens WHERE symbol = 'USDT')
        AND transactions.status = 'COMPLETED'
    GROUP BY 
        transactions.userId
)
SELECT 
    u.id AS userId,
    u.username,
    u.wallet AS walletAddress,
    ts.deposit_total,
    ts.withdrawal_total,
    ts.interest_total,
    ts.direct_commission_total,
    ts.matching_commission_total,
    ts.profit_commission_total,
    ts.rank_commission_total,
    ts.shareholder_commission_total,
    ts.nft_purchase_total,
    ts.nft_sale_total,
    ts.system_commission_total,
    
    -- Tính tổng thu nhập
    (
        COALESCE(ts.deposit_total, 0) + 
        COALESCE(ts.interest_total, 0) + 
        COALESCE(ts.direct_commission_total, 0) + 
        COALESCE(ts.matching_commission_total, 0) + 
        COALESCE(ts.profit_commission_total, 0) + 
        COALESCE(ts.rank_commission_total, 0) + 
        COALESCE(ts.shareholder_commission_total, 0) + 
        COALESCE(ts.nft_sale_total, 0) + 
        COALESCE(ts.system_commission_total, 0)
    ) AS total_income,
    
    -- Tính tổng chi tiêu
    (
        COALESCE(ts.withdrawal_total, 0) + 
        COALESCE(ts.nft_purchase_total, 0)
    ) AS total_expense,
    
    -- Tính số dư lý thuyết
    (
        COALESCE(ts.deposit_total, 0) + 
        COALESCE(ts.interest_total, 0) + 
        COALESCE(ts.direct_commission_total, 0) + 
        COALESCE(ts.matching_commission_total, 0) + 
        COALESCE(ts.profit_commission_total, 0) + 
        COALESCE(ts.rank_commission_total, 0) + 
        COALESCE(ts.shareholder_commission_total, 0) + 
        COALESCE(ts.nft_sale_total, 0) + 
        COALESCE(ts.system_commission_total, 0) - 
        COALESCE(ts.withdrawal_total, 0) - 
        COALESCE(ts.nft_purchase_total, 0)
    ) AS calculated_balance,
    
    -- Số dư thực tế trong bảng wallets
    w.usdtBalance AS wallet_balance,
    
    -- Số dư thực tế trong bảng token_balances
    tb.availableBalance AS token_balance,
    
    -- Chênh lệch giữa số dư lý thuyết và số dư thực tế trong wallets
    (
        (
            COALESCE(ts.deposit_total, 0) + 
            COALESCE(ts.interest_total, 0) + 
            COALESCE(ts.direct_commission_total, 0) + 
            COALESCE(ts.matching_commission_total, 0) + 
            COALESCE(ts.profit_commission_total, 0) + 
            COALESCE(ts.rank_commission_total, 0) + 
            COALESCE(ts.shareholder_commission_total, 0) + 
            COALESCE(ts.nft_sale_total, 0) + 
            COALESCE(ts.system_commission_total, 0) - 
            COALESCE(ts.withdrawal_total, 0) - 
            COALESCE(ts.nft_purchase_total, 0)
        ) - w.usdtBalance
    ) AS wallet_difference,
    
    -- Chênh lệch giữa số dư lý thuyết và số dư thực tế trong token_balances
    (
        (
            COALESCE(ts.deposit_total, 0) + 
            COALESCE(ts.interest_total, 0) + 
            COALESCE(ts.direct_commission_total, 0) + 
            COALESCE(ts.matching_commission_total, 0) + 
            COALESCE(ts.profit_commission_total, 0) + 
            COALESCE(ts.rank_commission_total, 0) + 
            COALESCE(ts.shareholder_commission_total, 0) + 
            COALESCE(ts.nft_sale_total, 0) + 
            COALESCE(ts.system_commission_total, 0) - 
            COALESCE(ts.withdrawal_total, 0) - 
            COALESCE(ts.nft_purchase_total, 0)
        ) - tb.availableBalance
    ) AS token_balance_difference
FROM 
    users u
LEFT JOIN TransactionSummary ts ON u.id = ts.userId
LEFT JOIN wallets w ON u.id = w.userId
LEFT JOIN token_balances tb ON u.id = tb.userId AND tb.tokenId = (SELECT id FROM tokens WHERE symbol = 'USDT')
WHERE 
    -- Lọc những người dùng có giao dịch hoặc có số dư
    (ts.userId IS NOT NULL OR w.usdtBalance > 0 OR tb.availableBalance > 0)
ORDER BY 
    ABS(
        (
            COALESCE(ts.deposit_total, 0) + 
            COALESCE(ts.interest_total, 0) + 
            COALESCE(ts.direct_commission_total, 0) + 
            COALESCE(ts.matching_commission_total, 0) + 
            COALESCE(ts.profit_commission_total, 0) + 
            COALESCE(ts.rank_commission_total, 0) + 
            COALESCE(ts.shareholder_commission_total, 0) + 
            COALESCE(ts.nft_sale_total, 0) + 
            COALESCE(ts.system_commission_total, 0) - 
            COALESCE(ts.withdrawal_total, 0) - 
            COALESCE(ts.nft_purchase_total, 0)
        ) - COALESCE(w.usdtBalance, 0)
    ) DESC;

-- Câu truy vấn tổng hợp để kiểm tra tổng số dư toàn hệ thống
SELECT 
    'Tổng số dư lý thuyết' AS description,
    SUM(
        COALESCE(ts.deposit_total, 0) + 
        COALESCE(ts.interest_total, 0) + 
        COALESCE(ts.direct_commission_total, 0) + 
        COALESCE(ts.matching_commission_total, 0) + 
        COALESCE(ts.profit_commission_total, 0) + 
        COALESCE(ts.rank_commission_total, 0) + 
        COALESCE(ts.shareholder_commission_total, 0) + 
        COALESCE(ts.nft_sale_total, 0) + 
        COALESCE(ts.system_commission_total, 0) - 
        COALESCE(ts.withdrawal_total, 0) - 
        COALESCE(ts.nft_purchase_total, 0)
    ) AS amount
FROM 
    users u
LEFT JOIN TransactionSummary ts ON u.id = ts.userId

UNION ALL

SELECT 
    'Tổng số dư thực tế (wallets)' AS description,
    SUM(w.usdtBalance) AS amount
FROM 
    wallets w

UNION ALL

SELECT 
    'Tổng số dư thực tế (token_balances)' AS description,
    SUM(tb.availableBalance) AS amount
FROM 
    token_balances tb
WHERE 
    tb.tokenId = (SELECT id FROM tokens WHERE symbol = 'USDT');

-- Kiểm tra chi tiết cho một người dùng cụ thể (thay thế USER_ID bằng ID người dùng cần kiểm tra)
-- SELECT * FROM transactions 
-- WHERE userId = 'USER_ID' 
-- AND tokenId = (SELECT id FROM tokens WHERE symbol = 'USDT')
-- AND status = 'COMPLETED'
-- ORDER BY createdAt;
