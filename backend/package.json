{"name": "worldmall-crypto-platform-backend", "version": "1.0.0", "description": "Backend service for WorldMall Crypto Platform", "author": "", "private": true, "license": "MIT", "engines": {"node": ">=20.0.0"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:nft-init": "ts-node test/nft-init-test.ts", "test:trading-session": "ts-node test/trading-session-test.ts", "test:session-debug": "ts-node test/session-debug.ts", "test:session-concurrency": "ts-node test/session-concurrency-test.ts", "test:nft-order": "ts-node test/nft-test.ts", "test:process-orders": "ts-node test/process-orders-test.ts", "test:immediate-purchase": "ts-node test/immediate-purchase-test.ts", "test:scheduling": "ts-node test/schedule-session-test.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/axios": "^4.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^11.0.11", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^7.1.17", "@nestjs/typeorm": "^10.0.1", "@nestjs/websockets": "^11.0.11", "@types/qrcode": "^1.5.5", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cookie-parser": "^1.4.7", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "ethers": "^6.13.5", "moment-timezone": "^0.5.47", "mysql2": "^3.6.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "qrcode": "^1.5.4", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "typeorm": "^0.3.17", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0", "cron": "^3.5.0", "ioredis": "^5.3.2", "bull": "^4.12.2", "@nestjs/bull": "^11.0.2", "redlock": "^5.0.0-beta.2", "siwe": "^3.0.0", "node-telegram-bot-api": "^0.61.0", "bignumber.js": "^9.1.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/axios": "^0.14.4", "@types/bcryptjs": "^2.4.6", "@types/dotenv": "^8.2.3", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "axios": "^1.8.2", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "pnpm": {"onlyBuiltDependencies": ["@nestjs/core"]}}