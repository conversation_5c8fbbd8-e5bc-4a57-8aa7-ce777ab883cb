# WorldMall Crypto Platform - Backend

Backend service cho nền tảng WorldMall Crypto, đư<PERSON><PERSON> xây dựng bằng NestJS và TypeORM.

## Tính năng

### 1. Quản lý người dùng
- <PERSON><PERSON><PERSON> ký, đ<PERSON><PERSON>h<PERSON>, <PERSON><PERSON><PERSON> thực JWT
- Qu<PERSON>n lý thông tin cá nhân
- <PERSON><PERSON> thống xếp hạng (BRONZE, SILVER, GOLD, PLATINUM, DIAMOND)
- Chương trình giới thiệu (Referral)

### 2. Mining System
- Đào WM token hàng ngày
- Hệ số mining tăng theo rank và NFT
- <PERSON><PERSON> khó tăng theo tổng lượng đã đào
- <PERSON><PERSON><PERSON> hệ số mining từ Lightning và Staking

### 3. NFT System
- 4 loại NFT: Phoenix (Phụng), Turtle (Quy), Unicorn (Lân), Dragon (Long)
- Mỗi loại có số lượng giới hạn và giá khác nhau
- Tăng hiệu suất mining khi sở hữu
- <PERSON>em thông tin và lịch sử giao dịch NFT

### 4. Lightning System
- Mua Lightning để tăng hiệu suất mining
- 5 tier giá theo số lượng mua
- Tự động áp dụng hệ số mining x2
- Xem lịch sử mua và sử dụng Lightning

### 5. Staking System
- 3 tier staking với lợi suất và hệ số khác nhau
- Nhận thưởng hàng tháng
- Tăng hiệu suất mining khi staking
- Quản lý và theo dõi staking position

### 6. Task System
- Nhiều loại nhiệm vụ: social media, testing, design...
- Phần thưởng bằng WM token
- Hệ thống xác minh hoàn thành nhiệm vụ
- Theo dõi tiến độ và lịch sử nhiệm vụ

### 7. Wallet System
- Tạo và quản lý ví WM token
- Ghi nhận tất cả giao dịch: mining, task, staking...
- Chuyển token giữa các ví
- Xem lịch sử giao dịch chi tiết

## Cài đặt

1. Clone repository:
\`\`\`bash
git clone <repository_url>
cd worldmall-crypto-platform/backend
\`\`\`

2. Cài đặt dependencies:
\`\`\`bash
npm install
\`\`\`

3. Tạo file .env và cấu hình các biến môi trường:
\`\`\`bash
cp .env.example .env
\`\`\`

4. Khởi động MySQL và tạo database:
\`\`\`bash
mysql -u root -p
CREATE DATABASE worldmall_crypto;
\`\`\`

5. Chạy ứng dụng:
\`\`\`bash
# development
npm run start

# watch mode
npm run start:dev

# production mode
npm run start:prod
\`\`\`

## API Documentation

Sau khi khởi động ứng dụng, truy cập Swagger UI tại:
\`http://localhost:5000/api-docs\`

## Cấu trúc thư mục

\`\`\`
src/
├── auth/           # Xác thực và phân quyền
├── common/         # Entities và enums dùng chung
├── lightning/      # Quản lý lightning
├── mining/         # Quản lý đào coin
├── nft/            # Quản lý NFT
├── staking/        # Quản lý staking
├── task/           # Quản lý nhiệm vụ
├── users/          # Quản lý người dùng
├── wallet/         # Quản lý ví và giao dịch
├── app.module.ts   # Module gốc
└── main.ts         # Entry point
\`\`\`

## Công nghệ sử dụng

- NestJS - Framework
- TypeORM - ORM
- MySQL - Database
- JWT - Authentication
- Swagger - API Documentation
- TypeScript - Language
- Jest - Testing

## Môi trường

- Node.js >= 14.x
- MySQL >= 8.x
- npm >= 6.x

## License

[MIT licensed](LICENSE)
