import { Injectable, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(private readonly configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        (request: Request) => {
          // Try to get the token from cookie first
          const token = request?.cookies?.accessToken;
          if (token) return token;
          
          // Fallback to Authorization header
          return ExtractJwt.fromAuthHeaderAsBearerToken()(request);
        },
      ]),
      ignoreExpiration: false,
      secretOrKey: configService.get('JWT_SECRET'),
    });
    this.logger.log(`JWT Strategy initialized with secret: ${configService.get('JWT_SECRET').substring(0, 3)}...`);
  }

  async validate(payload: any) {
    return { 
      id: payload.sub, 
      username: payload.username, 
      role: payload.role,
      wallet: payload.wallet
    };
  }
} 