import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { WalletLoginDto } from './dto/wallet-login.dto';
import { User } from '../users/entities/user.entity';
import { SiweMessage } from 'siwe';
import { Logger } from '@nestjs/common';
import { isValidEthereumAddress } from 'src/common/utils';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  async walletLogin(walletLoginDto: WalletLoginDto): Promise<{ accessToken: string; user: User }> {
    try {
      const { wallet, signature, message, ref } = walletLoginDto;

      // Kiểm tra định dạng địa chỉ ví
      if (!isValidEthereumAddress(wallet)) {
        this.logger.error(`[AuthService] Địa chỉ ví không hợp lệ: ${wallet}`);
        throw new UnauthorizedException('Invalid wallet address');
      }

      // 1. Xác thực SiweMessage
      const siweMessage = new SiweMessage(message);
      
      // Kiểm tra thêm các thông tin trong message
      if (siweMessage.address.toLowerCase() !== wallet.toLowerCase()) {
        this.logger.error(`[AuthService] Địa chỉ trong message không khớp với địa chỉ ví`);
        throw new UnauthorizedException('Message address does not match wallet address');
      }
      
      // Kiểm tra thời gian hết hạn
      if (siweMessage.expirationTime && new Date(siweMessage.expirationTime) < new Date()) {
        this.logger.error(`[AuthService] Message đã hết hạn`);
        throw new UnauthorizedException('Message has expired');
      }
      
      const response = await siweMessage.verify({signature});

      if (!response.success) {
        this.logger.error(`[AuthService] Chữ ký không hợp lệ: ${signature}`);
        throw new UnauthorizedException('Invalid signature');
      }

      // 2. Kiểm tra tài khoản người dùng (isLocked)
      let user = await this.usersService.getUserByWalletAddress(wallet);
      if (user) {
        // TH1: Tài khoản đã tồn tại trong hệ thống, nhưng bị khóa
        if (user.isLocked) {
          this.logger.error(`[AuthService] Tài khoản ${wallet} này đã bị khóa`);
          throw new UnauthorizedException('Your account has been locked. Please contact support.');
        }
      } else {
        // TH2: Tài khoản chưa tồn tại trong hệ thống
        // Xử lý trường hợp không có mã giới thiệu
        if (!ref) {
          this.logger.error(`[AuthService] Tài khoản mới ${wallet} không cung cấp mã giới thiệu`);
          throw new UnauthorizedException('A referral code is required for new accounts');
        }
        
        const referrer = await this.usersService.findByReferralCode(ref);
        if (!referrer) {
          this.logger.error(`[AuthService] Tài khoản ${wallet} sử dụng mã giới thiệu không hợp lệ: ${ref}`);
          throw new UnauthorizedException('Please enter a valid referral code');
        }

        // TH3: Tạo tài khoản mới
        const isFirstUser = await this.usersService.count() <= 0;
        
        // Đơn giản hóa điều kiện
        if (isFirstUser || referrer) {
          user = await this.usersService.createWithWallet(wallet, referrer);
          this.logger.log(`[AuthService] Đã tạo tài khoản mới cho wallet: ${wallet}`);
        }
      }

      // 3. Tạo token JWT
      const payload = {
        username: user.username,
        sub: user.id,
        role: user.role,
        wallet: user.wallet
      };

      const token = this.jwtService.sign(payload);

      this.logger.log(`[AuthService] Tài khoản ${user.username}, wallet: ${user.wallet} đăng nhập thành công`);

      return {
        accessToken: token,
        user,
      };
    } catch (error) {
      // Xử lý lỗi chi tiết hơn
      if (error instanceof UnauthorizedException) {
        throw error; // Giữ nguyên lỗi đã xác định
      }
      
      this.logger.error(`[AuthService] Lỗi đăng nhập không xác định: ${error.message || error}`);
      throw new UnauthorizedException('Authentication failed. Please try again later.');
    }
  }
} 