import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class WalletLoginDto {
  @ApiProperty({ description: 'Wallet address' })
  @IsString()
  wallet: string;

  @ApiProperty({ description: 'Signature from wallet' })
  @IsString()
  signature: string;

  @ApiProperty({ description: 'The message that was signed' })
  @IsString()
  message: string;

  @ApiProperty({ description: 'Referral code (optional)', required: false })
  @IsString()
  @IsOptional()
  ref?: string;
} 