import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, Logger } from '@nestjs/common';
import { ApiKeyService } from '../services/api-key.service';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  private readonly logger = new Logger(ApiKeyGuard.name);

  constructor(private readonly apiKeyService: ApiKeyService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const apiKey = request.headers['x-api-key'];

    if (!apiKey) {
      this.logger.error('Missing API Key');
      throw new UnauthorizedException('API Key is required');
    }

    const isValid = await this.apiKeyService.validateApiKey(apiKey);
    
    if (!isValid) {
      this.logger.error(`Invalid API Key`);
      throw new UnauthorizedException('Invalid API Key');
    }

    // Attach API Key to request for later use
    request.apiKey = apiKey;

    return true;
  }
} 