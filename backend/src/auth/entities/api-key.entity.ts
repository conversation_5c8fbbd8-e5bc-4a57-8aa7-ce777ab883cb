import { <PERSON><PERSON><PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('api_keys')
export class ApiKey {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'API Key ID' })
  id: string;

  @ApiProperty({ description: 'API Key' })
  @Column({ unique: true })
  key: string;

  @ApiProperty({ description: 'Description' })
  @Column({ nullable: true })
  description?: string;

  @ApiProperty({ description: 'Is API Key active' })
  @Column({ default: true })
  isActive: boolean;

  @ApiProperty({ description: 'Last used timestamp' })
  @Column({ nullable: true })
  lastUsedAt?: Date;

  @CreateDateColumn()
  @ApiProperty({ description: 'API Key creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'API Key last update timestamp' })
  updatedAt: Date;
} 