import { Injectable, Logger, UnauthorizedException, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApiKey } from '../entities/api-key.entity';
import { randomBytes } from 'crypto';

@Injectable()
export class ApiKeyService implements OnModuleInit {
  private readonly logger = new Logger(ApiKeyService.name);

  constructor(
    @InjectRepository(ApiKey)
    private readonly apiKeyRepository: Repository<ApiKey>,
  ) {}

  async onModuleInit() {
    await this.initializeDefaultApiKey();
  }

  async initializeDefaultApiKey(): Promise<void> {
    try {
      const existingApiKeys = await this.apiKeyRepository.find();
      
      if (existingApiKeys.length === 0) {
        this.logger.log('No API keys found. Creating default API key...');
        
        const key = randomBytes(32).toString('hex');
        
        const defaultApiKey = this.apiKeyRepository.create({
          key,
          description: 'Default API Key - Auto-generated on initialization',
          isActive: true
        });
        
        await this.apiKeyRepository.save(defaultApiKey);
        
        this.logger.log(`Default API key created successfully: ${key}`);
      } else {
        this.logger.log(`${existingApiKeys.length} API keys already exist. Skipping initialization.`);
      }
    } catch (error) {
      this.logger.error(`Failed to initialize default API key: ${error.message}`);
      throw error;
    }
  }

  async createApiKey(description?: string): Promise<ApiKey> {
    const key = randomBytes(32).toString('hex');
    const apiKey = this.apiKeyRepository.create({
      key,
      description,
      isActive: true
    });

    return this.apiKeyRepository.save(apiKey);
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    const key = await this.apiKeyRepository.findOne({
      where: { 
        key: apiKey,
        isActive: true
      }
    });

    if (key) {
      key.lastUsedAt = new Date();
      await this.apiKeyRepository.save(key);
      return true;
    }

    return false;
  }

  async deactivateApiKey(apiKey: string): Promise<boolean> {
    const result = await this.apiKeyRepository.update(
      { key: apiKey },
      { isActive: false }
    );
    return result.affected > 0;
  }

  async listApiKeys(): Promise<ApiKey[]> {
    return this.apiKeyRepository.find({
      order: { createdAt: 'DESC' }
    });
  }
} 