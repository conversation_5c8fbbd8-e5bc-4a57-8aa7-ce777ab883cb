import { Controller, Post, Body, UnauthorizedException, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { WalletLoginDto } from './dto/wallet-login.dto';
import { ApiResponseDto } from '../common/dto/api-response.dto';
import { AuthResponseDto } from './dto/auth-response.dto';
import { CookieOptions, Response } from 'express';
import { JwtService } from './services/jwt.service';
import { VerifyTokenDto } from './dto/verify-token.dto';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly jwtService: JwtService,
  ) {}

  @Post('wallet-login')
  @ApiOperation({ summary: 'Login with blockchain wallet' })
  @ApiResponse({ 
    status: 200, 
    description: 'Login successful',
    type: ApiResponseDto
  })
  @ApiResponse({ status: 401, description: 'Invalid signature' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async walletLogin(
    @Body() walletLoginDto: WalletLoginDto,
    @Res({ passthrough: true }) response: Response
  ) {
    try {
      const authResult = await this.authService.walletLogin(walletLoginDto);
      
      const cookieOptions: CookieOptions = {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        ...(process.env.NODE_ENV === 'production' && {
          domain: 'worldmall.app'
        }),
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      };

      response.cookie('accessToken', authResult.accessToken, cookieOptions);

      const authResponse = new AuthResponseDto(authResult.accessToken, authResult.user);
      return ApiResponseDto.success(authResponse, 'Wallet login successful');
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        return ApiResponseDto.error(error.message, 401);
      }
      throw error;
    }
  }

  @Post('verify-token')
  @ApiOperation({ summary: 'Verify JWT token' })
  @ApiResponse({ 
    status: 200, 
    description: 'Token is valid',
    type: ApiResponseDto
  })
  @ApiResponse({ status: 401, description: 'Invalid token' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async verifyToken(@Body() verifyTokenDto: VerifyTokenDto) {
    const { token } = verifyTokenDto;
    
    if (!token) {
      return ApiResponseDto.error('Token is required', 400);
    }

    const decoded = this.jwtService.verifyToken(token);
    
    if (!decoded) {
      return ApiResponseDto.error('Invalid token', 401);
    }

    return ApiResponseDto.success({ 
      valid: true,
      payload: decoded
    }, 'Token is valid');
  }
} 