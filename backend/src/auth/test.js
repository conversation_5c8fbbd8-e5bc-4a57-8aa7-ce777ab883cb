const { ethers } = require('ethers');

async function generateTestData() {
  const wallet = ethers.Wallet.createRandom();
  const message = 'Login to WorldMall Crypto Platform';
  const signature = await wallet.signMessage(message);

  console.log('=== Test Data for Wallet Login ===');
  console.log('Wallet Address:', wallet.address);
  console.log('Private Key:', wallet.privateKey);
  console.log('\nRequest Body:');
  console.log(JSON.stringify({
    wallet: wallet.address,
    signature,
    message,
    ref: 'TEST123'
  }, null, 2));
  
  console.log('\nCURL Command:');
  console.log(`curl -X POST http://localhost:5000/api/v1/auth/wallet-login \\
  -H "Content-Type: application/json" \\
  -d '${JSON.stringify({
    wallet: wallet.address,
    signature,
    message,
    ref: 'TEST123'
  })}'`);
}

generateTestData(); 