import { 
  WebSocketGateway, 
  WebSocketServer, 
  SubscribeMessage, 
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  ConnectedSocket,
  MessageBody
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

@WebSocketGateway({
  cors: {
    origin: process.env.NODE_ENV === 'production' ? ['https://worldmall.app'] : ['http://localhost:3000'],
    credentials: true
  }
})
export class SocketGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer() server: Server;
  private readonly logger = new Logger(SocketGateway.name);
  private userSockets: Map<string, Socket> = new Map();

  constructor(private readonly jwtService: JwtService) {}

  afterInit(server: Server) {
    this.logger.log('Socket.io server initialized');
  }

  async handleConnection(client: Socket) {
    //console.log('[SocketGateway] Handshake auth:', client.handshake.auth);
    try {
      const token = client.handshake.auth.token?.replace('Bearer ', '');
      if (!token) {
        this.logger.error('No token provided');
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token);
      if (!payload) {
        this.logger.error('Invalid token');
        client.disconnect();
        return;
      }

      const userId = payload.sub;
      client.data.userId = userId;
      this.userSockets.set(userId, client);
      
      this.logger.log(`[SocketGateway] Client connected: ${userId}`);
      
      // Join user's room
      client.join(`user:${userId}`);
    } catch (error) {
      this.logger.error('[SocketGateway] Connection error:', error);
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    const userId = client.data.userId;
    if (userId) {
      this.userSockets.delete(userId);
      this.logger.log(`[SocketGateway] Client disconnected: ${userId}`);
    }
  }

  @SubscribeMessage('join')
  handleJoin(@ConnectedSocket() client: Socket, @MessageBody() data: { userId: string }) {
    if (client.data.userId === data.userId) {
      client.join(`user:${data.userId}`);
      this.logger.log(`[SocketGateway] User ${data.userId} joined their room`);
    }
  }

  // Emit order status update to specific user
  async emitOrderStatus(userId: string, orderUpdate: any) {
    this.server.to(`user:${userId}`).emit('order:status', orderUpdate);
    this.logger.log(`[SocketGateway] Emitted order status to user ${userId}:`, orderUpdate);
  }

  // Emit transaction update to specific user
  async emitTransaction(userId: string, transaction: any) {
    this.server.to(`user:${userId}`).emit('transaction:new', transaction);
    this.logger.log(`[SocketGateway] Emitted transaction to user ${userId}:`, transaction);
  }

  // Emit mining update to specific user
  async emitMiningUpdate(userId: string, miningUpdate: any) {
    this.server.to(`user:${userId}`).emit('mining:update', miningUpdate);
    this.logger.log(`[SocketGateway] Emitted mining update to user ${userId}:`, miningUpdate);
  }
} 