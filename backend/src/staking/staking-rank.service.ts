import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not, IsNull, MoreThanOrEqual, Like, QueryRunner, In } from 'typeorm';
import { WManagerRank } from '../common/enums/user.enum';
import { User } from '../users/entities/user.entity';
import { Wallet } from '../wallet/entities/wallet.entity';
import { Transaction } from '../wallet/entities/transaction.entity';
import { TokenService } from '../token/token.service';
import { WalletService } from '../wallet/wallet.service';
import { TransactionStatus, TransactionType } from '../common/enums/transaction.enum';
import { STAKING_RANKS } from './constants/staking.constants';
import { getCurrentTime } from '../common/utils/date.utils';
import { Token } from 'src/token/entities/token.entity';
import { BigNumber } from 'bignumber.js';
@Injectable()
export class StakingRankService {
  private readonly logger = new Logger(StakingRankService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
    private readonly tokenService: TokenService,
    private readonly walletService: WalletService,
  ) { }

  /**
   * Cập nhật tổng thu nhập staking của người dùng và kiểm tra max out
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   * @param userId ID của người dùng
   * @param amount Số tiền thu nhập mới
   * @param now Thời gian hiện tại
   */
  private async updateUserStakingEarningsWithQueryRunner(queryRunner: any, userId: string, amount: number, now: Date): Promise<void> {
    // 1. Lấy thông tin người dùng
    const user = await queryRunner.manager.findOne(User, { where: { id: userId } });

    // 2. Cập nhật tổng thu nhập staking
    const currentTotalEarnings = new BigNumber(user.stakingTotalEarnings?.toString() || '0');
    const newTotalEarnings = currentTotalEarnings.plus(new BigNumber(amount.toString()));

    // 3. Kiểm tra max out (300% tổng đầu tư)
    const totalInvestment = new BigNumber(user.stakingTotalInvestment?.toString() || '0');
    const maxEarnings = totalInvestment.multipliedBy(3); // 300% tổng đầu tư

    // 4. Cập nhật trạng thái max out
    const isMaxedOut = newTotalEarnings.isGreaterThanOrEqualTo(maxEarnings);

    // 5. Cập nhật thông tin người dùng
    await queryRunner.manager.update(User, userId, {
      stakingTotalEarnings: newTotalEarnings.toNumber(),
      stakingMaxedOut: isMaxedOut
    });

    // 6. Ghi log nếu người dùng đã max out
    if (isMaxedOut && !user.stakingMaxedOut) {
      this.logger.log(`[StakingRankService][updateUserStakingEarningsWithQueryRunner] User ${userId} has reached max earnings (300% of investment): ${maxEarnings.toNumber()} USDT`);
    }
  }

  /**
   * Cập nhật cấp bậc WManager dựa trên stakingTotalVolume
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   */
  async summarizeWManagerRank(queryRunner: any): Promise<void> {
    try {
      // 1. Lấy thông tin của tất cả users có stakingTotalVolume >= 6000
      const users = await queryRunner.manager.find(User, {
        where: {
          stakingTotalVolume: MoreThanOrEqual(6000)
        },
        select: ['id', 'wManagerRank', 'stakingTotalVolume', 'stakingDirectActiveCount']
      });

      if (users.length === 0) {
        this.logger.debug('[StakingRankService][summarizeWManagerRank] Không có user nào đủ điều kiện xét rank');
        return;
      }

      // 2. Xử lý từng user
      this.logger.debug(`[StakingRankService][summarizeWManagerRank] Tổng số user cần xét rank: ${users.length}`);

      const rankUpdates = [];
      for (const user of users) {
        const volume = parseFloat(user.stakingTotalVolume.toString());
        const currentRank = user.wManagerRank; // Không sử dụng || '' để giữ nguyên giá trị null

        this.logger.debug(`[StakingRankService][summarizeWManagerRank] User ${user.id}: volume=${volume}, stakingTotalVolume=${user.stakingTotalVolume}, type=${typeof user.stakingTotalVolume}`);

        // Đếm số F1 active
        const activeF1Count = await this.countActiveF1(queryRunner, user.id);

        // Kiểm tra điều kiện cấp bậc
        let calculatedRank = null; // Sử dụng null thay vì 'None'

        this.logger.debug(`Kiểm tra cấp bậc cho user ${user.id}: volume=${volume}, activeF1Count=${activeF1Count}, currentRank=${currentRank || 'None'}`);

        // Check WM1 (2 F1 active và doanh số tổng 6000$)
        if (volume >= 6000 && activeF1Count >= 2) {
          calculatedRank = WManagerRank.WM1;
          this.logger.debug(`User ${user.id} đủ điều kiện cấp ${WManagerRank.WM1}`);
        }

        // Check WM2 (2WM1 ở 2 nhánh bất kỳ và doanh số tổng 15.000$)
        if (volume >= 15000) {
          const w1Count = await this.countF1WithRank(queryRunner, user.id, WManagerRank.WM1);
          this.logger.debug(`User ${user.id} có ${w1Count} nhánh đạt cấp ${WManagerRank.WM1}`);
          if (w1Count >= 2) {
            calculatedRank = WManagerRank.WM2;
            this.logger.debug(`User ${user.id} đủ điều kiện cấp ${WManagerRank.WM2}`);
          }
        }

        // Check WM3 (2WM2 ở 2 nhánh bất kỳ và doanh số tổng 40.000$)
        if (volume >= 40000) {
          const w2Count = await this.countF1WithRank(queryRunner, user.id, WManagerRank.WM2);
          this.logger.debug(`User ${user.id} có ${w2Count} nhánh đạt cấp ${WManagerRank.WM2}`);
          if (w2Count >= 2) {
            calculatedRank = WManagerRank.WM3;
            this.logger.debug(`User ${user.id} đủ điều kiện cấp ${WManagerRank.WM3}`);
          }
        }

        // Check WM4 (2WM3 ở 2 nhánh bất kỳ và doanh số tổng 100.000$)
        if (volume >= 100000) {
          const w3Count = await this.countF1WithRank(queryRunner, user.id, WManagerRank.WM3);
          this.logger.debug(`User ${user.id} có ${w3Count} nhánh đạt cấp ${WManagerRank.WM3}`);
          if (w3Count >= 2) {
            calculatedRank = WManagerRank.WM4;
            this.logger.debug(`User ${user.id} đủ điều kiện cấp ${WManagerRank.WM4}`);
          }
        }

        // Check WM5 (2WM4 ở 2 nhánh bất kỳ và doanh số tổng 200.000$)
        if (volume >= 200000) {
          const w4Count = await this.countF1WithRank(queryRunner, user.id, WManagerRank.WM4);
          this.logger.debug(`User ${user.id} có ${w4Count} nhánh đạt cấp ${WManagerRank.WM4}`);
          if (w4Count >= 2) {
            calculatedRank = WManagerRank.WM5;
            this.logger.debug(`User ${user.id} đủ điều kiện cấp ${WManagerRank.WM5}`);
          }
        }

        // So sánh cấp bậc tính toán với cấp bậc hiện tại
        // Chỉ nâng cấp, không giảm cấp
        let newRank = currentRank;

        // Hàm hỗ trợ để so sánh cấp bậc
        const getRankValue = (rank: string): number => {
          switch (rank) {
            case WManagerRank.WM5: return 5;
            case WManagerRank.WM4: return 4;
            case WManagerRank.WM3: return 3;
            case WManagerRank.WM2: return 2;
            case WManagerRank.WM1: return 1;
            default: return 0;
          }
        };

        const currentRankValue = getRankValue(currentRank);
        const calculatedRankValue = getRankValue(calculatedRank);

        // Chỉ nâng cấp, không giảm cấp
        if (calculatedRankValue > currentRankValue) {
          newRank = calculatedRank;
          this.logger.debug(`User ${user.id} được nâng cấp từ ${currentRank || 'None'} lên ${calculatedRank}`);
        } else if (calculatedRankValue < currentRankValue) {
          this.logger.debug(`User ${user.id} giữ nguyên cấp bậc ${currentRank} (cấp tính toán: ${calculatedRank})`);
        }

        // Kiểm tra xem cấp bậc có thay đổi không
        // Nếu currentRank là null hoặc empty string và newRank không phải là empty string, cập nhật
        this.logger.log(`[StakingRankService][summarizeWManagerRank] So sánh cấp bậc: currentRank=${currentRank}, newRank=${newRank}, typeof currentRank=${typeof currentRank}, typeof newRank=${typeof newRank}`);

        // Kiểm tra cụ thể cho user 08000bd2-5e23-42dc-9750-7627463ba1e1
        if (user.id === '08000bd2-5e23-42dc-9750-7627463ba1e1') {
          this.logger.log(`[StakingRankService][summarizeWManagerRank] USER ĐẶC BIỆT: ${user.id}, volume=${volume}, activeF1Count=${activeF1Count}, currentRank=${currentRank}, newRank=${newRank}`);
        }

        if ((currentRank === null && newRank !== null) || (currentRank !== null && newRank !== null && newRank !== currentRank)) {
          rankUpdates.push({
            id: user.id,
            oldRank: currentRank || 'No Rank',
            newRank: newRank
          });

          this.logger.debug(`[StakingRankService][summarizeWManagerRank] Sẽ cập nhật rank của user ${user.id} từ ${currentRank || 'No Rank'} thành ${newRank}`);
        } else {
          this.logger.debug(`[StakingRankService][summarizeWManagerRank] Không cần cập nhật rank của user ${user.id}: currentRank=${currentRank}, newRank=${newRank}`);
        }
      }

      // 3. Batch update ranks
      if (rankUpdates.length > 0) {
        this.logger.log(`[StakingRankService][summarizeWManagerRank] Có ${rankUpdates.length} users cần cập nhật rank`);

        for (const update of rankUpdates) {
          this.logger.log(
            `[StakingRankService][summarizeWManagerRank] Cập nhật rank của user ${update.id} từ ${update.oldRank} thành ${update.newRank}`
          );

          try {
            const result = await queryRunner.manager.update(User,
              { id: update.id },
              { wManagerRank: update.newRank }
            );

            this.logger.log(
              `[StakingRankService][summarizeWManagerRank] Kết quả cập nhật: ${JSON.stringify(result)}`
            );
          } catch (error) {
            this.logger.error(
              `[StakingRankService][summarizeWManagerRank] Lỗi khi cập nhật rank của user ${update.id}: ${error.message}`,
              error.stack
            );
          }
        }

        this.logger.log(
          `[StakingRankService][summarizeWManagerRank] Đã cập nhật rank cho ${rankUpdates.length} users`
        );
      } else {
        this.logger.log(`[StakingRankService][summarizeWManagerRank] Không có user nào cần cập nhật rank`);
      }

      // 4. Cập nhật stakingIsShareholder với điều kiện có 2 F1 đạt cấp WM5
      const users_W5 = await queryRunner.manager.find(User, {
        where: {
          wManagerRank: WManagerRank.WM5
        }
      });

      for (const user of users_W5) {
        const w5F1Count = await this.countF1WithRank(queryRunner, user.id, WManagerRank.WM5);

        if (w5F1Count >= 2) {
          await queryRunner.manager.update(User, user.id, {
            stakingIsShareholder: true
          });

          this.logger.debug(`[StakingRankService][summarizeWManagerRank] Cập nhật user ${user.id} thành cổ đông`);
        }
      }
    } catch (error) {
      this.logger.error('[StakingRankService][summarizeWManagerRank] Lỗi khi tổng hợp rank WManager:', error);
      throw error;
    }
  }

  /**
   * Đếm số F1 active
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   * @param userId ID của người dùng
   * @returns Số lượng F1 active
   */
  private async countActiveF1(queryRunner: any, userId: string): Promise<number> {
    const result = await queryRunner.manager.count(User, {
      where: {
        referredBy: userId,
        stakingActive: true
      }
    });

    return result;
  }

  /**
   * Đếm số nhánh có ít nhất 1 người đạt cấp bậc cụ thể
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   * @param userId ID của người dùng
   * @param rank Cấp bậc cần đếm
   * @returns Số lượng nhánh có ít nhất 1 người đạt cấp bậc cụ thể
   */
  private async countF1WithRank(queryRunner: any, userId: string, rank: string): Promise<number> {
    this.logger.log(`[StakingRankService][countF1WithRank] Kiểm tra cấp bậc ${rank} cho user ${userId} với SQL tối ưu`);

    // Sử dụng SQL tối ưu để đếm số nhánh có ít nhất 1 người đạt cấp bậc cụ thể
    const optimizedQuery = `
      WITH SourcePatterns AS (
        -- Bước 1: Lấy ID của user nguồn và tạo mẫu LIKE tương ứng
        SELECT
            U_Source.id AS source_user_id, -- Lấy ID của user nguồn
            CONCAT(U_Source.path, '.', U_Source.id, '.%') AS like_pattern
        FROM
            users AS U_Source
        WHERE
            U_Source.referredBy = '${userId}'
      ),
      CountPerSource AS (
        -- Bước 2: Đếm số lượng U_Target cho mỗi source_user_id từ Bước 1
        SELECT
            SP.source_user_id,                          -- ID của user nguồn
            COUNT(U_Target.id) AS count_of_descendants  -- Đếm số lượng user "con" phù hợp
        FROM
            SourcePatterns SP
        LEFT JOIN
            users AS U_Target
            ON U_Target.path LIKE SP.like_pattern       -- Khớp path với mẫu
            AND U_Target.wManagerRank = '${rank}'       -- Và target user phải có wManagerRank = rank
        GROUP BY
            SP.source_user_id
      )
      -- Bước 3: Đếm số nhánh có ít nhất 1 người đạt cấp bậc
      SELECT
          COUNT(CASE WHEN CPS.count_of_descendants > 0 THEN 1 ELSE NULL END) AS branches_with_rank
      FROM
          CountPerSource CPS
    `;

    try {
      const result = await queryRunner.query(optimizedQuery);
      const branchesWithRank = parseInt(result[0].branches_with_rank, 10);

      this.logger.log(`[StakingRankService][countF1WithRank] Kết quả: ${branchesWithRank} nhánh có cấp bậc ${rank} cho user ${userId}`);
      return branchesWithRank;
    } catch (error) {
      this.logger.error(`[StakingRankService][countF1WithRank] Lỗi khi thực hiện truy vấn SQL: ${error.message}`, error.stack);
      return 0; // Trả về 0 nếu có lỗi xảy ra
    }
  }

  /**
   * Kiểm tra xem có F1 đạt cấp bậc yêu cầu ở vị trí cụ thể không
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   * @param userId ID của người dùng
   * @param requiredRank Cấp bậc yêu cầu
   * @param position Vị trí (LEFT/RIGHT)
   * @returns true nếu có F1 đạt cấp bậc yêu cầu ở vị trí cụ thể
   */
  async checkRankInPosition(queryRunner: any, userId: string, requiredRank: string, position: string): Promise<boolean> {
    const referredUsers = await queryRunner.manager.find(User, {
      where: {
        referredBy: userId,
        position: position
      }
    });

    for (const referredUser of referredUsers) {
      if (referredUser.wManagerRank === requiredRank || this.isHigherRank(referredUser.wManagerRank, requiredRank)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Kiểm tra xem cấp bậc 1 có cao hơn cấp bậc 2 không
   * @param rank1 Cấp bậc 1
   * @param rank2 Cấp bậc 2
   * @returns true nếu cấp bậc 1 cao hơn cấp bậc 2
   */
  isHigherRank(rank1: string, rank2: string): boolean {
    const rankOrder = [WManagerRank.WM1, WManagerRank.WM2, WManagerRank.WM3, WManagerRank.WM4, WManagerRank.WM5];
    return rankOrder.indexOf(rank1 as WManagerRank) > rankOrder.indexOf(rank2 as WManagerRank);
  }

  /**
   * // Từ vị trí staking, lấy theo path ngược lên để tìm W-Manager,
    // Nếu trên path mà chỉ có 1 W-Manager thì người đó được hưởng nguyên mức hoa hồng.
    // Nếu có cấp W-Manager thấp hơn thì thì người thấp hơn được hưởng hoa hồng trước, rồi đến cấp tiếp theo tương ứng với mức hoa hồng 0.5%
    // Tổng hoa hồng tối đa chỉ là 3% tổng bán của một trader
    // Ghi nhận hoa hồng này là: Ranking commission (Lưu vào giao dịch + cộng tiền tài khoản)

    // Ví dụ 1:
    // - Staking A mua 1000 USDT, có 1 W1 và 1 W2 trên path.
    // - W1 được hưởng 1% hoa hồng từ A.
    // - W2 được hưởng 0.5% hoa hồng từ A (Vì mức tối đa của W2 là 1.5%).
    // - Tổng hoa hồng tối đa là 3% từ A, do đó W1 và W2 chỉ được hưởng 1.5% tổng bán của A.
    // Ví dụ 2:
    // - Staking B mua 1000 USDT, có 1 W1 và 1 W3 trên path.
    // - W1 được hưởng 1% hoa hồng từ B.
    // - W3 được hưởng 1% hoa hồng từ B (Vì mức tối đa của W3 là 2%).
    // - Tổng hoa hồng tối đa là 3% từ B, do đó W1 và W3 chỉ được hưởng 2% tổng bán của B.

    // Ví dụ 3:
    // - Staking C mua 1000 USDT, có 1 W5 trên path.
    // - W5 được hưởng 3% hoa hồng từ C.
    // - Tổng hoa hồng tối đa là 3% từ C, do đó W5 chỉ được hưởng 3% tổng bán của C.

    // Mỗi W được nhận hoa hồng thì người giới thiệu ra W-Manager sẽ được nhận 20% tổng hoa hồng từ W đó.
    // Ví dụ:
    // - Staking D bán mua 1000 USDT, có 1 W1 và 1 W2 trên path.
    // - W1 được hưởng 1% hoa hồng từ D.
    // - W2 được hưởng 0.5% hoa hồng từ D.
    // - Người giới thiệu ra W1 là R, thì R được nhận 20% hoa hồng từ W1.
    // - Ghi nhận hoa hồng này là: Matching Bonus (Lưu vào giao dịch + cộng tiền tài khoản)
   * @param queryRunner
   * @param sellTotalVolumeMap
   * @param usdtToken
   * @param sessionId
   * @param now
   * @returns
   */
  /**
   * Cập nhật hoa hồng WManager cho staking
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   * @param stakingVolumeMap Map lưu trữ khối lượng staking của từng người dùng
   * @param usdtToken Token USDT
   * @param packageCode Mã gói staking
   * @param now Thời gian hiện tại
   * @returns Danh sách các giao dịch hoa hồng đã tạo
   */
  async summarizeWManagerCommission(queryRunner: QueryRunner, stakingVolumeMap: Map<string, number>, usdtToken: Token, packageCode: string, now: Date) {
    try {
      // Định nghĩa tỷ lệ bằng points thay vì số thập phân
      const commissionRatePoints: Record<WManagerRank, number> = {
        [WManagerRank.WM1]: 100,   // 1% = 100 points
        [WManagerRank.WM2]: 150,   // 1.5% = 150 points
        [WManagerRank.WM3]: 200,   // 2% = 200 points
        [WManagerRank.WM4]: 250,   // 2.5% = 250 points
        [WManagerRank.WM5]: 300,   // 3% = 300 points
      };
      const RATE_DENOMINATOR = 10000;  // Mẫu số để tính rate
      const MATCHING_BONUS_NUMERATOR = 20; // 20%
      const MATCHING_BONUS_DENOMINATOR = 100;
      const rankingCommissionTransactions: Transaction[] = [];

      // 1. Tính tổng khối lượng staking
      let totalVolume = 0;
      for (const volume of stakingVolumeMap.values()) {
        totalVolume += volume;
      }

      this.logger.log(`[StakingRankService][summarizeWManagerCommission] Total staking volume: ${totalVolume} USDT, package: ${packageCode}`);

      // 2. Xử lý từng người dùng mua gói staking
      for (const [stakerId, volume] of stakingVolumeMap.entries()) {
        // Lấy thông tin người mua gói staking
        const staker = await queryRunner.manager.findOne(User, {
          where: { id: stakerId },
          select: ['id', 'username', 'path', 'isRoot', 'wManagerRank', 'referredBy']
        });

        if (!staker || staker.isRoot) continue;

        // Lấy danh sách upline của người mua gói staking
        const uplineIds = staker.path ? staker.path.split('.') : [];
        if (uplineIds.length === 0) continue;

        const uplines = await queryRunner.manager.find(User, {
          where: { id: In(uplineIds), wManagerRank: Not(IsNull()) },
          select: ['id', 'username', 'wManagerRank', 'stakingIsShareholder']
        });

        // Sắp xếp upline theo thứ tự từ gần đến xa
        const sortedUplines = uplines.sort((a, b) => {
          const aIndex = uplineIds.indexOf(a.id);
          const bIndex = uplineIds.indexOf(b.id);
          return aIndex - bIndex; // Thứ tự từ gần đến xa
        });

        let lastWManagerRank = null;
        let lastWManagerCommission = 0;
        let processedWManagers = new Set<string>();

        // Xử lý hoa hồng cho từng upline
        for (const upline of sortedUplines) {
          if (upline.id === staker.referredBy || processedWManagers.has(upline.id)) {
            continue;
          }

          // Lấy thông tin đầy đủ của upline để kiểm tra stakingMaxedOut
          const uplineUser = await queryRunner.manager.findOne(User, {
            where: { id: upline.id },
            select: ['id', 'username', 'wManagerRank', 'stakingIsShareholder', 'stakingMaxedOut']
          });

          if (!uplineUser) continue;

          // Kiểm tra xem người dùng đã max out chưa
          const isMaxedOut = uplineUser.stakingMaxedOut || false;

          if (isMaxedOut) {
            // Bỏ qua giao dịch nếu người dùng đã max out
            this.logger.log(`[StakingRankService][summarizeWManagerCommission] Commission for user ${uplineUser.id} (${uplineUser.username}) skipped - User has reached max earnings (300% of investment)`);

            // Quan trọng: Nếu người dùng này là W-Manager đầu tiên trong path, chúng ta cần đặt lastWManagerRank = null
            // để người dùng tiếp theo sẽ được xử lý như W-Manager đầu tiên
            if (lastWManagerRank === null) {
              // Không làm gì cả, tiếp tục tìm W-Manager đầu tiên không bị max out
            } else {
              // Nếu người dùng này không phải là W-Manager đầu tiên, chúng ta vẫn giữ nguyên lastWManagerRank và lastWManagerCommission
              // để người dùng tiếp theo có thể nhận được hoa hồng matching dựa trên W-Manager trước đó
            }

            continue;
          }

          const wallet = await queryRunner.manager.findOne(Wallet, { where: { userId: uplineUser.id } });
          if (!wallet) continue;

          const currentRankPoints = commissionRatePoints[uplineUser.wManagerRank as WManagerRank];
          if (!currentRankPoints) continue;

          let commission = 0;
          let isMatchingBonus = false;

          if (!lastWManagerRank) {
            // W-Manager đầu tiên - tính commission bằng points
            commission = (volume * currentRankPoints) / RATE_DENOMINATOR;
            lastWManagerRank = uplineUser.wManagerRank;
            lastWManagerCommission = commission;
            isMatchingBonus = false; // Đảm bảo đây không phải là matching bonus
          } else {
            // So sánh cấp bậc hiện tại với cấp bậc trước đó
            const currentRankValue = this.getRankValue(uplineUser.wManagerRank as WManagerRank);
            const lastRankValue = this.getRankValue(lastWManagerRank as WManagerRank);

            if (currentRankValue <= lastRankValue) {
              // Nếu rank thấp hơn hoặc bằng, chỉ nhận 20% của W trước đó
              commission = (lastWManagerCommission * MATCHING_BONUS_NUMERATOR) / MATCHING_BONUS_DENOMINATOR;
              lastWManagerCommission = commission;
              isMatchingBonus = true;
            } else {
              // Tính chênh lệch points thay vì rate
              const pointsDiff = currentRankPoints - commissionRatePoints[lastWManagerRank as WManagerRank];
              commission = (volume * pointsDiff) / RATE_DENOMINATOR;
              lastWManagerRank = uplineUser.wManagerRank;
              lastWManagerCommission = commission;
              isMatchingBonus = false;
            }
          }

          if (commission > 0) {
            // Kiểm tra lại một lần nữa xem người dùng đã max out chưa
            // Vì có thể người dùng đã max out sau khi chúng ta kiểm tra ở trên
            const latestUserInfo = await queryRunner.manager.findOne(User, {
              where: { id: uplineUser.id },
              select: ['id', 'username', 'stakingMaxedOut']
            });

            if (latestUserInfo?.stakingMaxedOut) {
              this.logger.log(`[StakingRankService][summarizeWManagerCommission] Commission for user ${uplineUser.id} (${uplineUser.username}) skipped (double-check) - User has reached max earnings (300% of investment)`);
              continue;
            }

            // Tạo giao dịch hoa hồng
            const transaction = await queryRunner.manager.save(Transaction, {
              userId: uplineUser.id,
              walletId: wallet.id,
              tokenId: usdtToken.id,
              type: isMatchingBonus ? TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_MATCHING : TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_RANK,
              amount: commission,
              status: TransactionStatus.COMPLETED,
              reference: stakerId,
              note: isMatchingBonus
                ? `Matching Bonus (${uplineUser.wManagerRank}) 20% from ${lastWManagerRank} commission of Global Mining Co-Share ${stakerId} in package ${packageCode} volume ${volume} (+${commission}USDT)`
                : `Ranking Commission (${uplineUser.wManagerRank}) from Global Mining Co-Share ${stakerId} in package ${packageCode} volume ${volume} (+${commission}USDT)`,
              createdAt: now,
              updatedAt: now,
              transactionAt: now
            });

            // Cập nhật số dư
            await this.walletService.updateTokenBalanceWithQueryRunner(
              queryRunner,
              wallet,
              usdtToken,
              'usdtBalance',
              transaction,
              commission,
              'add',
              now
            );

            // Cập nhật thống kê
            if (isMatchingBonus) {
              await queryRunner.manager.update(User, uplineUser.id, {
                stakingMatchingCommission: () => `stakingMatchingCommission + ${commission}`,
                stakingTotalCommission: () => `stakingTotalCommission + ${commission}`
              });
            } else {
              await queryRunner.manager.update(User, uplineUser.id, {
                stakingRankCommission: () => `stakingRankCommission + ${commission}`,
                stakingTotalCommission: () => `stakingTotalCommission + ${commission}`
              });
            }

            // Cập nhật tổng thu nhập staking và kiểm tra max out
            await this.updateUserStakingEarningsWithQueryRunner(queryRunner, uplineUser.id, commission, now);

            rankingCommissionTransactions.push(transaction);

            this.logger.log(
              `[StakingRankService][summarizeWManagerCommission] ${isMatchingBonus ? 'Matching Bonus' : 'Ranking Commission'} ` +
              `for user ${uplineUser.id} (${uplineUser.username}) ${uplineUser.wManagerRank}: ${commission} USDT`
            );

            processedWManagers.add(uplineUser.id);
          }
        }
      }

      // 3. Xử lý hoa hồng cổ đông
      if (totalVolume > 0) {
        // Lấy danh sách cổ đông với thông tin stakingMaxedOut
        const shareholders = await queryRunner.manager.find(User, {
          where: { stakingIsShareholder: true },
          select: ['id', 'username', 'stakingIsShareholder', 'stakingMaxedOut']
        });

        if (shareholders.length > 0) {
          // Lọc ra các cổ đông chưa max out
          const eligibleShareholders = shareholders.filter(shareholder => !shareholder.stakingMaxedOut);

          // Ghi log số lượng cổ đông bị bỏ qua do max out
          const skippedCount = shareholders.length - eligibleShareholders.length;
          if (skippedCount > 0) {
            this.logger.log(`[StakingRankService][summarizeWManagerCommission] Skipped ${skippedCount} shareholders who have reached max earnings (300% of investment)`);
          }

          // Nếu không có cổ đông nào đủ điều kiện, bỏ qua
          if (eligibleShareholders.length === 0) {
            this.logger.log(`[StakingRankService][summarizeWManagerCommission] No eligible shareholders found - All have reached max earnings`);
            return rankingCommissionTransactions;
          }

          const shareholderPool = totalVolume * 0.05; // 5% cho cổ đông
          const shareholderCommission = shareholderPool / eligibleShareholders.length;

          this.logger.log(`[StakingRankService][summarizeWManagerCommission] Total shareholder commission: ${shareholderPool} USDT, number of eligible shareholders: ${eligibleShareholders.length}, commission per shareholder: ${shareholderCommission} USDT`);

          for (const shareholder of eligibleShareholders) {
            const wallet = await queryRunner.manager.findOne(Wallet, { where: { userId: shareholder.id } });
            if (!wallet) continue;

            // Kiểm tra lại một lần nữa xem cổ đông đã max out chưa
            // Vì có thể cổ đông đã max out sau khi chúng ta lọc ở trên
            const latestShareholderInfo = await queryRunner.manager.findOne(User, {
              where: { id: shareholder.id },
              select: ['id', 'username', 'stakingMaxedOut']
            });

            if (latestShareholderInfo?.stakingMaxedOut) {
              this.logger.log(`[StakingRankService][summarizeWManagerCommission] Shareholder commission for user ${shareholder.id} (${shareholder.username}) skipped (double-check) - User has reached max earnings (300% of investment)`);
              continue;
            }

            // Tạo giao dịch hoa hồng cổ đông
            const transaction = await queryRunner.manager.save(Transaction, {
              userId: shareholder.id,
              walletId: wallet.id,
              tokenId: usdtToken.id,
              type: TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER,
              amount: shareholderCommission,
              status: TransactionStatus.COMPLETED,
              reference: packageCode,
              note: `Shareholder Commission (5% of total Global Mining Co-Share volume ${totalVolume} USDT) package ${packageCode} (+${shareholderCommission} USDT)`,
              createdAt: now,
              updatedAt: now,
              transactionAt: now
            });

            // Cập nhật số dư
            await this.walletService.updateTokenBalanceWithQueryRunner(
              queryRunner,
              wallet,
              usdtToken,
              'usdtBalance',
              transaction,
              shareholderCommission,
              'add',
              now
            );

            // Cập nhật thống kê
            await queryRunner.manager.update(User, shareholder.id, {
              stakingRankCommission: () => `stakingRankCommission + ${shareholderCommission}`,
              stakingTotalCommission: () => `stakingTotalCommission + ${shareholderCommission}`
            });

            // Cập nhật tổng thu nhập staking và kiểm tra max out
            await this.updateUserStakingEarningsWithQueryRunner(queryRunner, shareholder.id, shareholderCommission, now);

            rankingCommissionTransactions.push(transaction);

            this.logger.log(`[StakingRankService][summarizeWManagerCommission] Paid shareholder commission to user ${shareholder.id} (${shareholder.username}): ${shareholderCommission} USDT`);
          }
        }
      }

      return rankingCommissionTransactions;
    } catch (error) {
      this.logger.error(`[StakingRankService][summarizeWManagerCommission] Lỗi khi tính hoa hồng: ${error.message}`, error.stack);
      throw error;
    }
  }



  /**
   * Hàm hỗ trợ để lấy giá trị số của cấp bậc để so sánh
   * @param rank Cấp bậc cần lấy giá trị
   * @returns Giá trị số của cấp bậc
   */
  private getRankValue(rank: WManagerRank): number {
    switch (rank) {
      case WManagerRank.WM1: return 1;
      case WManagerRank.WM2: return 2;
      case WManagerRank.WM3: return 3;
      case WManagerRank.WM4: return 4;
      case WManagerRank.WM5: return 5;
      default: return 0;
    }
  }
}
