import { Controller, Post, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { TestRankService } from './test-rank.service';

@ApiTags('test-rank')
@Controller('test-rank')
export class TestRankController {
  constructor(private readonly testRankService: TestRankService) {}

  @Post('create-data')
  @ApiOperation({ summary: 'Tạo dữ liệu test' })
  @ApiResponse({ status: 200, description: 'Tạo dữ liệu test thành công' })
  async createTestData() {
    return await this.testRankService.createTestData();
  }

  @Post('update-ranks')
  @ApiOperation({ summary: 'Test cập nhật cấp bậc WManager' })
  @ApiResponse({ status: 200, description: 'Cập nhật cấp bậc thành công' })
  async testUpdateRanks() {
    return await this.testRankService.summarizeWManagerRank();
  }
}
