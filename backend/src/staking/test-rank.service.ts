import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, MoreThanOrEqual, Like } from 'typeorm';
import { TestUser } from './entities/test-user.entity';

@Injectable()
export class TestRankService {
  private readonly logger = new Logger(TestRankService.name);

  constructor(
    @InjectRepository(TestUser)
    private readonly testUserRepository: Repository<TestUser>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Tạo dữ liệu test
   */
  async createTestData(): Promise<any> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Xóa dữ liệu test cũ
      await queryRunner.manager.clear(TestUser);

      // Tạo cấu trúc người dùng test
      const testUsers = {
        root: null,
        level1: [],
        level2: [],
        level3: []
      };

      // 1. Tạo người dùng root
      testUsers.root = await queryRunner.manager.save(TestUser, {
        username: `root`,
        stakingTotalVolume: 250000,
        stakingDirectActiveCount: 5
      });

      // 2. Tạo 3 người dùng F1 (nhánh 1, 2, 3)
      for (let i = 0; i < 3; i++) {
        const f1User = await queryRunner.manager.save(TestUser, {
          username: `f1_${i}`,
          referredBy: testUsers.root.id,
          stakingTotalVolume: 120000,
          stakingDirectActiveCount: 3,
          stakingActive: true
        });

        testUsers.level1.push(f1User);

        // 3. Tạo 2 người dùng F2 cho mỗi F1
        for (let j = 0; j < 2; j++) {
          const f2User = await queryRunner.manager.save(TestUser, {
            username: `f2_${i}_${j}`,
            referredBy: f1User.id,
            stakingTotalVolume: 50000,
            stakingDirectActiveCount: 2,
            stakingActive: true
          });

          testUsers.level2.push(f2User);

          // 4. Tạo 2 người dùng F3 cho mỗi F2
          for (let k = 0; k < 2; k++) {
            const f3User = await queryRunner.manager.save(TestUser, {
              username: `f3_${i}_${j}_${k}`,
              referredBy: f2User.id,
              stakingTotalVolume: 10000,
              stakingDirectActiveCount: 0,
              stakingActive: true
            });

            testUsers.level3.push(f3User);
          }
        }
      }

      // 5. Cập nhật path cho tất cả người dùng
      await this.updateUserPaths(queryRunner, testUsers);

      // 6. Thiết lập cấp bậc ban đầu cho một số người dùng
      // Nhánh 1: Có 1 W1 ở F2
      await queryRunner.manager.update(TestUser, testUsers.level2[0].id, { wManagerRank: 'W1' });

      // Nhánh 2: Có 1 W2 ở F1
      await queryRunner.manager.update(TestUser, testUsers.level1[1].id, { wManagerRank: 'W2' });

      // Nhánh 3: Có 1 W3 ở F2
      await queryRunner.manager.update(TestUser, testUsers.level2[4].id, { wManagerRank: 'W3' });

      await queryRunner.commitTransaction();

      return {
        success: true,
        message: 'Tạo dữ liệu test thành công',
        testUsers: {
          root: testUsers.root.id,
          level1: testUsers.level1.map(u => ({ id: u.id, username: u.username })),
          level2: testUsers.level2.map(u => ({ id: u.id, username: u.username })),
          level3: testUsers.level3.map(u => ({ id: u.id, username: u.username }))
        }
      };
    } catch (error) {
      this.logger.error('Lỗi khi tạo dữ liệu test:', error);
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Cập nhật path cho người dùng test
   */
  async updateUserPaths(queryRunner: any, testUsers: any): Promise<void> {
    // Cập nhật path cho root
    await queryRunner.manager.update(TestUser, testUsers.root.id, {
      path: testUsers.root.id
    });

    // Cập nhật path cho F1
    for (const f1 of testUsers.level1) {
      await queryRunner.manager.update(TestUser, f1.id, {
        path: `${testUsers.root.id}.${f1.id}`
      });
    }

    // Cập nhật path cho F2
    for (const f2 of testUsers.level2) {
      const f1 = testUsers.level1.find(u => u.id === f2.referredBy);
      await queryRunner.manager.update(TestUser, f2.id, {
        path: `${testUsers.root.id}.${f1.id}.${f2.id}`
      });
    }

    // Cập nhật path cho F3
    for (const f3 of testUsers.level3) {
      const f2 = testUsers.level2.find(u => u.id === f3.referredBy);
      const f1 = testUsers.level1.find(u => u.id === f2.referredBy);
      await queryRunner.manager.update(TestUser, f3.id, {
        path: `${testUsers.root.id}.${f1.id}.${f2.id}.${f3.id}`
      });
    }
  }

  /**
   * Cập nhật cấp bậc WManager dựa trên stakingTotalVolume
   */
  async summarizeWManagerRank(): Promise<any> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. Lấy thông tin ban đầu
      const initialUsers = await queryRunner.manager.find(TestUser);
      const initialRanks = initialUsers.map(user => ({
        id: user.id,
        username: user.username,
        rank: user.wManagerRank || 'None'
      }));

      // 2. Lấy thông tin của tất cả users có stakingTotalVolume >= 6000
      const users = await queryRunner.manager.find(TestUser, {
        where: {
          stakingTotalVolume: MoreThanOrEqual(6000)
        }
      });

      if (users.length === 0) {
        this.logger.debug('Không có user nào đủ điều kiện xét rank');
        return { success: false, message: 'Không có user nào đủ điều kiện xét rank' };
      }

      // 3. Xử lý từng user
      const rankUpdates = [];
      for (const user of users) {
        const volume = user.stakingTotalVolume;
        const currentRank = user.wManagerRank || '';

        // Đếm số F1 active
        const activeF1Count = await this.countActiveF1(queryRunner, user.id);

        // Kiểm tra điều kiện cấp bậc
        let calculatedRank = 'None';

        this.logger.debug(`Kiểm tra cấp bậc cho user ${user.id} (${user.username}): volume=${volume}, activeF1Count=${activeF1Count}, currentRank=${currentRank || 'None'}`);

        // Check W1 (2 F1 active và doanh số tổng 6000$)
        if (volume >= 6000 && activeF1Count >= 2) {
          calculatedRank = 'W1';
          this.logger.debug(`User ${user.id} đủ điều kiện cấp W1`);
        }

        // Check W2 (2W1 ở 2 nhánh bất kỳ và doanh số tổng 15.000$)
        if (volume >= 15000) {
          const w1Count = await this.countF1WithRank(queryRunner, user.id, 'W1');
          this.logger.debug(`User ${user.id} có ${w1Count} nhánh đạt cấp W1`);
          if (w1Count >= 2) {
            calculatedRank = 'W2';
            this.logger.debug(`User ${user.id} đủ điều kiện cấp W2`);
          }
        }

        // Check W3 (2W2 ở 2 nhánh bất kỳ và doanh số tổng 40.000$)
        if (volume >= 40000) {
          const w2Count = await this.countF1WithRank(queryRunner, user.id, 'W2');
          this.logger.debug(`User ${user.id} có ${w2Count} nhánh đạt cấp W2`);
          if (w2Count >= 2) {
            calculatedRank = 'W3';
            this.logger.debug(`User ${user.id} đủ điều kiện cấp W3`);
          }
        }

        // Check W4 (2W3 ở 2 nhánh bất kỳ và doanh số tổng 100.000$)
        if (volume >= 100000) {
          const w3Count = await this.countF1WithRank(queryRunner, user.id, 'W3');
          this.logger.debug(`User ${user.id} có ${w3Count} nhánh đạt cấp W3`);
          if (w3Count >= 2) {
            calculatedRank = 'W4';
            this.logger.debug(`User ${user.id} đủ điều kiện cấp W4`);
          }
        }

        // Check W5 (2W4 ở 2 nhánh bất kỳ và doanh số tổng 200.000$)
        if (volume >= 200000) {
          const w4Count = await this.countF1WithRank(queryRunner, user.id, 'W4');
          this.logger.debug(`User ${user.id} có ${w4Count} nhánh đạt cấp W4`);
          if (w4Count >= 2) {
            calculatedRank = 'W5';
            this.logger.debug(`User ${user.id} đủ điều kiện cấp W5`);
          }
        }

        // So sánh cấp bậc tính toán với cấp bậc hiện tại
        // Chỉ nâng cấp, không giảm cấp
        let newRank = currentRank;

        // Hàm hỗ trợ để so sánh cấp bậc
        const getRankValue = (rank) => {
          switch (rank) {
            case 'W5': return 5;
            case 'W4': return 4;
            case 'W3': return 3;
            case 'W2': return 2;
            case 'W1': return 1;
            default: return 0;
          }
        };

        const currentRankValue = getRankValue(currentRank);
        const calculatedRankValue = getRankValue(calculatedRank);

        // Chỉ nâng cấp, không giảm cấp
        if (calculatedRankValue > currentRankValue) {
          newRank = calculatedRank;
          this.logger.debug(`User ${user.id} được nâng cấp từ ${currentRank || 'None'} lên ${calculatedRank}`);
        } else if (calculatedRankValue < currentRankValue) {
          this.logger.debug(`User ${user.id} giữ nguyên cấp bậc ${currentRank} (cấp tính toán: ${calculatedRank})`);
        }

        if (newRank !== currentRank) {
          rankUpdates.push({
            id: user.id,
            username: user.username,
            oldRank: currentRank || 'None',
            newRank: newRank
          });

          await queryRunner.manager.update(TestUser, user.id, {
            wManagerRank: newRank
          });
        }
      }

      // 4. Cập nhật stakingIsShareholder với điều kiện có 2 F1 đạt cấp W5
      const users_W5 = await queryRunner.manager.find(TestUser, {
        where: {
          wManagerRank: 'W5'
        }
      });

      const shareholderUpdates = [];
      for (const user of users_W5) {
        const w5F1Count = await this.countF1WithRank(queryRunner, user.id, 'W5');

        if (w5F1Count >= 2 && !user.stakingIsShareholder) {
          shareholderUpdates.push({
            id: user.id,
            username: user.username
          });

          await queryRunner.manager.update(TestUser, user.id, {
            stakingIsShareholder: true
          });
        }
      }

      // 5. Lấy thông tin sau khi cập nhật
      const updatedUsers = await queryRunner.manager.find(TestUser);
      const finalRanks = updatedUsers.map(user => ({
        id: user.id,
        username: user.username,
        rank: user.wManagerRank || 'None',
        isShareholder: user.stakingIsShareholder
      }));

      await queryRunner.commitTransaction();

      return {
        success: true,
        message: 'Cập nhật cấp bậc WManager thành công',
        initialRanks,
        finalRanks,
        rankUpdates,
        shareholderUpdates
      };
    } catch (error) {
      this.logger.error('Lỗi khi cập nhật cấp bậc WManager:', error);
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Đếm số F1 active
   */
  private async countActiveF1(queryRunner: any, userId: string): Promise<number> {
    const result = await queryRunner.manager.count(TestUser, {
      where: {
        referredBy: userId,
        stakingActive: true
      }
    });

    return result;
  }

  /**
   * Đếm số nhánh có ít nhất 1 người đạt cấp bậc cụ thể
   */
  private async countF1WithRank(queryRunner: any, userId: string, rank: string): Promise<number> {
    // 1. Lấy danh sách tất cả F1
    const f1Users = await queryRunner.manager.find(TestUser, {
      where: {
        referredBy: userId
      },
      select: ['id', 'path', 'wManagerRank']
    });

    if (f1Users.length === 0) return 0;

    // 2. Đếm số nhánh có ít nhất 1 người đạt cấp bậc cụ thể
    let branchesWithRank = 0;

    // Log thông tin để debug
    this.logger.debug(`Kiểm tra cấp bậc ${rank} cho user ${userId} với ${f1Users.length} F1`);

    for (const f1 of f1Users) {
      // Kiểm tra xem F1 có đạt cấp bậc không
      if (f1.wManagerRank === rank) {
        branchesWithRank++;
        this.logger.debug(`F1 ${f1.id} đạt cấp bậc ${rank}`);
        continue; // Nếu F1 đã đạt cấp bậc, không cần kiểm tra downline
      }

      // Kiểm tra xem có downline nào đạt cấp bậc không
      if (f1.path) {
        // Sử dụng raw query để tìm kiếm chính xác hơn
        const downlineQuery = `
          SELECT COUNT(*) as count
          FROM test_users
          WHERE path LIKE '${f1.path}.%'
          AND wManagerRank = '${rank}'
        `;

        const result = await queryRunner.query(downlineQuery);
        const downlineWithRank = parseInt(result[0].count, 10);

        if (downlineWithRank > 0) {
          branchesWithRank++;
          this.logger.debug(`Nhánh của F1 ${f1.id} có ${downlineWithRank} downline đạt cấp bậc ${rank}`);
        }
      }

      // Nếu đã tìm thấy ít nhất 2 nhánh, không cần kiểm tra tiếp
      if (branchesWithRank >= 2) {
        this.logger.debug(`Đã tìm thấy đủ 2 nhánh có cấp bậc ${rank}`);
        break;
      }
    }

    this.logger.debug(`Kết quả: ${branchesWithRank} nhánh có cấp bậc ${rank}`);
    return branchesWithRank;
  }
}
