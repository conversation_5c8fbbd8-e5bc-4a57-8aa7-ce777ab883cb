import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { TransactionStatus, TransactionType } from '../common/enums/transaction.enum';
import { getCurrentTime } from '../common/utils/date.utils';
import { TokenService } from '../token/token.service';
import { User } from '../users/entities/user.entity';
import { Transaction } from '../wallet/entities/transaction.entity';
import { Wallet } from '../wallet/entities/wallet.entity';
import { WalletService } from '../wallet/wallet.service';
import { StakingStatus, StakingTransaction } from './entities/staking-transaction.entity';
import { StakingService } from './staking.service';

@Injectable()
export class StakingExpiryService {
  private readonly logger = new Logger(StakingExpiryService.name);

  constructor(
    @InjectRepository(StakingTransaction)
    private readonly stakingTransactionRepository: Repository<StakingTransaction>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
    private readonly tokenService: TokenService,
    private readonly walletService: WalletService,
    @Inject(forwardRef(() => StakingService))
    private readonly stakingService: any,
    @InjectDataSource() public dataSource: DataSource,
  ) {}

  /**
   * Check and complete expired staking packages
   */
  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async completeExpiredStakings(): Promise<void> {
    this.logger.log('Checking for expired staking packages...');

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const now = getCurrentTime();

      // 1. Get all expired staking packages
      // Using query to compare elapsedDays with durationDays
      const expiredStakings = await queryRunner.manager
        .createQueryBuilder(StakingTransaction, 'staking')
        .where('staking.status = :status AND staking.endDate <= :now', {
          status: StakingStatus.ACTIVE,
          now: now
        })
        .orWhere('staking.status = :status AND staking.elapsedDays >= staking.durationDays', {
          status: StakingStatus.ACTIVE
        })
        .getMany();

      this.logger.log(`Found ${expiredStakings.length} expired staking packages`);

      // 2. Complete each package
      for (const staking of expiredStakings) {
        await this.completeStakingWithQueryRunner(queryRunner, staking, now);
      }

      // 3. Commit transaction
      await queryRunner.commitTransaction();

      this.logger.log('Successfully completed expired staking packages');
    } catch (error) {
      // Rollback on error
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      this.logger.error(`Error completing expired staking packages: ${error.message}`, error.stack);
    } finally {
      // Release queryRunner
      await queryRunner.release();
    }
  }

  /**
   * Complete a specific staking package
   * @param queryRunner QueryRunner to ensure transaction integrity
   * @param staking Staking package information
   * @param now Current time
   */
  async completeStakingWithQueryRunner(queryRunner: any, staking: StakingTransaction, now: Date): Promise<void> {
    // 1. Calculate final interest if needed
    if (staking.lastInterestDate < staking.endDate) {
      await this.stakingService.calculateInterestForStakingWithQueryRunner(queryRunner, staking, now);
    }

    // 2. Get user and wallet information
    const wallet = await queryRunner.manager.findOne(Wallet, { where: { userId: staking.userId } });
    const usdtToken = await this.tokenService.findBySymbol('USDT');
    const wmToken = await this.tokenService.findBySymbol('WM');

    // 3. Calculate bonus amount
    const bonusAmount = staking.amount * (staking.bonusRate / 100);

    // 4. Create bonus transaction
    const bonusTransaction = await queryRunner.manager.save(Transaction, {
      userId: staking.userId,
      walletId: wallet.id,
      tokenId: wmToken.id,
      type: TransactionType.GLOBAL_MINING_COSHARE_BONUS,
      amount: bonusAmount,
      status: TransactionStatus.COMPLETED,
      note: `Completion bonus for Global Mining Co-Share package ${staking.packageCode} (${staking.durationDays} days) (+${bonusAmount} WM)`,
      createdAt: now,
      updatedAt: now,
      transactionAt: now
    });

    // 5. Update WM balance for bonus
    await this.walletService.updateTokenBalanceWithQueryRunner(
      queryRunner,
      wallet,
      wmToken,
      'wmBalance',
      bonusTransaction,
      bonusAmount,
      'add',
      now
    );

    // 6. Create principal return transaction
    const principalTransaction = await queryRunner.manager.save(Transaction, {
      userId: staking.userId,
      walletId: wallet.id,
      tokenId: usdtToken.id,
      type: TransactionType.GLOBAL_MINING_COSHARE,
      amount: staking.amount,
      status: TransactionStatus.COMPLETED,
      note: `Principal return for completed Global Mining Co-Share package ${staking.packageCode} (+${staking.amount} USDT)`,
      createdAt: now,
      updatedAt: now,
      transactionAt: now
    });

    // 7. Update USDT balance for principal
    await this.walletService.updateTokenBalanceWithQueryRunner(
      queryRunner,
      wallet,
      usdtToken,
      'usdtBalance',
      principalTransaction,
      staking.amount,
      'add',
      now
    );

    // 8. Update staking package information
    await queryRunner.manager.update(StakingTransaction, staking.id, {
      status: StakingStatus.COMPLETED,
      totalBonus: bonusAmount,
      completionTransactionId: principalTransaction.id,
      updatedAt: now
    });

    this.logger.log(`Completed staking package ${staking.id}: Returned principal ${staking.amount} USDT, bonus ${bonusAmount} WM`);
  }
}
