import { ApiProperty } from '@nestjs/swagger';

export class ReferralMilestoneDto {
  @ApiProperty({ description: 'Milestone count' })
  milestone: number;

  @ApiProperty({ description: 'Reward amount' })
  rewardAmount: number;

  @ApiProperty({ description: 'Is milestone rewarded' })
  isRewarded: boolean;

  @ApiProperty({ description: 'Is milestone reachable' })
  isReachable: boolean;
}

export class StakingStatsResponseDto {
  @ApiProperty({ description: 'Active stakings count' })
  activeStakingsCount: number;

  @ApiProperty({ description: 'Completed stakings count' })
  completedStakingsCount: number;

  @ApiProperty({ description: 'Total staking amount' })
  totalStakingAmount: number;

  @ApiProperty({ description: 'Total interest earned' })
  totalInterestEarned: number;

  @ApiProperty({ description: 'Today interest earned' })
  todayInterest: number;

  @ApiProperty({ description: 'Total bonus earned' })
  totalBonusEarned: number;

  @ApiProperty({ description: 'WM/USDT exchange rate' })
  wmUsdRate: number;

  @ApiProperty({ description: 'Direct active count' })
  directActiveCount: number;

  @ApiProperty({ description: 'Rewarded milestone' })
  rewardedMilestone: number;

  @ApiProperty({ description: 'Total volume' })
  totalVolume: number;

  @ApiProperty({ description: 'Total commission' })
  totalCommission: number;

  @ApiProperty({ description: 'Direct commission' })
  directCommission: number;

  @ApiProperty({ description: 'Matching commission' })
  matchingCommission: number;

  @ApiProperty({ description: 'Rank commission' })
  rankCommission: number;

  @ApiProperty({ description: 'Rank' })
  rank: string;

  @ApiProperty({ description: 'Is shareholder' })
  isShareholder: boolean;

  @ApiProperty({ description: 'Referral milestones', type: [ReferralMilestoneDto] })
  referralMilestones: ReferralMilestoneDto[];
}
