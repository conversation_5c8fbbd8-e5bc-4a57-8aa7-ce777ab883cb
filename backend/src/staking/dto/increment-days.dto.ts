import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsN<PERSON>ber, Min } from 'class-validator';

export class IncrementDaysDto {
  @ApiProperty({
    description: 'Staking transaction ID (optional, if not provided will increment all active stakings)',
    required: false
  })
  @IsString()
  @IsOptional()
  stakingId?: string;

  @ApiProperty({
    description: 'Number of days to increment (default: 1)',
    required: false,
    default: 1
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  days?: number;
}
