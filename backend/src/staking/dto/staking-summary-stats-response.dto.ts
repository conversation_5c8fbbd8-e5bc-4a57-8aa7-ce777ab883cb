import { ApiProperty } from '@nestjs/swagger';

export class StakingSummaryStatsResponseDto {
  @ApiProperty({ description: 'Total staking transactions' })
  totalStakingCount: number;

  @ApiProperty({ description: 'Today staking transactions' })
  todayStakingCount: number;

  @ApiProperty({ description: 'Total earned' })
  totalEarned: number;

  @ApiProperty({ description: 'Today earned' })
  todayEarned: number;
}
