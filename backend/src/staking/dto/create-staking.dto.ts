import { ApiProperty } from '@nestjs/swagger';
import { IsString, <PERSON>N<PERSON>ber, IsNotEmpty, IsIn, IsOptional, IsBoolean } from 'class-validator';
import { STAKING_PACKAGES, STAKING_DURATIONS } from '../constants/staking.constants';

export class CreateStakingDto {
  @ApiProperty({
    description: 'Package code',
    example: 'PHOENIX',
    enum: Object.keys(STAKING_PACKAGES)
  })
  @IsString()
  @IsNotEmpty()
  @IsIn(Object.keys(STAKING_PACKAGES))
  packageCode: string;

  @ApiProperty({
    description: 'Duration in days',
    example: 30,
    enum: Object.values(STAKING_DURATIONS).map(d => d.days)
  })
  @IsNumber()
  @IsNotEmpty()
  @IsIn(Object.values(STAKING_DURATIONS).map(d => d.days))
  durationDays: number;
}
