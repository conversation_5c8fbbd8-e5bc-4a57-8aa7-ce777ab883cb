import { ApiProperty } from '@nestjs/swagger';
import { StakingStatus } from '../entities/staking-transaction.entity';

export class StakingResponseDto {
  @ApiProperty({ description: 'Staking transaction ID' })
  id: string;

  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({ description: 'Package code' })
  packageCode: string;

  @ApiProperty({ description: 'Package name' })
  packageName: string;

  @ApiProperty({ description: 'Staking amount' })
  amount: number;

  @ApiProperty({ description: 'Duration in days' })
  durationDays: number;

  @ApiProperty({ description: 'Interest rate (%)' })
  interestRate: number;

  @ApiProperty({ description: 'Bonus rate (%)' })
  bonusRate: number;

  @ApiProperty({ description: 'Daily interest rate (%)' })
  dailyInterestRate: number;

  @ApiProperty({ description: 'Start date' })
  startDate: Date;

  @ApiProperty({ description: 'End date' })
  endDate: Date;

  @ApiProperty({ description: 'Staking status', enum: StakingStatus })
  status: StakingStatus;

  @ApiProperty({ description: 'Total interest earned' })
  totalInterest: number;

  @ApiProperty({ description: 'Total bonus earned' })
  totalBonus: number;

  @ApiProperty({ description: 'Last interest calculation date' })
  lastInterestDate: Date;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Days remaining' })
  daysRemaining: number;

  @ApiProperty({ description: 'Progress percentage' })
  progressPercentage: number;

  @ApiProperty({ description: 'Expected total return' })
  expectedTotalReturn: number;
}
