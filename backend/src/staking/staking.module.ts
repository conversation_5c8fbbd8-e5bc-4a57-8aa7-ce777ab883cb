import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { StakingTransaction } from './entities/staking-transaction.entity';
import { StakingInterestHistory } from './entities/staking-interest-history.entity';
import { User } from '../users/entities/user.entity';
import { Wallet } from '../wallet/entities/wallet.entity';
import { Transaction } from '../wallet/entities/transaction.entity';
import { StakingService } from './staking.service';
import { StakingRankService } from './staking-rank.service';
import { StakingExpiryService } from './staking-expiry.service';
import { StakingController } from './staking.controller';
import { TokenModule } from '../token/token.module';
import { WalletModule } from '../wallet/wallet.module';
import { SystemConfigModule } from '../common/modules/system-config.module';
import { TestUser } from './entities/test-user.entity';
import { TestRankService } from './test-rank.service';
import { TestRankController } from './test-rank.controller';
import { ApiKeyModule } from 'src/auth/api-key.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      StakingTransaction,
      StakingInterestHistory,
      User,
      Wallet,
      Transaction,
      TestUser
    ]),
    TokenModule,
    WalletModule,
    SystemConfigModule,
    ApiKeyModule
  ],
  controllers: [StakingController, TestRankController],
  providers: [
    StakingRankService,
    StakingService,
    TestRankService,
    {
      provide: StakingExpiryService,
      useClass: StakingExpiryService
    }
  ],
  exports: [StakingService, StakingRankService, StakingExpiryService]
})
export class StakingModule {}
