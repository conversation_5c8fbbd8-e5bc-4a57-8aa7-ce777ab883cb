import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../users/entities/user.entity';
import { Transaction } from '../../wallet/entities/transaction.entity';
import { StakingTransaction } from './staking-transaction.entity';

@Entity('staking_interest_history')
export class StakingInterestHistory {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'Interest history ID' })
  id: string;

  @Column()
  @ApiProperty({ description: 'Staking transaction ID' })
  stakingId: string;

  @ManyToOne(() => StakingTransaction, staking => staking.interestHistory)
  @JoinColumn({ name: 'stakingId' })
  staking: StakingTransaction;

  @Column()
  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'decimal', precision: 18, scale: 8 })
  @ApiProperty({ description: 'Interest amount' })
  amount: number;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Transaction ID for interest payment' })
  transactionId: string;

  @ManyToOne(() => Transaction, { nullable: true })
  @JoinColumn({ name: 'transactionId' })
  transaction: Transaction;

  @Column()
  @ApiProperty({ description: 'Calculation date' })
  calculationDate: Date;

  @CreateDateColumn()
  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}
