import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('test_users')
export class TestUser {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  username: string;

  @Column({ nullable: true })
  referredBy?: string;

  @Column({ type: 'text', nullable: true })
  path?: string;

  @Column({ type: 'boolean', default: false })
  stakingActive: boolean;

  @Column({ type: 'int', default: 0 })
  stakingDirectActiveCount: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  stakingTotalVolume: number;

  @Column({ nullable: true })
  wManagerRank?: string;

  @Column({ type: 'boolean', default: false })
  stakingIsShareholder: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
