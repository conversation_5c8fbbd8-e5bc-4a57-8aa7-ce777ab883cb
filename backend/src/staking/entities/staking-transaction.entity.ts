import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../users/entities/user.entity';
import { Transaction } from '../../wallet/entities/transaction.entity';
import { StakingInterestHistory } from './staking-interest-history.entity';

export enum StakingStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

@Entity('staking_transactions')
export class StakingTransaction {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'Staking transaction ID' })
  id: string;

  @Column()
  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  @ApiProperty({ description: 'Package code (PHOENIX, UNICORN, DRAGON, etc.)' })
  packageCode: string;

  @Column({ type: 'decimal', precision: 18, scale: 8 })
  @ApiProperty({ description: 'Staking amount' })
  amount: number;

  @Column()
  @ApiProperty({ description: 'Duration in days' })
  durationDays: number;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  @ApiProperty({ description: 'Interest rate (%)' })
  interestRate: number;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  @ApiProperty({ description: 'Bonus rate (%)' })
  bonusRate: number;

  @Column()
  @ApiProperty({ description: 'Start date' })
  startDate: Date;

  @Column()
  @ApiProperty({ description: 'End date' })
  endDate: Date;

  @Column({ type: 'enum', enum: StakingStatus, default: StakingStatus.PENDING })
  @ApiProperty({ description: 'Staking status', enum: StakingStatus })
  status: StakingStatus;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Transaction ID for staking creation' })
  transactionId: string;

  @ManyToOne(() => Transaction, { nullable: true })
  @JoinColumn({ name: 'transactionId' })
  transaction: Transaction;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Transaction ID for staking completion' })
  completionTransactionId: string;

  @ManyToOne(() => Transaction, { nullable: true })
  @JoinColumn({ name: 'completionTransactionId' })
  completionTransaction: Transaction;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Total interest earned' })
  totalInterest: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Total bonus earned' })
  totalBonus: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'WM bonus amount calculated at creation time' })
  wmBonusAmount: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'WM/USD exchange rate at creation time' })
  wmUsdRate: number;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Last interest calculation date' })
  lastInterestDate: Date;

  @OneToMany(() => StakingInterestHistory, history => history.staking)
  interestHistory: StakingInterestHistory[];

  @Column({ name: 'elapsedDays', default: 0 })
  @ApiProperty({ description: 'Số ngày đã trôi qua trong gói staking' })
  elapsedDays: number;

  @CreateDateColumn()
  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}
