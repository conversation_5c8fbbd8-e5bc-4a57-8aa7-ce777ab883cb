import { Controller, Get, Post, Body, Param, Request, UseGuards, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse, ApiHeader } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThanOrEqual, Not, IsNull, In } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { UserRole, WManagerRank } from '../common/enums/user.enum';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { StakingService } from './staking.service';
import { StakingRankService } from './staking-rank.service';
import { TokenService } from '../token/token.service';
import { WalletService } from '../wallet/wallet.service';
import { CreateStakingDto } from './dto/create-staking.dto';
import { IncrementDaysDto } from './dto/increment-days.dto';
import { ApiResponseDto } from '../common/dto/api-response.dto';
import { STAKING_PACKAGES, STAKING_DURATIONS } from './constants/staking.constants';
import { getCurrentTime } from '../common/utils/date.utils';
import { Wallet } from '../wallet/entities/wallet.entity';
import { ApiKeyGuard } from '../auth/guards/api-key.guard';

@ApiTags('staking')
@Controller('staking')
export class StakingController {
  private readonly logger = new Logger(StakingController.name);

  constructor(
    private readonly stakingService: StakingService,
    private readonly stakingRankService: StakingRankService,
    private readonly tokenService: TokenService,
    private readonly walletService: WalletService,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
  ) {}

  @Get('packages')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get available staking packages' })
  async getStakingPackages() {
    const packages = Object.values(STAKING_PACKAGES).map(pkg => ({
      code: pkg.code,
      name: pkg.name,
      amount: pkg.amount,
      durations: Object.values(STAKING_DURATIONS).map(duration => ({
        days: duration.days,
        interestRate: duration.interestRate,
        bonusRate: duration.bonusRate,
        dailyInterestRate: duration.interestRate / 30, // Lãi suất hàng ngày
        totalReturn: pkg.amount * (1 + (duration.interestRate / 100) * (duration.days / 30) + (duration.bonusRate / 100)) // Tổng lợi nhuận
      }))
    }));

    return ApiResponseDto.success(packages);
  }

  @Get('my-stakings')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user staking packages' })
  async getUserStakings(@Request() req: any) {
    const userId = req.user.id;

    const stakings = await this.stakingService.findAllByUserId(userId);

    const result = stakings.map(staking => {
      const packageInfo = STAKING_PACKAGES[staking.packageCode];

      return {
        id: staking.id,
        packageCode: staking.packageCode,
        packageName: packageInfo?.name || staking.packageCode,
        amount: staking.amount,
        durationDays: staking.durationDays,
        interestRate: staking.interestRate,
        bonusRate: staking.bonusRate,
        dailyInterestRate: staking.interestRate / 30,
        startDate: staking.startDate,
        endDate: staking.endDate,
        status: staking.status,
        totalInterest: staking.totalInterest,
        totalBonus: staking.totalBonus,
        wmBonusAmount: staking.wmBonusAmount, // Thêm trường wmBonusAmount
        wmUsdRate: staking.wmUsdRate, // Thêm trường wmUsdRate
        lastInterestDate: staking.lastInterestDate,
        createdAt: staking.createdAt,
        elapsedDays: staking.elapsedDays || 0,
        daysRemaining: staking.status === 'ACTIVE' ? Math.max(0, staking.durationDays - staking.elapsedDays) : 0,
        progressPercentage: staking.status === 'ACTIVE'
          ? Math.min(100, (staking.elapsedDays / staking.durationDays) * 100)
          : (staking.status === 'COMPLETED' ? 100 : 0)
      };
    });

    return ApiResponseDto.success(result);
  }

  @Get('stakings/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get staking package details' })
  async getStakingDetails(@Request() req: any, @Param('id') id: string) {
    const userId = req.user.id;

    const staking = await this.stakingService.findOneByIdAndUserId(id, userId);

    if (!staking) {
      throw new NotFoundException({
        message: {
          en: 'Staking package not found',
        },
        code: 'STAKING_PACKAGE_NOT_FOUND',
      });
    }

    // Lấy lịch sử lãi suất
    const interestHistory = await this.stakingService.findInterestHistoryByStakingId(staking.id);

    const packageInfo = STAKING_PACKAGES[staking.packageCode];

    const result = {
      id: staking.id,
      packageCode: staking.packageCode,
      packageName: packageInfo?.name || staking.packageCode,
      amount: staking.amount,
      durationDays: staking.durationDays,
      interestRate: staking.interestRate,
      bonusRate: staking.bonusRate,
      dailyInterestRate: staking.interestRate / 30,
      startDate: staking.startDate,
      endDate: staking.endDate,
      status: staking.status,
      totalInterest: staking.totalInterest,
      totalBonus: staking.totalBonus,
      wmBonusAmount: staking.wmBonusAmount, // Thêm trường wmBonusAmount
      wmUsdRate: staking.wmUsdRate, // Thêm trường wmUsdRate
      lastInterestDate: staking.lastInterestDate,
      createdAt: staking.createdAt,
      elapsedDays: staking.elapsedDays || 0,
      daysRemaining: staking.status === 'ACTIVE' ? Math.max(0, staking.durationDays - staking.elapsedDays) : 0,
      progressPercentage: staking.status === 'ACTIVE'
        ? Math.min(100, (staking.elapsedDays / staking.durationDays) * 100)
        : (staking.status === 'COMPLETED' ? 100 : 0),
      expectedTotalReturn: staking.amount * (1 + (staking.interestRate / 100) * (staking.durationDays / 30) + (staking.bonusRate / 100)),
      interestHistory: interestHistory.map(history => ({
        id: history.id,
        amount: history.amount,
        calculationDate: history.calculationDate,
        createdAt: history.createdAt
      }))
    };

    return ApiResponseDto.success(result);
  }

  @Post('create')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create new staking package' })
  async createStaking(@Request() req: any, @Body() createStakingDto: CreateStakingDto) {
    const userId = req.user.id;

    try {
      const staking = await this.stakingService.createStaking(userId, createStakingDto);

      const packageInfo = STAKING_PACKAGES[staking.packageCode];

      const result = {
        id: staking.id,
        packageCode: staking.packageCode,
        packageName: packageInfo?.name || staking.packageCode,
        amount: staking.amount,
        durationDays: staking.durationDays,
        interestRate: staking.interestRate,
        bonusRate: staking.bonusRate,
        dailyInterestRate: staking.interestRate / 30,
        startDate: staking.startDate,
        endDate: staking.endDate,
        status: staking.status,
        createdAt: staking.createdAt
      };

      return ApiResponseDto.success(result, 'Staking package created successfully');
    } catch (error) {
      // Nếu error đã có định dạng chuẩn, truyền lại nguyên vẹn
      if (error.response && error.response.message && error.response.code) {
        throw error;
      }

      // Nếu không, chuẩn hóa lại
      throw new BadRequestException({
        message: {
          en: error.message || 'Failed to create staking package',
        },
        code: 'CREATE_STAKING_FAILED',
      });
    }
  }

  @Post('api-key/admin/create')
  @UseGuards(ApiKeyGuard)
  @ApiHeader({ name: 'x-api-key', description: 'API Key' })
  @ApiHeader({ name: 'x-wallet-address', description: 'Wallet Address' })
  @ApiOperation({ summary: 'Deposit USDT using API Key' })
  @ApiResponse({ status: 200, description: 'Deposit USDT created successfully' })
  async createStakingApiKey(@Request() req: any, @Body() createStakingDto: CreateStakingDto) {
    const walletAddress = req.headers['x-wallet-address'];
    if (!walletAddress) {
      throw new BadRequestException('Wallet address is required in x-wallet-address header');
    }

    const wallet = await this.walletService.findByAddress(walletAddress);
    if (!wallet) {
      throw new NotFoundException('Wallet not found');
    }

    try {
      const staking = await this.stakingService.createStaking(wallet.userId, createStakingDto);

      const packageInfo = STAKING_PACKAGES[staking.packageCode];

      const result = {
        id: staking.id,
        packageCode: staking.packageCode,
        packageName: packageInfo?.name || staking.packageCode,
        amount: staking.amount,
        durationDays: staking.durationDays,
        interestRate: staking.interestRate,
        bonusRate: staking.bonusRate,
        dailyInterestRate: staking.interestRate / 30,
        startDate: staking.startDate,
        endDate: staking.endDate,
        status: staking.status,
        createdAt: staking.createdAt
      };

      return ApiResponseDto.success(result, 'Staking package created successfully');
    } catch (error) {
      // Nếu error đã có định dạng chuẩn, truyền lại nguyên vẹn
      if (error.response && error.response.message && error.response.code) {
        throw error;
      }

      // Nếu không, chuẩn hóa lại
      throw new BadRequestException({
        message: {
          en: error.message || 'Failed to create staking package',
        },
        code: 'CREATE_STAKING_FAILED',
      });
    }
  }

  @Post('api-key/admin/system/manual-create')
  @UseGuards(ApiKeyGuard)
  @ApiHeader({ name: 'x-api-key', description: 'API Key' })
  @ApiHeader({ name: 'x-wallet-address', description: 'Wallet Address' })
  @ApiOperation({ summary: 'Deposit USDT using API Key' })
  @ApiResponse({ status: 200, description: 'Deposit USDT created successfully' })
  async createStakingApiKeySystemManual(@Request() req: any, @Body() createStakingDto: CreateStakingDto) {

    const walletAddress = req.headers['x-wallet-address'];
    if (!walletAddress) {
      throw new BadRequestException('Wallet address is required in x-wallet-address header');
    }

    const wallet = await this.walletService.findByAddress(walletAddress);
    if (!wallet) {
      throw new NotFoundException('Wallet not found');
    }

    try {
      const staking = await this.stakingService.createStakingSystemManual(wallet.userId, createStakingDto);

      const packageInfo = STAKING_PACKAGES[staking.packageCode];

      const result = {
        id: staking.id,
        packageCode: staking.packageCode,
        packageName: packageInfo?.name || staking.packageCode,
        amount: staking.amount,
        durationDays: staking.durationDays,
        interestRate: staking.interestRate,
        bonusRate: staking.bonusRate,
        dailyInterestRate: staking.interestRate / 30,
        startDate: staking.startDate,
        endDate: staking.endDate,
        status: staking.status,
        createdAt: staking.createdAt
      };

      return ApiResponseDto.success(result, 'Staking package created successfully');
    } catch (error) {
      // Nếu error đã có định dạng chuẩn, truyền lại nguyên vẹn
      if (error.response && error.response.message && error.response.code) {
        throw error;
      }

      // Nếu không, chuẩn hóa lại
      throw new BadRequestException({
        message: {
          en: error.message || 'Failed to create staking package',
        },
        code: 'CREATE_STAKING_FAILED',
      });
    }
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user staking statistics' })
  async getStakingStats(@Request() req: any) {
    const userId = req.user.id;

    const stats = await this.stakingService.getUserStats(userId);

    return ApiResponseDto.success(stats);
  }

  @Get('stats/summary')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get summary staking statistics' })
  async getStakingSummaryStats() {
    const stats = await this.stakingService.getSummaryStats();

    return ApiResponseDto.success(stats);
  }

  @Post('test/increment-days')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Increment elapsed days for staking package' })
  async incrementDays(@Body() incrementDaysDto: IncrementDaysDto) {
    const { stakingId, days = 1 } = incrementDaysDto;

    const result = await this.stakingService.incrementDays(stakingId, days);

    return ApiResponseDto.success(result);
  }

  @Get('test/user-info/:id')
  @ApiOperation({ summary: 'Get user information for testing' })
  async getUserInfo(@Param('id') id: string) {
    const user = await this.userRepository.findOne({
      where: { id },
      select: [
        'id', 'username', 'email', 'referredBy', 'wManagerRank',
        'stakingTotalVolume', 'stakingDirectActiveCount', 'stakingActive',
        'stakingDirectCommission', 'stakingMatchingCommission', 'stakingRankCommission',
        'stakingTotalCommission', 'stakingIsShareholder', 'stakingMaxedOut',
        'stakingTotalInvestment', 'stakingTotalEarnings'
      ]
    });

    if (!user) {
      throw new NotFoundException({
        message: {
          en: 'User not found',
        },
        code: 'USER_NOT_FOUND',
      });
    }

    // Tính toán thông tin bổ sung để kiểm tra nghiệp vụ
    const stakingTotalInvestment = parseFloat(user.stakingTotalInvestment?.toString() || '0');
    const stakingTotalEarnings = parseFloat(user.stakingTotalEarnings?.toString() || '0');
    const maxEarnings = stakingTotalInvestment * 3; // 300% tổng đầu tư
    const earningsPercentage = stakingTotalInvestment > 0
      ? (stakingTotalEarnings / stakingTotalInvestment) * 100
      : 0;
    const shouldBeMaxedOut = stakingTotalEarnings >= maxEarnings;

    return ApiResponseDto.success({
      ...user,
      stakingTotalInvestment: stakingTotalInvestment,
      stakingTotalEarnings: stakingTotalEarnings,
      maxEarnings,
      earningsPercentage,
      shouldBeMaxedOut,
      maxedOutStatus: {
        current: user.stakingMaxedOut,
        calculated: shouldBeMaxedOut,
        isCorrect: user.stakingMaxedOut === shouldBeMaxedOut
      }
    });
  }

  @Post('test/update-ranks')
  @ApiOperation({ summary: 'Update WManager ranks' })
  async testUpdateRanks() {
    const result = await this.stakingService.updateWManagerRanks();
    return ApiResponseDto.success(result);
  }

  @Post('calculate-daily-interest')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Calculate daily interest for staking packages (manual)' })
  async calculateDailyInterest() {
    try {
      await this.stakingService.calculateDailyInterest();
      return ApiResponseDto.success({
        success: true,
        message: 'Daily interest calculated successfully'
      });
    } catch (error) {
      this.logger.error(`Error calculating daily interest: ${error.message}`, error.stack);
      throw new BadRequestException({
        message: {
          en: `Failed to calculate daily interest: ${error.message}`,
        },
        code: 'CALCULATE_INTEREST_FAILED',
      });
    }
  }

  @Post('test/debug-ranks')
  @ApiOperation({ summary: 'Debug WManager ranks' })
  async debugRanks() {
    // Lấy thông tin của tất cả users có stakingTotalVolume >= 6000
    const users = await this.userRepository.find({
      where: {
        stakingTotalVolume: MoreThanOrEqual(6000)
      },
      select: ['id', 'username', 'wManagerRank', 'stakingTotalVolume', 'stakingDirectActiveCount', 'referredBy']
    });

    const debugInfo = [];

    for (const user of users) {
      const volume = parseFloat(user.stakingTotalVolume.toString());
      const activeF1Count = user.stakingDirectActiveCount || 0;

      // Kiểm tra điều kiện cấp W1
      const isW1Eligible = volume >= 6000 && activeF1Count >= 2;

      debugInfo.push({
        id: user.id,
        username: user.username,
        currentRank: user.wManagerRank || 'None',
        volume,
        activeF1Count,
        isW1Eligible,
        referredBy: user.referredBy
      });
    }

    // Tìm kiếm user cụ thể
    const specificUser = await this.userRepository.findOne({
      where: { id: '08000bd2-5e23-42dc-9750-7627463ba1e1' },
      select: ['id', 'username', 'wManagerRank', 'stakingTotalVolume', 'stakingDirectActiveCount', 'referredBy']
    });

    let specificUserInfo = null;
    if (specificUser) {
      const volume = parseFloat(specificUser.stakingTotalVolume.toString());
      const activeF1Count = specificUser.stakingDirectActiveCount || 0;
      const isW1Eligible = volume >= 6000 && activeF1Count >= 2;

      specificUserInfo = {
        id: specificUser.id,
        username: specificUser.username,
        currentRank: specificUser.wManagerRank || 'None',
        volume,
        activeF1Count,
        isW1Eligible,
        referredBy: specificUser.referredBy,
        rawWManagerRank: specificUser.wManagerRank,
        typeofWManagerRank: typeof specificUser.wManagerRank
      };
    }

    return ApiResponseDto.success({
      userCount: users.length,
      debugInfo,
      specificUser: specificUserInfo
    });
  }

  @Post('test/force-update-rank')
  @ApiOperation({ summary: 'Update WManager rank for a specific user' })
  async forceUpdateRank(@Body() body: { userId: string, rank: WManagerRank }) {
    const { userId, rank } = body;

    const user = await this.userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException({
        message: {
          en: 'User not found',
        },
        code: 'USER_NOT_FOUND',
      });
    }

    const oldRank = user.wManagerRank || 'None';

    await this.userRepository.update(
      { id: userId },
      { wManagerRank: rank }
    );

    return ApiResponseDto.success({
      success: true,
      message: `Updated user ${userId} rank from ${oldRank} to ${rank}`,
      user: {
        id: userId,
        oldRank,
        newRank: rank
      }
    });
  }

  @Post('test/setup-wm2')
  @ApiOperation({ summary: 'Setup test data for WM2 rank' })
  async setupWM2() {
    const queryRunner = this.stakingService.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. Lấy danh sách người dùng cần thiết lập
      const specialUserId = '08000bd2-5e23-42dc-9750-7627463ba1e1'; // User đã có cấp WM1
      const specialUser = await queryRunner.manager.findOne(User, {
        where: { id: specialUserId }
      });

      if (!specialUser) {
        throw new NotFoundException({
          message: {
            en: 'Special user not found',
          },
          code: 'SPECIAL_USER_NOT_FOUND',
        });
      }

      // 2. Tạo 2 người dùng F1 với cấp WM1
      const f1_1 = await queryRunner.manager.findOne(User, {
        where: { referredBy: specialUserId, username: 'test_f1_1' }
      });

      let f1_1Id = f1_1 ? f1_1.id : null;

      if (!f1_1Id) {
        // Tạo người dùng F1 đầu tiên
        const newF1_1 = await queryRunner.manager.save(User, {
          username: 'test_f1_1',
          email: '<EMAIL>',
          wallet: '0x' + Math.random().toString(16).substring(2, 42), // Tạo địa chỉ ví ngẫu nhiên
          referralCode: Math.random().toString(36).substring(2, 10).toUpperCase(),
          referredBy: specialUserId,
          stakingTotalVolume: 10000,
          stakingDirectActiveCount: 2,
          stakingActive: true,
          wManagerRank: WManagerRank.WM1
        });
        f1_1Id = newF1_1.id;
      } else {
        // Cập nhật cấp bậc WM1 cho F1 đầu tiên
        await queryRunner.manager.update(User, f1_1Id, {
          wManagerRank: WManagerRank.WM1
        });
      }

      const f1_2 = await queryRunner.manager.findOne(User, {
        where: { referredBy: specialUserId, username: 'test_f1_2' }
      });

      let f1_2Id = f1_2 ? f1_2.id : null;

      if (!f1_2Id) {
        // Tạo người dùng F1 thứ hai
        const newF1_2 = await queryRunner.manager.save(User, {
          username: 'test_f1_2',
          email: '<EMAIL>',
          wallet: '0x' + Math.random().toString(16).substring(2, 42), // Tạo địa chỉ ví ngẫu nhiên
          referralCode: Math.random().toString(36).substring(2, 10).toUpperCase(),
          referredBy: specialUserId,
          stakingTotalVolume: 10000,
          stakingDirectActiveCount: 2,
          stakingActive: true,
          wManagerRank: WManagerRank.WM1
        });
        f1_2Id = newF1_2.id;
      } else {
        // Cập nhật cấp bậc WM1 cho F1 thứ hai
        await queryRunner.manager.update(User, f1_2Id, {
          wManagerRank: WManagerRank.WM1
        });
      }

      // 3. Cập nhật stakingTotalVolume cho người dùng đặc biệt
      await queryRunner.manager.update(User, specialUserId, {
        stakingTotalVolume: 15000 // Đủ điều kiện cho WM2
      });

      // 4. Commit transaction
      await queryRunner.commitTransaction();

      // 5. Cập nhật cấp bậc
      await this.stakingService.updateWManagerRanks();

      // 6. Lấy thông tin người dùng sau khi cập nhật
      const updatedUser = await this.userRepository.findOne({
        where: { id: specialUserId }
      });

      return ApiResponseDto.success({
        success: true,
        message: 'Test data for WM2 rank setup successfully',
        user: updatedUser,
        f1Users: await this.userRepository.find({
          where: { referredBy: specialUserId }
        })
      });
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  @Post('test/maxed-out-commission')
  @ApiOperation({ summary: 'Test rank and shareholder commission with stakingMaxedOut' })
  async testMaxedOutCommission(@Body() body: { stakerId: string, volume: number }) {
    const { stakerId, volume = 1000 } = body;

    const queryRunner = this.stakingService.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. Lấy thông tin người dùng
      const staker = await queryRunner.manager.findOne(User, {
        where: { id: stakerId },
        select: ['id', 'username', 'path', 'referredBy']
      });

      if (!staker) {
        throw new NotFoundException({
          message: {
            en: 'User not found',
          },
          code: 'USER_NOT_FOUND',
        });
      }

      // 2. Tạo stakingVolumeMap
      const stakingVolumeMap = new Map<string, number>();
      stakingVolumeMap.set(stakerId, volume);

      // 3. Lấy token USDT
      const usdtToken = await this.tokenService.findBySymbol('USDT');

      // 4. Lấy thông tin upline users trước khi tính hoa hồng
      const uplineIds = staker.path ? staker.path.split('.') : [];
      const beforeUsers = await queryRunner.manager.find(User, {
        where: { id: In(uplineIds) },
        select: [
          'id', 'username', 'wManagerRank', 'stakingMaxedOut',
          'stakingRankCommission', 'stakingMatchingCommission', 'stakingTotalCommission',
          'stakingIsShareholder', 'stakingTotalEarnings', 'stakingTotalInvestment'
        ]
      });

      // 5. Lưu trữ thông tin hoa hồng trước khi tính toán
      const beforeCommissions = beforeUsers.map(user => ({
        id: user.id,
        username: user.username,
        wManagerRank: user.wManagerRank,
        stakingMaxedOut: user.stakingMaxedOut,
        stakingRankCommission: parseFloat(user.stakingRankCommission?.toString() || '0'),
        stakingMatchingCommission: parseFloat(user.stakingMatchingCommission?.toString() || '0'),
        stakingTotalCommission: parseFloat(user.stakingTotalCommission?.toString() || '0'),
        stakingTotalEarnings: parseFloat(user.stakingTotalEarnings?.toString() || '0'),
        stakingTotalInvestment: parseFloat(user.stakingTotalInvestment?.toString() || '0'),
        stakingIsShareholder: user.stakingIsShareholder
      }));

      // 6. Tính toán hoa hồng
      const now = getCurrentTime();
      const transactions = await this.stakingRankService.summarizeWManagerCommission(
        queryRunner,
        stakingVolumeMap,
        usdtToken,
        'TEST_PACKAGE',
        now
      );

      // 7. Lấy thông tin upline users sau khi tính hoa hồng
      const afterUsers = await queryRunner.manager.find(User, {
        where: { id: In(uplineIds) },
        select: [
          'id', 'username', 'wManagerRank', 'stakingMaxedOut',
          'stakingRankCommission', 'stakingMatchingCommission', 'stakingTotalCommission',
          'stakingIsShareholder', 'stakingTotalEarnings', 'stakingTotalInvestment'
        ]
      });

      // 8. Tính toán sự thay đổi
      const changes = afterUsers.map(afterUser => {
        const beforeUser = beforeCommissions.find(u => u.id === afterUser.id);

        return {
          id: afterUser.id,
          username: afterUser.username,
          wManagerRank: afterUser.wManagerRank,
          stakingMaxedOut: afterUser.stakingMaxedOut,
          stakingRankCommission: {
            before: beforeUser?.stakingRankCommission || 0,
            after: parseFloat(afterUser.stakingRankCommission?.toString() || '0'),
            diff: parseFloat(afterUser.stakingRankCommission?.toString() || '0') - (beforeUser?.stakingRankCommission || 0)
          },
          stakingMatchingCommission: {
            before: beforeUser?.stakingMatchingCommission || 0,
            after: parseFloat(afterUser.stakingMatchingCommission?.toString() || '0'),
            diff: parseFloat(afterUser.stakingMatchingCommission?.toString() || '0') - (beforeUser?.stakingMatchingCommission || 0)
          },
          stakingTotalCommission: {
            before: beforeUser?.stakingTotalCommission || 0,
            after: parseFloat(afterUser.stakingTotalCommission?.toString() || '0'),
            diff: parseFloat(afterUser.stakingTotalCommission?.toString() || '0') - (beforeUser?.stakingTotalCommission || 0)
          },
          stakingTotalEarnings: {
            before: beforeUser?.stakingTotalEarnings || 0,
            after: parseFloat(afterUser.stakingTotalEarnings?.toString() || '0'),
            diff: parseFloat(afterUser.stakingTotalEarnings?.toString() || '0') - (beforeUser?.stakingTotalEarnings || 0)
          },
          stakingIsShareholder: afterUser.stakingIsShareholder
        };
      });

      // 9. Commit transaction
      await queryRunner.commitTransaction();

      return ApiResponseDto.success({
        success: true,
        message: `Calculated rank and shareholder commission for user ${stakerId} with volume ${volume}`,
        transactionCount: transactions.length,
        changes
      });
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  @Post('test/setup-maxed-out')
  @ApiOperation({ summary: 'Setup test data for stakingMaxedOut' })
  async setupMaxedOut(@Body() body: { userId: string, maxedOut: boolean }) {
    const { userId, maxedOut } = body;

    const user = await this.userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException({
        message: {
          en: 'User not found',
        },
        code: 'USER_NOT_FOUND',
      });
    }

    // Cập nhật trạng thái maxedOut
    await this.userRepository.update(userId, {
      stakingMaxedOut: maxedOut
    });

    // Lấy thông tin người dùng sau khi cập nhật
    const updatedUser = await this.userRepository.findOne({
      where: { id: userId },
      select: [
        'id', 'username', 'wManagerRank', 'stakingTotalVolume',
        'stakingTotalInvestment', 'stakingTotalEarnings', 'stakingMaxedOut',
        'stakingDirectActiveCount', 'stakingIsShareholder'
      ]
    });

    return ApiResponseDto.success({
      success: true,
      message: `Updated maxedOut status of user ${userId} to ${maxedOut}`,
      user: updatedUser
    });
  }

  @Post('test/setup-earnings')
  @ApiOperation({ summary: 'Setup test data for total investment and total earnings' })
  async setupEarnings(@Body() body: { userId: string, totalInvestment: number, totalEarnings: number }) {
    const { userId, totalInvestment, totalEarnings } = body;

    const user = await this.userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException({
        message: {
          en: 'User not found',
        },
        code: 'USER_NOT_FOUND',
      });
    }

    // Tính toán trạng thái maxedOut
    const maxEarnings = totalInvestment * 3; // 300% tổng đầu tư
    const isMaxedOut = totalEarnings >= maxEarnings;

    // Cập nhật tổng đầu tư, tổng thu nhập và trạng thái maxedOut
    await this.userRepository.update(userId, {
      stakingTotalInvestment: totalInvestment,
      stakingTotalEarnings: totalEarnings,
      stakingMaxedOut: isMaxedOut
    });

    // Lấy thông tin người dùng sau khi cập nhật
    const updatedUser = await this.userRepository.findOne({
      where: { id: userId },
      select: [
        'id', 'username', 'wManagerRank', 'stakingTotalVolume',
        'stakingTotalInvestment', 'stakingTotalEarnings', 'stakingMaxedOut',
        'stakingDirectActiveCount', 'stakingIsShareholder'
      ]
    });

    return ApiResponseDto.success({
      success: true,
      message: `Updated total investment and total earnings for user ${userId}`,
      user: updatedUser,
      maxEarnings,
      earningsPercentage: totalInvestment > 0 ? (totalEarnings / totalInvestment) * 100 : 0
    });
  }

  @Post('test/setup-wm3')
  @ApiOperation({ summary: 'Setup test data for WM3 rank' })
  async setupWM3() {
    const queryRunner = this.stakingService.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. Lấy thông tin người dùng đặc biệt (cần đã có cấp WM2)
      const specialUserId = '08000bd2-5e23-42dc-9750-7627463ba1e1';
      const specialUser = await queryRunner.manager.findOne(User, {
        where: { id: specialUserId }
      });

      if (!specialUser || specialUser.wManagerRank !== WManagerRank.WM2) {
        throw new BadRequestException({
          message: {
            en: 'Special user has not reached WM2 rank',
          },
          code: 'SPECIAL_USER_NOT_WM2',
        });
      }

      // 2. Lấy thông tin 2 người dùng F1
      const f1Users = await queryRunner.manager.find(User, {
        where: { referredBy: specialUserId }
      });

      if (f1Users.length < 2) {
        throw new BadRequestException({
          message: {
            en: 'Not enough F1 users',
          },
          code: 'NOT_ENOUGH_F1_USERS',
        });
      }

      // 3. Cập nhật cấp bậc WM2 cho 2 người dùng F1
      for (const f1User of f1Users.slice(0, 2)) {
        await queryRunner.manager.update(User, f1User.id, {
          wManagerRank: WManagerRank.WM2
        });
      }

      // 4. Cập nhật stakingTotalVolume cho người dùng đặc biệt
      await queryRunner.manager.update(User, specialUserId, {
        stakingTotalVolume: 40000 // Đủ điều kiện cho WM3
      });

      // 5. Commit transaction
      await queryRunner.commitTransaction();

      // 6. Cập nhật cấp bậc
      await this.stakingService.updateWManagerRanks();

      // 7. Lấy thông tin người dùng sau khi cập nhật
      const updatedUser = await this.userRepository.findOne({
        where: { id: specialUserId }
      });

      return ApiResponseDto.success({
        success: true,
        message: 'Test data for WM3 rank setup successfully',
        user: updatedUser,
        f1Users: await this.userRepository.find({
          where: { referredBy: specialUserId }
        })
      });
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  @Post('test/setup-wm4')
  @ApiOperation({ summary: 'Setup test data for WM4 rank' })
  async setupWM4() {
    const queryRunner = this.stakingService.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. Lấy thông tin người dùng đặc biệt (cần đã có cấp WM3)
      const specialUserId = '08000bd2-5e23-42dc-9750-7627463ba1e1';
      const specialUser = await queryRunner.manager.findOne(User, {
        where: { id: specialUserId }
      });

      if (!specialUser || specialUser.wManagerRank !== WManagerRank.WM3) {
        throw new BadRequestException({
          message: {
            en: 'Special user has not reached WM3 rank',
          },
          code: 'SPECIAL_USER_NOT_WM3',
        });
      }

      // 2. Lấy thông tin 2 người dùng F1
      const f1Users = await queryRunner.manager.find(User, {
        where: { referredBy: specialUserId }
      });

      if (f1Users.length < 2) {
        throw new BadRequestException({
          message: {
            en: 'Not enough F1 users',
          },
          code: 'NOT_ENOUGH_F1_USERS',
        });
      }

      // 3. Cập nhật cấp bậc WM3 cho 2 người dùng F1
      for (const f1User of f1Users.slice(0, 2)) {
        await queryRunner.manager.update(User, f1User.id, {
          wManagerRank: WManagerRank.WM3
        });
      }

      // 4. Cập nhật stakingTotalVolume cho người dùng đặc biệt
      await queryRunner.manager.update(User, specialUserId, {
        stakingTotalVolume: 100000 // Đủ điều kiện cho WM4
      });

      // 5. Commit transaction
      await queryRunner.commitTransaction();

      // 6. Cập nhật cấp bậc
      await this.stakingService.updateWManagerRanks();

      // 7. Lấy thông tin người dùng sau khi cập nhật
      const updatedUser = await this.userRepository.findOne({
        where: { id: specialUserId }
      });

      return ApiResponseDto.success({
        success: true,
        message: 'Test data for WM4 rank setup successfully',
        user: updatedUser,
        f1Users: await this.userRepository.find({
          where: { referredBy: specialUserId }
        })
      });
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  @Post('test/setup-wm5')
  @ApiOperation({ summary: 'Setup test data for WM5 rank' })
  async setupWM5() {
    const queryRunner = this.stakingService.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. Lấy thông tin người dùng đặc biệt (cần đã có cấp WM4)
      const specialUserId = '08000bd2-5e23-42dc-9750-7627463ba1e1';
      const specialUser = await queryRunner.manager.findOne(User, {
        where: { id: specialUserId }
      });

      if (!specialUser || specialUser.wManagerRank !== WManagerRank.WM4) {
        throw new BadRequestException({
          message: {
            en: 'Special user has not reached WM4 rank',
          },
          code: 'SPECIAL_USER_NOT_WM4',
        });
      }

      // 2. Lấy thông tin 2 người dùng F1
      const f1Users = await queryRunner.manager.find(User, {
        where: { referredBy: specialUserId }
      });

      if (f1Users.length < 2) {
        throw new BadRequestException({
          message: {
            en: 'Not enough F1 users',
          },
          code: 'NOT_ENOUGH_F1_USERS',
        });
      }

      // 3. Cập nhật cấp bậc WM4 cho 2 người dùng F1
      for (const f1User of f1Users.slice(0, 2)) {
        await queryRunner.manager.update(User, f1User.id, {
          wManagerRank: WManagerRank.WM4
        });
      }

      // 4. Cập nhật stakingTotalVolume cho người dùng đặc biệt
      await queryRunner.manager.update(User, specialUserId, {
        stakingTotalVolume: 200000 // Đủ điều kiện cho WM5
      });

      // 5. Commit transaction
      await queryRunner.commitTransaction();

      // 6. Cập nhật cấp bậc
      await this.stakingService.updateWManagerRanks();

      // 7. Lấy thông tin người dùng sau khi cập nhật
      const updatedUser = await this.userRepository.findOne({
        where: { id: specialUserId }
      });

      return ApiResponseDto.success({
        success: true,
        message: 'Test data for WM5 rank setup successfully',
        user: updatedUser,
        f1Users: await this.userRepository.find({
          where: { referredBy: specialUserId }
        })
      });
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  @Post('test/setup-shareholder')
  @ApiOperation({ summary: 'Setup test data for shareholders' })
  async setupShareholder() {
    const queryRunner = this.stakingService.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. Lấy thông tin người dùng đặc biệt (cần đã có cấp WM5)
      const specialUserId = '08000bd2-5e23-42dc-9750-7627463ba1e1';
      const specialUser = await queryRunner.manager.findOne(User, {
        where: { id: specialUserId }
      });

      if (!specialUser || specialUser.wManagerRank !== WManagerRank.WM5) {
        throw new BadRequestException({
          message: {
            en: 'Special user has not reached WM5 rank',
          },
          code: 'SPECIAL_USER_NOT_WM5',
        });
      }

      // 2. Lấy thông tin 2 người dùng F1
      const f1Users = await queryRunner.manager.find(User, {
        where: { referredBy: specialUserId }
      });

      if (f1Users.length < 2) {
        throw new BadRequestException({
          message: {
            en: 'Not enough F1 users',
          },
          code: 'NOT_ENOUGH_F1_USERS',
        });
      }

      // 3. Cập nhật cấp bậc WM5 cho 2 người dùng F1
      for (const f1User of f1Users.slice(0, 2)) {
        await queryRunner.manager.update(User, f1User.id, {
          wManagerRank: WManagerRank.WM5
        });
      }

      // 4. Commit transaction
      await queryRunner.commitTransaction();

      // 5. Cập nhật cấp bậc và trạng thái cổ đông
      await this.stakingService.updateWManagerRanks();

      // 6. Lấy thông tin người dùng sau khi cập nhật
      const updatedUser = await this.userRepository.findOne({
        where: { id: specialUserId }
      });

      return ApiResponseDto.success({
        success: true,
        message: 'Test data for shareholders setup successfully',
        user: updatedUser,
        f1Users: await this.userRepository.find({
          where: { referredBy: specialUserId }
        })
      });
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  @Post('test/test-commission')
  @ApiOperation({ summary: 'Test rank commission calculation' })
  async testCommission() {
    const queryRunner = this.stakingService.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. Lấy thông tin token USDT
      const usdtToken = await this.tokenService.findBySymbol('USDT');
      if (!usdtToken) {
        throw new NotFoundException({
          message: {
            en: 'USDT token not found',
          },
          code: 'USDT_TOKEN_NOT_FOUND',
        });
      }

      // 2. Tạo một map lưu trữ khối lượng staking của từng người dùng
      const stakingVolumeMap = new Map<string, number>();

      // 3. Lấy danh sách người dùng có cấp bậc
      const users = await queryRunner.manager.find(User, {
        where: {
          wManagerRank: Not(IsNull())
        },
        select: ['id', 'username', 'wManagerRank', 'stakingTotalVolume', 'stakingRankCommission', 'stakingTotalCommission', 'stakingMatchingCommission']
      });

      // 4. Thiết lập khối lượng staking cho từng người dùng
      for (const user of users) {
        // Giả lập khối lượng staking mới (1000 USDT cho mỗi người dùng)
        stakingVolumeMap.set(user.id, 1000);
      }

      // 5. Lưu trữ thông tin hoa hồng trước khi tính toán
      const beforeCommissions = users.map(user => ({
        id: user.id,
        username: user.username,
        wManagerRank: user.wManagerRank,
        stakingRankCommission: parseFloat(user.stakingRankCommission?.toString() || '0'),
        stakingMatchingCommission: parseFloat(user.stakingMatchingCommission?.toString() || '0'),
        stakingTotalCommission: parseFloat(user.stakingTotalCommission?.toString() || '0')
      }));

      // 6. Tính toán hoa hồng
      const now = getCurrentTime();
      const transactions = await this.stakingRankService.summarizeWManagerCommission(
        queryRunner,
        stakingVolumeMap,
        usdtToken,
        'TEST_PACKAGE',
        now
      );

      // Lưu các giao dịch hoa hồng
      if (transactions && transactions.length > 0) {
        this.logger.log(`Created ${transactions.length} ranking commission transactions in test`);
      }

      // 7. Commit transaction
      await queryRunner.commitTransaction();

      // 8. Lấy thông tin người dùng sau khi tính toán hoa hồng
      const updatedUsers = await this.userRepository.find({
        where: {
          id: In(users.map(user => user.id))
        },
        select: ['id', 'username', 'wManagerRank', 'stakingRankCommission', 'stakingMatchingCommission', 'stakingTotalCommission']
      });

      // 9. Tính toán sự thay đổi hoa hồng
      const commissionChanges = updatedUsers.map(updatedUser => {
        const beforeUser = beforeCommissions.find(u => u.id === updatedUser.id);
        const beforeRankCommission = beforeUser?.stakingRankCommission || 0;
        const beforeMatchingCommission = beforeUser?.stakingMatchingCommission || 0;
        const beforeTotalCommission = beforeUser?.stakingTotalCommission || 0;
        const currentRankCommission = parseFloat(updatedUser.stakingRankCommission?.toString() || '0');
        const currentMatchingCommission = parseFloat(updatedUser.stakingMatchingCommission?.toString() || '0');
        const currentTotalCommission = parseFloat(updatedUser.stakingTotalCommission?.toString() || '0');

        return {
          id: updatedUser.id,
          username: updatedUser.username,
          wManagerRank: updatedUser.wManagerRank,
          beforeRankCommission,
          currentRankCommission,
          rankCommissionChange: currentRankCommission - beforeRankCommission,
          beforeMatchingCommission,
          currentMatchingCommission,
          matchingCommissionChange: currentMatchingCommission - beforeMatchingCommission,
          beforeTotalCommission,
          currentTotalCommission,
          totalCommissionChange: currentTotalCommission - beforeTotalCommission
        };
      });

      return ApiResponseDto.success({
        success: true,
        message: 'Rank commission calculation test completed successfully',
        transactions: transactions.map(tx => ({
          id: tx.id,
          userId: tx.userId,
          amount: tx.amount,
          type: tx.type,
          note: tx.note
        })),
        commissionChanges
      });
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
