import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { BigNumber } from 'bignumber.js';
import { addDays, differenceInDays } from 'date-fns';
import { DataSource, MoreThanOrEqual, Repository } from 'typeorm';
import { TransactionStatus, TransactionType } from '../common/enums/transaction.enum';
import { WManagerRank } from '../common/enums/user.enum';
import { SystemConfigService } from '../common/services/system-config.service';
import { getCurrentTime, getCurrentDateAtMidnight } from '../common/utils/date.utils';
import { TokenService } from '../token/token.service';
import { User } from '../users/entities/user.entity';
import { Transaction } from '../wallet/entities/transaction.entity';
import { Wallet } from '../wallet/entities/wallet.entity';
import { WalletService } from '../wallet/wallet.service';
import { REFERRAL_MILESTONES, STAKING_DURATIONS, STAKING_PACKAGES } from './constants/staking.constants';
import { CreateStakingDto } from './dto/create-staking.dto';
import { StakingInterestHistory } from './entities/staking-interest-history.entity';
import { StakingStatus, StakingTransaction } from './entities/staking-transaction.entity';
import { StakingRankService } from './staking-rank.service';

@Injectable()
export class StakingService {
  private readonly logger = new Logger(StakingService.name);

  constructor(
    @InjectRepository(StakingTransaction)
    private readonly stakingTransactionRepository: Repository<StakingTransaction>,
    @InjectRepository(StakingInterestHistory)
    private readonly stakingInterestHistoryRepository: Repository<StakingInterestHistory>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
    private readonly tokenService: TokenService,
    private readonly walletService: WalletService,
    private readonly systemConfigService: SystemConfigService,
    private readonly stakingRankService: StakingRankService,
    @InjectDataSource() public dataSource: DataSource,
  ) {}

  /**
   * Tạo gói staking mới
   * @param userId ID của người dùng
   * @param createStakingDto Thông tin gói staking
   * @returns Thông tin gói staking đã tạo
   */
  async createStaking(userId: string, createStakingDto: CreateStakingDto): Promise<StakingTransaction> {
    // Sử dụng queryRunner để đảm bảo tính toàn vẹn của giao dịch
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. Lấy thông tin gói và thời hạn từ hằng số
      const packageInfo = STAKING_PACKAGES[createStakingDto.packageCode];
      const durationInfo = Object.values(STAKING_DURATIONS).find(d => d.days === createStakingDto.durationDays);

      if (!packageInfo || !durationInfo) {
        throw new BadRequestException({
          message: {
            en: 'Invalid staking package or duration',
          },
          code: 'INVALID_STAKING_PACKAGE_OR_DURATION',
        });
      }

      // 2. Lấy thông tin người dùng và ví
      const user = await queryRunner.manager.findOne(User, { where: { id: userId } });
      if (!user) {
        throw new NotFoundException({
          message: {
            en: 'User not found',
          },
          code: 'USER_NOT_FOUND',
        });
      }

      const wallet = await queryRunner.manager.findOne(Wallet, { where: { userId } });
      if (!wallet) {
        throw new NotFoundException({
          message: {
            en: 'Wallet not found',
          },
          code: 'WALLET_NOT_FOUND',
        });
      }

      // 3. Lấy thông tin token USDT và WM
      const usdtToken = await this.tokenService.findBySymbol('USDT');
      if (!usdtToken) {
        throw new NotFoundException({
          message: {
            en: 'USDT token not found',
          },
          code: 'USDT_TOKEN_NOT_FOUND',
        });
      }

      const wmToken = await this.tokenService.findBySymbol('WM');
      if (!wmToken) {
        throw new NotFoundException({
          message: {
            en: 'WM token not found',
          },
          code: 'WM_TOKEN_NOT_FOUND',
        });
      }

      // 4. Lấy tỷ giá WM/USDT từ cấu hình hệ thống
      const wmUsdRateConfig = await this.systemConfigService.findByKey('WM_USD_RATE');
      const wmUsdRate = Number(wmUsdRateConfig?.value || 0);

      if (wmUsdRate <= 0) {
        throw new BadRequestException({
          message: {
            en: 'Invalid WM_USD_RATE configuration',
          },
          code: 'INVALID_WM_USD_RATE',
        });
      }

      // 5. Tính toán số lượng WM và USDT cần thanh toán (20% WM, 80% USDT)
      const totalAmount = packageInfo.amount;
      const usdtAmount = totalAmount * 0.8; // 80% giá trị gói bằng USDT
      const wmValueInUsd = totalAmount * 0.2; // 20% giá trị gói quy đổi sang USD
      const wmAmount = wmValueInUsd / wmUsdRate; // Quy đổi sang số lượng WM

      // Kiểm tra số dư WM
      if (wallet.wmBalance < wmAmount) {
        throw new BadRequestException({
          message: {
            en: `Insufficient WM balance.`,
          },
          code: 'INSUFFICIENT_WM_BALANCE',
        });
      }

      // Kiểm tra số dư USDT
      if (wallet.usdtBalance < usdtAmount) {
        throw new BadRequestException({
          message: {
            en: `Insufficient USDT balance.`,
          },
          code: 'INSUFFICIENT_USDT_BALANCE',
        });
      }

      const now = getCurrentTime();

      // 6. Tạo giao dịch và trừ tiền
      let usdtTransaction;
      let wmTransaction;

      // 6.1. Tạo giao dịch USDT (80% giá trị gói)
      usdtTransaction = await queryRunner.manager.save(Transaction, {
        userId,
        walletId: wallet.id,
        tokenId: usdtToken.id,
        type: TransactionType.GLOBAL_MINING_COSHARE,
        amount: -usdtAmount,
        status: TransactionStatus.COMPLETED,
        note: `Global Mining Co-Share ${packageInfo.code} for ${durationInfo.days} days (-${usdtAmount} USDT)`,
        createdAt: now,
        updatedAt: now,
        transactionAt: now
      });

      // 6.2. Cập nhật số dư USDT
      await this.walletService.updateTokenBalanceWithQueryRunner(
        queryRunner,
        wallet,
        usdtToken,
        'usdtBalance',
        usdtTransaction,
        usdtAmount,
        'subtract',
        now
      );

      // 6.3. Tạo giao dịch WM (20% giá trị gói)
      wmTransaction = await queryRunner.manager.save(Transaction, {
        userId,
        walletId: wallet.id,
        tokenId: wmToken.id,
        type: TransactionType.GLOBAL_MINING_COSHARE,
        amount: -wmAmount,
        status: TransactionStatus.COMPLETED,
        note: `Global Mining Co-Share ${packageInfo.code} for ${durationInfo.days} days (-${wmAmount} WM)`,
        createdAt: now,
        updatedAt: now,
        transactionAt: now
      });

      // 6.4. Cập nhật số dư WM
      await this.walletService.updateTokenBalanceWithQueryRunner(
        queryRunner,
        wallet,
        wmToken,
        'wmBalance',
        wmTransaction,
        wmAmount,
        'subtract',
        now
      );

      // 6. Tạo gói staking
      const startDate = now;
      const endDate = addDays(startDate, durationInfo.days);

      // Tính toán số lượng WM thưởng dựa trên tỷ giá hiện tại
      const bonusValueInUsd = packageInfo.amount * (durationInfo.bonusRate / 100);
      const wmBonusAmount = bonusValueInUsd / wmUsdRate;

      const staking = await queryRunner.manager.save(StakingTransaction, {
        userId,
        packageCode: packageInfo.code,
        amount: packageInfo.amount,
        durationDays: durationInfo.days,
        interestRate: durationInfo.interestRate,
        bonusRate: durationInfo.bonusRate,
        startDate,
        endDate,
        status: StakingStatus.ACTIVE,
        transactionId: usdtTransaction.id,
        lastInterestDate: now,
        wmBonusAmount: wmBonusAmount,
        wmUsdRate: wmUsdRate,
        createdAt: now,
        updatedAt: now
      });

      // 7. Cập nhật số F1 active cho người giới thiệu
      await this.updateReferrerActiveCountWithQueryRunner(queryRunner, userId, now);

      // 8. Tính hoa hồng trực tiếp
      await this.calculateDirectCommissionsWithQueryRunner(queryRunner, staking, now);

      // 9. Cập nhật tổng khối lượng staking cho toàn bộ upline
      const stakingVolumeMap = new Map<string, number>();
      stakingVolumeMap.set(userId, packageInfo.amount);
      await this.summarizeStakingTotalVolume(queryRunner, userId, packageInfo.amount);

      // 9.1 Cập nhật tổng đầu tư staking của người dùng
      const currentUser = await queryRunner.manager.findOne(User, { where: { id: userId } });
      const currentTotalInvestment = new BigNumber(currentUser.stakingTotalInvestment?.toString() || '0');
      const newTotalInvestment = currentTotalInvestment.plus(new BigNumber(packageInfo.amount.toString()));

      // Kiểm tra xem người dùng đã max out chưa
      const currentTotalEarnings = new BigNumber(currentUser.stakingTotalEarnings?.toString() || '0');
      const maxEarnings = newTotalInvestment.multipliedBy(3); // 300% tổng đầu tư
      const isMaxedOut = currentTotalEarnings.isGreaterThanOrEqualTo(maxEarnings);

      // Cập nhật tổng đầu tư và trạng thái max out
      await queryRunner.manager.update(User, userId, {
        stakingTotalInvestment: newTotalInvestment.toNumber(),
        stakingMaxedOut: isMaxedOut // Đặt lại trạng thái max out dựa trên tổng đầu tư mới
      });

      // Ghi log nếu trạng thái max out thay đổi
      if (currentUser.stakingMaxedOut && !isMaxedOut) {
        this.logger.log(`User ${userId} is no longer maxed out after new investment. New max earnings: ${maxEarnings.toNumber()} USDT`);
      }

      // 10. Cập nhật cấp bậc
      await this.stakingRankService.summarizeWManagerRank(queryRunner);

      // 11. Cập nhật hoa hồng rank và cổ đông
      const rankingCommissionTransactions = await this.stakingRankService.summarizeWManagerCommission(queryRunner, stakingVolumeMap, usdtToken, staking.packageCode, now);

      // Ghi log số lượng giao dịch hoa hồng đã được tạo
      if (rankingCommissionTransactions && rankingCommissionTransactions.length > 0) {
        this.logger.log(`Created ${rankingCommissionTransactions.length} ranking commission transactions`);
      }

      // 12. Commit transaction
      await queryRunner.commitTransaction();

      return staking;
    } catch (error) {
      // Rollback nếu có lỗi
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      // Giải phóng queryRunner
      await queryRunner.release();
    }
  }

  async createStakingSystemManual(userId: string, createStakingDto: CreateStakingDto): Promise<StakingTransaction> {
    // Sử dụng queryRunner để đảm bảo tính toàn vẹn của giao dịch
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. Lấy thông tin gói và thời hạn từ hằng số
      const packageInfo = STAKING_PACKAGES[createStakingDto.packageCode];
      const durationInfo = Object.values(STAKING_DURATIONS).find(d => d.days === createStakingDto.durationDays);

      if (!packageInfo || !durationInfo) {
        throw new BadRequestException({
          message: {
            en: 'Invalid staking package or duration',
          },
          code: 'INVALID_STAKING_PACKAGE_OR_DURATION',
        });
      }

      // 2. Lấy thông tin người dùng và ví
      const user = await queryRunner.manager.findOne(User, { where: { id: userId } });
      if (!user) {
        throw new NotFoundException({
          message: {
            en: 'User not found',
          },
          code: 'USER_NOT_FOUND',
        });
      }

      const wallet = await queryRunner.manager.findOne(Wallet, { where: { userId } });
      if (!wallet) {
        throw new NotFoundException({
          message: {
            en: 'Wallet not found',
          },
          code: 'WALLET_NOT_FOUND',
        });
      }

      // 3. Lấy thông tin token USDT và WM
      const usdtToken = await this.tokenService.findBySymbol('USDT');
      if (!usdtToken) {
        throw new NotFoundException({
          message: {
            en: 'USDT token not found',
          },
          code: 'USDT_TOKEN_NOT_FOUND',
        });
      }

      const wmToken = await this.tokenService.findBySymbol('WM');
      if (!wmToken) {
        throw new NotFoundException({
          message: {
            en: 'WM token not found',
          },
          code: 'WM_TOKEN_NOT_FOUND',
        });
      }

      // 4. Lấy tỷ giá WM/USDT từ cấu hình hệ thống
      const wmUsdRateConfig = await this.systemConfigService.findByKey('WM_USD_RATE');
      const wmUsdRate = Number(wmUsdRateConfig?.value || 0);

      if (wmUsdRate <= 0) {
        throw new BadRequestException({
          message: {
            en: 'Invalid WM_USD_RATE configuration',
          },
          code: 'INVALID_WM_USD_RATE',
        });
      }

      // 5. Tính toán số lượng WM và USDT cần thanh toán (20% WM, 80% USDT)
      const totalAmount = packageInfo.amount;
      const usdtAmount = totalAmount * 0.8; // 80% giá trị gói bằng USDT
      const wmValueInUsd = totalAmount * 0.2; // 20% giá trị gói quy đổi sang USD
      const wmAmount = wmValueInUsd / wmUsdRate; // Quy đổi sang số lượng WM

      const now = getCurrentTime();

      // 6. Tạo giao dịch và trừ tiền
      let usdtTransaction;
      let wmTransaction;

      // 6.1. Tạo giao dịch USDT (80% giá trị gói)
      usdtTransaction = await queryRunner.manager.save(Transaction, {
        userId,
        walletId: wallet.id,
        tokenId: usdtToken.id,
        type: TransactionType.GLOBAL_MINING_COSHARE,
        amount: -usdtAmount,
        status: TransactionStatus.COMPLETED,
        note: `Global Mining Co-Share ${packageInfo.code} for ${durationInfo.days} days (-${usdtAmount} USDT)`,
        createdAt: now,
        updatedAt: now,
        transactionAt: now
      });

      // 6.3. Tạo giao dịch WM (20% giá trị gói)
      wmTransaction = await queryRunner.manager.save(Transaction, {
        userId,
        walletId: wallet.id,
        tokenId: wmToken.id,
        type: TransactionType.GLOBAL_MINING_COSHARE,
        amount: -wmAmount,
        status: TransactionStatus.COMPLETED,
        note: `Global Mining Co-Share ${packageInfo.code} for ${durationInfo.days} days (-${wmAmount} WM)`,
        createdAt: now,
        updatedAt: now,
        transactionAt: now
      });

      // 6. Tạo gói staking
      const startDate = now;
      const endDate = addDays(startDate, durationInfo.days);

      // Tính toán số lượng WM thưởng dựa trên tỷ giá hiện tại
      const bonusValueInUsd = packageInfo.amount * (durationInfo.bonusRate / 100);
      const wmBonusAmount = bonusValueInUsd / wmUsdRate;

      const staking = await queryRunner.manager.save(StakingTransaction, {
        userId,
        packageCode: packageInfo.code,
        amount: packageInfo.amount,
        durationDays: durationInfo.days,
        interestRate: durationInfo.interestRate,
        bonusRate: durationInfo.bonusRate,
        startDate,
        endDate,
        status: StakingStatus.ACTIVE,
        transactionId: usdtTransaction.id,
        lastInterestDate: now,
        wmBonusAmount: wmBonusAmount,
        wmUsdRate: wmUsdRate,
        createdAt: now,
        updatedAt: now
      });

      // 9.1 Cập nhật tổng đầu tư staking của người dùng
      const currentUser = await queryRunner.manager.findOne(User, { where: { id: userId } });
      const currentTotalInvestment = new BigNumber(currentUser.stakingTotalInvestment?.toString() || '0');
      const newTotalInvestment = currentTotalInvestment.plus(new BigNumber(packageInfo.amount.toString()));

      // Kiểm tra xem người dùng đã max out chưa
      const currentTotalEarnings = new BigNumber(currentUser.stakingTotalEarnings?.toString() || '0');
      const maxEarnings = newTotalInvestment.multipliedBy(3); // 300% tổng đầu tư
      const isMaxedOut = currentTotalEarnings.isGreaterThanOrEqualTo(maxEarnings);

      // Cập nhật tổng đầu tư và trạng thái max out
      await queryRunner.manager.update(User, userId, {
        stakingTotalInvestment: newTotalInvestment.toNumber(),
        stakingMaxedOut: isMaxedOut // Đặt lại trạng thái max out dựa trên tổng đầu tư mới
      });

      // Ghi log nếu trạng thái max out thay đổi
      if (currentUser.stakingMaxedOut && !isMaxedOut) {
        this.logger.log(`User ${userId} is no longer maxed out after new investment. New max earnings: ${maxEarnings.toNumber()} USDT`);
      }

      // 12. Commit transaction
      await queryRunner.commitTransaction();

      return staking;
    } catch (error) {
      // Rollback nếu có lỗi
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      // Giải phóng queryRunner
      await queryRunner.release();
    }
  }

  /**
   * Cập nhật số F1 active cho người giới thiệu
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   * @param userId ID của người dùng
   * @param now Thời gian hiện tại
   */
  async updateReferrerActiveCountWithQueryRunner(queryRunner: any, userId: string, now: Date): Promise<void> {
    const user = await queryRunner.manager.findOne(User, { where: { id: userId } });

    // Kiểm tra xem người dùng có người giới thiệu không
    if (user.referredBy) {
      // Lấy thông tin người giới thiệu
      const referrer = await queryRunner.manager.findOne(User, { where: { id: user.referredBy } });
      if (!referrer) return;

      // Tăng số F1 active cho người giới thiệu
      const currentCount = referrer.stakingDirectActiveCount || 0;
      const newCount = currentCount + 1;

      // Cập nhật trực tiếp giá trị mới thay vì sử dụng raw SQL
      await queryRunner.manager.update(User, user.referredBy, {
        stakingDirectActiveCount: newCount
      });

      this.logger.log(`Increased active F1 count for user ${user.referredBy} from ${currentCount} to ${newCount}`);

      // Kiểm tra mốc thưởng cho người giới thiệu
      await this.checkReferralMilestonesWithQueryRunner(queryRunner, user.referredBy, now);
    }
  }

  /**
   * Tính hoa hồng trực tiếp cho F1, F2, F3
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   * @param staking Thông tin gói staking
   * @param now Thời gian hiện tại
   */
  async calculateDirectCommissionsWithQueryRunner(queryRunner: any, staking: StakingTransaction, now: Date): Promise<void> {
    const user = await queryRunner.manager.findOne(User, { where: { id: staking.userId } });
    const usdtToken = await this.tokenService.findBySymbol('USDT');

    // F1 Commission (5%)
    const isApplyCommission = true;
    if (user.referredBy && isApplyCommission) {
      const referrer = await queryRunner.manager.findOne(User, { where: { id: user.referredBy } });

      if (referrer) {
        const referrerWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: referrer.id } });

        // Tính hoa hồng F1
        const f1Commission = staking.amount * 0.05;

        // Kiểm tra xem người dùng đã max out chưa
        const isMaxedOut = referrer.stakingMaxedOut || false;

        if (!isMaxedOut) {
          // Tạo giao dịch hoa hồng F1
          const f1Transaction = await queryRunner.manager.save(Transaction, {
            userId: referrer.id,
            walletId: referrerWallet.id,
            tokenId: usdtToken.id,
            type: TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_DIRECT,
            amount: f1Commission,
            status: TransactionStatus.COMPLETED,
            note: `Direct commission (F1) from ${user.username} staking ${staking.packageCode} (+${f1Commission} USDT)`,
            createdAt: now,
            updatedAt: now,
            transactionAt: now
          });

          // Cập nhật số dư USDT
          await this.walletService.updateTokenBalanceWithQueryRunner(
            queryRunner,
            referrerWallet,
            usdtToken,
            'usdtBalance',
            f1Transaction,
            f1Commission,
            'add',
            now
          );

          // Cập nhật thống kê hoa hồng
          // Sử dụng BigNumber để xử lý số thập phân chính xác
          const currentDirectCommission = new BigNumber(referrer.stakingDirectCommission?.toString() || '0');
          const currentTotalCommission = new BigNumber(referrer.stakingTotalCommission?.toString() || '0');
          const f1CommissionBN = new BigNumber(f1Commission.toString());

          await queryRunner.manager.update(User, referrer.id, {
            stakingDirectCommission: currentDirectCommission.plus(f1CommissionBN).toNumber(),
            stakingTotalCommission: currentTotalCommission.plus(f1CommissionBN).toNumber()
          });

          // Cập nhật tổng thu nhập staking và kiểm tra max out
          await this.updateUserStakingEarningsWithQueryRunner(queryRunner, referrer.id, f1Commission, now);
        } else {
          // Bỏ qua giao dịch nếu người dùng đã max out
          this.logger.log(`Direct commission for user ${referrer.id} skipped - User has reached max earnings (300% of investment)`);
        }

        // F2 Commission (3%)
        if (referrer.referredBy) {
          const referrerOfReferrer = await queryRunner.manager.findOne(User, { where: { id: referrer.referredBy } });

          if (referrerOfReferrer) {
            const referrerOfReferrerWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: referrerOfReferrer.id } });

            // Tính hoa hồng F2
            const f2Commission = staking.amount * 0.03;

            // Kiểm tra xem người dùng đã max out chưa
            const isMaxedOut = referrerOfReferrer.stakingMaxedOut || false;

            if (!isMaxedOut) {
              // Tạo giao dịch hoa hồng F2
              const f2Transaction = await queryRunner.manager.save(Transaction, {
                userId: referrerOfReferrer.id,
                walletId: referrerOfReferrerWallet.id,
                tokenId: usdtToken.id,
                type: TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_DIRECT,
                amount: f2Commission,
                status: TransactionStatus.COMPLETED,
                note: `Direct commission (F2) from ${user.username} staking ${staking.packageCode} (+${f2Commission} USDT)`,
                createdAt: now,
                updatedAt: now,
                transactionAt: now
              });

              // Cập nhật số dư USDT
              await this.walletService.updateTokenBalanceWithQueryRunner(
                queryRunner,
                referrerOfReferrerWallet,
                usdtToken,
                'usdtBalance',
                f2Transaction,
                f2Commission,
                'add',
                now
              );

              // Cập nhật thống kê hoa hồng
              // Sử dụng BigNumber để xử lý số thập phân chính xác
              const currentDirectCommission = new BigNumber(referrerOfReferrer.stakingDirectCommission?.toString() || '0');
              const currentTotalCommission = new BigNumber(referrerOfReferrer.stakingTotalCommission?.toString() || '0');
              const f2CommissionBN = new BigNumber(f2Commission.toString());

              await queryRunner.manager.update(User, referrerOfReferrer.id, {
                stakingDirectCommission: currentDirectCommission.plus(f2CommissionBN).toNumber(),
                stakingTotalCommission: currentTotalCommission.plus(f2CommissionBN).toNumber()
              });

              // Cập nhật tổng thu nhập staking và kiểm tra max out
              await this.updateUserStakingEarningsWithQueryRunner(queryRunner, referrerOfReferrer.id, f2Commission, now);
            } else {
              // Bỏ qua giao dịch nếu người dùng đã max out
              this.logger.log(`Direct commission for user ${referrerOfReferrer.id} skipped - User has reached max earnings (300% of investment)`);
            }

            // F3 Commission (2%)
            if (referrerOfReferrer.referredBy) {
              const referrerOfReferrerOfReferrer = await queryRunner.manager.findOne(User, { where: { id: referrerOfReferrer.referredBy } });

              if (referrerOfReferrerOfReferrer) {
                const referrerOfReferrerOfReferrerWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: referrerOfReferrerOfReferrer.id } });

                // Tính hoa hồng F3
                const f3Commission = staking.amount * 0.02;

                // Kiểm tra xem người dùng đã max out chưa
                const isMaxedOut = referrerOfReferrerOfReferrer.stakingMaxedOut || false;

                if (!isMaxedOut) {
                  // Tạo giao dịch hoa hồng F3
                  const f3Transaction = await queryRunner.manager.save(Transaction, {
                    userId: referrerOfReferrerOfReferrer.id,
                    walletId: referrerOfReferrerOfReferrerWallet.id,
                    tokenId: usdtToken.id,
                    type: TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_DIRECT,
                    amount: f3Commission,
                    status: TransactionStatus.COMPLETED,
                    note: `Direct commission (F3) from ${user.username} staking ${staking.packageCode} (+${f3Commission} USDT)`,
                    createdAt: now,
                    updatedAt: now,
                    transactionAt: now
                  });

                  // Cập nhật số dư USDT
                  await this.walletService.updateTokenBalanceWithQueryRunner(
                    queryRunner,
                    referrerOfReferrerOfReferrerWallet,
                    usdtToken,
                    'usdtBalance',
                    f3Transaction,
                    f3Commission,
                    'add',
                    now
                  );

                  // Cập nhật thống kê hoa hồng
                  // Sử dụng BigNumber để xử lý số thập phân chính xác
                  const currentDirectCommission = new BigNumber(referrerOfReferrerOfReferrer.stakingDirectCommission?.toString() || '0');
                  const currentTotalCommission = new BigNumber(referrerOfReferrerOfReferrer.stakingTotalCommission?.toString() || '0');
                  const f3CommissionBN = new BigNumber(f3Commission.toString());

                  await queryRunner.manager.update(User, referrerOfReferrerOfReferrer.id, {
                    stakingDirectCommission: currentDirectCommission.plus(f3CommissionBN).toNumber(),
                    stakingTotalCommission: currentTotalCommission.plus(f3CommissionBN).toNumber()
                  });

                  // Cập nhật tổng thu nhập staking và kiểm tra max out
                  await this.updateUserStakingEarningsWithQueryRunner(queryRunner, referrerOfReferrerOfReferrer.id, f3Commission, now);
                } else {
                  // Bỏ qua giao dịch nếu người dùng đã max out
                  this.logger.log(`Direct commission for user ${referrerOfReferrerOfReferrer.id} skipped - User has reached max earnings (300% of investment)`);
                }
              }
            }
          }
        }
      }
    }
  }

  /**
   * Kiểm tra mốc thưởng F1 và trả thưởng nếu đạt mốc mới
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   * @param userId ID của người dùng
   * @param now Thời gian hiện tại
   */
  async checkReferralMilestonesWithQueryRunner(queryRunner: any, userId: string, now: Date): Promise<void> {
    // 1. Lấy thông tin người dùng
    const user = await queryRunner.manager.findOne(User, { where: { id: userId } });
    const directActiveCount = user.stakingDirectActiveCount || 0;
    const rewardedMilestone = user.stakingRewardedMilestone || 0;

    // 2. Lấy token WM
    const wmToken = await this.tokenService.findBySymbol('WM');
    const wallet = await queryRunner.manager.findOne(Wallet, { where: { userId } });

    // 3. Kiểm tra từng mốc theo thứ tự tăng dần
    for (const milestone of REFERRAL_MILESTONES) {
      // Chỉ xét các mốc cao hơn mốc đã thưởng
      if (milestone.milestone > rewardedMilestone && directActiveCount >= milestone.milestone) {
        // Tạo giao dịch thưởng WM
        const transaction = await queryRunner.manager.save(Transaction, {
          userId,
          walletId: wallet.id,
          tokenId: wmToken.id,
          type: TransactionType.GLOBAL_MINING_COSHARE_REFERRAL_MILESTONE,
          amount: milestone.rewardAmount,
          status: TransactionStatus.COMPLETED,
          note: `Referral milestone reward for ${milestone.milestone} active F1 users (+${milestone.rewardAmount} WM)`,
          createdAt: now,
          updatedAt: now,
          transactionAt: now
        });

        // Cập nhật số dư WM
        await this.walletService.updateTokenBalanceWithQueryRunner(
          queryRunner,
          wallet,
          wmToken,
          'wmBalance',
          transaction,
          milestone.rewardAmount,
          'add',
          now
        );

        // Cập nhật mốc thưởng cao nhất đã nhận
        await queryRunner.manager.update(User, userId, {
          stakingRewardedMilestone: milestone.milestone
        });

        this.logger.log(`User ${userId} received milestone reward for ${milestone.milestone} active F1: ${milestone.rewardAmount} WM`);
      }
    }
  }

  /**
   * Cập nhật tổng khối lượng staking cho toàn bộ upline
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   * @param userId ID của người dùng
   * @param amount Số tiền staking
   */
  async summarizeStakingTotalVolume(queryRunner: any, userId: string, amount: number): Promise<void> {
    try {
      // Lấy thông tin người dùng
      const user = await queryRunner.manager.findOne(User, {
        where: { id: userId },
        select: ['id', 'path', 'referredBy', 'isRoot', 'stakingTotalVolume', 'stakingActive']
      });

      if (!user) {
        return;
      }

      // Xử lý upline IDs từ path
      let uplineIds: string[] = [];

      // Nếu là root user, chỉ cập nhật cho chính nó
      if (user.isRoot) {
        const currentStakingTotalVolume = user.stakingTotalVolume || 0;
        await queryRunner.manager.update(User, user.id, {
          stakingTotalVolume: currentStakingTotalVolume + amount
        });
        return;
      }

      // Nếu không có path, thoát
      if (!user.path) {
        return;
      }

      // Lấy danh sách ID của tất cả người giới thiệu từ path
      uplineIds = user.path.split('.').filter((id: string) => id !== user.id);

      if (uplineIds.length === 0) {
        return;
      }

      // Cập nhật stakingTotalVolume cho tất cả người giới thiệu trong một truy vấn
      await queryRunner.manager.query(`
        UPDATE users
        SET stakingTotalVolume = COALESCE(stakingTotalVolume, 0) + ?
        WHERE id IN (?)
      `, [amount, uplineIds]);

      // Cập nhật stakingTotalVolume cho người dùng hiện tại
      const currentStakingTotalVolume = user.stakingTotalVolume || 0;
      await queryRunner.manager.update(User, user.id, {
        stakingTotalVolume: currentStakingTotalVolume + amount
      });

      // Đánh dấu người dùng đã active staking
      if (!user.stakingActive) {
        await queryRunner.manager.update(User, user.id, {
          stakingActive: true
        });
      }

      this.logger.log(`Updated stakingTotalVolume for ${uplineIds.length} upline users of ${user.id}`);
    } catch (error) {
      this.logger.error(`Error updating stakingTotalVolume for uplines of ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Tính lãi hàng ngày cho các gói staking
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT) // Chạy vào nửa đêm theo đề xuất
  async calculateDailyInterest(): Promise<void> {
    this.logger.log('Calculating daily interest for staking packages...');

    try {
      const now = getCurrentTime();

      // 1. Lấy tất cả gói staking đang hoạt động
      const activeStakings = await this.stakingTransactionRepository.find({
        where: {
          status: StakingStatus.ACTIVE,
          endDate: MoreThanOrEqual(now)
        },
        order: {
          id: 'ASC' // Sắp xếp theo ID để tránh deadlock
        }
      });

      this.logger.log(`Found ${activeStakings.length} active staking packages`);

      // 2. Xử lý từng gói staking trong một transaction riêng biệt
      for (const staking of activeStakings) {
        try {
          await this.processStakingWithRetry(staking.id, now);
        } catch (stakingError) {
          // Ghi log lỗi nhưng tiếp tục xử lý các gói staking khác
          this.logger.error(`Error processing staking ${staking.id}: ${stakingError.message}`, stakingError.stack);
        }
      }

      this.logger.log('Daily interest calculation completed');
    } catch (error) {
      this.logger.error(`Error calculating daily interest: ${error.message}`, error.stack);
    }
  }

  /**
   * Xử lý một gói staking với cơ chế retry
   * @param stakingId ID của gói staking
   * @param now Thời gian hiện tại
   * @param retryCount Số lần thử lại (mặc định: 0)
   * @param maxRetries Số lần thử lại tối đa (mặc định: 3)
   */
  private async processStakingWithRetry(stakingId: string, now: Date, retryCount: number = 0, maxRetries: number = 3): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. Lấy thông tin gói staking
      const staking = await queryRunner.manager.findOne(StakingTransaction, { where: { id: stakingId } });
      if (!staking || staking.status !== StakingStatus.ACTIVE) {
        return;
      }

      // 2. Cập nhật trường elapsedDays không sử dụng FOR UPDATE
      await queryRunner.manager.update(StakingTransaction, stakingId, {
        elapsedDays: () => 'elapsedDays + 1',
        updatedAt: now
      });

      // 3. Lấy thông tin gói staking đã cập nhật
      const updatedStaking = await queryRunner.manager.findOne(StakingTransaction, {
        where: { id: stakingId }
      });

      // 4. Tính lãi cho gói staking
      await this.calculateInterestForStakingWithQueryRunner(queryRunner, updatedStaking, now);

      // 5. Kiểm tra xem gói staking đã hết hạn chưa
      if (updatedStaking.elapsedDays >= updatedStaking.durationDays) {
        await this.completeStakingWithQueryRunner(queryRunner, updatedStaking, now);
      }

      // 6. Commit transaction
      await queryRunner.commitTransaction();
    } catch (error) {
      // Rollback nếu có lỗi
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }

      // Thử lại nếu cần
      if (retryCount < maxRetries) {
        // Thử lại sau một khoảng thời gian ngẫu nhiên
        const delay = Math.floor(Math.random() * 1000) + 500; // 500-1500ms
        this.logger.log(`Error processing staking ${stakingId}, retrying in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`);

        await new Promise(resolve => setTimeout(resolve, delay));
        return this.processStakingWithRetry(stakingId, now, retryCount + 1, maxRetries);
      }

      this.logger.error(`Error processing staking ${stakingId}: ${error.message}`, error.stack);
    } finally {
      // Giải phóng queryRunner
      await queryRunner.release();
    }
  }

  /**
   * Hoàn thành một gói staking cụ thể
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   * @param staking Thông tin gói staking
   * @param now Thời gian hiện tại
   */
  async completeStakingWithQueryRunner(queryRunner: any, staking: StakingTransaction, now: Date): Promise<void> {
    // 1. Lấy thông tin người dùng và ví
    const wallet = await queryRunner.manager.findOne(Wallet, { where: { userId: staking.userId } });
    const usdtToken = await this.tokenService.findBySymbol('USDT');
    const wmToken = await this.tokenService.findBySymbol('WM');

    // 2. Lấy thưởng bonus WM đã được tính tại thời điểm tạo gói
    // Sử dụng giá trị wmBonusAmount đã được tính trước đó dựa trên tỷ giá WM/USD tại thời điểm tạo gói
    const bonusAmount = staking.wmBonusAmount || (staking.amount * (staking.bonusRate / 100) / staking.wmUsdRate);

    // 3. Tạo giao dịch trả thưởng bonus
    const bonusTransaction = await queryRunner.manager.save(Transaction, {
      userId: staking.userId,
      walletId: wallet.id,
      tokenId: wmToken.id,
      type: TransactionType.GLOBAL_MINING_COSHARE_BONUS,
      amount: bonusAmount,
      status: TransactionStatus.COMPLETED,
      note: `Global Mining Co-Share package completion bonus ${staking.packageCode} (+${bonusAmount} WM)`,
      createdAt: now,
      updatedAt: now,
      transactionAt: now
    });

    // 4. Cập nhật số dư WM cho bonus
    await this.walletService.updateTokenBalanceWithQueryRunner(
      queryRunner,
      wallet,
      wmToken,
      'wmBalance',
      bonusTransaction,
      bonusAmount,
      'add',
      now
    );

    // 5. Tạo giao dịch trả gốc
    const principalTransaction = await queryRunner.manager.save(Transaction, {
      userId: staking.userId,
      walletId: wallet.id,
      tokenId: usdtToken.id,
      type: TransactionType.GLOBAL_MINING_COSHARE,
      amount: staking.amount,
      status: TransactionStatus.COMPLETED,
      note: `Principal return for Global Mining Co-Share package ${staking.packageCode} (+${staking.amount} USDT)`,
      createdAt: now,
      updatedAt: now,
      transactionAt: now
    });

    // 6. Cập nhật số dư USDT cho tiền gốc
    await this.walletService.updateTokenBalanceWithQueryRunner(
      queryRunner,
      wallet,
      usdtToken,
      'usdtBalance',
      principalTransaction,
      staking.amount,
      'add',
      now
    );

    // 7. Cập nhật thông tin gói staking
    await queryRunner.manager.update(StakingTransaction, staking.id, {
      status: StakingStatus.COMPLETED,
      totalBonus: bonusAmount,
      completionTransactionId: principalTransaction.id,
      updatedAt: now
    });

    this.logger.log(`Completed staking package ${staking.id}: Principal returned ${staking.amount} USDT, bonus ${bonusAmount} WM`);
  }

  /**
   * Tính lãi cho một gói staking cụ thể
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   * @param staking Thông tin gói staking
   * @param now Thời gian hiện tại
   */
  async calculateInterestForStakingWithQueryRunner(queryRunner: any, staking: StakingTransaction, now: Date): Promise<void> {
    // Kiểm tra xem người dùng đã max out chưa
    const user = await queryRunner.manager.findOne(User, { where: { id: staking.userId } });
    if (user.stakingMaxedOut) {
      this.logger.log(`Interest calculation for user ${staking.userId} skipped - User has reached max earnings (300% of investment)`);
      return;
    }

    // 1. Tính số ngày từ lần tính lãi gần nhất
    let daysSinceLastInterest = 1; // Mặc định là 1 ngày khi sử dụng elapsedDays

    // Chỉ kiểm tra ngày thực tế nếu không sử dụng elapsedDays
    if (staking.elapsedDays === 0) {
      const lastInterestDate = staking.lastInterestDate || staking.startDate;
      daysSinceLastInterest = differenceInDays(now, lastInterestDate);

      if (daysSinceLastInterest < 1) {
        return; // Chưa đủ 1 ngày, bỏ qua
      }
    }

    // 2. Tính lãi hàng ngày
    const dailyInterestRate = staking.interestRate / 30; // Lãi suất hàng ngày
    const dailyInterest = staking.amount * (dailyInterestRate / 100);
    let totalInterest = dailyInterest * daysSinceLastInterest;

    // 3. Lấy thông tin ví và token
    const wallet = await queryRunner.manager.findOne(Wallet, { where: { userId: staking.userId } });
    const wmToken = await this.tokenService.findBySymbol('WM');
    const usdtToken = await this.tokenService.findBySymbol('USDT');

    const usersInterestUSDT = [
      'eb71b2a7-bd1c-4bab-b0e6-635cd2a6ceca', //'4116D938'
      '2bc63909-2b89-467d-a059-0365e4612437', //'6FAC35F8'
      '8ed6dd58-32f9-4250-8349-9c891a613d42', //'A709D5C2'
      '121f7165-1ca9-428f-a20d-e41058bbc075', //'D7136FC4'
      '840b67d1-b8c7-4205-98c2-3fe21f87ff13', //'DD528C91'
      '03a0b440-5aea-44e1-88a6-cfb9f03c4fcd', //'EE7DD770'
    ];

    // Các ID trả bằng USDT
    if (usersInterestUSDT.includes(staking.userId)) {
      // 4. Tạo giao dịch trả lãi bằng USDT
      const transaction = await queryRunner.manager.save(Transaction, {
        userId: staking.userId,
        walletId: wallet.id,
        tokenId: usdtToken.id,
        type: TransactionType.GLOBAL_MINING_COSHARE_INTEREST,
        amount: Number(totalInterest),
        status: TransactionStatus.COMPLETED,
        note: `Daily interest for Global Mining Co-Share package ${staking.packageCode} (+${totalInterest} USDT)`,
        createdAt: now,
        updatedAt: now,
        transactionAt: now
      });

      // 5. Cập nhật số dư WM
      await this.walletService.updateTokenBalanceWithQueryRunner(
        queryRunner,
        wallet,
        usdtToken,
        'usdtBalance',
        transaction,
        Number(totalInterest),
        'add',
        now
      );

      // 6. Lưu lịch sử lãi suất
      await queryRunner.manager.save(StakingInterestHistory, {
        stakingId: staking.id,
        userId: staking.userId,
        amount: totalInterest,
        transactionId: transaction.id,
        calculationDate: now,
        createdAt: now,
        updatedAt: now
      });
    } else {
      // 4. Lấy tỷ giá WM/USDT từ cấu hình hệ thống
      const wmUsdRateConfig = await this.systemConfigService.findByKey('WM_USD_RATE');
      let wmUsdRate = Number(wmUsdRateConfig?.value || 0);

      if (wmUsdRate <= 0) {
        wmUsdRate = 0;
      }

      const totalInterestWM = new BigNumber(totalInterest)
              .dividedBy(wmUsdRate);

      // 4. Tạo giao dịch trả lãi bằng WM
      const transaction = await queryRunner.manager.save(Transaction, {
        userId: staking.userId,
        walletId: wallet.id,
        tokenId: wmToken.id,
        type: TransactionType.GLOBAL_MINING_COSHARE_INTEREST,
        amount: Number(totalInterestWM),
        status: TransactionStatus.COMPLETED,
        note: `Daily interest for Global Mining Co-Share package ${staking.packageCode} (+${totalInterestWM} WM)`,
        createdAt: now,
        updatedAt: now,
        transactionAt: now
      });

      // 5. Cập nhật số dư WM
      await this.walletService.updateTokenBalanceWithQueryRunner(
        queryRunner,
        wallet,
        wmToken,
        'wmBalance',
        transaction,
        Number(totalInterestWM),
        'add',
        now
      );

      // 6. Lưu lịch sử lãi suất
      await queryRunner.manager.save(StakingInterestHistory, {
        stakingId: staking.id,
        userId: staking.userId,
        amount: Number(totalInterestWM),
        transactionId: transaction.id,
        calculationDate: now,
        createdAt: now,
        updatedAt: now
      });
    }

    // 7. Cập nhật thông tin gói staking
    // Sử dụng TypeORM để cập nhật totalInterest
    const currentStaking = await queryRunner.manager.findOne(StakingTransaction, { where: { id: staking.id } });

    // Sử dụng BigNumber để xử lý số thập phân chính xác
    const currentTotalInterest = new BigNumber(currentStaking.totalInterest.toString() || '0');
    const newTotalInterest = currentTotalInterest.plus(new BigNumber(totalInterest.toString())).toNumber();

    await queryRunner.manager.update(StakingTransaction, staking.id, {
      totalInterest: newTotalInterest,
      lastInterestDate: now,
      updatedAt: now
    });

    // 8. Cập nhật tổng thu nhập staking của người dùng và kiểm tra max out
    await this.updateUserStakingEarningsWithQueryRunner(queryRunner, staking.userId, totalInterest, now);

    // 9. Kiểm tra xem người dùng đã max out chưa (lấy lại thông tin người dùng vì có thể đã thay đổi sau khi cập nhật thu nhập)
    const updatedUser = await queryRunner.manager.findOne(User, { where: { id: staking.userId } });

    // 10. Tính hoa hồng matching profit (5% lợi nhuận của F1-F10) nếu chưa max out
    const isApplyCommission = false;
    if (!updatedUser.stakingMaxedOut && isApplyCommission) {
      await this.calculateMatchingCommissionsWithQueryRunner(queryRunner, staking, totalInterest, now);
    }

    this.logger.log(`Calculated interest for staking package ${staking.id}: ${totalInterest} USDT`);
  }

  /**
   * Cập nhật tổng thu nhập staking của người dùng và kiểm tra max out
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   * @param userId ID của người dùng
   * @param amount Số tiền thu nhập mới
   * @param now Thời gian hiện tại (không sử dụng trong phương thức này)
   */
  async updateUserStakingEarningsWithQueryRunner(queryRunner: any, userId: string, amount: number, _now: Date): Promise<void> {
    // 1. Lấy thông tin người dùng
    const user = await queryRunner.manager.findOne(User, { where: { id: userId } });

    // 2. Cập nhật tổng thu nhập staking
    const currentTotalEarnings = new BigNumber(user.stakingTotalEarnings?.toString() || '0');
    const newTotalEarnings = currentTotalEarnings.plus(new BigNumber(amount.toString()));

    // 3. Kiểm tra max out (300% tổng đầu tư)
    const totalInvestment = new BigNumber(user.stakingTotalInvestment?.toString() || '0');
    const maxEarnings = totalInvestment.multipliedBy(3); // 300% tổng đầu tư

    // 4. Cập nhật trạng thái max out
    const isMaxedOut = newTotalEarnings.isGreaterThanOrEqualTo(maxEarnings);

    // 5. Cập nhật thông tin người dùng
    await queryRunner.manager.update(User, userId, {
      stakingTotalEarnings: newTotalEarnings.toNumber(),
      stakingMaxedOut: isMaxedOut
    });

    // 6. Ghi log nếu người dùng đã max out
    if (isMaxedOut && !user.stakingMaxedOut) {
      this.logger.log(`User ${userId} has reached max earnings (300% of investment): ${maxEarnings.toNumber()} USDT`);
    }
  }

  /**
   * Tính hoa hồng matching profit
   * @param queryRunner QueryRunner để đảm bảo tính toàn vẹn của giao dịch
   * @param staking Thông tin gói staking
   * @param profit Lợi nhuận
   * @param now Thời gian hiện tại
   */
  async calculateMatchingCommissionsWithQueryRunner(queryRunner: any, staking: StakingTransaction, profit: number, now: Date): Promise<void> {
    const user = await queryRunner.manager.findOne(User, { where: { id: staking.userId } });

    // Tìm người giới thiệu từ F1 đến F10
    let currentReferrerId = user.referredBy;
    let level = 1;

    while (currentReferrerId && level <= 10) {
      const referrer = await queryRunner.manager.findOne(User, { where: { id: currentReferrerId, isKycCompleted: true } });
      if (!referrer) break;

      const referrerWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: referrer.id } });

      // Tính hoa hồng matching (5% lợi nhuận)
      const matchingCommission = profit * 0.05;

      // Kiểm tra xem người dùng đã max out chưa
      const isMaxedOut = referrer.stakingMaxedOut || false;

      if (!isMaxedOut) {
        // Lấy token USDT để trả hoa hồng
        const usdtToken = await this.tokenService.findBySymbol('USDT');

        // Tạo giao dịch hoa hồng matching profit bằng USDT
        const transaction = await queryRunner.manager.save(Transaction, {
          userId: referrer.id,
          walletId: referrerWallet.id,
          tokenId: usdtToken.id,
          type: TransactionType.GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING,
          amount: matchingCommission,
          status: TransactionStatus.COMPLETED,
          note: `Matching profit commission (F${level}) from Global Mining Co-Share interest of ${user.username} (+${matchingCommission} USDT)`,
          createdAt: now,
          updatedAt: now,
          transactionAt: now
        });

        // Cập nhật số dư USDT
        await this.walletService.updateTokenBalanceWithQueryRunner(
          queryRunner,
          referrerWallet,
          usdtToken,
          'usdtBalance',
          transaction,
          matchingCommission,
          'add',
          now
        );

        // Cập nhật thống kê hoa hồng
        // Sử dụng BigNumber để xử lý số thập phân chính xác
        const currentMatchingCommission = new BigNumber(referrer.stakingMatchingCommission?.toString() || '0');
        const currentTotalCommission = new BigNumber(referrer.stakingTotalCommission?.toString() || '0');
        const matchingCommissionBN = new BigNumber(matchingCommission.toString());

        await queryRunner.manager.update(User, referrer.id, {
          stakingMatchingCommission: currentMatchingCommission.plus(matchingCommissionBN).toNumber(),
          stakingTotalCommission: currentTotalCommission.plus(matchingCommissionBN).toNumber()
        });

        // Cập nhật tổng thu nhập staking và kiểm tra max out
        await this.updateUserStakingEarningsWithQueryRunner(queryRunner, referrer.id, matchingCommission, now);
      } else {
        // Ghi log nếu người dùng đã max out
        this.logger.log(`Matching profit commission for user ${referrer.id} skipped - User has reached max earnings`);
      }

      // Chuyển sang người giới thiệu tiếp theo
      currentReferrerId = referrer.referredBy;
      level++;
    }
  }

  /**
   * Lấy danh sách gói staking của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách gói staking
   */
  async findAllByUserId(userId: string): Promise<StakingTransaction[]> {
    return this.stakingTransactionRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' }
    });
  }

  /**
   * Lấy thông tin gói staking cụ thể
   * @param id ID của gói staking
   * @param userId ID của người dùng
   * @returns Thông tin gói staking
   */
  async findOneByIdAndUserId(id: string, userId: string): Promise<StakingTransaction> {
    return this.stakingTransactionRepository.findOne({
      where: { id, userId }
    });
  }

  /**
   * Lấy lịch sử lãi suất của gói staking
   * @param stakingId ID của gói staking
   * @returns Danh sách lịch sử lãi suất
   */
  async findInterestHistoryByStakingId(stakingId: string): Promise<StakingInterestHistory[]> {
    return this.stakingInterestHistoryRepository.find({
      where: { stakingId },
      order: { calculationDate: 'DESC' }
    });
  }

  /**
   * Lấy thống kê staking của người dùng
   * @param userId ID của người dùng
   * @returns Thống kê staking
   */
  async getUserStats(userId: string): Promise<any> {
    const user = await this.userRepository.findOne({ where: { id: userId } });

    // Lấy tổng số gói staking đang hoạt động
    const activeStakingsCount = await this.stakingTransactionRepository.count({
      where: { userId, status: StakingStatus.ACTIVE }
    });

    // Lấy tổng số gói staking đã hoàn thành
    const completedStakingsCount = await this.stakingTransactionRepository.count({
      where: { userId, status: StakingStatus.COMPLETED }
    });

    // Lấy tổng số tiền đang staking
    const totalStakingAmount = await this.stakingTransactionRepository
      .createQueryBuilder('staking')
      .where('staking.userId = :userId AND staking.status = :status', { userId, status: StakingStatus.ACTIVE })
      .select('SUM(staking.amount)', 'total')
      .getRawOne();

    // Lấy tổng lãi đã nhận
    const totalInterestEarned = await this.stakingTransactionRepository
      .createQueryBuilder('staking')
      .where('staking.userId = :userId', { userId })
      .select('SUM(staking.totalInterest)', 'total')
      .getRawOne();

    // Lấy tổng bonus đã nhận
    const totalBonusEarned = await this.stakingTransactionRepository
      .createQueryBuilder('staking')
      .where('staking.userId = :userId AND staking.status = :status', { userId, status: StakingStatus.COMPLETED })
      .select('SUM(staking.totalBonus)', 'total')
      .getRawOne();

    // Lấy lãi nhận được trong ngày hôm nay
    const today = getCurrentTime();
    today.setHours(0, 0, 0, 0);

    const todayInterest = await this.transactionRepository
      .createQueryBuilder('transaction')
      .where('transaction.userId = :userId', { userId })
      .andWhere('transaction.type = :type', { type: TransactionType.GLOBAL_MINING_COSHARE_INTEREST })
      .andWhere('transaction.createdAt >= :today', { today })
      .select('SUM(transaction.amount)', 'total')
      .getRawOne();

    // Lấy tỷ giá WM/USDT từ cấu hình hệ thống
    const wmUsdRateConfig = await this.systemConfigService.findByKey('WM_USD_RATE');
    const wmUsdRate = Number(wmUsdRateConfig?.value || 0);

    // Tính toán các mốc đã đạt được
    const rewardedMilestone = user.stakingRewardedMilestone || 0;
    const directActiveCount = user.stakingDirectActiveCount || 0;

    const referralMilestones = REFERRAL_MILESTONES.map(milestone => ({
      milestone: milestone.milestone,
      rewardAmount: milestone.rewardAmount,
      isRewarded: milestone.milestone <= rewardedMilestone,
      isReachable: milestone.milestone <= directActiveCount
    }));

    // Tính toán thông tin về max out
    const totalInvestment = user.stakingTotalInvestment || 0;
    const totalEarnings = user.stakingTotalEarnings || 0;
    const maxEarnings = totalInvestment * 3; // 300% tổng đầu tư
    const earningsPercentage = totalInvestment > 0 ? (totalEarnings / totalInvestment) * 100 : 0;

    return {
      activeStakingsCount: activeStakingsCount || 0,
      completedStakingsCount: completedStakingsCount || 0,
      totalStakingAmount: totalStakingAmount?.total || 0,
      totalInterestEarned: totalInterestEarned?.total || 0,
      todayInterest: todayInterest?.total || 0,
      totalBonusEarned: totalBonusEarned?.total || 0,
      directActiveCount: user.stakingDirectActiveCount || 0,
      rewardedMilestone: user.stakingRewardedMilestone || 0,
      totalVolume: user.stakingTotalVolume || 0,
      totalCommission: user.stakingTotalCommission || 0,
      directCommission: user.stakingDirectCommission || 0,
      matchingCommission: user.stakingMatchingCommission || 0,
      rankCommission: user.stakingRankCommission || 0,
      rank: user.wManagerRank || null,
      isShareholder: user.stakingIsShareholder || false,
      referralMilestones: referralMilestones,
      // Thông tin về max out
      totalInvestment: totalInvestment,
      totalEarnings: totalEarnings,
      maxEarnings: maxEarnings,
      earningsPercentage: earningsPercentage,
      isMaxedOut: user.stakingMaxedOut || false,
      // Thông tin về tỷ giá WM/USDT
      wmUsdRate: wmUsdRate
    };
  }

  /**
   * Lấy thống kê tổng hợp về staking
   * @returns Thống kê tổng hợp
   */
  async getSummaryStats(): Promise<any> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // 1. Tổng giao dịch staking
    const totalStakingCount = await this.stakingTransactionRepository.count();

    // 2. Tổng giao dịch staking trong ngày
    const todayStakingCount = await this.stakingTransactionRepository.count({
      where: {
        createdAt: MoreThanOrEqual(today)
      }
    });

    // 3. Tổng nhận được (lãi + thưởng)
    const totalEarned = await this.transactionRepository
      .createQueryBuilder('transaction')
      .where('transaction.type IN (:...types)', {
        types: [
          TransactionType.GLOBAL_MINING_COSHARE_INTEREST,
          TransactionType.GLOBAL_MINING_COSHARE_BONUS,
          TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_DIRECT,
          TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_MATCHING,
          TransactionType.GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING,
          TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_RANK,
          TransactionType.GLOBAL_MINING_COSHARE_REFERRAL_MILESTONE,
          TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER
        ]
      })
      .select('SUM(transaction.amount)', 'total')
      .getRawOne();

    // 4. Tổng nhận được trong ngày
    const todayEarned = await this.transactionRepository
      .createQueryBuilder('transaction')
      .where('transaction.type IN (:...types)', {
        types: [
          TransactionType.GLOBAL_MINING_COSHARE_INTEREST,
          TransactionType.GLOBAL_MINING_COSHARE_BONUS,
          TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_DIRECT,
          TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_MATCHING,
          TransactionType.GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING,
          TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_RANK,
          TransactionType.GLOBAL_MINING_COSHARE_REFERRAL_MILESTONE,
          TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER
        ]
      })
      .andWhere('transaction.createdAt >= :today', { today })
      .select('SUM(transaction.amount)', 'total')
      .getRawOne();

    return {
      totalStakingCount: totalStakingCount || 0,
      todayStakingCount: todayStakingCount || 0,
      totalEarned: totalEarned?.total || 0,
      todayEarned: todayEarned?.total || 0
    };
  }

  /**
   * Tăng số ngày đã trôi qua cho gói staking
   * @param stakingId ID của gói staking (nếu không cung cấp sẽ tăng cho tất cả gói đang hoạt động)
   * @param days Số ngày cần tăng (mặc định: 1)
   * @returns Kết quả tăng ngày
   */
  async incrementDays(stakingId?: string, days: number = 1): Promise<any> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Tìm các gói staking cần tăng ngày
      let stakings: StakingTransaction[] = [];

      if (stakingId) {
        // Tăng ngày cho một gói cụ thể
        const staking = await queryRunner.manager.findOne(StakingTransaction, { where: { id: stakingId } });
        if (staking) {
          stakings = [staking];
        }
      } else {
        // Tăng ngày cho tất cả gói đang hoạt động
        stakings = await queryRunner.manager.find(StakingTransaction, {
          where: { status: StakingStatus.ACTIVE }
        });
      }

      if (stakings.length === 0) {
        return { success: false, message: 'No staking packages found to increment days' };
      }

      // Tăng ngày cho các gói staking
      for (const staking of stakings) {
        // Sử dụng raw query để cập nhật trường elapsedDays
        await queryRunner.query(
          `UPDATE staking_transactions SET elapsedDays = elapsedDays + ? WHERE id = ?`,
          [days, staking.id]
        );

        // Cập nhật updatedAt bằng TypeORM
        await queryRunner.manager.update(StakingTransaction, staking.id, {
          updatedAt: getCurrentDateAtMidnight()
        });
      }

      // Tính lãi hàng ngày cho các gói staking
      const now = getCurrentDateAtMidnight();
      for (const staking of stakings) {
        // Lấy thông tin gói staking đã cập nhật
        const updatedStaking = await queryRunner.manager.findOne(StakingTransaction, { where: { id: staking.id } });

        // Tính lãi
        await this.calculateInterestForStakingWithQueryRunner(queryRunner, updatedStaking, now);

        // Kiểm tra xem gói staking đã hết hạn chưa
        if (updatedStaking.elapsedDays >= updatedStaking.durationDays) {
          // Gói staking đã hết hạn, hoàn thành gói
          await this.completeStakingWithQueryRunner(queryRunner, updatedStaking, now);
        }
      }

      // Commit transaction
      await queryRunner.commitTransaction();

      return {
        success: true,
        message: `Incremented ${days} days for ${stakings.length} staking packages`,
        stakingCount: stakings.length
      };
    } catch (error) {
      // Rollback nếu có lỗi
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      this.logger.error(`Error incrementing days: ${error.message}`, error.stack);
      return { success: false, message: `Error incrementing days: ${error.message}` };
    } finally {
      // Giải phóng queryRunner
      await queryRunner.release();
    }
  }

  /**
   * Cập nhật cấp bậc WManager
   * @returns Kết quả cập nhật cấp bậc
   */
  async updateWManagerRanks(): Promise<any> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Cập nhật cấp bậc cho tất cả người dùng
      await this.stakingRankService.summarizeWManagerRank(queryRunner);

      // Cập nhật trực tiếp cho người dùng đặc biệt
      const specialUserId = '08000bd2-5e23-42dc-9750-7627463ba1e1';
      const specialUser = await queryRunner.manager.findOne(User, {
        where: { id: specialUserId },
        select: ['id', 'wManagerRank', 'stakingTotalVolume', 'stakingDirectActiveCount']
      });

      if (specialUser) {
        const volume = parseFloat(specialUser.stakingTotalVolume.toString());
        const activeF1Count = specialUser.stakingDirectActiveCount || 0;

        this.logger.log(`[StakingService][updateWManagerRanks] Người dùng đặc biệt: ${specialUser.id}, volume=${volume}, activeF1Count=${activeF1Count}, currentRank=${specialUser.wManagerRank}`);

        // Kiểm tra điều kiện cấp bậc
        // Chỉ cập nhật nếu người dùng chưa có cấp bậc
        if (volume >= 6000 && activeF1Count >= 2 && !specialUser.wManagerRank) {
          await queryRunner.manager.update(User, specialUserId, {
            wManagerRank: WManagerRank.WM1
          });

          this.logger.log(`[StakingService][updateWManagerRanks] Đã cập nhật cấp bậc cho người dùng đặc biệt: ${specialUser.id} từ ${specialUser.wManagerRank || 'None'} thành ${WManagerRank.WM1}`);
        }
      }

      await queryRunner.commitTransaction();
      return { success: true, message: 'WManager ranks updated successfully' };
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
