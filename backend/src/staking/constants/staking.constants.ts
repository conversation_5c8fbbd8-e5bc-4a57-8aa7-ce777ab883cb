/**
 * <PERSON><PERSON><PERSON> hằng số cho hệ thống Staking
 */

// <PERSON><PERSON><PERSON> gói staking
export const STAKING_PACKAGES = {
  PHOENIX: {
    code: 'PHOENIX',
    name: 'Phoenix Package',
    amount: 100,
    tokenSymbol: 'USDT'
  },
  SPIRIT_TURTLE: {
    code: 'SPIRIT_TURTLE',
    name: 'Spirit Turtle Package',
    amount: 250,
    tokenSymbol: 'USDT'
  },
  UNICORN: {
    code: 'UNICORN',
    name: 'Unicorn Package',
    amount: 500,
    tokenSymbol: 'USDT'
  },
  DRAGON: {
    code: 'DRAGON',
    name: 'Dragon Package',
    amount: 1000,
    tokenSymbol: 'USDT'
  },
  DRAGON_LORD: {
    code: 'DRAGON_LORD',
    name: 'Dragon Lord Package',
    amount: 2500,
    tokenSymbol: 'USDT'
  },
  ETERNAL_DRAGON: {
    code: 'ETERNAL_DRAGON',
    name: 'Eternal Dragon Package',
    amount: 5000,
    tokenSymbol: 'USDT'
  },
  SOVEREIGN_DRAGON: {
    code: 'SOVEREIGN_DRAGON',
    name: 'Sovereign Dragon Package',
    amount: 10000,
    tokenSymbol: 'USDT'
  }
};

// <PERSON><PERSON><PERSON> thời hạn staking
export const STAKING_DURATIONS = {
  DAYS_30: {
    days: 30,
    interestRate: 8,
    bonusRate: 5
  },
  DAYS_60: {
    days: 60,
    interestRate: 12,
    bonusRate: 10
  },
  DAYS_90: {
    days: 90,
    interestRate: 15,
    bonusRate: 15
  },
  DAYS_180: {
    days: 180,
    interestRate: 18,
    bonusRate: 20
  },
  DAYS_360: {
    days: 360,
    interestRate: 21,
    bonusRate: 25
  }
};

// Các mốc thưởng F1
export const REFERRAL_MILESTONES = [
  { milestone: 1, rewardAmount: 5 },
  { milestone: 3, rewardAmount: 15 },
  { milestone: 10, rewardAmount: 50 },
  { milestone: 50, rewardAmount: 300 },
  { milestone: 100, rewardAmount: 1000 }
];

// Các cấp bậc (Rank)
export const STAKING_RANKS = {
  W1: {
    code: 'W1',
    name: 'W1 Rank',
    minDirectActive: 2,
    minVolume: 6000,
    requiredLeftRank: null,
    requiredRightRank: null,
    commissionRate: 1
  },
  W2: {
    code: 'W2',
    name: 'W2 Rank',
    minDirectActive: 2,
    minVolume: 15000,
    requiredLeftRank: 'W1',
    requiredRightRank: 'W1',
    commissionRate: 1.5
  },
  W3: {
    code: 'W3',
    name: 'W3 Rank',
    minDirectActive: 2,
    minVolume: 40000,
    requiredLeftRank: 'W2',
    requiredRightRank: 'W2',
    commissionRate: 2
  },
  W4: {
    code: 'W4',
    name: 'W4 Rank',
    minDirectActive: 2,
    minVolume: 100000,
    requiredLeftRank: 'W3',
    requiredRightRank: 'W3',
    commissionRate: 2.5
  },
  W5: {
    code: 'W5',
    name: 'W5 Rank',
    minDirectActive: 2,
    minVolume: 200000,
    requiredLeftRank: 'W4',
    requiredRightRank: 'W4',
    commissionRate: 3
  }
};
