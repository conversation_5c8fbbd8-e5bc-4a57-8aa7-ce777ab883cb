import { ApiProperty } from '@nestjs/swagger';

export class ApiResponseDto<T> {
  @ApiProperty({ description: 'Status code', example: 200 })
  statusCode: number;

  @ApiProperty({ description: 'Success status', example: true })
  success: boolean;

  @ApiProperty({ description: 'Response message', example: 'Operation successful' })
  message: string;

  @ApiProperty({ description: 'Response data' })
  data: T;

  @ApiProperty({ description: 'Timestamp of the response', example: '2025-03-05T18:30:00.000Z' })
  timestamp: string;

  @ApiProperty({ description: 'Stack trace', example: 'Error: Operation failed' })
  stack?: string;

  @ApiProperty({ description: 'Error code', example: 'USER_UPDATE_PROFILE_INVALID_EMAIL' })
  errorCode?: string;

  @ApiProperty({ description: 'Errors', example: [{ en: 'Email is required' }] })
  errors?: Record<string, string>[];

  constructor(statusCode: number, success: boolean, message: string, data: T, errorCode?: string, errors?: Record<string, string>[]) {
    this.statusCode = statusCode;
    this.success = success;
    this.message = message;
    this.data = data;
    this.timestamp = new Date().toISOString();
    this.errorCode = errorCode;
    this.errors = errors;
  }

  static success<T>(data: T, message = 'Operation successful', statusCode = 200): ApiResponseDto<T> {
    return new ApiResponseDto<T>(statusCode, true, message, data);
  }

  static error<T>(message = 'Operation failed', statusCode = 400, data?: T, errorCode?: string, errors?: Record<string, string>[]): ApiResponseDto<T> {
    return new ApiResponseDto<T>(statusCode, false, message, data, errorCode, errors);
  }
} 