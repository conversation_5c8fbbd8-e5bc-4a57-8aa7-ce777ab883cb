import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiResponseDto } from '../dto/api-response.dto';

@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, ApiResponseDto<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponseDto<T>> {
    return next.handle().pipe(
      map(data => {
        // Nếu response đã là ApiResponseDto, trả về nguyên vẹn
        if (data instanceof ApiResponseDto) {
          return data;
        }
        
        // Nếu không, chuyển đổi thành ApiResponseDto
        return ApiResponseDto.success(data);
      }),
    );
  }
} 