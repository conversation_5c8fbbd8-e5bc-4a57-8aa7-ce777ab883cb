import { isValidEthereumAddress, shortenAddress, generateUsernameFromWallet } from './blockchain.utils';

describe('Blockchain Utils', () => {
  describe('isValidEthereumAddress', () => {
    it('should return true for valid Ethereum addresses', () => {
      expect(isValidEthereumAddress('******************************************')).toBe(true);
      expect(isValidEthereumAddress('******************************************')).toBe(true);
    });

    it('should return false for invalid Ethereum addresses', () => {
      expect(isValidEthereumAddress('')).toBe(false);
      expect(isValidEthereumAddress('0x123')).toBe(false);
      expect(isValidEthereumAddress('1234567890123456789012345678901234567890')).toBe(false);
      expect(isValidEthereumAddress('0xGHIJK67890123456789012345678901234567890')).toBe(false);
    });
  });

  describe('shortenAddress', () => {
    it('should shorten valid Ethereum addresses', () => {
      expect(shortenAddress('******************************************')).toBe('0x1234...7890');
    });

    it('should handle custom length parameters', () => {
      expect(shortenAddress('******************************************', 4, 6)).toBe('0x12...567890');
    });
  });

  describe('generateUsernameFromWallet', () => {
    it('should generate username from wallet address', () => {
      expect(generateUsernameFromWallet('******************************************')).toBe('0x123456');
    });

    it('should handle custom length parameter', () => {
      expect(generateUsernameFromWallet('******************************************', 10)).toBe('0x12345678');
    });
  });
}); 