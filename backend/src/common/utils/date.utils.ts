import moment from 'moment-timezone';

const DEFAULT_TIMEZONE = 'Asia/Dubai';

export const getCurrentTime = (): Date => {
  return moment().tz(DEFAULT_TIMEZONE).toDate();
};

/**
 * L<PERSON>y thời gian hiện tại với giờ cố định 00:00:00
 * @returns Date object với giờ 00:00:00 của ngày hiện tại
 */
export const getCurrentDateAtMidnight = (): Date => {
  return moment().tz(DEFAULT_TIMEZONE).startOf('day').toDate();
};

export const formatToTimezone = (date: Date | string): string => {
  return moment(date).tz(DEFAULT_TIMEZONE).format('DD/MM/YYYY HH:mm:ss');
};

export const parseToTimezone = (dateStr: string): Date => {
  return moment.tz(dateStr, DEFAULT_TIMEZONE).toDate();
};

// Thêm các helper function khác nếu cần
export const addMinutes = (date: Date, minutes: number): Date => {
  return moment(date).tz(DEFAULT_TIMEZONE).add(minutes, 'minutes').toDate();
};

export const subtractDays = (date: Date, days: number): Date => {
  return moment(date).tz(DEFAULT_TIMEZONE).subtract(days, 'days').toDate();
};