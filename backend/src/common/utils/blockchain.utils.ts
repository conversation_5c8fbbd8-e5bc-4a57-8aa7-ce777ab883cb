/**
 * Utility functions for blockchain operations
 */

/**
 * Kiểm tra tính hợp lệ của địa chỉ Ethereum
 * @param address Địa chỉ Ethereum cần kiểm tra
 * @returns true nếu địa chỉ hợp lệ, false nếu không hợp lệ
 */
export function isValidEthereumAddress(address: string): boolean {
  if (!address) return false;
  return /^0x[a-fA-F0-9]{40}$/.test(address);
}

/**
 * Kiểm tra tính hợp lệ của địa chỉ Ethereum với checksum
 * Phương thức này kiểm tra cả checksum theo EIP-55
 * @param address Địa chỉ Ethereum cần kiểm tra
 * @returns true nếu địa chỉ hợp lệ và checksum đúng, false nếu không
 */
export function isValidChecksumAddress(address: string): boolean {
  // Kiểm tra định dạng cơ bản trước
  if (!isValidEthereumAddress(address)) return false;
  
  // <PERSON><PERSON> thể thêm logic kiểm tra checksum theo EIP-55 ở đây
  // Hoặc sử dụng thư viện ethers.js hoặc web3.js
  
  return true; // Giả sử đúng nếu chỉ kiểm tra định dạng
}

/**
 * Rút gọn địa chỉ Ethereum để hiển thị
 * @param address Địa chỉ Ethereum đầy đủ
 * @param startChars Số ký tự giữ lại ở đầu (mặc định: 6)
 * @param endChars Số ký tự giữ lại ở cuối (mặc định: 4)
 * @returns Địa chỉ đã rút gọn, ví dụ: 0x1234...5678
 */
export function shortenAddress(address: string, startChars: number = 6, endChars: number = 4): string {
  if (!address) return '';
  if (!isValidEthereumAddress(address)) return address;
  
  return `${address.substring(0, startChars)}...${address.substring(address.length - endChars)}`;
}

/**
 * Tạo username từ địa chỉ ví
 * @param walletAddress Địa chỉ ví
 * @param length Độ dài username (mặc định: 8)
 * @returns Username được tạo từ địa chỉ ví
 */
export function generateUsernameFromWallet(walletAddress: string, length: number = 8): string {
  if (!walletAddress) return '';
  return walletAddress.substring(0, length);
} 