import { TokenType } from './token.enum';

export enum TransactionType {
  UPDATE_PROFILE_BONUS = 'UPDATE_PROFILE_BONUS', // Bonus khi cập nhật thông tin cá nhân (WM)
  DEPOSIT = 'DEPOSIT', // N<PERSON><PERSON> tiền (USDT)
  WITHDRAWAL = 'WITHDRAWAL', // Rút tiền (USDT)
  TRANSFER = 'TRANSFER', // Chuyển tiền (WM)
  MINING = 'MINING', // <PERSON> công (WM)
  REFERRAL_BONUS = 'REFERRAL_BONUS', // Bonus giới thiệu (WM)
  REFERRAL_BONUS_MINING = 'REFERRAL_BONUS_MINING', // Bonus giới thiệu (WM)
  TASK_REWARD = 'TASK_REWARD', // Bonus nhiệm vụ (WM)
  KYC_VERIFY_BONUS = 'KYC_VERIFY_BONUS', // Bonus khi xác thực <PERSON> (WM)
  KYC_VERIFY_COMMISSION = 'KYC_VERIFY_COMMISSION', // Commission khi xác thực KYC (WM)
  KYC_PURCHASE = 'KYC_PURCHASE', // Mua KYC (USDT)
  LIGHTNING_BOLT_PURCHASE = 'LIGHTNING_BOLT_PURCHASE', // Mua Lightning Bolt (USDT)
  PRIORITY_POSITION_PURCHASE = 'PRIORITY_POSITION_PURCHASE', // Mua Priority Position (USDT)
  NFT_PURCHASE = 'NFT_PURCHASE', // Mua NFT (USDT)
  NFT_WITHDRAWAL = 'NFT_WITHDRAWAL', // Rút NFT (USDT) - Không dùng
  NFT_SALE = 'NFT_SALE', // Bán NFT (USDT)
  SALE_BONUS = 'SALE_BONUS', // Bonus bán NFT (WM)
  DIRECT_COMMISSION = 'DIRECT_COMMISSION', // Commission trực tiếp (USDT)
  RANKING_COMMISSION = 'RANKING_COMMISSION', // Commission hạng (USDT)
  CO_SHAREHOLDER_BONUS = 'CO_SHAREHOLDER_BONUS', // Bonus cổ phần (USDT)
  GAS_FEE = 'GAS_FEE', // Fee gas (WM)
  SCHEDULING_FEE = 'SCHEDULING_FEE', // Fee lịch sử (WM)
  SYSTEM_COMMISSION = 'SYSTEM_COMMISSION', // Commission hệ thống (USDT)

  // Các loại giao dịch cho staking
  GLOBAL_MINING_COSHARE = 'GLOBAL_MINING_COSHARE', // Giao dịch staking (USDT)
  GLOBAL_MINING_COSHARE_INTEREST = 'GLOBAL_MINING_COSHARE_INTEREST', // Lãi staking (USDT)
  GLOBAL_MINING_COSHARE_BONUS = 'GLOBAL_MINING_COSHARE_BONUS', // Thưởng staking (WM)
  GLOBAL_MINING_COSHARE_COMMISSION_DIRECT = 'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', // Hoa hồng trực tiếp staking (USDT)
  GLOBAL_MINING_COSHARE_COMMISSION_MATCHING = 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING', // Hoa hồng matching staking (USDT) - Dùng cho rank
  GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING = 'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', // Hoa hồng matching lợi nhuận (5% lãi cho F1-F20) (USDT)
  GLOBAL_MINING_COSHARE_COMMISSION_RANK = 'GLOBAL_MINING_COSHARE_COMMISSION_RANK', // Hoa hồng rank staking (USDT)
  GLOBAL_MINING_COSHARE_COMMISSION_SAME_LEVEL = 'GLOBAL_MINING_COSHARE_COMMISSION_SAME_LEVEL', // Hoa hồng đồng cấp staking (WM)
  GLOBAL_MINING_COSHARE_REFERRAL_MILESTONE = 'GLOBAL_MINING_COSHARE_REFERRAL_MILESTONE', // Thưởng mốc giới thiệu staking (WM)
  GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER = 'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER' // Hoa hồng cổ đông staking (USDT)
}

export enum TransactionStatus {
  PENDING = 'PENDING', // Chờ xử lý
  COMPLETED = 'COMPLETED', // Hoàn tất
  FAILED = 'FAILED', // Thất bại
  CANCELLED = 'CANCELLED', // Hủy bỏ
  PROCESSING = 'PROCESSING' // Đang xử lý
}

// Metadata cho TransactionType
export const TransactionTypeMetadata = {
  [TransactionType.DEPOSIT]: {
    requiresApproval: true,
    allowedTokenTypes: [TokenType.STABLECOIN, TokenType.NATIVE],
    description: 'Deposit'
  },
  [TransactionType.WITHDRAWAL]: {
    requiresApproval: true,
    allowedTokenTypes: [TokenType.STABLECOIN, TokenType.NATIVE],
    description: 'Withdraw'
  },
  [TransactionType.TRANSFER]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.STABLECOIN, TokenType.NATIVE, TokenType.UTILITY],
    description: 'Transfer'
  },
  [TransactionType.MINING]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.NATIVE],
    description: 'Mining'
  },
  [TransactionType.REFERRAL_BONUS]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.NATIVE],
    description: 'Referral Bonus'
  },
  [TransactionType.TASK_REWARD]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.NATIVE, TokenType.REWARD],
    description: 'Task Reward'
  },
  [TransactionType.LIGHTNING_BOLT_PURCHASE]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.STABLECOIN],
    description: 'Purchase Lightning Bolt'
  },
  [TransactionType.PRIORITY_POSITION_PURCHASE]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.STABLECOIN],
    description: 'Purchase Priority Position'
  },
  [TransactionType.NFT_PURCHASE]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.NFT],
    description: 'Purchase NFT'
  },
  [TransactionType.NFT_WITHDRAWAL]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.NFT],
    description: 'Withdraw NFT'
  },
  [TransactionType.NFT_SALE]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.STABLECOIN],
    description: 'NFT Sale'
  },
  [TransactionType.GAS_FEE]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.NATIVE],
    description: 'Gas Fee'
  },
  [TransactionType.SCHEDULING_FEE]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.STABLECOIN],
    description: 'Scheduling Fee'
  },

  // Metadata cho các loại giao dịch Global Mining Co-Share
  [TransactionType.GLOBAL_MINING_COSHARE]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.STABLECOIN],
    description: 'Global Mining Co-Share'
  },
  [TransactionType.GLOBAL_MINING_COSHARE_INTEREST]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.STABLECOIN],
    description: 'Global Mining Co-Share Interest'
  },
  [TransactionType.GLOBAL_MINING_COSHARE_BONUS]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.NATIVE],
    description: 'Global Mining Co-Share Bonus'
  },
  [TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_DIRECT]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.STABLECOIN],
    description: 'Global Mining Co-Share Direct Commission'
  },
  [TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_MATCHING]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.STABLECOIN],
    description: 'Global Mining Co-Share Matching Commission'
  },
  [TransactionType.GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.STABLECOIN],
    description: 'Global Mining Co-Share Profit Matching Commission'
  },
  [TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_RANK]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.STABLECOIN],
    description: 'Global Mining Co-Share Rank Commission'
  },
  [TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_SAME_LEVEL]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.NATIVE],
    description: 'Global Mining Co-Share Same Level Commission'
  },
  [TransactionType.GLOBAL_MINING_COSHARE_REFERRAL_MILESTONE]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.NATIVE],
    description: 'Global Mining Co-Share Referral Milestone Reward'
  },
  [TransactionType.GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER]: {
    requiresApproval: false,
    allowedTokenTypes: [TokenType.STABLECOIN],
    description: 'Global Mining Co-Share Shareholder Commission'
  }
};

// Helper functions
export function requiresApproval(type: TransactionType): boolean {
  return TransactionTypeMetadata[type].requiresApproval;
}

export function isAllowedTokenType(transactionType: TransactionType, tokenType: TokenType): boolean {
  return TransactionTypeMetadata[transactionType].allowedTokenTypes.includes(tokenType);
}

// Type guards
export function isCompletedTransaction(status: TransactionStatus): boolean {
  return status === TransactionStatus.COMPLETED;
}

export function isPendingTransaction(status: TransactionStatus): boolean {
  return status === TransactionStatus.PENDING;
}

export function isFailedTransaction(status: TransactionStatus): boolean {
  return status === TransactionStatus.FAILED;
}

// Validation decorators
export function IsValidTransactionType(validTypes: TransactionType[]) {
  return function (object: Object, propertyName: string) {
    const validator = {
      validate(value: any) {
        return validTypes.includes(value);
      }
    };
    return validator;
  };
}