/**
 * Enum định nghĩa tất cả mã lỗi trong hệ thống
 * <PERSON><PERSON><PERSON><PERSON> sử dụng để trả về mã lỗi từ backend cho frontend
 * Frontend sẽ sử dụng mã lỗi này để hiển thị thông báo lỗi phù hợp với ngôn ngữ người dùng
 */
export enum ErrorCode {
  // Lỗi chung
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  MAINTENANCE_MODE = 'MAINTENANCE_MODE',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  BAD_REQUEST = 'BAD_REQUEST',
  
  // Lỗi liên quan đến tài khoản và xác thực
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  ACCOUNT_DISABLED = 'ACCOUNT_DISABLED',
  ACCOUNT_NOT_FOUND = 'ACCOUNT_NOT_FOUND',
  INVALID_TOKEN = 'INVALID_TOKEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_SIGNATURE = 'INVALID_SIGNATURE',
  INVALID_WALLET_ADDRESS = 'INVALID_WALLET_ADDRESS',
  WALLET_ALREADY_CONNECTED = 'WALLET_ALREADY_CONNECTED',
  
  // Lỗi liên quan đến số dư và giao dịch
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  INSUFFICIENT_USDT_BALANCE = 'INSUFFICIENT_USDT_BALANCE',
  INSUFFICIENT_WM_BALANCE = 'INSUFFICIENT_WM_BALANCE',
  TRANSACTION_FAILED = 'TRANSACTION_FAILED',
  INVALID_TRANSACTION = 'INVALID_TRANSACTION',
  TRANSACTION_NOT_FOUND = 'TRANSACTION_NOT_FOUND',
  INVALID_AMOUNT = 'INVALID_AMOUNT',
  MINIMUM_AMOUNT_REQUIRED = 'MINIMUM_AMOUNT_REQUIRED',
  MAXIMUM_AMOUNT_EXCEEDED = 'MAXIMUM_AMOUNT_EXCEEDED',
  
  // Lỗi liên quan đến KYC
  KYC_ALREADY_COMPLETED = 'KYC_ALREADY_COMPLETED',
  KYC_REQUIRED = 'KYC_REQUIRED',
  KYC_VERIFICATION_FAILED = 'KYC_VERIFICATION_FAILED',
  KYC_IN_PROGRESS = 'KYC_IN_PROGRESS',
  
  // Lỗi liên quan đến giới thiệu
  INVALID_REFERRAL_CODE = 'INVALID_REFERRAL_CODE',
  SELF_REFERRAL_NOT_ALLOWED = 'SELF_REFERRAL_NOT_ALLOWED',
  REFERRAL_ALREADY_EXISTS = 'REFERRAL_ALREADY_EXISTS',
  
  // Lỗi liên quan đến staking
  PACKAGE_NOT_FOUND = 'PACKAGE_NOT_FOUND',
  INVALID_DURATION = 'INVALID_DURATION',
  MAX_STAKINGS_REACHED = 'MAX_STAKINGS_REACHED',
  STAKING_NOT_FOUND = 'STAKING_NOT_FOUND',
  STAKING_ALREADY_COMPLETED = 'STAKING_ALREADY_COMPLETED',
  STAKING_ALREADY_ACTIVE = 'STAKING_ALREADY_ACTIVE',
  CREATE_STAKING_FAILED = 'CREATE_STAKING_FAILED',
  
  // Lỗi liên quan đến NFT
  NFT_NOT_FOUND = 'NFT_NOT_FOUND',
  NFT_ALREADY_OWNED = 'NFT_ALREADY_OWNED',
  NFT_NOT_OWNED = 'NFT_NOT_OWNED',
  NFT_ALREADY_LISTED = 'NFT_ALREADY_LISTED',
  NFT_NOT_LISTED = 'NFT_NOT_LISTED',
  NFT_PURCHASE_FAILED = 'NFT_PURCHASE_FAILED',
  
  // Lỗi liên quan đến Flash Buy
  SESSION_NOT_FOUND = 'SESSION_NOT_FOUND',
  SESSION_ALREADY_STARTED = 'SESSION_ALREADY_STARTED',
  SESSION_ALREADY_ENDED = 'SESSION_ALREADY_ENDED',
  SESSION_NOT_ACTIVE = 'SESSION_NOT_ACTIVE',
  SESSION_FULL = 'SESSION_FULL',
  ALREADY_SCHEDULED = 'ALREADY_SCHEDULED',
  
  // Lỗi liên quan đến ticket support
  INVALID_CONTENT = 'INVALID_CONTENT',
  INVALID_TX_HASH = 'INVALID_TX_HASH',
  TOO_MANY_TICKETS = 'TOO_MANY_TICKETS',
  TICKET_NOT_FOUND = 'TICKET_NOT_FOUND',
  TICKET_ALREADY_RESOLVED = 'TICKET_ALREADY_RESOLVED',
  
  // Lỗi liên quan đến Lightning Boost và Priority Position
  ALREADY_PURCHASED = 'ALREADY_PURCHASED',
  PURCHASE_FAILED = 'PURCHASE_FAILED',
  
  // Lỗi liên quan đến hạn chế và giới hạn
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  DAILY_LIMIT_EXCEEDED = 'DAILY_LIMIT_EXCEEDED',
  MONTHLY_LIMIT_EXCEEDED = 'MONTHLY_LIMIT_EXCEEDED',
  
  // Lỗi liên quan đến dữ liệu
  DUPLICATE_ENTRY = 'DUPLICATE_ENTRY',
  DATA_INTEGRITY_ERROR = 'DATA_INTEGRITY_ERROR',
  INVALID_DATA_FORMAT = 'INVALID_DATA_FORMAT',
}
