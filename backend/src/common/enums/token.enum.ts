import { ApiProperty } from '@nestjs/swagger';

export enum TokenType {
  NATIVE = 'NATIVE',      // Native token của platform (WM)
  STABLECOIN = 'STABLECOIN', // Stablecoin (USDT, USDC...)
  UTILITY = 'UTILITY',    // Utility token
  GOVERNANCE = 'GOVERNANCE', // Governance token
  REWARD = 'REWARD',      // Reward token
  NFT = 'NFT'            // NFT token
}

// Metadata cho TokenType
export const TokenTypeMetadata = {
  [TokenType.NATIVE]: {
    canMine: true,
    canTransfer: true,
    requiresKYC: false,
    description: 'Platform native token'
  },
  [TokenType.STABLECOIN]: {
    canMine: false,
    canTransfer: true,
    requiresKYC: true,
    description: 'Stable value token'
  },
  [TokenType.UTILITY]: {
    canMine: false,
    canTransfer: true,
    requiresKYC: false,
    description: 'Utility token for platform features'
  },
  [TokenType.GOVERNANCE]: {
    canMine: false,
    canTransfer: true,
    requiresKYC: true,
    description: 'Governance token for platform decisions'
  },
  [TokenType.REWARD]: {
    canMine: false,
    canTransfer: false,
    requiresKYC: false,
    description: 'Reward token for platform activities'
  }
};

// Helper functions
export function isTransferable(type: TokenType): boolean {
  return TokenTypeMetadata[type].canTransfer;
}

export function requiresKYC(type: TokenType): boolean {
  return TokenTypeMetadata[type].requiresKYC;
}

export function canMine(type: TokenType): boolean {
  return TokenTypeMetadata[type].canMine;
}

// Type guards
export function isNativeToken(type: TokenType): boolean {
  return type === TokenType.NATIVE;
}

export function isStablecoin(type: TokenType): boolean {
  return type === TokenType.STABLECOIN;
} 