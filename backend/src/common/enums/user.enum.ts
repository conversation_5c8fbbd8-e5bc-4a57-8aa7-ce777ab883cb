import { ApiProperty } from '@nestjs/swagger';

export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER'
}

export enum UserRank {
  BRONZE = 'BRONZE',
  SILVER = 'SILVER',
  GOLD = 'GOLD',
  PLATINUM = 'PLATINUM',
  DIAMOND = 'DIAMOND'
}

export enum WManagerRank {
  WM1 = 'WM1',
  WM2 = 'WM2',
  WM3 = 'WM3',
  WM4 = 'WM4',
  WM5 = 'WM5'
}

// Metadata cho UserRank
export const UserRankMetadata = {
  [UserRank.BRONZE]: { 
    minMined: 0, 
    multiplier: 1,
    description: 'Mined <= 50 WM'
  },
  [UserRank.SILVER]: { 
    minMined: 50, 
    multiplier: 1.2,
    description: 'Mined > 50 WM'
  },
  [UserRank.GOLD]: { 
    minMined: 100, 
    multiplier: 1.5,
    description: 'Mined > 100 WM'
  },
  [UserRank.PLATINUM]: { 
    minMined: 1000, 
    multiplier: 2,
    description: 'Mined > 1000 WM'
  },
  [UserRank.DIAMOND]: { 
    minMined: 10000, 
    multiplier: 3,
    description: 'Mined > 10000 WM'
  }
};

// Helper functions
export function getNextRank(currentRank: UserRank): UserRank | null {
  const ranks = Object.values(UserRank);
  const currentIndex = ranks.indexOf(currentRank);
  return currentIndex < ranks.length - 1 ? ranks[currentIndex + 1] : null;
}

export function getRankByMinedAmount(minedAmount: number): UserRank {
  const ranks = Object.entries(UserRankMetadata);
  const rank = ranks.reverse().find(([_, meta]) => minedAmount >= meta.minMined);
  return rank ? rank[0] as UserRank : UserRank.BRONZE;
}

// Type guards
export function isAdmin(role: UserRole): boolean {
  return role === UserRole.ADMIN;
}

export function isMaxRank(rank: UserRank): boolean {
  return rank === UserRank.DIAMOND;
} 