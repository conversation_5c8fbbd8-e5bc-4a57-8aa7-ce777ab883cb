import { Controller, Get, Put, Body, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SystemConfigService } from '../services/system-config.service';
import { SystemConfig } from '../entities/system-config.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../enums/user.enum';
import { ApiResponseDto } from '../dto/api-response.dto';

@ApiTags('system')
@Controller('system')
export class SystemConfigController {
  constructor(private readonly systemConfigService: SystemConfigService) {}

  @Get('config')
  @ApiOperation({ summary: 'Get all system configs' })
  @ApiResponse({ status: 200, description: 'Return all system configs', type: ApiResponseDto })
  async findAll(): Promise<ApiResponseDto<SystemConfig[]>> {
    const configs = await Promise.all([
      this.systemConfigService.findByKey('MINING_BASE_REWARD'),
      this.systemConfigService.findByKey('MINING_COOLDOWN'),
      this.systemConfigService.findByKey('MINING_ALLOCATION'),
      this.systemConfigService.findByKey('DAILY_MINING_LIMIT'),
      this.systemConfigService.findByKey('LIGHTNING_BOLT_PRICE'),
      this.systemConfigService.findByKey('LIGHTNING_BOLT_REWARD_MULTIPLIER'),
      this.systemConfigService.findByKey('REFERRAL_BONUS_PERCENTAGE'),
      this.systemConfigService.findByKey('MIN_WITHDRAWAL'),
      this.systemConfigService.findByKey('WITHDRAWAL_FEE'),
      this.systemConfigService.findByKey('TOTAL_LIGHTNING_BOLTS_SOLD'),
      this.systemConfigService.findByKey('TOTAL_WM_MINED'),
      this.systemConfigService.findByKey('CURRENT_MINING_DIFFICULTY'),
      this.systemConfigService.findByKey('LIGHTNING_BOLT_PRICE_TIER_RANK_1'),
      this.systemConfigService.findByKey('LIGHTNING_BOLT_PRICE_TIER_RANK_2'),
      this.systemConfigService.findByKey('LIGHTNING_BOLT_PRICE_TIER_RANK_3'),
      this.systemConfigService.findByKey('LIGHTNING_BOLT_PRICE_TIER_RANK_4'),
      this.systemConfigService.findByKey('LIGHTNING_BOLT_PRICE_TIER_1'),
      this.systemConfigService.findByKey('LIGHTNING_BOLT_PRICE_TIER_2'),
      this.systemConfigService.findByKey('LIGHTNING_BOLT_PRICE_TIER_3'),
      this.systemConfigService.findByKey('LIGHTNING_BOLT_PRICE_TIER_4'),
      this.systemConfigService.findByKey('LIGHTNING_BOLT_PRICE_TIER_5'),
      this.systemConfigService.findByKey('PRIORITY_POSITION_PRICE'),
      this.systemConfigService.findByKey('SYSTEM_PRIMARY_WALLET_ADDRESS'),
      this.systemConfigService.findByKey('TELEGRAM_CHANNEL_ID'),
      this.systemConfigService.findByKey('CREDITS_FOR_NEW_USER'),
      this.systemConfigService.findByKey('NFT_PHOENIX_PRICE'),
      this.systemConfigService.findByKey('NFT_SPIRIT_TURTLE_PRICE'),
      this.systemConfigService.findByKey('NFT_UNICORN_PRICE'),
      this.systemConfigService.findByKey('NFT_DRAGON_PRICE'),
      this.systemConfigService.findByKey('NFT_PHOENIX_GAS_FEE'),
      this.systemConfigService.findByKey('NFT_SPIRIT_TURTLE_GAS_FEE'),
      this.systemConfigService.findByKey('NFT_UNICORN_GAS_FEE'),
      this.systemConfigService.findByKey('NFT_DRAGON_GAS_FEE'),
      this.systemConfigService.findByKey('NFT_PHOENIX_QUANTITY'),
      this.systemConfigService.findByKey('NFT_SPIRIT_TURTLE_QUANTITY'),
      this.systemConfigService.findByKey('NFT_UNICORN_QUANTITY'),
      this.systemConfigService.findByKey('NFT_DRAGON_QUANTITY'),
      this.systemConfigService.findByKey('NFT_GROWTH_RATE'),
      this.systemConfigService.findByKey('NFT_SPLIT_THRESHOLD'),
      this.systemConfigService.findByKey('NFT_SPLIT_COUNT'),
      this.systemConfigService.findByKey('NFT_WITHDRAWAL_FEE'),
    ]);
    return ApiResponseDto.success(configs.filter(Boolean), 'System configs retrieved successfully');
  }

  @Get('config/:key')
  @ApiOperation({ summary: 'Get system config by key' })
  @ApiResponse({ status: 200, description: 'Return system config by key', type: ApiResponseDto })
  async findByKey(@Param('key') key: string): Promise<ApiResponseDto<SystemConfig>> {
    const config = await this.systemConfigService.findByKey(key);
    return ApiResponseDto.success(config, 'System config retrieved successfully');
  }

  @Put('config/:key')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update system config by key (Admin only)' })
  @ApiResponse({ status: 200, description: 'System config updated successfully', type: ApiResponseDto })
  async update(
    @Param('key') key: string,
    @Body('value') value: string
  ): Promise<ApiResponseDto<SystemConfig>> {
    const config = await this.systemConfigService.update(key, value);
    return ApiResponseDto.success(config, 'System config updated successfully');
  }
} 