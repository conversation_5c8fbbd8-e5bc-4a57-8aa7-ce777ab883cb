import { <PERSON><PERSON><PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('system_config')
export class SystemConfig {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'Config ID' })
  id: string;

  @Column({ type: 'varchar', unique: true })
  @ApiProperty({ description: 'Config key' })
  key: string;

  @Column({ type: 'text' })
  @ApiProperty({ description: 'Config value' })
  value: string;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({ description: 'Config description' })
  description: string;

  @CreateDateColumn()
  @ApiProperty({ description: 'Config creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Config last update timestamp' })
  updatedAt: Date;
} 