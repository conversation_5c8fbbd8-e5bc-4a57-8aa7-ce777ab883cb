import { Module } from '@nestjs/common';
import { SystemConfigService } from './services/system-config.service';
import { SystemConfigController } from './controllers/system-config.controller';
import { DatabaseModule } from '../database/database.module';
import { SharedModule } from '../shared/shared.module';
import { DateService } from './services/date.service';
import { ConfigModule } from '@nestjs/config';
import { Web3SocketService } from './services/web3-socket.service';
import { TelegramBotService } from './services/telegram-bot.service';
import { SupportTelegramBotService } from './services/support-telegram-bot.service';

@Module({
  imports: [
    DatabaseModule,
    SharedModule,
    ConfigModule,
  ],
  controllers: [SystemConfigController],
  providers: [
    SystemConfigService,
    DateService,
    Web3SocketService,
    TelegramBotService,
    SupportTelegramBotService,
  ],
  exports: [
    SystemConfigService,
    DateService,
    Web3SocketService,
    TelegramBotService,
    SupportTelegramBotService,
  ],
})
export class CommonModule {}