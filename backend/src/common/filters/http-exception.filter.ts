import { ExceptionFilter, Catch, ArgumentsHost, HttpException, Logger, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiResponseDto } from '../dto/api-response.dto';
import { ConfigService } from '@nestjs/config';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);
  private readonly isProduction: boolean;

  constructor(private configService: ConfigService) {
    this.isProduction = configService.get('NODE_ENV') === 'production';
  }

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse();

    // Mặc định cho thông điệp lỗi
    let message = 'Operation failed';
    let errorCode = 'UNKNOWN_ERROR';
    let data = null;
    let errors = null;

    // Xử lý thông điệp lỗi dựa trên loại của exceptionResponse
    if (typeof exceptionResponse === 'string') {
      message = exceptionResponse;
    } else if (typeof exceptionResponse === 'object') {
      const resp = exceptionResponse as any;
      
      // Xử lý thông điệp
      if (resp.message) {
        if (typeof resp.message === 'string') {
          message = resp.message;
        } else if (resp.message.en) {
          message = resp.message; // Giữ nguyên đối tượng đa ngôn ngữ
        } else if (Array.isArray(resp.message)) {
          // Xử lý lỗi validation từ class-validator
          message = 'Some fields are invalid';
          errors = resp.message;
        }
      }
      
      // Xử lý mã lỗi
      if (resp.code) {
        errorCode = resp.code;
      } else if (resp.error) {
        errorCode = resp.error.toUpperCase().replace(/\s+/g, '_');
      }
      
      // Xử lý lỗi validation
      if (resp.errors) {
        errors = resp.errors;
      }
    }

    // Tạo đối tượng phản hồi lỗi
    const errorResponse = ApiResponseDto.error(
      message, 
      status, 
      data, 
      errorCode, 
      errors
    );

    // Thêm stack trace trong môi trường development
    if (!this.isProduction && exception.stack) {
      errorResponse.stack = exception.stack;
    }

    // Ghi log với thông tin chi tiết hơn
    const logContext = {
      url: request.url,
      method: request.method,
      statusCode: status,
      errorCode,
      body: this.sanitizeRequestBody(request.body),
      headers: this.sanitizeHeaders(request.headers),
      timestamp: new Date().toISOString(),
      ip: request.ip || request.connection.remoteAddress,
    };

    // Phân loại log dựa trên mức độ nghiêm trọng
    if (status >= HttpStatus.INTERNAL_SERVER_ERROR) {
      this.logger.error(`[HttpExceptionFilter] Server error: ${request.method} ${request.url}`, {
        ...logContext,
        stack: exception.stack,
      });
    } else if (status >= HttpStatus.BAD_REQUEST) {
      this.logger.warn(`[HttpExceptionFilter] Client error: ${request.method} ${request.url}`, logContext);
    } else {
      this.logger.log(`[HttpExceptionFilter] Other error: ${request.method} ${request.url}`, logContext);
    }
    
    // Gửi phản hồi về client
    response
      .status(status)
      .json(errorResponse);
  }

  // Loại bỏ thông tin nhạy cảm từ request body
  private sanitizeRequestBody(body: any): any {
    if (!body) return {};
    
    const sanitized = { ...body };
    const sensitiveFields = ['password', 'token', 'secret', 'privateKey', 'authorization'];
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '***REDACTED***';
      }
    }
    
    return sanitized;
  }

  // Loại bỏ thông tin nhạy cảm từ headers
  private sanitizeHeaders(headers: any): any {
    if (!headers) return {};
    
    const sanitized = { ...headers };
    const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];
    
    for (const header of sensitiveHeaders) {
      if (sanitized[header]) {
        sanitized[header] = '***REDACTED***';
      }
    }
    
    return sanitized;
  }
}