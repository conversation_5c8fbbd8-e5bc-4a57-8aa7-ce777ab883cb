import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisService } from './services/redis.service';
import redisConfig from '../config/redis.config';
import { BullModule } from '@nestjs/bull';

@Global()
@Module({
  imports: [
    ConfigModule.forFeature(redisConfig),
    BullModule.registerQueue({
      name: 'session-queue',
    }),
  ],
  providers: [
    {
      provide: RedisService,
      useFactory: (configService: ConfigService) => {
        return new RedisService(configService);
      },
      inject: [ConfigService],
    },
  ],
  exports: [RedisService, BullModule],
})
export class RedisModule {} 