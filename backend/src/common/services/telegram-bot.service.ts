import { Injectable, Logger, OnApplicationShutdown, OnModuleInit } from '@nestjs/common';
import TelegramBot from 'node-telegram-bot-api';
import { ConfigService } from '@nestjs/config';
@Injectable()
export class TelegramBotService implements OnApplicationShutdown, OnModuleInit {
  private readonly bot: TelegramBot;
  private readonly logger = new Logger(TelegramBotService.name);
  private callbackHandlers: ((query: TelegramBot.CallbackQuery) => Promise<void>)[] = [];

  constructor(private readonly configService: ConfigService) {
    let telegramBotToken = this.configService.get<string>('TELEGRAM_BOT_TOKEN');
    const NODE_ENV = this.configService.get<string>('NODE_ENV');
    if (NODE_ENV === 'development') {
      telegramBotToken = '**********************************************';
    }
    if (!telegramBotToken) {
      throw new Error('Token telegram bot is not defined in environment variables');
    }
    this.bot = new TelegramBot(telegramBotToken, { polling: true });
    this.logger.log('TelegramBot đã khởi tạo với polling');
  }

  async onModuleInit() {
    this.bot.on('callback_query', async (query) => {
      try {
        for (const handler of this.callbackHandlers) {
          await handler(query);
        }
        await this.bot.answerCallbackQuery(query.id);
      } catch (error) {
        this.logger.error(`Lỗi phần callback query telegram: ${error.message}`, error.stack);
        await this.bot.answerCallbackQuery(query.id, {
          text: `Error: ${error.message}`,
          show_alert: true,
        });
      }
    });
  }

  registerCallbackQueryHandler(handler: (query: TelegramBot.CallbackQuery) => Promise<void>) {
    this.callbackHandlers.push(handler);
  }

  async sendMessage(chatId: string, text: string, options: TelegramBot.SendMessageOptions = {}) {
    try {
      return await this.bot.sendMessage(chatId, text, options);
    } catch (error) {
      this.logger.error(`Lỗi gửi tin nhắn: ${error.message}`, error.stack);
      throw error;
    }
  }

  async editMessageText(text: string, options: TelegramBot.EditMessageTextOptions) {
    try {
      return await this.bot.editMessageText(text, options);
    } catch (error) {
      if (error.message.includes('message is not modified')) {
        return;
      }
      this.logger.error(`Lỗi chỉnh sửa tin nhắn: ${error.message}`, error.stack);
      throw error;
    }
  }

  async onApplicationShutdown() {
    this.logger.log('Dừng telegram bot polling');
    await this.bot.stopPolling();
  }
}