import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class SupportTelegramBotService {
  private readonly logger = new Logger(SupportTelegramBotService.name);

  /**
   * Send message to support Telegram chat
   * @param message Message to send
   * @returns Promise<boolean> Success status
   */
  async sendSupportMessage(message: string): Promise<boolean> {
    try {
      const botToken = process.env.SUPPORT_TELEGRAM_BOT_TOKEN;
      const chatId = process.env.TELEGRAM_SUPPORT_CHAT_ID;

      if (!botToken || !chatId) {
        this.logger.warn('Support Telegram bot token or chat ID not configured');
        return false;
      }

      const url = `https://api.telegram.org/bot${botToken}/sendMessage`;
      await axios.post(url, {
        chat_id: chatId,
        text: message,
        parse_mode: 'Markdown',
        disable_web_page_preview: true
      });
      
      return true;
    } catch (error) {
      this.logger.error(`Failed to send support Telegram message: ${error.message}`);
      return false;
    }
  }
}
