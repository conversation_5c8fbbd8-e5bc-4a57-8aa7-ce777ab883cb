import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ethers } from 'ethers';

const ERC20_ABI = [
    'function transfer(address to, uint256 value) public returns (bool)',
    'function balanceOf(address owner) public view returns (uint256)',
    'function decimals() public view returns (uint8)',
];

const USDT_CONTRACT_ADDRESS = '******************************************';
const WALLET_PRIVATE_KEY = 'aa48cc1597dca6219d97ddb48d4d57caea590205bfe4473a0266be85ac7130a2';

@Injectable()
export class Web3Service {
    private readonly logger = new Logger(Web3Service.name);
    private readonly provider: ethers.JsonRpcProvider;
    private readonly wallet: ethers.Wallet;
    private readonly walletAddress: string;

    constructor(private configService: ConfigService) {
        const rpcUrl = this.configService.get<string>('BSC_RPC_URL', 'https://bsc-dataseed.binance.org/');
        this.provider = new ethers.JsonRpcProvider(rpcUrl);

        const privateKey = WALLET_PRIVATE_KEY;//this.configService.get<string>('WALLET_PRIVATE_KEY');
        if (!privateKey) {
            throw new Error('WALLET_PRIVATE_KEY is not defined in .env');
        }

        this.wallet = new ethers.Wallet(privateKey, this.provider);
        this.walletAddress = this.wallet.address;

        this.logger.log(`Web3Service initialized with wallet: ${this.walletAddress} on BSC Mainnet`);
    }

    /**
     * Chuyển USDT từ ví quản lý sang địa chỉ khác
     * @param toAddress Địa chỉ nhận
     * @param amount Số USDT (dạng string, ví dụ: '100')
     * @returns Transaction hash
     */
    async transferUSDT(toAddress: string, amount: string): Promise<string> {
        return this.transferToken(USDT_CONTRACT_ADDRESS, toAddress, amount);
    }

    /**
     * Rút USDT từ ví quản lý sang địa chỉ khác
     * @param toAddress Địa chỉ rút tiền đến
     * @param amount Số USDT (dạng string)
     * @returns Transaction hash
     */
    async withdrawUSDT(toAddress: string, amount: string): Promise<string> {
        const balance = await this.checkBalance(this.walletAddress, USDT_CONTRACT_ADDRESS);
        if (Number(balance) < Number(amount)) {
            throw new Error('Insufficient USDT balance');
        }

        return this.transferUSDT(toAddress, amount);
    }

    /**
     * Chuyển BNB từ ví quản lý sang địa chỉ khác
     * @param toAddress Địa chỉ nhận
     * @param amount Số BNB (dạng string, ví dụ: '0.1')
     * @returns Transaction hash
     */
    async transferBNB(toAddress: string, amount: string): Promise<string> {
        try {
            const value = ethers.parseEther(amount); 
            const gasPrice = await this.provider.getFeeData().then((fee) => fee.gasPrice);
            const nonce = await this.provider.getTransactionCount(this.walletAddress, 'pending');

            const tx = {
                to: toAddress,
                value,
                gasLimit: 21000,
                gasPrice,
                nonce,
            };

            const txResponse = await this.wallet.sendTransaction(tx);
            const receipt = await txResponse.wait();

            this.logger.log(`Transferred ${amount} BNB to ${toAddress}. TxHash: ${receipt.hash}`);
            return receipt.hash;
        } catch (error) {
            this.logger.error(`Failed to transfer BNB: ${error.message}`);
            throw error;
        }
    }

    /**
     * Chuyển token ERC20 từ ví quản lý sang địa chỉ khác
     * @param contractAddress Địa chỉ contract token
     * @param toAddress Địa chỉ nhận
     * @param amount Số token (dạng string, ví dụ: '100')
     * @returns Transaction hash
     */
    async transferToken(contractAddress: string, toAddress: string, amount: string): Promise<string> {
        try {
            const contract = new ethers.Contract(contractAddress, ERC20_ABI, this.wallet);
            const decimals = await contract.decimals();
            const value = ethers.parseUnits(amount, decimals);
            const gasPrice = await this.provider.getFeeData().then((fee) => fee.gasPrice);
            const nonce = await this.provider.getTransactionCount(this.walletAddress, 'pending');

            const txResponse = await contract.transfer(toAddress, value, {
                gasLimit: 60000,
                gasPrice,
                nonce,
            });
            const receipt = await txResponse.wait();

            this.logger.log(`Transferred ${amount} tokens to ${toAddress}. TxHash: ${receipt.hash}`);
            return receipt.hash;
        } catch (error) {
            this.logger.error(`Failed to transfer token: ${error.message}`);
            throw error;
        }
    }

    /**
     * Kiểm tra số dư BNB hoặc token của một địa chỉ
     * @param address Địa chỉ cần kiểm tra (mặc định là ví quản lý)
     * @param contractAddress Địa chỉ contract token (null nếu kiểm tra BNB)
     * @returns Số dư (dạng string)
     */
    async checkBalance(address: string = this.walletAddress, contractAddress?: string): Promise<string> {
        try {
            if (contractAddress) {
                const contract = new ethers.Contract(contractAddress, ERC20_ABI, this.provider);
                const balance = await contract.balanceOf(address);
                const decimals = await contract.decimals();
                return ethers.formatUnits(balance, decimals);
            } else {
                const balance = await this.provider.getBalance(address);
                return ethers.formatEther(balance);
            }
        } catch (error) {
            this.logger.error(`Failed to check balance: ${error.message}`);
            throw error;
        }
    }

    /**
     * Rút BNB từ ví quản lý sang địa chỉ khác
     * @param toAddress Địa chỉ rút tiền đến
     * @param amount Số BNB (dạng string)
     * @returns Transaction hash
     */
    async withdrawBNB(toAddress: string, amount: string): Promise<string> {
        const balance = await this.checkBalance();
        if (Number(balance) < Number(amount)) {
            throw new Error('Insufficient BNB balance');
        }

        return this.transferBNB(toAddress, amount);
    }

    /**
     * Rút token từ ví quản lý sang địa chỉ khác
     * @param contractAddress Địa chỉ contract token
     * @param toAddress Địa chỉ rút tiền đến
     * @param amount Số token (dạng string)
     * @returns Transaction hash
     */
    async withdrawToken(contractAddress: string, toAddress: string, amount: string): Promise<string> {
        const balance = await this.checkBalance(this.walletAddress, contractAddress);
        if (Number(balance) < Number(amount)) {
            throw new Error('Insufficient token balance');
        }

        return this.transferToken(contractAddress, toAddress, amount);
    }

    /**
     * Lấy địa chỉ ví quản lý
     */
    getWalletAddress(): string {
        return this.walletAddress;
    }
}