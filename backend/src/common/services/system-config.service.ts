import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { QueryRunner, Repository } from 'typeorm';
import { SystemConfig } from '../entities/system-config.entity';
import e from 'express';

@Injectable()
export class SystemConfigService {
  constructor(
    @InjectRepository(SystemConfig)
    private readonly systemConfigRepository: Repository<SystemConfig>,
  ) {
    this.initializeDefaultConfigs();
  }

  private async initializeDefaultConfigs() {
    const defaultConfigs = [
      {
        key: 'MINING_BASE_REWARD',
        value: '0.1',
        description: 'Base reward for mining (in WM tokens)',
      },
      {
        key: 'MINING_COOLDOWN',
        value: '3600',
        description: 'Cooldown period for mining (in seconds)',
      },
      {
        key: 'MINING_ALLOCATION',
        value: '100000000',
        description: 'Total WM tokens that can be mined',
      },
      {
        key: 'DAILY_MINING_LIMIT',
        value: '100',
        description: 'Maximum WM tokens that can be mined per day',
      },
      {
        key: 'LIGHTNING_BOLT_PRICE',
        value: '10',
        description: 'Price of a lightning bolt (in WM tokens)',
      },
      {
        key: 'LIGHTNING_BOLT_REWARD_MULTIPLIER',
        value: '5',
        description: 'Reward multiplier when using a lightning bolt',
      },
      {
        key: 'REFERRAL_BONUS_PERCENTAGE',
        value: '10',
        description: 'Referral bonus percentage',
      },
      {
        key: 'MIN_WITHDRAWAL',
        value: '100',
        description: 'Minimum balance required for withdrawal (in USDT)',
      },
      {
        key: 'WITHDRAWAL_FEE',
        value: '1',
        description: 'Withdrawal fee percentage',
      },
      {
        key: 'TOTAL_LIGHTNING_BOLTS_SOLD',
        value: '0',
        description: 'Total number of lightning bolts sold',
      },
      {
        key: 'TOTAL_WM_MINED',
        value: '0',
        description: 'Total WM tokens mined by all users',
      },
      {
        key: 'CURRENT_MINING_DIFFICULTY',
        value: '1',
        description: 'Current mining difficulty multiplier',
      },
      {
        key: 'LIGHTNING_BOLT_PRICE_TIER_1',
        value: '2',
        description: 'Current lightning bolt price tier 1',
      },
      {
        key: 'LIGHTNING_BOLT_PRICE_TIER_RANK_1',
        value: '1000',
        description: 'Current lightning bolt price tier rank 1',
      },
      {
        key: 'LIGHTNING_BOLT_PRICE_TIER_2',
        value: '4',
        description: 'Current lightning bolt price tier 2',
      },
      {
        key: 'LIGHTNING_BOLT_PRICE_TIER_RANK_2',
        value: '2000',
        description: 'Current lightning bolt price tier rank 2',
      },
      {
        key: 'LIGHTNING_BOLT_PRICE_TIER_3',
        value: '6',
        description: 'Current lightning bolt price tier 3',
      },
      {
        key: 'LIGHTNING_BOLT_PRICE_TIER_RANK_3',
        value: '5000',
        description: 'Current lightning bolt price tier rank 3',
      },
      {
        key: 'LIGHTNING_BOLT_PRICE_TIER_4',
        value: '8',
        description: 'Current lightning bolt price tier 4',
      },
      {
        key: 'LIGHTNING_BOLT_PRICE_TIER_RANK_4',
        value: '10000',
        description: 'Current lightning bolt price tier rank 4',
      },
      {
        key: 'LIGHTNING_BOLT_PRICE_TIER_5',
        value: '10',
        description: 'Current lightning bolt price tier 5',
      },
      {
        key: 'PRIORITY_POSITION_PRICE',
        value: '5',
        description: 'Current priority position price',
      },
      {
        key: 'SYSTEM_PRIMARY_WALLET_ADDRESS',
        value: '******************************************',
        description: 'World Mall wallet address',
      },
      {
        key: 'TELEGRAM_CHANNEL_ID',
        value: '1084008408',
        description: 'Telegram channel id for transaction',
      },
      {
        key: 'CREDITS_FOR_NEW_USER',
        value: '100',
        description: 'Credits for new user',
      },
      {
        key: 'NFT_PHOENIX_PRICE',
        value: '100',
        description: 'Initial price for PHOENIX NFT (USDT)'
      },
      {
        key: 'NFT_SPIRIT_TURTLE_PRICE',
        value: '250',
        description: 'Initial price for SPIRIT_TURTLE NFT (USDT)'
      },
      {
        key: 'NFT_UNICORN_PRICE',
        value: '500',
        description: 'Initial price for UNICORN NFT (USDT)'
      },
      {
        key: 'NFT_DRAGON_PRICE',
        value: '1000',
        description: 'Initial price for DRAGON NFT (USDT)'
      },
      {
        key: 'NFT_PHOENIX_GAS_FEE',
        value: '1',
        description: 'Gas fee for PHOENIX NFT (WM)'
      },
      {
        key: 'NFT_SPIRIT_TURTLE_GAS_FEE',
        value: '2',
        description: 'Gas fee for SPIRIT_TURTLE NFT (WM)'
      },
      {
        key: 'NFT_UNICORN_GAS_FEE',
        value: '3.5',
        description: 'Gas fee for UNICORN NFT (WM)'
      },
      {
        key: 'NFT_DRAGON_GAS_FEE',
        value: '6',
        description: 'Gas fee for DRAGON NFT (WM)'
      },
      {
        key: 'NFT_PHOENIX_QUANTITY',
        value: '100',
        description: 'Quantity for PHOENIX NFT'
      },
      {
        key: 'NFT_SPIRIT_TURTLE_QUANTITY',
        value: '100',
        description: 'Quantity for SPIRIT_TURTLE NFT'
      },
      {
        key: 'NFT_UNICORN_QUANTITY',
        value: '100',
        description: 'Quantity for UNICORN NFT'
      },
      {
        key: 'NFT_DRAGON_QUANTITY',
        value: '100',
        description: 'Quantity for DRAGON NFT'
      },
      {
        key: 'NFT_GROWTH_RATE',
        value: '8',
        description: 'NFT growth rate per session (%)'
      },
      {
        key: 'NFT_SPLIT_THRESHOLD',
        value: '1000',
        description: 'Price threshold for NFT split (USDT)'
      },
      {
        key: 'NFT_SPLIT_COUNT',
        value: '6',
        description: 'Number of NFTs created after split'
      },
      {
        key: 'NFT_WITHDRAWAL_FEE',
        value: '3',
        description: 'NFT profit withdrawal fee (%)'
      },
      {
        key: 'NFT_TRADING_SESSION_TIMEZONE',
        value: 'Asia/Dubai',
        description: 'NFT trading session timezone'
      },
      {
        key: 'NFT_TRADING_SESSION_DURATION',
        value: '60',
        description: 'NFT trading session duration (minutes)'
      },
      {
        key: 'NFT_TRADING_SESSION_MORNING_START_TIME',
        value: '06:00',
        description: 'NFT trading session morning start time'
      },
      {
        key: 'NFT_TRADING_SESSION_MIDDLE_START_TIME',
        value: '11:00',
        description: 'NFT trading session middle start time'
      },
      {
        key: 'NFT_TRADING_SESSION_EVENING_START_TIME',
        value: '18:00',
        description: 'NFT trading session evening start time'
      },
      {
        key: 'NFT_TRADING_SESSION_DAYS_TO_CREATE',
        value: '7',
        description: 'Number of days to create trading sessions'
      },
      {
        key: 'NFT_SCHEDULING_FEE',
        value: '1',
        description: 'NFT scheduling fee (WM)'
      },
      {
        key: 'WITHDRAW_USDT_AMOUNT_THRESHOLD_TELEGRAM_CONFIRM',
        value: '100',
        description: 'Withdrawal USDT amount threshold for telegram confirm'
      },
      {
        key: 'MAX_SESSION_VOLUME',
        value: '100000',
        description: 'Maximum session volume'
      },
      {
        key: 'WM_USD_RATE',
        value: '1',
        description: 'WM USD rate'
      }
    ];

    for (const configData of defaultConfigs) {
      const existingConfig = await this.findByKey(configData.key);
      if (!existingConfig) {
        await this.create(configData.key, configData.value, configData.description);
      }
    }
  }

  async findByKey(key: string): Promise<SystemConfig> {
    return this.systemConfigRepository.findOne({ where: { key } });
  }

  async findByKeyWithQueryRunner(queryRunner: QueryRunner, key: string): Promise<SystemConfig> {
    return queryRunner.manager.findOne(SystemConfig, { where: { key } });
  }

  async create(key: string, value: string, description?: string): Promise<SystemConfig> {
    const config = this.systemConfigRepository.create({
      key,
      value,
      description,
    });
    return this.systemConfigRepository.save(config);
  }

  async createWithQueryRunner(queryRunner: QueryRunner, key: string, value: string, description?: string): Promise<SystemConfig> {
    const config = queryRunner.manager.create(SystemConfig, {
      key,
      value,
      description,
    });
    return queryRunner.manager.save(SystemConfig, config);
  }

  async update(key: string, value: string): Promise<SystemConfig> {
    const config = await this.findByKey(key);
    if (!config) {
      return this.create(key, value);
    }

    config.value = value;
    return this.systemConfigRepository.save(config);
  }

  async updateWithQueryRunner(queryRunner: QueryRunner, key: string, value: string): Promise<SystemConfig> {
    const config = await this.findByKeyWithQueryRunner(queryRunner, key);
    if (!config) {
      return this.createWithQueryRunner(queryRunner, key, value);
    }

    config.value = value;
    return queryRunner.manager.save(SystemConfig, config);
  }

  async incrementValue(key: string, increment: number = 1): Promise<SystemConfig> {
    const config = await this.findByKey(key);
    if (!config) {
      return this.create(key, increment.toString());
    }

    const currentValue = parseFloat(config.value);
    const newValue = (currentValue + increment).toString();
    config.value = newValue;
    return this.systemConfigRepository.save(config);
  }

  async incrementValueWithQueryRunner(queryRunner: QueryRunner, key: string, increment: number = 1): Promise<SystemConfig> {
    const config = await this.findByKeyWithQueryRunner(queryRunner, key);
    if (!config) {
      return this.createWithQueryRunner(queryRunner, key, increment.toString());
    }

    const currentValue = parseFloat(config.value);
    const newValue = (currentValue + increment).toString();
    config.value = newValue;
    return queryRunner.manager.save(SystemConfig, config);
  }


  // Mining related methods
  async getTotalWmMined(): Promise<number> {
    const config = await this.findByKey('TOTAL_WM_MINED');
    return config ? parseFloat(config.value) : 0;
  }

  async getTotalWmMinedWithQueryRunner(queryRunner: QueryRunner): Promise<number> {
    const config = await queryRunner.manager.findOne(SystemConfig, { where: { key: 'TOTAL_WM_MINED' } });
    return config ? parseFloat(config.value) : 0;
  }

  async incrementTotalWmMined(amount: number): Promise<number> {
    const config = await this.incrementValue('TOTAL_WM_MINED', amount);
    return parseFloat(config.value);
  }

  async incrementTotalWmMinedWithQueryRunner(queryRunner: QueryRunner, amount: number): Promise<number> {
    const config = await this.incrementValueWithQueryRunner(queryRunner, 'TOTAL_WM_MINED', amount);
    return parseFloat(config.value);
  }

  async getCurrentMiningDifficulty(): Promise<number> {
    const config = await this.findByKey('CURRENT_MINING_DIFFICULTY');
    return config ? parseFloat(config.value) : 1;
  }

  async updateMiningDifficulty(difficulty: number): Promise<number> {
    const config = await this.update('CURRENT_MINING_DIFFICULTY', difficulty.toString());
    return parseFloat(config.value);
  }

  async updateMiningDifficultyWithQueryRunner(queryRunner: QueryRunner, difficulty: number): Promise<number> {
    const config = await this.updateWithQueryRunner(queryRunner, 'CURRENT_MINING_DIFFICULTY', difficulty.toString());
    return parseFloat(config.value);
  }

  async checkAndUpdateMiningDifficulty(): Promise<number> {
    const totalMined = await this.getTotalWmMined();
    const difficultyLevel = Math.floor(totalMined / 1000000) + 1;

    // Difficulty doubles after each 1,000,000 WM mined
    const difficulty = Math.pow(2, difficultyLevel - 1);

    await this.updateMiningDifficulty(difficulty);
    return difficulty;
  }

  async checkAndUpdateMiningDifficultyWithQueryRunner(queryRunner: QueryRunner): Promise<number> {
    const totalMined = await this.getTotalWmMinedWithQueryRunner(queryRunner);
    const difficultyLevel = Math.floor(totalMined / 1000000) + 1;

    // Difficulty doubles after each 1,000,000 WM mined
    const difficulty = Math.pow(2, difficultyLevel - 1);
    await this.updateMiningDifficultyWithQueryRunner(queryRunner, difficulty);
    return difficulty;
  }

  // Lightning bolt related methods
  async getTotalLightningBoltsSold(): Promise<number> {
    const config = await this.findByKey('TOTAL_LIGHTNING_BOLTS_SOLD');
    return config ? parseInt(config.value, 10) : 0;
  }

  async incrementLightningBoltsSold(): Promise<number> {
    const config = await this.incrementValue('TOTAL_LIGHTNING_BOLTS_SOLD');
    return parseInt(config.value, 10);
  }

  async getLightningBoltPriceTier(): Promise<number> {
    // 1.000 tia sét WM đầu tiên giá 2$
    // Từ 1.001 - 1.999 giá 4$
    // Từ 2.000 - 4.999 giá 6$
    // Từ 5.000 - 9.999 giá 8$
    // Từ 10.000 trở lên giá 10$

    const totalSold = await this.getTotalLightningBoltsSold();
    const configLightningBoltPriceTierRank1 = await this.findByKey('LIGHTNING_BOLT_PRICE_TIER_RANK_1');
    const lightningBoltPriceTierRank1 = configLightningBoltPriceTierRank1 ? parseFloat(configLightningBoltPriceTierRank1.value) : 1000;
    const configLightningBoltPriceTierRank2 = await this.findByKey('LIGHTNING_BOLT_PRICE_TIER_RANK_2');
    const lightningBoltPriceTierRank2 = configLightningBoltPriceTierRank2 ? parseFloat(configLightningBoltPriceTierRank2.value) : 2000;
    const configLightningBoltPriceTierRank3 = await this.findByKey('LIGHTNING_BOLT_PRICE_TIER_RANK_3');
    const lightningBoltPriceTierRank3 = configLightningBoltPriceTierRank3 ? parseFloat(configLightningBoltPriceTierRank3.value) : 5000;
    const configLightningBoltPriceTierRank4 = await this.findByKey('LIGHTNING_BOLT_PRICE_TIER_RANK_4');
    const lightningBoltPriceTierRank4 = configLightningBoltPriceTierRank4 ? parseFloat(configLightningBoltPriceTierRank4.value) : 10000;

    if (totalSold < lightningBoltPriceTierRank1) {
      const configLightningBoltPriceTier1 = await this.findByKey('LIGHTNING_BOLT_PRICE_TIER_1');
      return configLightningBoltPriceTier1 ? parseFloat(configLightningBoltPriceTier1.value) : 2;
    } else if (totalSold > lightningBoltPriceTierRank1 && totalSold <= lightningBoltPriceTierRank2) {
      const configLightningBoltPriceTier2 = await this.findByKey('LIGHTNING_BOLT_PRICE_TIER_2');
      return configLightningBoltPriceTier2 ? parseFloat(configLightningBoltPriceTier2.value) : 4;
    } else if (totalSold > lightningBoltPriceTierRank2 && totalSold <= lightningBoltPriceTierRank3) {
      const configLightningBoltPriceTier3 = await this.findByKey('LIGHTNING_BOLT_PRICE_TIER_3');
      return configLightningBoltPriceTier3 ? parseFloat(configLightningBoltPriceTier3.value) : 6;
    } else if (totalSold > lightningBoltPriceTierRank3 && totalSold <= lightningBoltPriceTierRank4) {
      const configLightningBoltPriceTier4 = await this.findByKey('LIGHTNING_BOLT_PRICE_TIER_4');
      return configLightningBoltPriceTier4 ? parseFloat(configLightningBoltPriceTier4.value) : 8;
    } else if (totalSold > lightningBoltPriceTierRank4) {
      const configLightningBoltPriceTier5 = await this.findByKey('LIGHTNING_BOLT_PRICE_TIER_5');
      return configLightningBoltPriceTier5 ? parseFloat(configLightningBoltPriceTier5.value) : 10;
    }
  }

  async getLightningBoltPrice(): Promise<number> {
    const config = await this.findByKey('LIGHTNING_BOLT_PRICE');
    return parseFloat(config.value);
  }

  async getPriorityPositionPrice(): Promise<number> {
    const config = await this.findByKey('PRIORITY_POSITION_PRICE');
    return parseFloat(config.value);
  }

  async getWMPrice(): Promise<number> {
    const config = await this.findByKey('WM_USD_RATE');
    return config ? parseFloat(config.value) : 1;
  }
}