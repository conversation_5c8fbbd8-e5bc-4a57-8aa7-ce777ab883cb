import { Injectable, OnModuleInit, OnApplicationShutdown, Logger } from '@nestjs/common';
import Redis from 'ioredis';
import { Queue } from 'bull';
import Redlock from 'redlock';
import { ConfigService } from '@nestjs/config';
import Bull from 'bull';

@Injectable()
export class RedisService implements OnModuleInit, OnApplicationShutdown {
  private readonly logger = new Logger(RedisService.name);
  private redisClient: Redis;
  private redlock: Redlock;
  private orderQueue: Queue;
  private readonly config: any;

  constructor(private configService: ConfigService) {
    this.config = this.configService.get('redis');
    this.logger.debug('[RedisService] Configuration loaded:', {
      host: this.config?.host,
      port: this.config?.port,
      db: this.config?.db
    });
  }

  async onModuleInit() {
    this.logger.log('[RedisService] Initializing Redis service...');
    
    try {
      // Initialize Redis client with basic config
      this.logger.log('[RedisService] Creating Redis client...');
      this.redisClient = new Redis({
        host: this.config.host,
        port: this.config.port,
        password: this.config.password,
        db: this.config.db,
        connectTimeout: this.config.connectTimeout,
        maxRetriesPerRequest: this.config.maxRetriesPerRequest,
        retryStrategy: this.config.retryStrategy,
        reconnectOnError: this.config.reconnectOnError
      });

      // Test connection with retry
      await this.connectWithRetry();

      // Initialize features
      this.logger.log('[RedisService] Initializing Redis features...');
      await this.initializeRedisFeatures();
      this.logger.log('[RedisService] Redis service initialization completed successfully');
      
    } catch (error) {
      this.logger.error('[RedisService] Redis initialization failed:', error);
      if (this.config.required) {
        throw error;
      }
    }
  }

  private async initializeRedisFeatures() {
    try {
      if (this.orderQueue) {
        this.logger.debug('[RedisService] Redis features already initialized');
        return;
      }

      this.logger.log('[RedisService] Initializing Redlock...');
      await this.initializeRedlock();
      this.logger.log('[RedisService] Redlock initialized');

      this.logger.log('[RedisService] Initializing Queue...');
      await this.initializeQueue();
      this.logger.log('[RedisService] Queue initialized');
      
      this.logger.log('[RedisService] Redis features initialized successfully');
    } catch (error) {
      this.logger.error('[RedisService] Failed to initialize Redis features:', error);
      throw error;
    }
  }

  private async initializeQueue(retries = 3) {
    this.logger.log('[RedisService] Initializing Bull Queue...');
    
    for (let i = 0; i < retries; i++) {
      try {
        this.orderQueue = new Bull('order-processing', {
          redis: {
            host: this.config.host,
            port: this.config.port,
            password: this.config.password,
            db: this.config.db,
            connectTimeout: 10000,
            maxRetriesPerRequest: 3,
          },
          prefix: this.config.queue.prefix,
          defaultJobOptions: this.config.queue.defaultJobOptions,
          settings: {
            lockDuration: 30000,
            stalledInterval: 30000,
          }
        });

        await this.waitForQueueReady();
        await this.setupQueueEventHandlers();
        this.logger.log('[RedisService] Bull Queue initialized successfully');
        return;
        
      } catch (error) {
        this.logger.warn(`[RedisService] Queue initialization attempt ${i + 1} failed:`, error);
        if (i < retries - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }
    throw new Error('Failed to initialize queue after multiple attempts');
  }

  private async waitForQueueReady(timeout = 15000): Promise<void> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('Queue initialization timeout'));
      }, timeout);

      let isResolved = false;

      const checkConnection = async () => {
        try {
          await this.orderQueue.client.ping();
          if (!isResolved) {
            isResolved = true;
            clearTimeout(timer);
            resolve();
          }
        } catch (error) {
          this.logger.warn('[RedisService] Queue connection check failed:', error);
        }
      };

      this.orderQueue
        .on('error', (error) => {
          this.logger.error('[RedisService] Queue error:', error);
          if (!isResolved) {
            isResolved = true;
            clearTimeout(timer);
            reject(error);
          }
        })
        .on('ready', () => {
          if (!isResolved) {
            isResolved = true;
            clearTimeout(timer);
            resolve();
          }
        });

      checkConnection();
      
      const interval = setInterval(async () => {
        if (!isResolved) {
          await checkConnection();
        } else {
          clearInterval(interval);
        }
      }, 1000);

      timer.unref();
    });
  }

  private async setupQueueEventHandlers() {
    this.orderQueue
      .on('error', (error) => {
        this.logger.error('[RedisService] Queue error:', error);
      })
      .on('failed', (job, error) => {
        this.logger.error(`[RedisService] Job ${job.id} failed:`, error);
      })
      .on('completed', (job) => {
        this.logger.debug(`[RedisService] Job ${job.id} completed successfully`);
      })
      .on('stalled', (job) => {
        this.logger.warn(`[RedisService] Job ${job.id} is stalled`);
      })
      .on('waiting', (jobId) => {
        this.logger.debug(`[RedisService] Job ${jobId} is waiting`);
      })
      .on('active', (job) => {
        this.logger.debug(`[RedisService] Processing job ${job.id}`);
      })
      .on('cleaned', (jobs, type) => {
        this.logger.debug(`[RedisService] Cleaned ${jobs.length} ${type} jobs`);
      });
  }

  private async connectWithRetry(retries = 3, delay = 1000): Promise<void> {
    for (let i = 0; i < retries; i++) {
      try {
        await this.redisClient.ping();
        this.logger.log('[RedisService] Redis connected successfully');
        return;
      } catch (error) {
        this.logger.warn(`[RedisService] Redis connection attempt ${i + 1} failed:`, error);
        if (i < retries - 1) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    throw new Error('Failed to connect to Redis after multiple attempts');
  }

  async checkQueueHealth(): Promise<boolean> {
    try {
      const client = this.orderQueue.client;
      if (!client) return false;
      
      await client.ping();
      
      const counts = await this.orderQueue.getJobCounts();
      const isPaused = await this.orderQueue.isPaused();
      
      return !isPaused && counts !== null;
    } catch (error) {
      this.logger.error('[RedisService] Queue health check failed:', error);
      return false;
    }
  }

  async onApplicationShutdown(signal?: string) {
    this.logger.log(`[RedisService] Shutting down Redis service (signal: ${signal})`);
    
    try {
      if (this.orderQueue) {
        await this.orderQueue.pause(true);
        await this.orderQueue.clean(0, 'delayed');
        await this.orderQueue.clean(0, 'wait');
        await this.orderQueue.close();
      }

      if (this.redisClient) {
        await this.redisClient.quit();
      }
      
      this.logger.log('[RedisService] Redis service shutdown completed');
    } catch (error) {
      this.logger.error('[RedisService] Error during Redis service shutdown:', error);
    }
  }

  private async initializeRedlock() {
    this.redlock = new Redlock(
      [this.redisClient],
      {
        driftFactor: 0.01,
        retryCount: 10,
        retryDelay: 200,
        retryJitter: 200,
        automaticExtensionThreshold: 500
      }
    );
  }

  async getClient(): Promise<Redis> {
    return this.redisClient;
  }

  async quit(): Promise<void> {
    try {
      await this.redisClient.quit();
      await this.orderQueue.close();
      this.logger.log('[RedisService] Redis connections closed');
    } catch (error) {
      this.logger.error('[RedisService] Error closing Redis connections:', error);
    }
  }

  async set(key: string, value: string): Promise<'OK'> {
    return this.redisClient.set(key, value);
  }

  async get(key: string): Promise<string | null> {
    return this.redisClient.get(key);
  }

  async expire(key: string, seconds: number): Promise<number> {
    return this.redisClient.expire(key, seconds);
  }

  async acquireLock(key: string, ttl: number): Promise<any> {
    return this.redlock.acquire([key], ttl);
  }

  async releaseLock(lock: any): Promise<void> {
    await lock.release();
  }

  async addToOrderQueue(data: any, opts?: Bull.JobOptions): Promise<Bull.Job> {
    try {
      const options = {
        ...this.config.queue.defaultJobOptions,
        ...opts,
      };
      return await this.orderQueue.add(data, options);
    } catch (error) {
      this.logger.error('[RedisService] Failed to add job to order queue:', error);
      throw error;
    }
  }

  getOrderQueue(): Queue | null {
    if (!this.orderQueue) {
      this.logger.warn('[RedisService] Order queue not initialized');
      return null;
    }
    return this.orderQueue;
  }

  async getFromCache<T>(key: string): Promise<T | null> {
    try {
      const data = await this.redisClient.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      this.logger.error(`[RedisService] Failed to get from cache for key ${key}:`, error);
      return null;
    }
  }

  async setToCache(key: string, value: any, ttl: number = this.config.cache.ttl): Promise<void> {
    try {
      const serialized = JSON.stringify(value);
      if (ttl > 0) {
        await this.redisClient.set(key, serialized, 'EX', ttl);
      } else {
        await this.redisClient.set(key, serialized);
      }
    } catch (error) {
      this.logger.error(`[RedisService] Failed to set cache for key ${key}:`, error);
    }
  }

  async clearCache(pattern: string): Promise<void> {
    try {
      const keys = await this.redisClient.keys(pattern);
      if (keys.length > 0) {
        await this.redisClient.del(...keys);
        this.logger.debug(`[RedisService] Cleared ${keys.length} keys matching pattern: ${pattern}`);
      }
    } catch (error) {
      this.logger.error(`[RedisService] Failed to clear cache for pattern ${pattern}:`, error);
    }
  }

  async zadd(key: string, score: number, member: string): Promise<number> {
    try {
      const result = await this.redisClient.zadd(key, score, member);
      this.logger.debug(`Added member ${member} with score ${score} to sorted set ${key}`);
      return result;
    } catch (error) {
      this.logger.error(`[RedisService] Failed to add member to sorted set ${key}:`, error);
      throw error;
    }
  }

  async zrem(key: string, member: string): Promise<number> {
    try {
      const result = await this.redisClient.zrem(key, member);
      this.logger.debug(`Removed member ${member} from sorted set ${key}`);
      return result;
    } catch (error) {
      this.logger.error(`[RedisService] Failed to remove member from sorted set ${key}:`, error);
      throw error;
    }
  }

  async zrange(key: string, start: number, stop: number): Promise<string[]> {
    try {
      const result = await this.redisClient.zrange(key, start, stop);
      this.logger.debug(`Retrieved range [${start}, ${stop}] from sorted set ${key}`);
      return result;
    } catch (error) {
      this.logger.error(`[RedisService] Failed to get range from sorted set ${key}:`, error);
      throw error;
    }
  }

  async zrank(key: string, member: string): Promise<number | null> {
    try {
      const result = await this.redisClient.zrank(key, member);
      this.logger.debug(`Retrieved rank for member ${member} from sorted set ${key}`);
      return result;
    } catch (error) {
      this.logger.error(`[RedisService] Failed to get rank from sorted set ${key}:`, error);
      throw error;
    }
  }

  async zscore(key: string, member: string): Promise<string | null> {
    try {
      const result = await this.redisClient.zscore(key, member);
      this.logger.debug(`Retrieved score for member ${member} from sorted set ${key}`);
      return result;
    } catch (error) {
      this.logger.error(`[RedisService] Failed to get score from sorted set ${key}:`, error);
      throw error;
    }
  }

  // Key Operations following existing patterns
  async del(...keys: string[]): Promise<number> {
    try {
      const result = await this.redisClient.del(...keys);
      this.logger.debug(`Deleted ${result} keys`);
      return result;
    } catch (error) {
      this.logger.error(`[RedisService] Failed to delete keys:`, error);
      throw error;
    }
  }

  async exists(key: string): Promise<number> {
    try {
      const result = await this.redisClient.exists(key);
      this.logger.debug(`Checked existence of key ${key}`);
      return result;
    } catch (error) {
      this.logger.error(`[RedisService] Failed to check existence of key ${key}:`, error);
      throw error;
    }
  }
} 