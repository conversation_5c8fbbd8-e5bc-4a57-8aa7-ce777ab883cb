import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { addMinutes, differenceInMinutes, isAfter, isBefore } from 'date-fns';

@Injectable()
export class DateService {
  private readonly dbTimeZone: string;
  private readonly appTimeZone: string;

  constructor(private configService: ConfigService) {
    // Lấy múi giờ từ config hoặc sử dụng giá trị mặc định
    this.dbTimeZone = this.configService.get('DB_TIMEZONE', '+04:00');
    this.appTimeZone = this.configService.get('APP_TIMEZONE', '+04:00');
  }

  /**
   * Lấy thời gian hiện tại
   */
  now(): Date {
    return new Date();
  }

  /**
   * Thêm phút vào thời gian
   */
  addMinutes(date: Date, minutes: number): Date {
    return addMinutes(date, minutes);
  }

  /**
   * <PERSON><PERSON>h số phút giữa hai thời điểm
   */
  differenceInMinutes(dateLeft: Date, dateRight: Date): number {
    return differenceInMinutes(dateLeft, dateRight);
  }

  /**
   * Kiểm tra xem thời điểm đầu có sau thời điểm thứ hai không
   */
  isAfter(date: Date, dateToCompare: Date): boolean {
    return isAfter(date, dateToCompare);
  }

  /**
   * Kiểm tra xem thời điểm đầu có trước thời điểm thứ hai không
   */
  isBefore(date: Date, dateToCompare: Date): boolean {
    return isBefore(date, dateToCompare);
  }

  /**
   * Kiểm tra xem một order có hết hạn chưa
   */
  isOrderExpired(createdAt: Date, expirationMinutes: number = 30): boolean {
    const expirationTime = this.addMinutes(createdAt, expirationMinutes);
    return this.isAfter(this.now(), expirationTime);
  }

  /**
   * Tính thời gian hết hạn của order
   */
  getOrderExpirationTime(createdAt: Date, expirationMinutes: number = 30): Date {
    return this.addMinutes(createdAt, expirationMinutes);
  }
} 