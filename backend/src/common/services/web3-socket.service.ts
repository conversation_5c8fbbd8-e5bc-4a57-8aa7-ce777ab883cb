import { Injectable, OnModuleInit, OnModuleD<PERSON>roy, <PERSON><PERSON>, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ethers } from 'ethers';
import { Subject } from 'rxjs';

export interface TokenTransferEvent {
  from: string;
  to: string;
  value: bigint;
  valueFormatted: number;
  transactionHash: string;
  blockNumber: number;
  tokenAddress: string;
  decimals: number;
  rawEvent: any;
}

@Injectable()
export class Web3SocketService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(Web3SocketService.name);
  private wsProvider: ethers.WebSocketProvider;
  private tokenContracts: Map<string, ethers.Contract> = new Map();
  private reconnectInterval: NodeJS.Timeout;
  private isConnecting: boolean = false;
  
  // Observable để các service khác có thể subscribe
  private transferEvents = new Subject<TokenTransferEvent>();
  public transferEvents$ = this.transferEvents.asObservable();

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit() {
    this.logger.log('Web3SocketService initializing...');
    await this.initializeWebSocket();
    this.logger.log('Web3SocketService initialized');
  }

  async onModuleDestroy() {
    this.logger.log('Web3SocketService destroying...');
    if (this.wsProvider) {
      this.wsProvider.destroy();
    }
    if (this.reconnectInterval) {
      clearInterval(this.reconnectInterval);
    }
    this.logger.log('Web3SocketService destroyed');
  }

  private async initializeWebSocket() {
    if (this.isConnecting) return;
    this.isConnecting = true;

    try {
      const wsUrl = this.configService.get('BSC_WS_URL') || 'wss://bsc-testnet.publicnode.com';
      this.logger.log(`Initializing WebSocket with URL: ${wsUrl}`);
      
      if (this.wsProvider) {
        this.wsProvider.destroy();
      }
      
      this.wsProvider = new ethers.WebSocketProvider(wsUrl);
      
      // Kiểm tra kết nối
      const network = await this.wsProvider.getNetwork();
      this.logger.log(`Connected to network: ${network.name} ${network.chainId}`);
      
      // Thiết lập ping để giữ kết nối
      if (this.reconnectInterval) {
        clearInterval(this.reconnectInterval);
      }
      
      this.reconnectInterval = setInterval(async () => {
        try {
          await this.wsProvider.getBlockNumber();
          this.logger.debug('WebSocket ping successful');
        } catch (error) {
          this.logger.error(`Ping failed, reconnecting WebSocket: ${error.message}`);
          this.initializeWebSocket();
        }
      }, 30000); // Ping mỗi 30 giây
      
      // Khởi tạo lại các token contracts đã đăng ký
      for (const [tokenAddress, contract] of this.tokenContracts.entries()) {
        await this.listenToTokenTransfers(tokenAddress, contract.interface.fragments[0].inputs[2].baseType === 'uint256' ? 18 : 6);
      }
      
      this.isConnecting = false;
    } catch (error) {
      this.logger.error(`Error initializing WebSocket: ${error.message}`);
      this.isConnecting = false;
      
      // Thử kết nối lại sau 5 giây
      setTimeout(() => this.initializeWebSocket(), 5000);
    }
  }

  /**
   * Đăng ký lắng nghe sự kiện transfer của ví hệ thống.
   * @param walletAddress Địa chỉ ví cần lắng nghe
   */
  async listenToSystemWalletTransfers(walletAddress: string): Promise<void> {
    try {
      if (!this.wsProvider) {
        throw new Error('WebSocket provider not initialized');
      }

      // Khởi tạo contract interface để decode event logs
      const erc20Interface = new ethers.Interface([
        'event Transfer(address indexed from, address indexed to, uint256 value)',
        'function decimals() view returns (uint8)'
      ]);
      
      this.logger.log(`[Web3SocketService] Listening for transfers to ${walletAddress}`);

      // Lọc event Transfer với địa chỉ to là ví hệ thống
      const filter = {
        topics: [
          ethers.id('Transfer(address,address,uint256)'),
          null,
          ethers.zeroPadValue(walletAddress.toLowerCase(), 32)
        ]
      };

      this.logger.log(`[Web3SocketService] Filter created for address ${walletAddress}`);

      // Cache để lưu decimals của các token
      const decimalsCache = new Map<string, number>();

      // Helper function để serialize BigInt
      const serializeWithBigInt = (obj: any): any => {
        return JSON.parse(JSON.stringify(obj, (_, value) =>
          typeof value === 'bigint' ? value.toString() : value
        ));
      };

      // Đăng ký lắng nghe event
      this.wsProvider.on(filter, async (log) => {
        try {
          // Parse event log
          const parsedLog = erc20Interface.parseLog(log);
          const value = BigInt(parsedLog.args.value.toString());
          const tokenAddress = log.address;

          this.logger.log(`[Web3SocketService] Received transfer event from ${parsedLog.args.from} to ${parsedLog.args.to}`);
          this.logger.debug(`[Web3SocketService] Raw log: ${JSON.stringify(serializeWithBigInt(log))}`);

          // Lấy số decimals của token
          let decimals: number;
          if (decimalsCache.has(tokenAddress)) {
            decimals = decimalsCache.get(tokenAddress);
            this.logger.debug(`[Web3SocketService] Using cached decimals for token ${tokenAddress}: ${decimals}`);
          } else {
            try {
              // Tạo contract instance để gọi hàm decimals()
              const tokenContract = new ethers.Contract(tokenAddress, erc20Interface, this.wsProvider);
              decimals = await tokenContract.decimals();
              decimalsCache.set(tokenAddress, decimals);
              this.logger.debug(`[Web3SocketService] Cached new decimals for token ${tokenAddress}: ${decimals}`);
            } catch (error) {
              this.logger.error(`[Web3SocketService] Error getting decimals for token ${tokenAddress}: ${error.message}`);
              this.logger.warn(`[Web3SocketService] Using fallback decimals (18) for token ${tokenAddress}`);
              decimals = 18;
            }
          }
          
          // Tạo object chứa thông tin transfer
          const transferEvent: TokenTransferEvent = {
            from: parsedLog.args.from,
            to: parsedLog.args.to,
            value: value,
            valueFormatted: parseFloat(ethers.formatUnits(value, decimals)),
            transactionHash: log.transactionHash,
            blockNumber: log.blockNumber,
            tokenAddress: tokenAddress,
            decimals: decimals,
            rawEvent: serializeWithBigInt(log) // Serialize rawEvent để tránh lỗi BigInt
          };

          this.logger.log(`[Web3SocketService] Processing transfer: ${transferEvent.valueFormatted} tokens from ${transferEvent.from} to ${transferEvent.to}`);

          // Gọi handler xử lý event với các tham số riêng biệt
          await this.handleTransferEvent(
            transferEvent.from,
            transferEvent.to,
            transferEvent.value,
            transferEvent.rawEvent,
            transferEvent.tokenAddress,
            transferEvent.decimals
          );

          this.logger.log(`[Web3SocketService] Transfer event processed successfully`);
        } catch (error) {
          this.logger.error(`[Web3SocketService] Error processing transfer event: ${error.message}`, error.stack);
        }
      });

      this.logger.log(`[Web3SocketService] Event listener setup completed for ${walletAddress}`);
    } catch (error) {
      this.logger.error(`[Web3SocketService] Failed to setup transfer listener: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Đăng ký lắng nghe sự kiện Transfer của một token
   * @param tokenAddress Địa chỉ của token
   * @param decimals Số decimals của token (mặc định là 18)
   * @returns Promise<void>
   */
  async listenToTokenTransfers(tokenAddress: string, decimals: number = 18): Promise<void> {
    try {
      if (!this.wsProvider) {
        throw new Error('WebSocket provider not initialized');
      }
      
      // Kiểm tra xem đã đăng ký lắng nghe token này chưa
      if (this.tokenContracts.has(tokenAddress)) {
        this.logger.log(`Already listening to token transfers for ${tokenAddress}`);
        return;
      }
      
      this.logger.log(`Registering Transfer event listener for token: ${tokenAddress}`);
      
      const tokenAbi = [
        "event Transfer(address indexed from, address indexed to, uint256 value)"
      ];
      
      const tokenContract = new ethers.Contract(tokenAddress, tokenAbi, this.wsProvider);
      this.tokenContracts.set(tokenAddress, tokenContract);
      
      // Đăng ký event listener
      tokenContract.on(
        tokenContract.filters.Transfer,
        (from, to, value, event) => {
          this.handleTransferEvent(from, to, value, event, tokenAddress, decimals);
        }
      );
      
      this.logger.log(`Successfully registered Transfer event listener for token: ${tokenAddress}`);
    } catch (error) {
      this.logger.error(`Error listening to token transfers: ${error.message}`);
      throw error;
    }
  }

  /**
   * Hủy đăng ký lắng nghe sự kiện Transfer của một token
   * @param tokenAddress Địa chỉ của token
   * @returns Promise<void>
   */
  async stopListeningToTokenTransfers(tokenAddress: string): Promise<void> {
    try {
      const contract = this.tokenContracts.get(tokenAddress);
      if (contract) {
        contract.removeAllListeners();
        this.tokenContracts.delete(tokenAddress);
        this.logger.log(`Stopped listening to token transfers for ${tokenAddress}`);
      }
    } catch (error) {
      this.logger.error(`Error stopping token transfer listener: ${error.message}`);
    }
  }

  private async handleTransferEvent(from: string, to: string, value: bigint, event: any, tokenAddress: string, decimals: number) {
    try {
      // Helper function để serialize BigInt
      const serializeData = (data: any) => {
        return JSON.parse(JSON.stringify(data, (_, value) =>
          typeof value === 'bigint' ? value.toString() : value
        ));
      };

      let transactionHash = null;
      let blockNumber = null;

      this.logger.log(`[Web3SocketService] Handling transfer event: ${from} -> ${to}, amount: ${value.toString()}, token: ${tokenAddress}, decimals: ${decimals}`);
      this.logger.debug(`[Web3SocketService] Raw event: ${JSON.stringify(serializeData(event))}`);
      
      // Thử các cách khác nhau để lấy transactionHash
      if (event && event.log && event.log.transactionHash) {
        transactionHash = event.log.transactionHash;
        blockNumber = event.log.blockNumber;
      } else if (event && event.transactionHash) {
        transactionHash = event.transactionHash;
        blockNumber = event.blockNumber;
      }

      const valueFormatted = parseFloat(ethers.formatUnits(value, decimals));

      this.logger.log(`[Web3SocketService] New token transfer detected: ${from} -> ${to}, amount: ${valueFormatted}, token: ${tokenAddress}, decimals: ${decimals}, hash: ${transactionHash}`);

      // Nếu không có transactionHash, không thể xác minh giao dịch
      if (!transactionHash) {
        this.logger.warn('[Web3SocketService] Missing transactionHash in event, cannot verify transaction');
        return;
      }

      // Tạo đối tượng event và emit
      const transferEvent: TokenTransferEvent = {
        from,
        to,
        value,
        valueFormatted,
        transactionHash,
        blockNumber,
        tokenAddress,
        decimals,
        rawEvent: serializeData(event)
      };

      this.logger.log(`[Web3SocketService] Emitting transfer event for transaction ${transactionHash}`);

      this.transferEvents.next(transferEvent);
      
      this.logger.log(`[Web3SocketService] Transfer event processed successfully`);
    } catch (error) {
      this.logger.error(`[Web3SocketService] Error handling transfer event: ${error.message}`, error.stack);
    }
  }

  /**
   * Lấy provider hiện tại
   * @returns ethers.WebSocketProvider
   */
  getProvider(): ethers.WebSocketProvider {
    return this.wsProvider;
  }

  /**
   * Kiểm tra xem WebSocket có đang kết nối không
   * @returns boolean
   */
  isConnected(): boolean {
    return !!this.wsProvider;
  }
} 