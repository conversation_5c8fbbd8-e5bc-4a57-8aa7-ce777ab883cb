import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { Wallet } from '../wallet/entities/wallet.entity';
import { Transaction } from '../wallet/entities/transaction.entity';
import { Token } from '../token/entities/token.entity';
import { TokenBalance } from '../token/entities/token-balance.entity';
import { SystemConfig } from '../common/entities/system-config.entity';
import { NFT } from '../nft/entities/nft.entity';
import { NFTOrder } from '../nft/entities/nft-order.entity';
import { ApiKey } from '../auth/entities/api-key.entity';
import { StakingTransaction } from '../staking/entities/staking-transaction.entity';
import { StakingInterestHistory } from '../staking/entities/staking-interest-history.entity';
import { Ticket } from '../support/entities/ticket.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Wallet,
      Transaction,
      Token,
      TokenBalance,
      SystemConfig,
      NFT,
      NFTOrder,
      ApiKey,
      StakingTransaction,
      StakingInterestHistory,
      Ticket
    ]),
  ],
  exports: [TypeOrmModule],
})
export class DatabaseModule {}