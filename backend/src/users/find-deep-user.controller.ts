import { Controller, Get, Param } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('tusers')
@Controller('tusers')
export class FindDeepUserController {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  @Get('find-deep-user')
  async findDeepUser() {
    // Tìm người dùng có đường dẫn dài nhất (nhiều dấu / nhất)
    const users = await this.userRepository.createQueryBuilder('user')
      .select(['user.id', 'user.username', 'user.path', 'user.referredBy'])
      .orderBy('LENGTH(user.path)', 'DESC')
      .take(10)
      .getMany();

    // T<PERSON>h độ sâu của mỗi người dùng
    const usersWithDepth = users.map(user => {
      const depth = user.path ? (user.path.match(/\./g) || []).length : 0;
      return {
        id: user.id,
        username: user.username,
        path: user.path,
        referredBy: user.referredBy,
        depth: depth
      };
    });

    return {
      success: true,
      data: usersWithDepth
    };
  }

  @Get('check-referral-path/:userId')
  async checkReferralPath(@Param('userId') userId: string) {
    // Tìm người dùng
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['id', 'username', 'path', 'referredBy']
    });

    if (!user) {
      return {
        success: false,
        message: 'User not found'
      };
    }

    // Tách path thành mảng các ID
    const pathIds = user.path ? user.path.split('.') : [];

    // Lấy thông tin về tất cả người dùng trong đường dẫn
    const referrers = await this.userRepository.createQueryBuilder('user')
      .where('user.id IN (:...ids)', { ids: pathIds })
      .select(['user.id', 'user.username', 'user.referredBy'])
      .getMany();

    // Xây dựng chuỗi giới thiệu
    const referralChain = [];
    let currentId = user.referredBy;
    let level = 1;

    while (currentId && level <= 20) {
      const referrer = referrers.find(r => r.id === currentId);
      if (!referrer) break;

      referralChain.push({
        level: level,
        id: referrer.id,
        username: referrer.username
      });

      currentId = referrer.referredBy;
      level++;
    }

    return {
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          referredBy: user.referredBy
        },
        pathIds: pathIds,
        pathLength: pathIds.length,
        referralChain: referralChain
      }
    };
  }
}
