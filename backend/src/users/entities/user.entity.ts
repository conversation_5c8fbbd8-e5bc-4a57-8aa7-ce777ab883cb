import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToOne, OneToMany, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Wallet } from '../../wallet/entities/wallet.entity';
import { UserRole, UserRank, WManagerRank } from '../../common/enums/user.enum';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'User ID' })
  id: string;

  @Column({ unique: true })
  @ApiProperty({ description: 'Username, generated from wallet address' })
  username: string;

  @Column({ unique: true })
  @ApiProperty({ description: 'Wallet address used for login' })
  wallet: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'User email address', required: false })
  email?: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'User full name', required: false })
  name?: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'User phone number', required: false })
  phone?: string;

  @Column({ type: 'enum', enum: UserRole, default: UserRole.USER })
  @ApiProperty({ description: 'User role', enum: UserRole })
  role: UserRole;

  @Column({ type: 'enum', enum: UserRank, default: UserRank.BRONZE })
  @ApiProperty({ description: 'User rank', enum: UserRank })
  rank: UserRank;

  @Column({ type: 'enum', enum: WManagerRank, nullable: true })
  @ApiProperty({ description: 'W-Manager rank', enum: WManagerRank })
  wManagerRank?: WManagerRank;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user is shareholder' })
  isShareholder: boolean;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 1 })
  @ApiProperty({ description: 'Mining efficiency multiplier' })
  miningMultiplier: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Total mined WM tokens' })
  totalMined: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Total mined WM tokens (backup)' })
  totalMinedBackup: number;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Last mining timestamp', required: false })
  lastMiningTime?: Date;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user has purchased lightning bolt (permanent 2x mining)' })
  hasLightningBolt: boolean;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Amount paid for lightning bolt (in USDT)' })
  lightningBoltPrice: number;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Date when lightning bolt was purchased', required: false })
  lightningBoltPurchaseDate?: Date;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user has deposited minimum 5 USDT' })
  hasMinDeposit: boolean;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 100 })
  @ApiProperty({ description: 'User credits for platform activities' })
  credits: number;

  @Column({ unique: true })
  @ApiProperty({ description: 'Unique referral code for affiliate program' })
  referralCode: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Referrer ID', required: false })
  referredBy?: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'referredBy' })
  @ApiProperty({ description: 'Referrer user object', required: false })
  referrer?: User;

  @OneToMany(() => User, user => user.referrer)
  @ApiProperty({ description: 'List of referred users', type: () => [User] })
  referrals?: User[];

  @Column({ type: 'text', nullable: true })
  @ApiProperty({ description: 'Tree path for efficient tree queries', required: false })
  path?: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'ID of user\'s Web3 wallet', required: false })
  web3WalletId?: string;

  @OneToOne('Wallet', 'user', {
    nullable: true,
    onDelete: 'SET NULL'
  })
  @JoinColumn({ name: 'web3WalletId' })
  @ApiProperty({ description: 'User\'s Web3 wallet', type: () => Wallet })
  web3Wallet?: Wallet;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 1 })
  @ApiProperty({ description: 'Daily mining rate (WM per day)' })
  dailyMiningRate: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Bonus mining from referrals (25% of F1 daily)' })
  referralMiningBonus: number;

  @Column({ type: 'int', default: 0 })
  @ApiProperty({ description: 'Number of completed tasks' })
  completedTasks: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Total earnings from referral program' })
  totalEarnings: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Total earnings from referral program per day' })
  totalEarningsPerDay: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Total earnings from referral program per session' })
  totalEarningsSession: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Total volume from referral program' })
  totalVolume: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Total volume from referral program per day' })
  totalVolumePerDay: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Total volume from referral program per session' })
  totalVolumeSession: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Total sell volume from referral program' })
  totalSellVolume: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Total sell volume from referral program per day' })
  totalSellVolumePerDay: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Total sell volume from referral program per session' })
  totalSellVolumeSession: number;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user has completed F1 buy session' })
  hasF1BuySession: boolean;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user has completed F1 sell session' })
  hasF1SellSession: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: 'First deposit timestamp', required: false })
  firstDepositTime?: Date;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Last withdrawal timestamp', required: false })
  lastWithdrawalTime?: Date;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user is root', required: false })
  isRoot?: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: 'User national ID number', required: false })
  nationalId?: string;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user has completed first profile', required: false })
  isFirstProfileCompleted?: boolean;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user has completed KYC', required: false })
  isKycCompleted?: boolean;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user has completed invite', required: false })
  isInviteCompleted?: boolean;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user has completed NFT', required: false })
  isNftCompleted?: boolean;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user is locked', required: false })
  isLocked?: boolean;

  // Ưu tiên mua trước trong phiên
  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user has priority to buy first in session', required: false })
  isPriorityBuy?: boolean;

  // Các trường cho staking
  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user is active in staking', required: false })
  stakingActive?: boolean;

  @Column({ type: 'int', default: 0 })
  @ApiProperty({ description: 'Number of direct active referrals in staking' })
  stakingDirectActiveCount: number;

  @Column({ type: 'int', default: 0 })
  @ApiProperty({ description: 'Highest rewarded milestone in staking' })
  stakingRewardedMilestone: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Total staking volume' })
  stakingTotalVolume: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Left staking volume' })
  stakingLeftVolume: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Right staking volume' })
  stakingRightVolume: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Total staking commission' })
  stakingTotalCommission: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Direct staking commission' })
  stakingDirectCommission: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Matching staking commission' })
  stakingMatchingCommission: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Rank staking commission' })
  stakingRankCommission: number;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user is shareholder in staking' })
  stakingIsShareholder: boolean;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Total staking investment amount' })
  stakingTotalInvestment: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Total staking earnings (interest + commissions)' })
  stakingTotalEarnings: number;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether user has reached max earnings (300% of investment)' })
  stakingMaxedOut: boolean;

  @CreateDateColumn()
  @ApiProperty({ description: 'User creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'User last update timestamp' })
  updatedAt: Date;
}