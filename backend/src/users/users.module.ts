import { Module, forwardRef } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { FindDeepUserController } from './find-deep-user.controller';
import { DatabaseModule } from '../database/database.module';
import { WalletModule } from '../wallet/wallet.module';
import { TokenModule } from '../token/token.module';
import { ApiKeyModule } from '../auth/api-key.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';

@Module({
  imports: [
    DatabaseModule,
    TypeOrmModule.forFeature([User]),
    forwardRef(() => WalletModule),
    forwardRef(() => TokenModule),
    ApiKeyModule,
  ],
  controllers: [UsersController, FindDeepUserController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}