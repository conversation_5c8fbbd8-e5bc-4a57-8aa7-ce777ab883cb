import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, IsNotEmpty, Matches } from 'class-validator';

export class UpdateKycDto {
  @ApiProperty({ description: 'User email address' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'User full name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'User phone number' })
  @IsString()
  phone: string;

  @ApiProperty({ description: 'User national ID number' })
  @IsString()
  nationalId: string;

  @ApiProperty({ description: 'Transaction hash' })
  @IsString()
  @IsNotEmpty()
  transactionHash: string;
} 