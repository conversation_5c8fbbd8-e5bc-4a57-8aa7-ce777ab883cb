import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { WManagerRank } from '../../common/enums/user.enum';

export class UpdateWManagerRankDto {
  @ApiProperty({ 
    description: 'W-Manager rank',
    enum: WManagerRank,
    required: false,
    example: WManagerRank.WM1
  })
  @IsOptional()
  @IsEnum(WManagerRank)
  wManagerRank?: WManagerRank;
}
