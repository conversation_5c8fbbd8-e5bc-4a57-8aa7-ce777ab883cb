import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, MinL<PERSON>th, IsOptional, IsBoolean, IsDate, IsNumber, IsEnum, IsUUID } from 'class-validator';
import { UserRank } from '../../common/enums/user.enum';

export class UpdateUserDto {
  @ApiProperty({ description: 'User email address', required: false })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({ description: 'User username', required: false })
  @IsString()
  @MinLength(3)
  @IsOptional()
  username?: string;

  @ApiProperty({ description: 'User full name', required: false })
  @IsString()
  @MinLength(3)
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'User password', required: false })
  @IsString()
  @MinLength(6)
  @IsOptional()
  password?: string;

  @ApiProperty({ description: 'User phone number', required: false })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({ description: 'Last mining time', required: false })
  @IsDate()
  @IsOptional()
  lastMiningTime?: Date;

  @ApiProperty({ description: 'Total mined amount', required: false })
  @IsOptional()
  totalMined?: number;

  @ApiProperty({ description: 'User rank', enum: UserRank, required: false })
  @IsEnum(UserRank)
  @IsOptional()
  rank?: UserRank;

  @ApiProperty({ description: 'Whether user has purchased lightning bolt', required: false })
  @IsBoolean()
  @IsOptional()
  hasLightningBolt?: boolean;

  @ApiProperty({ description: 'Amount paid for lightning bolt', required: false })
  @IsNumber()
  @IsOptional()
  lightningBoltPrice?: number;

  @ApiProperty({ description: 'Date when lightning bolt was purchased', required: false })
  @IsDate()
  @IsOptional()
  lightningBoltPurchaseDate?: Date;

  @ApiProperty({ description: 'Mining efficiency multiplier', required: false })
  @IsNumber()
  @IsOptional()
  miningMultiplier?: number;

  @ApiProperty({ description: 'Number of completed tasks', required: false })
  @IsNumber()
  @IsOptional()
  completedTasks?: number;

  @ApiProperty({ description: 'Bonus mining from referrals', required: false })
  @IsNumber()
  @IsOptional()
  referralMiningBonus?: number;

  @ApiProperty({ description: 'Total earnings from referral program', required: false })
  @IsNumber()
  @IsOptional()
  totalEarnings?: number;

  @ApiProperty({ description: 'Whether user has deposited minimum 5 USDT', required: false })
  @IsBoolean()
  @IsOptional()
  hasMinDeposit?: boolean;

  @ApiProperty({ description: 'ID of user\'s Web3 wallet', required: false })
  @IsUUID()
  @IsOptional()
  web3WalletId?: string;

  @ApiProperty({ description: 'First deposit timestamp', required: false })
  @IsDate()
  @IsOptional()
  firstDepositTime?: Date;
} 