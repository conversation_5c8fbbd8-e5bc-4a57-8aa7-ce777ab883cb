import { ApiProperty } from '@nestjs/swagger';
import { WManagerRank } from 'src/common/enums/user.enum';

export class TreeNode {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;
  
  @ApiProperty()
  username: string;

  @ApiProperty()
  walletAddress: string;

  @ApiProperty()
  rank: string;

  @ApiProperty()
  wManagerRank?: WManagerRank;

  @ApiProperty()
  totalMined: number;

  @ApiProperty()
  totalEarnings: number;

  @ApiProperty()
  totalVolume: number;

  @ApiProperty()
  totalVolumePerDay: number;

  @ApiProperty()
  totalEarningsPerDay: number;

  @ApiProperty()
  totalVolumeSession: number;

  @ApiProperty()
  totalEarningsSession: number;

  @ApiProperty()
  level: string;

  @ApiProperty()
  totalReferrals: number;

  @ApiProperty()
  totalReferralsKyc: number;

  @ApiProperty()
  directReferrals: number;

  @ApiProperty()
  directReferralsKyc: number;

  @ApiProperty()
  totalSellVolume: number;

  @ApiProperty()
  totalSellVolumePerDay: number;

  @ApiProperty()
  totalSellVolumeSession: number;

  @ApiProperty()
  hasF1BuySession: boolean;

  @ApiProperty()
  hasF1SellSession: boolean;

  @ApiProperty({ nullable: true })
  firstDepositTime: string | null;

  @ApiProperty()
  joinedAt: string;

  @ApiProperty()
  hasLightningBolt: boolean;

  @ApiProperty({ type: () => [TreeNode] })
  children: TreeNode[];

  @ApiProperty({ nullable: true })
  hasMoreChildren?: boolean;
}

export class TreeStats {
  @ApiProperty()
  totalReferrals: number;

  @ApiProperty()
  totalReferralsKyc: number;

  @ApiProperty()
  totalDirectReferrals: number;

  @ApiProperty()
  totalDirectReferralsKyc: number;

  @ApiProperty()
  totalVolume: number;

  @ApiProperty()
  totalEarnings: number;

  @ApiProperty()
  totalVolumePerDay: number;

  @ApiProperty()
  totalEarningsPerDay: number;

  @ApiProperty()
  totalVolumeSession: number;

  @ApiProperty()
  totalEarningsSession: number;

  @ApiProperty()
  totalVolumeDirectReferrals: number;

  @ApiProperty()
  totalVolumePerDayDirectReferrals: number;

  @ApiProperty()
  totalEarningsDirectReferrals: number;

  @ApiProperty()
  totalEarningsPerDayDirectReferrals: number;

  @ApiProperty()
  totalVolumeSessionDirectReferrals: number;

  @ApiProperty()
  totalEarningsSessionDirectReferrals: number;

  @ApiProperty()
  totalSellVolume: number;

  @ApiProperty()
  totalSellVolumePerDay: number;

  @ApiProperty()
  totalSellVolumeSession: number;

  @ApiProperty()
  hasF1BuySession: boolean;

  @ApiProperty()
  hasF1SellSession: boolean;
}

export class TreeResponseDto {
  @ApiProperty({ type: TreeStats })
  stats: TreeStats;

  @ApiProperty({ type: TreeNode })
  tree: TreeNode;

  @ApiProperty({ type: [TreeNode] })
  directReferrals: TreeNode[];
} 