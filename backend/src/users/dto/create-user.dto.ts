import { ApiProperty } from '@nestjs/swagger';
import { IsE<PERSON>, IsString, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';

export class CreateUserDto {
  @ApiProperty({ description: 'User email address' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'User username' })
  @IsString()
  @MinLength(3)
  username: string;

  @ApiProperty({ description: 'User full name' })
  @IsString()
  @MinLength(3)
  name: string;

  @ApiProperty({ description: 'User password' })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({ description: 'Referrer code (optional)', required: false })
  @IsString()
  @IsOptional()
  referrerCode?: string;
} 