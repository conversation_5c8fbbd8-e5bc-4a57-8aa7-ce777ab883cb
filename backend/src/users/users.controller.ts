import { Controller, Get, UseGuards, Request, InternalServerErrorException, Param, Patch, Body, NotFoundException, BadRequestException, Post, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiHeader } from '@nestjs/swagger';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiResponseDto } from '../common/dto/api-response.dto';
import { TreeResponseDto } from './dto/tree-response.dto';
import { User } from './entities/user.entity';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { UpdateKycDto } from './dto/update-kyc.dto';
import { ApiKeyGuard } from 'src/auth/guards/api-key.guard';
import { UserRole } from '../common/enums/user.enum';
import { UpdateKycApiKeyDto } from './dto/update-kyc.api-key.dto';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UpdateWManagerRankDto } from './dto/update-wmanager-rank.dto';

@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) { }

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user information' })
  @ApiResponse({
    status: 200,
    description: 'Return current user data successfully',
    type: ApiResponseDto
  })
  async getCurrentUser(@Request() req): Promise<ApiResponseDto<User>> {
    try {
      const userId = req.user.id;
      const user = await this.usersService.findOne(userId);

      return ApiResponseDto.success(user, 'Current user retrieved successfully');
    } catch (error) {
      console.error('Error getting current user:', error);
      throw new InternalServerErrorException('Failed to get current user');
    }
  }

  @Patch('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({
    status: 200,
    description: 'Profile updated successfully',
    type: ApiResponseDto
  })
  async updateProfile(
    @Request() req,
    @Body() updateProfileDto: UpdateProfileDto
  ): Promise<ApiResponseDto<User>> {
      const userId = req.user.id;
      const updatedUser = await this.usersService.updateProfile(userId, updateProfileDto);
      return ApiResponseDto.success(updatedUser, 'Profile updated successfully');
  }

  @Patch('kyc')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({
    status: 200,
    description: 'Kyc updated successfully',
    type: ApiResponseDto
  })
  async updateKyc(
    @Request() req,
    @Body() updateKycDto: UpdateKycDto
  ) {
    const userId = req.user.id;
    return await this.usersService.updateKyc(userId, updateKycDto);
  }

  @Post('api-key/kyc')
  @UseGuards(ApiKeyGuard)
  @ApiHeader({ name: 'x-api-key', description: 'API Key' })
  @ApiHeader({ name: 'x-wallet-address', description: 'Wallet Address' })
  @ApiOperation({ summary: 'Update KYC using API Key' })
  @ApiResponse({ status: 200, description: 'Update KYC successful' })
  async updateKycApiKey(@Request() req, @Body() updateKycApiKeyDto: UpdateKycApiKeyDto) {  
    const walletAddress = req.headers['x-wallet-address'];
    if (!walletAddress) {
      throw new BadRequestException('Wallet address is required in x-wallet-address header');
    }
    
    const user = await this.usersService.findByWalletAddress(walletAddress);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const updateKycDto = {
      transactionHash: updateKycApiKeyDto.transactionHash,
      email: user.email,
      name: user.name,
      phone: user.phone,
      nationalId: user.nationalId
    }
    
    return await this.usersService.updateKyc(user.id, updateKycDto);
  }

  @Patch('admin/kyc/:walletAddress')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Admin update KYC for user by wallet address' })
  @ApiResponse({ status: 200, description: 'Update KYC successful' })
  async updateKycAdmin(@Param('walletAddress') walletAddress: string, @Body() updateKycApiKeyDto: UpdateKycApiKeyDto) {
    const user = await this.usersService.findByWalletAddress(walletAddress);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const updateKycDto = {
      transactionHash: updateKycApiKeyDto.transactionHash,
      email: user.email,
      name: user.name,
      phone: user.phone,
      nationalId: user.nationalId
    }

    return await this.usersService.updateKyc(user.id, updateKycDto);
  }

  @Patch('admin/:id/wmanager-rank')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Admin update W-Manager rank for user' })
  @ApiResponse({ status: 200, description: 'W-Manager rank updated successfully' })
  async updateWManagerRank(
    @Param('id') id: string,
    @Body() updateWManagerRankDto: UpdateWManagerRankDto
  ): Promise<ApiResponseDto<User>> {
    const user = await this.usersService.findOne(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const updatedUser = await this.usersService.updateWManagerRank(id, updateWManagerRankDto.wManagerRank);
    return ApiResponseDto.success(updatedUser, 'W-Manager rank updated successfully');
  }

  @Get('tree')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get affiliate tree data including stats and direct referrals' })
  @ApiResponse({
    status: 200,
    description: 'Return tree data successfully',
    type: TreeResponseDto
  })
  async getTreeData(@Request() req): Promise<ApiResponseDto<TreeResponseDto>> {
    try {
      const userId = req.user.id;

      // Get tree data
      const tree = await this.usersService.getReferralTree(userId);

      // Get stats
      const { totalReferrals, totalReferralsKyc, totalDirectReferrals, totalDirectReferralsKyc } = await this.usersService.countAllReferrals(userId);
      const { totalVolume, totalVolumePerDay, totalEarnings, totalEarningsPerDay, totalVolumeSession, totalEarningsSession, totalSellVolume, totalSellVolumePerDay, totalSellVolumeSession, hasF1BuySession, hasF1SellSession } = await this.usersService.getTotalVolumeAndEarnings(userId);

      const directReferrals = await this.usersService.getDirectReferrals(userId, tree);
      let totalVolumeDirectReferrals = 0;
      let totalVolumePerDayDirectReferrals = 0;
      let totalEarningsDirectReferrals = 0;
      let totalEarningsPerDayDirectReferrals = 0;
      let totalVolumeSessionDirectReferrals = 0;
      let totalEarningsSessionDirectReferrals = 0;
      let totalSellVolumeDirectReferrals = 0;
      let totalSellVolumePerDayDirectReferrals = 0;
      let totalSellVolumeSessionDirectReferrals = 0;

      for (const referral of directReferrals) {
        totalVolumeDirectReferrals += referral.totalVolume;
        totalVolumePerDayDirectReferrals += referral.totalVolumePerDay;
        totalEarningsDirectReferrals += referral.totalEarnings;
        totalEarningsPerDayDirectReferrals += referral.totalEarningsPerDay;
        totalVolumeSessionDirectReferrals += referral.totalVolumeSession;
        totalEarningsSessionDirectReferrals += referral.totalEarningsSession;
        totalSellVolumeDirectReferrals += referral.totalSellVolume;
        totalSellVolumePerDayDirectReferrals += referral.totalSellVolumePerDay;
        totalSellVolumeSessionDirectReferrals += referral.totalSellVolumeSession;
      }
      const response: TreeResponseDto = {
        stats: {
          totalReferrals,
          totalReferralsKyc,
          totalDirectReferrals,
          totalDirectReferralsKyc,
          totalVolume: Number(totalVolume || 0),
          totalVolumePerDay: Number(totalVolumePerDay || 0),
          totalEarnings: Number(totalEarnings || 0),
          totalEarningsPerDay: Number(totalEarningsPerDay || 0),
          totalVolumeSession: Number(totalVolumeSession || 0),
          totalEarningsSession: Number(totalEarningsSession || 0),
          totalVolumeDirectReferrals: Number(totalVolumeDirectReferrals || 0),
          totalVolumePerDayDirectReferrals: Number(totalVolumePerDayDirectReferrals || 0),
          totalEarningsDirectReferrals: Number(totalEarningsDirectReferrals || 0),
          totalEarningsPerDayDirectReferrals: Number(totalEarningsPerDayDirectReferrals || 0),
          totalVolumeSessionDirectReferrals: Number(totalVolumeSessionDirectReferrals || 0),
          totalEarningsSessionDirectReferrals: Number(totalEarningsSessionDirectReferrals || 0),
          totalSellVolume: Number(totalSellVolume || 0),
          totalSellVolumePerDay: Number(totalSellVolumePerDay || 0),
          totalSellVolumeSession: Number(totalSellVolumeSession || 0),
          hasF1BuySession,
          hasF1SellSession
        },
        tree,
        directReferrals
      };

      return ApiResponseDto.success(response, 'Tree data retrieved successfully');
    } catch (error) {
      console.error('Error getting tree data:', error);
      throw new InternalServerErrorException('Failed to get tree data');
    }
  }

  @Get('admin/test')
  async adminTest(): Promise<any> {
    return { message: 'Admin test route works!' };
  }

  @Get('test-no-auth')
  async testNoAuth(): Promise<any> {
    return { message: 'Test route without auth works!', timestamp: new Date().toISOString() };
  }

  @Get('debug/me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async debugMe(@Request() req): Promise<any> {
    return {
      message: 'Debug user info',
      user: {
        id: req.user.id,
        email: req.user.email,
        role: req.user.role,
        wallet: req.user.wallet
      },
      timestamp: new Date().toISOString()
    };
  }

  @Get('admin/search')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Admin search user by wallet address or referral code' })
  @ApiResponse({ status: 200, description: 'User found successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async adminSearchUser(@Query('query') query: string): Promise<ApiResponseDto<User>> {
    if (!query || !query.trim()) {
      throw new BadRequestException('Search query is required');
    }

    const searchQuery = query.trim();
    let user: User | null = null;

    try {
      // Try to find by wallet address first
      if (searchQuery.startsWith('0x') && searchQuery.length === 42) {
        try {
          user = await this.usersService.findByWalletAddress(searchQuery);
        } catch (error) {
          // findByWalletAddress throws NotFoundException if not found
          user = null;
        }
      }

      // If not found by wallet, try by referral code
      if (!user) {
        user = await this.usersService.findByReferralCode(searchQuery);
      }

      if (!user) {
        throw new NotFoundException('User not found with the provided wallet address or referral code');
      }

      // Get user with full details including tokenBalances
      const fullUser = await this.usersService.findOne(user.id);

      return ApiResponseDto.success(fullUser, 'User found successfully');
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error in adminSearchUser:', error);
      throw new NotFoundException('User not found with the provided wallet address or referral code');
    }
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({
    status: 200,
    description: 'Return user data successfully',
    type: User
  })
  async findOne(@Param('id') id: string): Promise<ApiResponseDto<User>> {
    try {
      const user = await this.usersService.findOne(id);
      return ApiResponseDto.success(user, 'User retrieved successfully');
    } catch (error) {
      console.error('Error getting user:', error);
      throw new InternalServerErrorException('Failed to get user');
    }
  }
} 