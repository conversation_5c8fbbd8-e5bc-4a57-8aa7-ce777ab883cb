import { HttpService } from '@nestjs/axios';
import { BadRequestException, ConflictException, forwardRef, Inject, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron } from '@nestjs/schedule';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { AxiosResponse } from 'axios';
import BigNumber from 'bignumber.js';
import { isSameDay } from 'date-fns';
import { ethers } from 'ethers';
import moment from 'moment-timezone';
import * as TelegramBot from 'node-telegram-bot-api';
import { TelegramBotService } from 'src/common/services/telegram-bot.service';
import { Web3Service } from 'src/common/services/web3.service';
import { TokenBalance } from 'src/token/entities/token-balance.entity';
import { Token } from 'src/token/entities/token.entity';
import { User } from 'src/users/entities/user.entity';
import { DataSource, QueryRunner, Repository } from 'typeorm';
import { ApiKeyService } from '../auth/services/api-key.service';
import { TELEGRAM_CHAT_IDS } from '../common/constants/telegram.constants';
import { TransactionStatus, TransactionType } from '../common/enums/transaction.enum';
import { SystemConfigService } from '../common/services/system-config.service';
import { getCurrentTime, subtractDays } from "../common/utils/date.utils";
import { SocketGateway } from '../socket/socket.gateway';
import { TokenService } from '../token/token.service';
import { UsersService } from '../users/users.service';
import { AdjustWmDto } from './dto/adjust-wm-wallet.dto';
import { CreateTransactionDto } from './dto/create-transaction.dto';
import { DepositUsdtDto } from './dto/deposit-usdt-wallet.dto';
import { TransferWmDto } from './dto/transfer-wm-wallet.dto';
import { TransferUsdtDto } from './dto/transfer-usdt.dto';
import { UpdateWalletDto } from './dto/update-wallet.dto';
import { WithdrawUsdtDto } from './dto/withdraw-usdt-wallet.dto';
import { TransactionLock } from './entities/transaction-lock.entity';
import { Transaction } from './entities/transaction.entity';
import { Wallet } from './entities/wallet.entity';

@Injectable()
export class WalletService {
  private readonly logger = new Logger(WalletService.name);
  private pendingOrders: Map<string, {
    address: string;
    amount: string;
    orderId: string;
  }> = new Map();

  private readonly bscScanApiUrl = 'https://api.bscscan.com/api';

  constructor(
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
    @InjectRepository(TransactionLock)
    private readonly transactionLockRepository: Repository<TransactionLock>,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
    private readonly tokenService: TokenService,
    private readonly configService: ConfigService,
    private readonly systemConfigService: SystemConfigService,
    @InjectDataSource() public dataSource: DataSource,
    private readonly socketGateway: SocketGateway,
    private readonly httpService: HttpService,
    private readonly telegramBotService: TelegramBotService,
    private readonly web3Service: Web3Service,
    @Inject(forwardRef(() => ApiKeyService))
    private readonly apiKeyService: ApiKeyService,
  ) {
    this.setupTelegramBotListeners();
  }

  // async onModuleInit() {
  //   this.logger.debug('WalletService initializing...');

  //   // Đăng ký lắng nghe sự kiện nạp tiền vào ví hệ thống.
  //   const configDefaultWallet = await this.systemConfigService.findByKey('SYSTEM_PRIMARY_WALLET_ADDRESS');
  //   if (!configDefaultWallet) {
  //     throw new NotFoundException('Cấu hình SYSTEM_PRIMARY_WALLET_ADDRESS hệ thống không tồn tại');
  //   }

  //   await this.web3SocketService.listenToSystemWalletTransfers(configDefaultWallet.value);

  //   // Subscribe để nhận sự kiện Transfer
  //   this.transferSubscription = this.web3SocketService.transferEvents$.subscribe(
  //     (event: TokenTransferEvent) => {
  //       this.handleTokenTransfer(event);
  //     }
  //   );

  //   // Log pending orders khi khởi động
  //   this.logPendingOrders();

  //   this.logger.debug('WalletService initialized');
  // }

  // async onModuleDestroy() {
  //   this.logger.debug('WalletService destroying...');

  //   // Hủy subscription
  //   if (this.transferSubscription) {
  //     this.transferSubscription.unsubscribe();
  //   }

  //   this.logger.debug('WalletService destroyed');
  // }

  private setupTelegramBotListeners() {
    this.telegramBotService.registerCallbackQueryHandler(async (callbackQuery) => {
      try {
        const action = callbackQuery.data;
        const msg = callbackQuery.message;

        if (!action || !msg) {
          this.logger.warn('[setupTelegramBotListeners] Invalid callback query data or message');
          return;
        }

        // Phân tích action theo format: action_type_transactionId
        const [prefix, actionType, transactionId] = action.split('_');

        if (!['approve', 'reject'].includes(prefix)) {
          return;
        }

        if (!prefix || (prefix !== 'approve' && prefix !== 'reject') || !transactionId) {
          this.logger.warn(`[setupTelegramBotListeners] Invalid action format: ${action}`);
          return;
        }

        // Tìm transaction
        const transaction = await this.transactionRepository.findOne({
          where: { id: transactionId }
        });

        if (!transaction) {
          this.logger.warn(`[setupTelegramBotListeners] Transaction ${transactionId} not found`);
          await this.telegramBotService.editMessageText(
            `❌ *ERROR*\n\nTransaction ${transactionId} not found or already processed.`,
            {
              chat_id: msg.chat.id,
              message_id: msg.message_id,
              parse_mode: 'Markdown'
            }
          );
          return;
        }

        // Kiểm tra trạng thái transaction
        if (transaction.status !== TransactionStatus.PENDING) {
          this.logger.warn(`[setupTelegramBotListeners] Transaction ${transactionId} is not in PENDING state`);
          await this.telegramBotService.editMessageText(
            `❌ *ERROR*\n\nTransaction ${transactionId} has already been ${transaction.status.toLowerCase()}.`,
            {
              chat_id: msg.chat.id,
              message_id: msg.message_id,
              parse_mode: 'Markdown'
            }
          );
          return;
        }

        // Xử lý theo action
        if (prefix === 'approve') {
          await this.handleWithdrawalApproval(transaction, msg);
        } else if (prefix === 'reject') {
          await this.handleWithdrawalRejection(transaction, msg);
        }

      } catch (error) {
        this.logger.error(`[setupTelegramBotListeners] Error processing callback query: ${error.message}`, error.stack);
      }
    });
  }

  async checkTransactionStatus(txHash: string, maxRetries = 5, retryDelay = 3000): Promise<any> {
    let retries = 0;

    const checkStatus = async (): Promise<any> => {
      try {
        // Delay 5s trước lần check đầu tiên
        if (retries === 0) {
          this.logger.debug(`[checkTransactionStatus] Đợi 5s trước khi kiểm tra giao dịch ${txHash.substring(0, 10)}...`);
          await new Promise(resolve => setTimeout(resolve, 5000));
        }

        const apiKey = this.configService.get<string>('BSCSCAN_API_KEY');

        const response: AxiosResponse = await this.httpService
          .get(this.bscScanApiUrl, {
            params: {
              module: 'transaction',
              action: 'gettxreceiptstatus',
              txhash: txHash,
              apikey: apiKey,
            },
          })
          .toPromise();

        const result = response.data;

        // Kiểm tra trạng thái giao dịch
        if (!result || result.status !== '1' || (result.result && result.result.status !== '1')) {
          if (retries < maxRetries) {
            retries++;
            this.logger.debug(`[checkTransactionStatus] Giao dịch ${txHash.substring(0, 10)}... chưa được xác nhận, thử lại lần ${retries}/${maxRetries}`);

            // Đợi và thử lại
            await new Promise(resolve => setTimeout(resolve, retryDelay));
            return checkStatus();
          }
        }

        return result;
      } catch (error) {
        if (retries < maxRetries) {
          retries++;
          this.logger.warn(`[checkTransactionStatus] Lỗi kiểm tra giao dịch ${txHash.substring(0, 10)}..., thử lại lần ${retries}/${maxRetries}: ${error.message}`);

          // Đợi và thử lại
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          return checkStatus();
        }

        this.logger.error(`[checkTransactionStatus] Lỗi kiểm tra trạng thái giao dịch sau ${maxRetries} lần thử: ${error.message}`, error.stack);
        throw new Error(`Failed to check transaction status after ${maxRetries} retries: ${error.message}`);
      }
    };

    return checkStatus();
  }

  /**
   * Lấy thông tin ví mặc định của hệ thống
   * @param event Lắng nghe sự kiện Transfer của USDT
   * @returns
   */
  async getDefaultWallet(userId: string): Promise<string> {
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const configDefaultWallet = await this.systemConfigService.findByKey('SYSTEM_PRIMARY_WALLET_ADDRESS');
    if (!configDefaultWallet) {
      throw new NotFoundException('Default wallet address not found');
    }

    return configDefaultWallet.value;
  }

  /**
   * Tạo khóa giao dịch để ngăn xử lý đồng thời
   * @param transactionId ID của giao dịch cần khóa
   * @returns true nếu khóa thành công, false nếu giao dịch đã bị khóa
   */
  async acquireTransactionLock(transactionId: string): Promise<boolean> {
    try {
      // Tạo một ID tiến trình duy nhất
      const processId = `process_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

      // Kiểm tra xem giao dịch đã bị khóa chưa
      const existingLock = await this.transactionLockRepository.findOne({
        where: { transactionId, isActive: true }
      });

      if (existingLock) {
        this.logger.warn(`[acquireTransactionLock] Transaction ${transactionId} is already locked by process ${existingLock.processId}`);
        return false;
      }

      // Tạo khóa mới
      const lock = this.transactionLockRepository.create({
        transactionId,
        processId,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      await this.transactionLockRepository.save(lock);
      this.logger.log(`[acquireTransactionLock] Lock acquired for transaction ${transactionId} by process ${processId}`);

      return true;
    } catch (error) {
      this.logger.error(`[acquireTransactionLock] Error acquiring lock for transaction ${transactionId}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Giải phóng khóa giao dịch
   * @param transactionId ID của giao dịch cần giải phóng khóa
   */
  async releaseTransactionLock(transactionId: string): Promise<void> {
    try {
      await this.transactionLockRepository.update(
        { transactionId, isActive: true },
        { isActive: false, updatedAt: new Date() }
      );

      this.logger.log(`[releaseTransactionLock] Lock released for transaction ${transactionId}`);
    } catch (error) {
      this.logger.error(`[releaseTransactionLock] Error releasing lock for transaction ${transactionId}: ${error.message}`, error.stack);
    }
  }

  async findAll(filters: { isActive?: boolean } = {}): Promise<Wallet[]> {
    return this.walletRepository.find({
      where: { ...filters },
      relations: ['user'],
      order: { createdAt: 'DESC' }
    });
  }

  async findOne(id: string): Promise<Wallet> {
    const wallet = await this.walletRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!wallet) {
      throw new NotFoundException(`Wallet with ID ${id} not found`);
    }

    return wallet;
  }

  async findByAddress(address: string): Promise<Wallet> {
    const wallet = await this.walletRepository.findOne({
      where: { address },
      relations: ['user'],
    });

    if (!wallet) {
      throw new NotFoundException(`Wallet with address ${address} not found`);
    }

    return wallet;
  }

  async findByUserId(userId: string, type?: string): Promise<Wallet> {
    const where: any = { userId };
    if (type) {
      where.type = type;
    }
    const wallet = await this.walletRepository.findOne({ where });
    if (!wallet) {
      throw new NotFoundException(`Wallet for user ${userId} not found`);
    }
    return wallet;
  }

  async update(id: string, updateWalletDto: UpdateWalletDto): Promise<Wallet> {
    const wallet = await this.findOne(id);
    Object.assign(wallet, updateWalletDto);
    return this.walletRepository.save(wallet);
  }

  async remove(id: string): Promise<void> {
    const wallet = await this.findOne(id);
    await this.walletRepository.softDelete(id);
  }

  async createWeb3Wallet(userId: string, walletAddress?: string): Promise<Wallet> {
    try {
      // 1. Kiểm tra xem đã có ví WEB3 chưa?
      this.logger.debug(`[WalletService] Checking if user ${userId} has a Web3 wallet`);
      try {
        const existingWallet = await this.walletRepository.findOne({
          where: { userId, type: 'WEB3' }
        });
        if (existingWallet) {
          return existingWallet;
        }
      } catch (error) {
        // No existing wallet found, continue with creation
      }

      // 2. Nếu chưa có ví WEB3, tạo mới
      this.logger.debug(`[WalletService] Tạo ví WEB3 mới cho người dùng ${userId}`);

      let address;
      let privateKey;

      if (!walletAddress) {
        // Tạo ví mới
        const wallet = ethers.Wallet.createRandom();
        address = wallet.address;
        privateKey = wallet.privateKey;
      } else {
        // Sử dụng ví đã tồn tại
        address = walletAddress;
        privateKey = address;
      }

      // 3. Mã hóa private key
      const encryptedPrivateKey = privateKey;

      // 4. Tạo record ví
      const newWallet = this.walletRepository.create({
        userId,
        address,
        encryptedPrivateKey,
        type: 'WEB3',
        wmBalance: 0,
        usdtBalance: 0,
        isActive: true,
      });

      this.logger.debug(`[WalletService] Ví WEB3 mới đã được tạo: ${newWallet.id}`);

      // 5. Lưu ví
      const savedWallet = await this.walletRepository.save(newWallet);
      this.logger.debug(`[WalletService] Ví WEB3 đã được lưu: ${savedWallet.id}`);

      // 6. Cập nhật user với ID ví
      const user = await this.usersService.findOne(userId);
      user.web3WalletId = savedWallet.id;
      await this.usersService.update(user.id, { web3WalletId: savedWallet.id });
      this.logger.debug(`[WalletService] Người dùng đã được cập nhật với ID ví WEB3: ${savedWallet.id}`);

      // 7. Lấy token USDT
      const usdtToken = await this.tokenService.findBySymbol('USDT');
      if (!usdtToken) {
        this.logger.error(`[WalletService] Token USDT không tồn tại`);
        throw new Error('USDT token not found');
      }
      this.logger.debug(`[WalletService] Token USDT đã được tìm thấy: ${usdtToken.id}`);

      // 8. Tạo TokenBalance cho USDT
      try {
        await this.tokenService.createTokenBalance({
          userId,
          tokenId: usdtToken.id,
          availableBalance: 0,
          lockedBalance: 0
        });
        this.logger.log(`Đã tạo TokenBalance USDT cho người dùng ${userId}`);
      } catch (error) {
        console.error(`Lỗi tạo TokenBalance USDT cho người dùng ${userId}:`, error);
        // Don't throw error here as wallet is already created
      }

      this.logger.debug(`[WalletService] TokenBalance USDT đã được tạo cho người dùng ${userId}`);

      // 9. Lấy token WM
      const wmToken = await this.tokenService.findBySymbol('WM');
      if (!wmToken) {
        this.logger.error(`[WalletService] Token WM không tồn tại`);
        throw new Error('WM token not found');
      }
      this.logger.debug(`[WalletService] Token WM đã được tìm thấy: ${wmToken.id}`);

      // 10. Tạo TokenBalance cho WM
      try {
        await this.tokenService.createTokenBalance({
          userId,
          tokenId: wmToken.id,
          availableBalance: 0,
          lockedBalance: 0
        });
        this.logger.log(`Đã tạo TokenBalance WM cho người dùng ${userId}`);
      } catch (error) {
        console.error(`Lỗi tạo TokenBalance WM cho người dùng ${userId}:`, error);
      }

      this.logger.debug(`[WalletService] TokenBalance WM đã được tạo cho người dùng ${userId}`);

      return savedWallet;
    } catch (error) {
      this.logger.error(`[WalletService] Lỗi tạo ví WEB3: ${error.message}`, error.stack);
      throw new Error('Failed to create Web3 wallet');
    }
  }

  async createTransaction(createTransactionDto: CreateTransactionDto): Promise<Transaction> {
    const { walletId, userId, tokenId, type, amount, status, reference, note, recipientAddress, senderAddress, txHash } = createTransactionDto;

    // Verify wallet exists
    const wallet = await this.findOne(walletId);

    // Create transaction
    const transaction = this.transactionRepository.create({
      walletId,
      userId: userId || wallet.userId,
      tokenId,
      type,
      amount,
      status: status,
      reference,
      note,
      recipientAddress,
      senderAddress,
      txHash,
      transactionAt: moment().toDate(),
      createdAt: moment().toDate(),
      updatedAt: moment().toDate(),
    });

    // Save transaction
    const savedTransaction = await this.transactionRepository.save(transaction);

    return savedTransaction;
  }

  async getTransactions(
    walletId: string,
    filters: { status?: TransactionStatus; type?: TransactionType } = {}
  ): Promise<Transaction[]> {
    return this.transactionRepository.find({
      where: { walletId, ...filters },
      order: { createdAt: 'DESC' },
      relations: ['wallet'],
    });
  }

  async getTransactionsByUserId(
    userId: string,
    filters: { status?: TransactionStatus; type?: TransactionType } = {}
  ): Promise<Transaction[]> {
    // Tìm các giao dịch của user
    return this.transactionRepository.find({
      where: {
        userId,
        ...filters
      },
      order: { createdAt: 'DESC' },
      relations: ['wallet', 'token'],
    });
  }

  async mineWm(userId: string) {
    // Khởi tạo queryRunner
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Sử dụng một giá trị thời gian nhất quán
      const now = getCurrentTime();

      // 1. Lấy user và ví
      const user = await queryRunner.manager.findOne(User, {
        where: { id: userId }
      });
      if (!user) {
        throw new NotFoundException({
          message: { en: 'User not found' },
          code: 'USER_NOT_FOUND'
        });
      }

      const wallet = await queryRunner.manager.findOne(Wallet, {
        where: { userId }
      });
      if (!wallet) {
        throw new NotFoundException({
          message: { en: 'Wallet not found' },
          code: 'WALLET_NOT_FOUND'
        });
      }

      // 2. Kiểm tra mining hôm nay
      if (user.lastMiningTime && isSameDay(user.lastMiningTime, now)) {
        throw new BadRequestException({
          message: { en: 'You have already mined today' },
          code: 'ALREADY_MINED_TODAY'
        });
      }

      // 3. Kiểm tra phân bổ khai thác
      const totalMined = await this.systemConfigService.getTotalWmMinedWithQueryRunner(queryRunner);
      const miningAllocationConfig = await this.systemConfigService.findByKeyWithQueryRunner(queryRunner, 'MINING_ALLOCATION');
      const miningAllocation = parseFloat(miningAllocationConfig.value);

      if (totalMined >= miningAllocation) {
        throw new BadRequestException({
          message: { en: 'Mining allocation has been exhausted' },
          code: 'MINING_ALLOCATION_EXHAUSTED'
        });
      }

      // 4. Lấy độ khó khai thác
      const difficulty = await this.systemConfigService.checkAndUpdateMiningDifficultyWithQueryRunner(queryRunner);

      // 5. Tính toán số lượng
      const baseAmount = 0.25;
      const miningAmount = (baseAmount / difficulty) * user.miningMultiplier;

      // 6. Lấy token WM
      const wmToken = await this.tokenService.findBySymbol('WM');
      if (!wmToken) {
        throw new NotFoundException({
          message: { en: 'WM token not found' },
          code: 'WM_TOKEN_NOT_FOUND'
        });
      }

      // 8. Cập nhật thông tin user
      await queryRunner.manager.update(User, user.id, {
        lastMiningTime: now,
        totalMined: Number(user.totalMined) + Number(miningAmount)
      });

      // 9. Tạo transaction record
      const miningTransaction = queryRunner.manager.create(Transaction, {
        walletId: wallet.id,
        userId: user.id,
        tokenId: wmToken.id,
        type: TransactionType.MINING,
        amount: Number(miningAmount),
        status: TransactionStatus.COMPLETED,
        note: `Daily mining (+${miningAmount}WM)`,
        transactionAt: now,
        createdAt: now,
        updatedAt: now
      });

      const savedMiningTransaction = await queryRunner.manager.save(Transaction, miningTransaction);

      // 7. Cập nhật token với số lượng khai thác
      await this.updateTokenBalanceWithQueryRunner(queryRunner, wallet, wmToken, 'wmBalance', savedMiningTransaction, Number(miningAmount), 'add', now);

      // 10. Cập nhật thống kê hệ thống
      await this.systemConfigService.incrementTotalWmMinedWithQueryRunner(queryRunner, Number(miningAmount));
      const newDifficulty = await this.systemConfigService.checkAndUpdateMiningDifficultyWithQueryRunner(queryRunner);

      // 11. Xử lý bonus tham gia
      let referralBonus = 0;
      if (user.referredBy) {
        const referrer = await queryRunner.manager.findOne(User, {
          where: { id: user.referredBy }
        });

        if (referrer) {
          const referrerWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: referrer.id } });
          // Tính 25% bonus
          referralBonus = Number(miningAmount) * 0.25;

          // Cập nhật thống kê referrer
          await queryRunner.manager.update(User, referrer.id, {
            referralMiningBonus: Number(referrer.referralMiningBonus) + Number(referralBonus),
            totalEarnings: Number(referrer.totalEarnings) + Number(referralBonus),
            updatedAt: now
          });

          // Tạo transaction record cho referral bonus
          const referralBonusTransaction = queryRunner.manager.create(Transaction, {
            walletId: referrerWallet.id,
            userId: referrer.id,
            tokenId: wmToken.id,
            type: TransactionType.REFERRAL_BONUS_MINING,
            amount: Number(referralBonus),
            status: TransactionStatus.COMPLETED,
            reference: user.id,
            note: `Referral bonus mining from ${wallet.address} (+${referralBonus}WM)`,
            transactionAt: now,
            createdAt: now,
            updatedAt: now
          });

          const savedReferralBonusTransaction = await queryRunner.manager.save(Transaction, referralBonusTransaction);

          // Cập nhật số dư cho referrer vào tài khoản locked của ví
          await this.updateTokenBalanceWithQueryRunner(queryRunner, referrerWallet, wmToken, 'wmLockedBalance', savedReferralBonusTransaction, Number(referralBonus), 'add', now);
        }
      }

      // Commit transaction
      await queryRunner.commitTransaction();

      // Return kết quả
      return {
        success: true,
        amount: miningAmount,
        newBalance: wallet.wmBalance.toString(),
        totalMined: Number(user.totalMined) + Number(miningAmount),
        rank: user.rank,
        difficulty,
        nextDifficulty: newDifficulty,
        referralBonus: Number(referralBonus),
        lastMiningTime: now,
        miningMultiplier: Number(user.miningMultiplier),
        dailyMiningRate: Number(baseAmount / difficulty),
        referralMiningBonus: Number(user.referralMiningBonus) + (user.referredBy ? Number(referralBonus) : 0),
        hasLightningBolt: user.hasLightningBolt,
        message: `Successfully mined ${miningAmount} WM tokens`
      };

    } catch (error) {
      // Rollback nếu có lỗi
      await queryRunner.rollbackTransaction();

      this.logger.error(`[mineWm] Error: ${error.message}`, {
        userId,
        error: error.stack
      });

      throw error;

    } finally {
      // Release queryRunner
      await queryRunner.release();
    }
  }

  async completeTask(userId: string, taskId: string, rewardAmount: number): Promise<Transaction> {
    // 1. Get user, wallet and WM token
    const user = await this.usersService.findOne(userId);
    const wallet = await this.findByUserId(userId);
    const wmToken = await this.tokenService.findBySymbol('WM');

    // 2. Update user's task stats
    user.completedTasks += 1;
    await this.usersService.update(user.id, { completedTasks: user.completedTasks });

    // 3. Update token balance
    await this.updateTokenBalance(wallet.id, wmToken, rewardAmount, 'add');

    // 4. Create transaction record
    const transaction = await this.createTransaction({
      walletId: wallet.id,
      userId: user.id,
      tokenId: wmToken.id,
      type: TransactionType.TASK_REWARD,
      amount: rewardAmount,
      status: TransactionStatus.COMPLETED,
      reference: taskId,
      note: `Reward for completing task ${taskId}`
    });

    this.logger.debug(`[WalletService][completeTask] Đã tạo giao dịch ${TransactionType.TASK_REWARD} cho ${user.id} từ ${user.id}, Số tiền ${rewardAmount} WM`);

    return transaction;
  }

  async getTransactionHistory(
    userId: string,
    page: number = 1,
    limit: number = 10,
    type?: TransactionType | 'ALL'
  ): Promise<{ transactions: Transaction[]; total: number; page: number; limit: number }> {
    this.logger.debug(`[WalletService][getTransactionHistory] Lấy lịch sử giao dịch cho ${userId}, Trang ${page}, Giới hạn ${limit}, Loại ${type}`);

    const [transactions, total] = await this.transactionRepository.findAndCount({
      where: {
        userId,
        ...(type && type !== 'ALL' ? { type } : {})
      },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
      relations: ['token']
    });

    return {
      transactions,
      total,
      page,
      limit
    };
  }

  async getTokenBalance(walletId: string, tokenType: string): Promise<number> {
    try {
      const wallet = await this.walletRepository.findOne({
        where: { id: walletId },
      });

      if (!wallet) {
        throw new NotFoundException(`Wallet with ID ${walletId} not found`);
      }

      const balance = tokenType === 'WM' ? wallet.wmBalance : wallet.usdtBalance;
      return balance ? balance : 0;
    } catch (error) {
      this.logger.error(`Error getting ${tokenType} balance for wallet ${walletId}:`, error);
      throw new InternalServerErrorException(`Failed to get ${tokenType} balance`);
    }
  }

  async updateTokenBalanceWithQueryRunner(
    queryRunner: QueryRunner,
    wallet: Wallet,
    token: Token,
    balanceField: 'wmBalance' | 'usdtBalance' | 'wmLockedBalance',
    transaction: Transaction,
    amount: number,
    type: 'add' | 'subtract',
    now?: Date,
  ): Promise<void> {
    const currentTime = now || getCurrentTime();

    const amountBigNumber = new BigNumber(amount);
    let currentBalanceBigNumber = new BigNumber(wallet[balanceField] || 0);

    // Tính toán số dư mới
    let newBalance: BigNumber;
    if (type === 'add') {
      newBalance = currentBalanceBigNumber.plus(amountBigNumber);
    } else if (type === 'subtract') {
      if (currentBalanceBigNumber.lt(amountBigNumber)) {
        currentBalanceBigNumber = amountBigNumber;
      }
      newBalance = currentBalanceBigNumber.minus(amountBigNumber);
    }

    // Cập nhật số dư trong ví
    wallet[balanceField] = newBalance.toNumber();
    wallet.updatedAt = currentTime;

    // Lưu ví đã cập nhật
    await queryRunner.manager.save(wallet);

    // Tìm kiếm TokenBalance
    let tokenBalance = await queryRunner.manager.findOne(TokenBalance, {
      where: { userId: wallet.userId, tokenId: token.id },
    });

    // Nếu không tìm thấy TokenBalance, tạo mới
    if (!tokenBalance) {
      tokenBalance = new TokenBalance();
      tokenBalance.userId = wallet.userId;
      tokenBalance.tokenId = token.id;
      tokenBalance.createdAt = currentTime;
    }

    // Cập nhật TokenBalance
    if (balanceField === 'wmLockedBalance' && token.symbol === 'WM') {
      tokenBalance.lockedBalance = wallet[balanceField];
    } else {
      tokenBalance.availableBalance = wallet[balanceField];
    }

    tokenBalance.totalBalance = tokenBalance.availableBalance;
    tokenBalance.updatedAt = currentTime;

    // Lưu TokenBalance
    await queryRunner.manager.save(TokenBalance, tokenBalance);

    // Gửi thông báo cho quản trị qua telegram
    const isTelegramNotification = false;
    if (isTelegramNotification) {
      try {
        const balanceChangeMessage = `💰 *BALANCE UPDATE NOTIFICATION*

🔄 *Transaction Type:* ${transaction.type}
🔄 *Transaction ID:* ${transaction.id}
💎 *Token:* ${token.symbol}
👛 *Wallet ID:* \`${wallet.address}\`

📊 *Balance Changes:*
• Previous: \`${currentBalanceBigNumber.toString()} ${token.symbol}\`
• Change: \`${type === 'add' ? '+' : '-'}${amountBigNumber.toString()} ${token.symbol}\`
• Current: \`${newBalance.toString()} ${token.symbol}\`

⏰ *Time:* \`${moment().format('YYYY-MM-DD HH:mm:ss')}\`

${transaction.type} *Context Details:*
${transaction.note}`;

        await Promise.all(
          TELEGRAM_CHAT_IDS.map(chatId =>
            this.telegramBotService.sendMessage(
              chatId,
              balanceChangeMessage,
              {
                parse_mode: 'Markdown',
                disable_web_page_preview: true
              }
            ).catch(error => {
              this.logger.warn(`Failed to send balance update to chat ${chatId}: ${error.message}`);
            })
          )
        );
      } catch (error) {
        this.logger.error(`[updateTokenBalanceWithQueryRunner] Lỗi gửi thông báo balance update: ${error.message}`, {
          error: error.stack
        });
      }
    }
  }

  async updateTokenBalance(walletId: string, token: Token, amount: number, type: 'add' | 'subtract'): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const wallet = await this.walletRepository.findOne({
        where: { id: walletId },
      });

      if (!wallet) {
        throw new NotFoundException(`Wallet with ID ${walletId} not found`);
      }

      console.log(`[UpdateWallet] DB wmBalance: ${wallet.wmBalance}, amount: ${amount}, type: ${type}`);

      const adjustedAmount = new BigNumber(amount);
      console.log(`[UpdateWallet] Adjusted amount: ${adjustedAmount.toString()}`);

      if (type === 'add') {
        if (token.symbol === 'WM') {
          const newBalance = new BigNumber(wallet.wmBalance).plus(adjustedAmount);
          console.log(`[UpdateWallet] New wmBalance: ${newBalance.toString()}`);
          wallet.wmBalance = newBalance.toNumber();
        } else if (token.symbol === 'USDT') {
          const newBalance = new BigNumber(wallet.usdtBalance).plus(adjustedAmount);
          wallet.usdtBalance = newBalance.toNumber();
        }
      } else if (type === 'subtract') {
        if (token.symbol === 'WM') {
          const newBalance = new BigNumber(wallet.wmBalance).minus(adjustedAmount);
          if (newBalance.lt(0)) throw new Error('Insufficient WM balance');
          wallet.wmBalance = newBalance.toNumber();
        } else if (token.symbol === 'USDT') {
          const newBalance = new BigNumber(wallet.usdtBalance).minus(adjustedAmount);
          if (newBalance.lt(0)) throw new Error('Insufficient USDT balance');
          wallet.usdtBalance = newBalance.toNumber();
        }
      }

      console.log(`[UpdateWallet] Saving wallet with wmBalance: ${wallet.wmBalance}`);

      await this.walletRepository.save(wallet);

      // Cập nhật số dư token balance
      await this.tokenService.updateTokenBalance(wallet.userId, token.id, amount, type);

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error updating ${token.symbol} balance for wallet ${walletId}:`, error);
      throw new InternalServerErrorException(`Failed to update ${token.symbol} balance: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  async updateTokenBalanceBySymbol(walletId: string, symbol: string, amount: number, type: 'add' | 'subtract'): Promise<void> {
    const token = await this.tokenService.findBySymbol(symbol);
    if (!token) {
      throw new NotFoundException(`Token with symbol ${symbol} not found`);
    }
    await this.updateTokenBalance(walletId, token, amount, type);
  }

  async getWMBalance(userId: string): Promise<number> {
    const wallet = await this.findByUserId(userId);
    return this.getTokenBalance(wallet.id, 'WM');
  }

  async getUSDTBalance(userId: string): Promise<number> {
    const wallet = await this.findByUserId(userId);
    return this.getTokenBalance(wallet.id, 'USDT');
  }

  async purchaseLightning(userId: string, transactionHash: string) {
    this.logger.log(`[purchaseLightning] Xử lý mua tia sét cho người dùng ${userId}`);

    // Kiểm tra transaction hash
    if (!transactionHash) {
      throw new BadRequestException({
        message: {
          en: 'Transaction hash is required',
        },
        code: 'TRANSACTION_HASH_REQUIRED',
      });
    }

    // Kiểm tra trạng thái giao dịch blockchain
    const transactionStatus = await this.checkTransactionStatus(transactionHash);
    if (!transactionStatus?.result?.status || transactionStatus.result.status !== '1') {
      this.logger.warn(`[purchaseLightning] Giao dịch ${transactionHash} không được xác nhận`);
      throw new BadRequestException({
        message: {
          en: 'Transaction is not confirmed or invalid response',
        },
        code: 'TRANSACTION_NOT_CONFIRMED',
      });
    }

    // 1. Lấy thông tin user và ví
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException({
        message: {
          en: 'User not found'
        },
        code: 'USER_NOT_FOUND'
      });
    }

    // Kiểm tra xem user đã có tia sét chưa
    if (user.hasLightningBolt) {
      this.logger.warn(`[purchaseLightning] Người dùng ${userId} đã có tia sét`);
      throw new BadRequestException({
        message: {
          en: 'You already have a lightning bolt'
        },
        code: 'LIGHTNING_BOLT_ALREADY_PURCHASED'
      });
    }

    const wallet = await this.findByUserId(userId);
    if (!wallet) {
      throw new NotFoundException({
        message: {
          en: 'Wallet not found'
        },
        code: 'WALLET_NOT_FOUND'
      });
    }

    // 2. Lấy cấu hình giá tia sét
    const price = 15;

    // 3. Kiểm tra token USDT
    const usdtToken = await this.tokenService.findBySymbol('USDT');
    if (!usdtToken) {
      throw new NotFoundException({
        message: {
          en: 'USDT token not found'
        },
        code: 'USDT_TOKEN_NOT_FOUND'
      });
    }

    // 4. Kiểm tra token WM
    const wmToken = await this.tokenService.findBySymbol('WM');
    if (!wmToken) {
      throw new NotFoundException({
        message: {
          en: 'WM token not found'
        },
        code: 'WM_TOKEN_NOT_FOUND'
      });
    }

    // 4. Bắt đầu transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Sử dụng một giá trị thời gian nhất quán
      const now = getCurrentTime();

      // Cập nhật thông tin user
      user.hasLightningBolt = true;
      user.miningMultiplier = 2; // x2 hệ số mining
      user.updatedAt = now;
      await queryRunner.manager.save(user);

      // Tạo transaction record cho việc mua tia sét
      const transaction = await queryRunner.manager.save(Transaction, {
        walletId: wallet.id,
        userId: user.id,
        tokenId: usdtToken.id,
        type: TransactionType.LIGHTNING_BOLT_PURCHASE,
        amount: -price,
        status: TransactionStatus.COMPLETED,
        note: `Purchased Lightning Bolt for ${price} USDT`,
        txHash: transactionHash,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      // Tạo transaction record +5 WM cho việc mua tia sét
      const bonusWM = 5;
      const transactionWM = await queryRunner.manager.save(Transaction, {
        walletId: wallet.id,
        userId: user.id,
        tokenId: wmToken.id,
        type: TransactionType.LIGHTNING_BOLT_PURCHASE,
        amount: bonusWM,
        status: TransactionStatus.COMPLETED,
        note: `Bonus purchased Lightning Bolt for (+${price} WM)`,
        txHash: transactionHash,
        reference: transaction.id,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      // Cập nhật số dư WM cho việc thưởng mua tia sét
      await this.updateTokenBalanceWithQueryRunner(queryRunner, wallet, wmToken, 'wmBalance', transactionWM, price, 'add', now);

      // Cập nhật hoa hồng hệ thống 0.5 USDT/đời, hoa hồng 10 đời
      await this.usersService.updateSystemCommission(queryRunner, user, usdtToken, 'lightning');

      await queryRunner.commitTransaction();

      // 8. Emit event thông báo mua tia sét thành công
      this.socketGateway.server.emit('lightning.purchase.completed', {
        userId: user.id,
        price,
        totalSold: 1,
        purchasedAt: now
      });

      // 9. Trả về kết quả
      return {
        userId: user.id,
        price,
        totalSold: 1,
        purchasedAt: now,
        success: true
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`[purchaseLightning] Lỗi mua tia sét: ${error.message}`, {
        userId,
        transactionHash,
        error: error.stack
      });

      // Xử lý lỗi chi tiết hơn
      if (error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException) {
        throw error; // Giữ nguyên lỗi đã xác định
      }

      throw new InternalServerErrorException({
        message: {
          en: `Failed to purchase lightning bolt. Please try again later.`,
        },
        code: 'LIGHTNING_BOLT_PURCHASE_FAILED'
      });
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Mua vị trí (ưu tiên)
   * Mô tả: Người dùng thanh toán 5 USDT để mua vị trí (ưu tiên)
   * - Tạo giao dịch
   * - Cập nhật thông tin user (firstDepositTime -> now)
   * - Trả về kết quả
   */
  async purchasePriority(userId: string, transactionHash: string) {
    this.logger.log(`[purchasePriority] Xử lý mua vị trí (ưu tiên) cho người dùng ${userId}`);

    // Kiểm tra transaction hash
    if (!transactionHash) {
      throw new BadRequestException({
        message: {
          en: 'Transaction hash is required',
        },
        code: 'TRANSACTION_HASH_REQUIRED',
      });
    }

    // Kiểm tra trạng thái giao dịch blockchain
    const transactionStatus = await this.checkTransactionStatus(transactionHash);
    if (!transactionStatus?.result?.status || transactionStatus.result.status !== '1') {
      this.logger.warn(`[purchasePriority] Giao dịch ${transactionHash} không được xác nhận`);
      throw new BadRequestException({
        message: {
          en: 'Transaction is not confirmed or invalid response',
        },
        code: 'TRANSACTION_NOT_CONFIRMED',
      });
    }

    // 1. Lấy thông tin user và ví
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException({
        message: {
          en: 'User not found'
        },
        code: 'USER_NOT_FOUND'
      });
    }

    // Kiểm tra xem user đã mua vị trí (ưu tiên) chưa
    if (user.firstDepositTime) {
      this.logger.warn(`[purchasePriority] Người dùng ${userId} đã mua vị trí (ưu tiên)`);
      throw new BadRequestException({
        message: {
          en: 'You already have a priority position'
        },
        code: 'PRIORITY_POSITION_ALREADY_PURCHASED'
      });
    }

    const wallet = await this.findByUserId(userId);
    if (!wallet) {
      throw new NotFoundException({
        message: {
          en: 'Wallet not found'
        },
        code: 'WALLET_NOT_FOUND'
      });
    }

    // 2. Lấy giá priority từ system config
    const priorityPriceConfig = await this.systemConfigService.findByKey('PRIORITY_POSITION_PRICE');
    const price = priorityPriceConfig ? parseFloat(priorityPriceConfig.value) : 5;

    // 3. Kiểm tra token USDT
    const usdtToken = await this.tokenService.findBySymbol('USDT');
    if (!usdtToken) {
      throw new NotFoundException({
        message: {
          en: 'USDT token not found'
        },
        code: 'USDT_TOKEN_NOT_FOUND'
      });
    }

    // 4. Bắt đầu transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Sử dụng một giá trị thời gian nhất quán
      const now = getCurrentTime();

      // 5. Tạo transaction record cho việc mua priority
      const transaction = await queryRunner.manager.save(Transaction, {
        walletId: wallet.id,
        userId: user.id,
        tokenId: usdtToken.id,
        type: TransactionType.PRIORITY_POSITION_PURCHASE,
        amount: -price,
        status: TransactionStatus.COMPLETED,
        note: `Purchased Priority Position for ${price} USDT`,
        txHash: transactionHash,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      // 6. Cập nhật thông tin user
      user.firstDepositTime = now;
      user.updatedAt = now;
      await queryRunner.manager.save(user);

      await queryRunner.commitTransaction();

      // 7. Emit event thông báo mua priority thành công
      this.socketGateway.server.emit('priority.purchase.completed', {
        userId: user.id,
        price,
        purchaseTime: now
      });

      // 8. Trả về kết quả
      return {
        success: true,
        userId: user.id,
        price,
        purchaseTime: now,
        message: {
          en: 'Priority position purchased successfully',
        }
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`[purchasePriority] Lỗi mua vị trí (ưu tiên): ${error.message}`, {
        userId,
        transactionHash,
        error: error.stack
      });

      // Xử lý lỗi chi tiết hơn
      if (error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException) {
        throw error; // Giữ nguyên lỗi đã xác định
      }

      throw new InternalServerErrorException({
        message: {
          en: `Failed to purchase priority position. Please try again later.`,
        },
        code: 'PRIORITY_POSITION_PURCHASE_FAILED'
      });
    } finally {
      await queryRunner.release();
    }
  }

  async depositUsdt(userId: string, depositUsdtDto: DepositUsdtDto) {
    this.logger.log(`[depositUsdt] Xử lý nạp USDT cho người dùng ${userId}`);

    const amount = depositUsdtDto.amount;
    const transactionHash = depositUsdtDto.transactionHash;

    // Kiểm tra đầu vào
    if (!amount || amount <= 0) {
      throw new BadRequestException({
        message: {
          en: 'Amount must be greater than 0',
        },
        code: 'INVALID_AMOUNT',
      });
    }

    if (!transactionHash) {
      throw new BadRequestException({
        message: {
          en: 'Transaction hash is required',
        },
        code: 'TRANSACTION_HASH_REQUIRED',
      });
    }

    // Kiểm tra trạng thái giao dịch blockchain
    const transactionStatus = await this.checkTransactionStatus(transactionHash);
    if (!transactionStatus?.result?.status || transactionStatus.result.status !== '1') {
      this.logger.warn(`[depositUsdt] Giao dịch ${transactionHash} không được xác nhận`);
      throw new BadRequestException({
        message: {
          en: 'Transaction is not confirmed or invalid response',
        },
        code: 'TRANSACTION_NOT_CONFIRMED',
      });
    }

    // 1. Lấy thông tin user và ví
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException({
        message: {
          en: 'User not found',
        },
        code: 'USER_NOT_FOUND'
      });
    }

    const wallet = await this.findByUserId(userId);
    if (!wallet) {
      throw new NotFoundException({
        message: {
          en: 'Wallet not found',
        },
        code: 'WALLET_NOT_FOUND'
      });
    }

    const usdtToken = await this.tokenService.findBySymbol('USDT');
    if (!usdtToken) {
      throw new NotFoundException({
        message: {
          en: 'USDT token not found',
        },
        code: 'USDT_TOKEN_NOT_FOUND'
      });
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Sử dụng một giá trị thời gian nhất quán
      const now = getCurrentTime();

      const transaction = queryRunner.manager.create(Transaction, {
        walletId: wallet.id,
        userId: user.id,
        tokenId: usdtToken.id,
        type: TransactionType.DEPOSIT,
        amount: amount,
        status: TransactionStatus.COMPLETED,
        note: `Deposit USDT (+${amount} USDT)`,
        txHash: transactionHash,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      const savedTransaction = await queryRunner.manager.save(Transaction, transaction);

      await this.updateTokenBalanceWithQueryRunner(queryRunner, wallet, usdtToken, 'usdtBalance', savedTransaction, amount, 'add', now);

      await queryRunner.commitTransaction();

      this.socketGateway.server.emit('deposit.usdt.completed', {
        userId: user.id,
        amount,
        transactionHash,
        transactionId: transaction.id,
        timestamp: now
      });

      return {
        success: true,
        userId: user.id,
        amount,
        transactionHash,
        transactionId: transaction.id,
        timestamp: now
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`[depositUsdt] Lỗi nạp USDT: ${error.message}`, {
        userId,
        amount,
        transactionHash,
        error: error.stack
      });

      // Xử lý lỗi chi tiết hơn
      if (error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException) {
        throw error; // Giữ nguyên lỗi đã xác định
      }

      throw new InternalServerErrorException({
        message: {
          en: `Failed to deposit USDT. Please try again later.`,
        },
        code: 'DEPOSIT_USDT_FAILED'
      });
    } finally {
      await queryRunner.release();
    }
  }

  async depositWm(userId: string, depositWmDto: DepositUsdtDto) {
    this.logger.log(`[depositWm] Xử lý nạp WM cho người dùng ${userId}`);

    const amount = depositWmDto.amount;
    const transactionHash = depositWmDto.transactionHash;

    // Kiểm tra đầu vào
    if (!amount || amount <= 0) {
      throw new BadRequestException({
        message: {
          en: 'Amount must be greater than 0',
        },
        code: 'INVALID_AMOUNT',
      });
    }

    if (!transactionHash) {
      throw new BadRequestException({
        message: {
          en: 'Transaction hash is required',
        },
        code: 'TRANSACTION_HASH_REQUIRED',
      });
    }

    // Kiểm tra trạng thái giao dịch blockchain
    const transactionStatus = await this.checkTransactionStatus(transactionHash);
    if (!transactionStatus?.result?.status || transactionStatus.result.status !== '1') {
      this.logger.warn(`[depositUsdt] Giao dịch ${transactionHash} không được xác nhận`);
      throw new BadRequestException({
        message: {
          en: 'Transaction is not confirmed or invalid response',
        },
        code: 'TRANSACTION_NOT_CONFIRMED',
      });
    }

    // 1. Lấy thông tin user và ví
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException({
        message: {
          en: 'User not found',
        },
        code: 'USER_NOT_FOUND'
      });
    }

    const wallet = await this.findByUserId(userId);
    if (!wallet) {
      throw new NotFoundException({
        message: {
          en: 'Wallet not found',
        },
        code: 'WALLET_NOT_FOUND'
      });
    }

    const wmToken = await this.tokenService.findBySymbol('WM');
    if (!wmToken) {
      throw new NotFoundException({
        message: {
          en: 'USDT token not found',
        },
        code: 'WM_TOKEN_NOT_FOUND'
      });
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Sử dụng một giá trị thời gian nhất quán
      const now = getCurrentTime();

      const transaction = queryRunner.manager.create(Transaction, {
        walletId: wallet.id,
        userId: user.id,
        tokenId: wmToken.id,
        type: TransactionType.DEPOSIT,
        amount: amount,
        status: TransactionStatus.COMPLETED,
        note: `Deposit WM (+${amount} WM)`,
        txHash: transactionHash,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      const savedTransaction = await queryRunner.manager.save(Transaction, transaction);

      await this.updateTokenBalanceWithQueryRunner(queryRunner, wallet, wmToken, 'wmBalance', savedTransaction, amount, 'add', now);

      await queryRunner.commitTransaction();

      this.socketGateway.server.emit('deposit.wm.completed', {
        userId: user.id,
        amount,
        transactionHash,
        transactionId: transaction.id,
        timestamp: now
      });

      return {
        success: true,
        userId: user.id,
        amount,
        transactionHash,
        transactionId: transaction.id,
        timestamp: now
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`[depositWm] Lỗi nạp WM: ${error.message}`, {
        userId,
        amount,
        transactionHash,
        error: error.stack
      });

      // Xử lý lỗi chi tiết hơn
      if (error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException) {
        throw error; // Giữ nguyên lỗi đã xác định
      }

      throw new InternalServerErrorException({
        message: {
          en: `Failed to deposit WM. Please try again later.`,
        },
        code: 'DEPOSIT_WM_FAILED'
      });
    } finally {
      await queryRunner.release();
    }
  }

  /**
 * Phần rút tiền, người dùng gửi một lệnh rút với USDT
 * - Sau khi nhận được lưu vào DB với trạng thái PENDING
 * - Sau khi admin duyệt (gửi qua bot telegram để duyệt với 2 nút confirm/cancel), cập nhật lại trạng thái thành SUCCESS và cập nhật lại số dư vào DB
 * - Nếu admin từ chối, cập nhật lại trạng thái thành FAILED
 * - Gửi thông báo qua socket về cho người dùng trong cả hai trường hợp
 */
  async withdrawUsdt(userId: string, withdrawUsdtDto: WithdrawUsdtDto) {
    this.logger.log(`[WalletService] Processing withdraw for user ${userId} for amount ${withdrawUsdtDto.amount} USDT`);
    const amount = withdrawUsdtDto.amount;

    // Giới hạn chỉ được rút tối thiểu 10 USDT
    if (amount < 10) {
      throw new BadRequestException({
        message: { en: 'Amount must be greater than 10 USDT' },
        code: 'INVALID_AMOUNT',
      });
    }

    // Kiểm tra user có tồn tại không
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException({
        message: { en: 'User not found' },
        code: 'USER_NOT_FOUND',
      });
    }

    // Giới hạn chỉ được rút 1 lần/ngày/user (Sử dụng một cột lastWithdrawalTime ở user)
    const now = getCurrentTime();
    if (user.lastWithdrawalTime && user.lastWithdrawalTime > subtractDays(getCurrentTime(), 1)) {
      throw new BadRequestException({
        message: { en: 'You can only withdraw once per day' },
        code: 'WITHDRAWAL_LIMIT_REACHED',
      });
    }

    // Kiểm tra KYC đã hoàn thành chưa
    if (!user.isKycCompleted) {
      throw new BadRequestException({
        message: { en: 'KYC is not completed' },
        code: 'KYC_NOT_COMPLETED',
      });
    }

    const wmUsdRateConfig = await this.systemConfigService.findByKey('WM_USD_RATE');
    const wmUsdRate = Number(wmUsdRateConfig?.value || 0);

    if (wmUsdRate <= 0) {
        throw new BadRequestException('Invalid WM_USD_RATE configuration');
    }

    // Kiểm tra ví có tồn tại không
    const wallet = await this.findByUserId(userId);
    if (!wallet) {
      throw new NotFoundException({
        message: { en: 'Wallet not found' },
        code: 'WALLET_NOT_FOUND',
      });
    }

    const destinationAddress = wallet.address;

    // Kiểm tra token USDT có tồn tại không
    const usdtToken = await this.tokenService.findBySymbol('USDT');
    if (!usdtToken) {
      throw new NotFoundException({
        message: { en: 'USDT token not found' },
        code: 'USDT_TOKEN_NOT_FOUND',
      });
    }

    // Kiểm tra số dư USDT có đủ không
    const usdtBalance = await this.tokenService.findTokenByUserIdAndId(userId, usdtToken.id);
    if (!usdtBalance || usdtBalance.availableBalance < amount) {
      throw new BadRequestException({
        message: { en: 'Insufficient USDT balance' },
        code: 'INSUFFICIENT_USDT_BALANCE',
      });
    }

    // Kiểm tra token WM có tồn tại không
    const wmToken = await this.tokenService.findBySymbol('WM');
    if (!wmToken) {
      throw new NotFoundException({
        message: { en: 'WM token not found' },
        code: 'WM_TOKEN_NOT_FOUND',
      });
    }

    // Kiểm tra số dư WM có đủ +3% không
    const wmBalance = await this.tokenService.findTokenByUserIdAndId(userId, usdtToken.id);
    const wmAmountFee = amount*3/100/wmUsdRate;
    if (!wmBalance || wmBalance.availableBalance < wmAmountFee) {
      throw new BadRequestException({
        message: { en: 'Insufficient WM balance' },
        code: 'INSUFFICIENT_WM_BALANCE',
      });
    }

    const telegramChannelIdConfig = await this.systemConfigService.findByKey('TELEGRAM_CHANNEL_ID');
    const TELEGRAM_CHAT_ID = telegramChannelIdConfig.value;

    const withdrawUsdtAmountThresholdTelegramConfirmConfig = await this.systemConfigService.findByKey(
      'WITHDRAW_USDT_AMOUNT_THRESHOLD_TELEGRAM_CONFIRM',
    );
    const withdrawUsdtAmountThresholdTelegramConfirm = parseFloat(withdrawUsdtAmountThresholdTelegramConfirmConfig.value) || 100;

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const now = getCurrentTime();
      // Tạo giao dịch trừ phí WM khi tạo lệnh rút (3%)
      const transactionFee = wmAmountFee;

      // Tạo giao dịch rút tiền với trạng thái PENDING
      const transaction = await queryRunner.manager.save(Transaction, {
        walletId: wallet.id,
        userId: user.id,
        tokenId: usdtToken.id,
        type: TransactionType.WITHDRAWAL,
        amount: -amount,
        amountFee: transactionFee, // Lưu lại phí WM
        status: TransactionStatus.PENDING,
        note: `Withdrawal to ${destinationAddress} (-${amount} USDT)`,
        recipientAddress: destinationAddress,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      await this.updateTokenBalanceWithQueryRunner(queryRunner, wallet, usdtToken, 'usdtBalance', transaction, Math.abs(transaction.amount), 'subtract', now);

      const transactionWMFee = await queryRunner.manager.save(Transaction, {
        walletId: wallet.id,
        userId: user.id,
        tokenId: wmToken.id,
        type: TransactionType.WITHDRAWAL,
        amount: -transactionFee,
        status: TransactionStatus.COMPLETED,
        note: `Withdrawal fee to ${destinationAddress} (-${transactionFee} WM)`,
        recipientAddress: destinationAddress,
        reference: transaction.id,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      await this.updateTokenBalanceWithQueryRunner(queryRunner, wallet, wmToken, 'wmBalance', transactionWMFee, Math.abs(transactionWMFee.amount), 'subtract', now);

      user.lastWithdrawalTime = now;
      await queryRunner.manager.update(User, user.id, {
        lastWithdrawalTime: now
      });

      this.logger.log(
        `[WalletService] Sending withdrawal request to Telegram for approval: User ${userId}, amount ${amount} USDT to ${destinationAddress}`,
      );

      const inlineKeyboard = {
        inline_keyboard: [
          [
            { text: '✅ Approve', callback_data: `approve_withdrawal_${transaction.id}` },
            { text: '⛔ Reject', callback_data: `reject_withdrawal_${transaction.id}` },
          ],
        ],
      };

      const message = `🔔 *NEW WITHDRAWAL REQUEST*

👤 *User:* ${user.wallet || 'N/A'}
🆔 *ID:* \`${user.referralCode} | ${user.username}\`
💰 *Amount:* \`${amount} USDT\`
💰 *Fee:* \`${transactionFee} WM\`
👛 *Wallet:* \`${destinationAddress}\`
📝 *Transaction ID:* \`${transaction.id}\`
⏰ *Time:* \`${new Date().toISOString()}\`

⚠️ *Action Required:* Please review and process this request.`;

      // Gửi thông báo đến Telegram nếu có chat ID
      if (TELEGRAM_CHAT_IDS.length > 0) {
        try {
          for (const chatId of TELEGRAM_CHAT_IDS) {
            await this.telegramBotService.sendMessage(
              chatId,
              message,
              {
                parse_mode: 'Markdown',
                reply_markup: inlineKeyboard,
              },
            );

            try {
              const financialReport = await this.generateFinancialReport(userId);
              if (financialReport) {
                await this.telegramBotService.sendMessage(chatId, financialReport, {
                  parse_mode: 'Markdown',
                  disable_web_page_preview: true,
                });
              }
            } catch (reportError) {
              this.logger.error(`[WalletService] Withdrawal failed to generate financial report: ${reportError.message}`, reportError.stack);
            }
          }
          this.logger.log(`[WalletService] Withdrawal notification sent to Telegram successfully for transaction ${transaction.id}`);
        } catch (telegramError) {
          // Chỉ ghi log lỗi, không dừng quy trình rút tiền
          this.logger.error(`[WalletService] Failed to send Telegram notification: ${telegramError.message}`, telegramError.stack);
          // Tiếp tục quy trình, không throw lỗi
        }
      } else {
        this.logger.warn(`[WalletService] No Telegram chat IDs configured, skipping notification for transaction ${transaction.id}`);
      }

      await queryRunner.commitTransaction();

      return {
        success: true,
        transactionId: transaction.id,
        status: amount < withdrawUsdtAmountThresholdTelegramConfirm ? TransactionStatus.COMPLETED : TransactionStatus.PENDING,
        message: amount < withdrawUsdtAmountThresholdTelegramConfirm
          ? { en: 'Withdrawal has been processed successfully' }
          : { en: 'Withdrawal request submitted successfully and awaiting approval' },
      };
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      this.logger.error(`[WalletService] Withdrawal failed: ${error.message}`, error.stack);

      throw new InternalServerErrorException({
        message: { en: `Withdrawal processing failed: ${error.message}` },
        code: 'WITHDRAWAL_PROCESSING_FAILED',
      });
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Generate financial report for a user
   * @param userId User ID
   * @returns Formatted financial report message for Telegram
   */
  private async generateFinancialReport(userId: string): Promise<string | null> {
    try {
      // Get financial report from users service
      const report = await this.usersService.getFinancialReport(userId);

      if (!report || !report.fullFinancialReport) {
        return null;
      }

      // Format the report for Telegram
      // Replace \n with actual newlines and format as code block
      const formattedReport = report.fullFinancialReport
        .replace(/\\n/g, '\n')
        .replace(/\*/g, '\\*')  // Escape Markdown characters
        .replace(/\_/g, '\\_')
        .replace(/\`/g, '\\`');

      let message = `📊 *BÁO CÁO TÀI CHÍNH* 📊\n\n`;
      message += `\`\`\`\n${formattedReport}\n\`\`\`\n\n`;
      //message += `💰 *Số dư hệ thống:* \`${report.systemBalance}\`\n`;
      //message += `⚖️ *Chênh lệch:* \`${report.discrepancy}\`\n`;

      return message;
    } catch (error) {
      this.logger.error(`Error generating financial report: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Xử lý yêu cầu phê duyệt rút tiền
   * @param transaction Giao dịch cần phê duyệt
   * @param msg Tin nhắn Telegram (sử dụng để lấy thông tin callback query)
   */
  private async handleWithdrawalApproval(
    transaction: Transaction,
    msg: TelegramBot.Message
  ) {
    this.logger.log(`[WalletService] Admin approved withdrawal for transaction ${transaction.id}`);

    // Thử lấy khóa giao dịch
    const lockAcquired = await this.acquireTransactionLock(transaction.id);
    if (!lockAcquired) {
      this.logger.warn(`[handleWithdrawalApproval] Could not acquire lock for transaction ${transaction.id}, another process might be handling it`);

      const lockFailedMessage = `⚠️ *WITHDRAWAL PROCESSING*

📝 *Transaction ID:* \`${transaction.id}\`

⚠️ *Note:* This withdrawal request is currently being processed by another admin.

🔄 *Status:* Please wait for the process to complete.
`;

      // Gửi tin nhắn mới thay vì chỉnh sửa tin nhắn cũ
      if (TELEGRAM_CHAT_IDS.length > 0) {
        try {
          for (const chatId of TELEGRAM_CHAT_IDS) {
            // Gửi tin nhắn mới
            await this.telegramBotService.sendMessage(
              chatId,
              lockFailedMessage,
              {
                parse_mode: 'Markdown',
              },
            );
          }
        } catch (telegramError) {
          // Chỉ ghi log lỗi, không dừng quy trình
          this.logger.error(`[WalletService] Failed to send lock failed message to Telegram: ${telegramError.message}`, telegramError.stack);
        }
      }

      return;
    }

    const usdtToken = await this.tokenService.findBySymbol('USDT');
    if (!usdtToken) {
      // Giải phóng khóa nếu có lỗi
      await this.releaseTransactionLock(transaction.id);

      throw new NotFoundException({
        message: { en: 'USDT token not found' },
        code: 'USDT_TOKEN_NOT_FOUND',
      });
    }

    const wmToken = await this.tokenService.findBySymbol('WM');
    if (!wmToken) {
      // Giải phóng khóa nếu có lỗi
      await this.releaseTransactionLock(transaction.id);

      throw new NotFoundException({
        message: { en: 'WM token not found' },
        code: 'WM_TOKEN_NOT_FOUND',
      });
    }

    const adminApprovalRunner = this.dataSource.createQueryRunner();
    await adminApprovalRunner.connect();
    await adminApprovalRunner.startTransaction();

    const user = await adminApprovalRunner.manager.findOne(User, { where: { id: transaction.userId } });

    try {
      // Kiểm tra lại trạng thái hiện tại của giao dịch
      const currentTransaction = await adminApprovalRunner.manager.findOne(Transaction, {
        where: { id: transaction.id }
      });

      if (!currentTransaction) {
        throw new Error(`Transaction ${transaction.id} not found`);
      }

      if (currentTransaction.status !== TransactionStatus.PENDING) {
        this.logger.warn(`[handleWithdrawalApproval] Transaction ${transaction.id} is not in PENDING state anymore, current state: ${currentTransaction.status}`);

        //         const alreadyProcessedMessage = `⚠️ *WITHDRAWAL ALREADY PROCESSED*

        // 👤 *User:* \`${user.wallet || 'N/A'}\`
        // 🆔 *ID:* \`${user.referralCode} | ${user.username}\`
        // 💰 *Amount:* \`${Math.abs(transaction.amount)} USDT\`
        // 👛 *Wallet:* \`${transaction.recipientAddress}\`
        // 📝 *Transaction ID:* \`${transaction.id}\`
        // ⏰ *Time:* \`${new Date().toISOString()}\`

        // 🔄 *Current Status:* ${currentTransaction.status}

        // ⚠️ *Note:* This withdrawal request has already been processed.

        // 👨‍💼 *Support Reference:* \`${transaction.id}\`
        // `;

        //         // Gửi tin nhắn mới thay vì chỉnh sửa tin nhắn cũ
        //         if (TELEGRAM_CHAT_IDS.length > 0) {
        //           try {
        //             for (const chatId of TELEGRAM_CHAT_IDS) {
        //               // Gửi tin nhắn mới
        //               await this.telegramBotService.sendMessage(
        //                 chatId,
        //                 alreadyProcessedMessage,
        //                 {
        //                   parse_mode: 'Markdown',
        //                 },
        //               );
        //             }
        //           } catch (telegramError) {
        //             this.logger.error(`[WalletService] Failed to send already processed message to Telegram: ${telegramError.message}`, telegramError.stack);
        //           }
        //         }

        return;
      }

      const now = getCurrentTime();
      // Cập nhật lại trạng thái thành PROCESSING
      await adminApprovalRunner.manager.update(Transaction, transaction.id, {
        status: TransactionStatus.PROCESSING,
        transactionAt: now,
        updatedAt: now,
      });

      try {
        // Lấy giá trị tuyệt đối của số tiền để chuyển USDT
        const txHash = await this.web3Service.transferUSDT(transaction.recipientAddress, Math.abs(transaction.amount).toString());

        const now = getCurrentTime();
        await adminApprovalRunner.manager.update(Transaction, transaction.id, {
          status: TransactionStatus.COMPLETED,
          txHash: txHash,
          updatedAt: now,
          transactionAt: now,
        });

        const approvedMessage = `🟢 *WITHDRAWAL APPROVED*

👤 *User:* \`${user.wallet || 'N/A'}\`
🆔 *ID:* \`${user.referralCode} | ${user.username}\`
💰 *Amount:* \`${Math.abs(transaction.amount)} USDT\`
💰 *Fee:* \`${Math.abs(transaction.amountFee)} WM\`
👛 *Wallet:* \`${transaction.recipientAddress}\`
📝 *Transaction ID:* \`${transaction.id}\`
⏰ *Time:* \`${new Date().toISOString()}\`
🔗 *TxHash:* \`${txHash}\`

✅ *Status:* Transaction has been processed successfully.
🌐 *Network:* BSC (BNB Smart Chain)

📊 View on Explorer: [View Transaction](https://bscscan.com/tx/${txHash})`;

        // Gửi tin nhắn mới thay vì chỉnh sửa tin nhắn cũ
        try {
          await this.telegramBotService.editMessageText(
            approvedMessage,
            {
              chat_id: msg.chat.id,
              message_id: msg.message_id,
              parse_mode: 'Markdown',
              disable_web_page_preview: true,
            }
          );
          this.logger.log(`[WalletService] Approval notification sent to Telegram successfully for transaction ${transaction.id}`);
        } catch (telegramError) {
          // Chỉ ghi log lỗi, không dừng quy trình
          this.logger.error(`[WalletService] Failed to send approval notification to Telegram: ${telegramError.message}`, telegramError.stack);
          // Tiếp tục quy trình, không throw lỗi
        }

        await adminApprovalRunner.commitTransaction();

        // Giải phóng khóa sau khi hoàn thành
        await this.releaseTransactionLock(transaction.id);
      } catch (web3Error) {
        this.logger.error(`[WalletService] Web3 transfer failed: ${web3Error.message}`, web3Error.stack);
        try {
          await adminApprovalRunner.manager.update(Transaction, transaction.id, {
            status: TransactionStatus.FAILED,
            note: `Withdrawal to ${transaction.recipientAddress} (-${transaction.amount} USDT)`,
          });

          await adminApprovalRunner.manager.save(Transaction, {
            walletId: transaction.walletId,
            userId: user.id,
            tokenId: wmToken.id,
            type: TransactionType.WITHDRAWAL,
            amount: Math.abs(transaction.amountFee),
            status: TransactionStatus.COMPLETED,
            note: `Refund withdrawal fee to ${user.wallet} (+${Math.abs(transaction.amountFee)} WM)`,
            recipientAddress: user.wallet,
            reference: transaction.id,
            transactionAt: now,
            createdAt: now,
            updatedAt: now,
          });

          // Hoàn lại tiền
          const wallet = await adminApprovalRunner.manager.findOne(Wallet, { where: { userId: transaction.userId } });
          await this.updateTokenBalanceWithQueryRunner(adminApprovalRunner, wallet, usdtToken, 'usdtBalance', transaction, Math.abs(transaction.amount), 'add', now);
          await this.updateTokenBalanceWithQueryRunner(adminApprovalRunner, wallet, wmToken, 'wmBalance', transaction, Math.abs(transaction.amountFee), 'add', now);
          await adminApprovalRunner.commitTransaction();

          // Giải phóng khóa sau khi xử lý lỗi
          await this.releaseTransactionLock(transaction.id);

          const errorMessage = `⚠️ *WITHDRAWAL APPROVED WITH ISSUES*

  👤 *User:* \`${user.wallet || 'N/A'}\`
  🆔 *ID:* \`${user.referralCode} | ${user.username}\`
  💰 *Amount:* \`${Math.abs(transaction.amount)} USDT\`
  💰 *Fee:* \`${Math.abs(transaction.amountFee)} WM\`
  👛 *Wallet:* \`${transaction.recipientAddress}\`
  📝 *Transaction ID:* \`${transaction.id}\`
  ⏰ *Time:* \`${new Date().toISOString()}\`

  ❌ *Error Details:*
  \`\`\`
  ${web3Error.message}
  \`\`\`

  🔴 *Status:* Transaction requires manual intervention
  📋 *Required Action:* Please process this transaction manually

  ⚡️ *Technical Note:* Web3 transaction failed but balance has been deducted.
  🔧 *Next Steps:*
  1. Verify user balance
  2. Check transaction parameters
  3. Process manual transfer
  4. Update transaction status

  👨‍💼 *Support Reference:* \`${transaction.id}\``;

          // Gửi tin nhắn mới thay vì chỉnh sửa tin nhắn cũ
          try {
            await this.telegramBotService.editMessageText(
              errorMessage,
              {
                chat_id: msg.chat.id,
                message_id: msg.message_id,
                parse_mode: 'Markdown',
                disable_web_page_preview: true,
              }
            );
          } catch (telegramError) {
            this.logger.error(`[WalletService] Failed to send error message to Telegram: ${telegramError.message}`, telegramError.stack);
          }
        } catch (error) {
          await adminApprovalRunner.rollbackTransaction();
          this.logger.error(`[WalletService] DB error during admin approval: ${error.message}`, error.stack);

          // Giải phóng khóa trong trường hợp lỗi
          await this.releaseTransactionLock(transaction.id);
        }
      }
    } catch (error) {
      await adminApprovalRunner.rollbackTransaction();
      this.logger.error(`[WalletService] DB error during admin approval: ${error.message}`, error.stack);

      // Giải phóng khóa trong trường hợp lỗi
      await this.releaseTransactionLock(transaction.id);
      const systemErrorMessage = `🚨 *SYSTEM ERROR - WITHDRAWAL FAILED*

👤 *User:* \`${user.wallet || 'N/A'}\`
🆔 *ID:* \`${user.referralCode} | ${user.username}\`
💰 *Amount:* \`${Math.abs(transaction.amount)} USDT\`
💰 *Fee:* \`${Math.abs(transaction.amountFee)} WM\`
👛 *Wallet:* \`${transaction.recipientAddress}\`
📝 *Transaction ID:* \`${transaction.id}\`
⏰ *Time:* \`${new Date().toISOString()}\`

❌ *Error Details:*
\`\`\`
${error.message}
\`\`\`

⚠️ *System Status:* Database transaction failed
📋 *Impact:* User withdrawal request could not be processed
🔄 *State:* Transaction rolled back - No balance deducted

🔧 *Required Actions:*
1. Check database logs
2. Verify transaction integrity
3. Contact system administrator
4. Inform user if necessary

💡 *Note:* User may retry withdrawal after system verification

👨‍💻 *Technical Reference:* \`${transaction.id}\`
🆘 *Priority:* High - Needs immediate attention`;

      // Gửi tin nhắn mới thay vì chỉnh sửa tin nhắn cũ
      try {
        await this.telegramBotService.editMessageText(
          systemErrorMessage,
          {
            chat_id: msg.chat.id,
            message_id: msg.message_id,
            parse_mode: 'Markdown',
            disable_web_page_preview: true,
          }
        );
      } catch (telegramError) {
        this.logger.error(`[WalletService] Failed to send system error message to Telegram: ${telegramError.message}`, telegramError.stack);
      }
    } finally {
      await adminApprovalRunner.release();
    }
  }

  /**
   * Xử lý yêu cầu từ chối rút tiền
   * @param transaction Giao dịch cần từ chối
   * @param msg Tin nhắn Telegram (sử dụng để lấy thông tin callback query)
   */
  private async handleWithdrawalRejection(
    transaction: Transaction,
    msg: TelegramBot.Message,
  ) {
    this.logger.log(`[WalletService] Admin rejected withdrawal for transaction ${transaction.id}`);

    // Thử lấy khóa giao dịch
    const lockAcquired = await this.acquireTransactionLock(transaction.id);
    if (!lockAcquired) {
      this.logger.warn(`[handleWithdrawalRejection] Could not acquire lock for transaction ${transaction.id}, another process might be handling it`);

      //       const lockFailedMessage = `⚠️ *WITHDRAWAL PROCESSING*

      // 📝 *Transaction ID:* \`${transaction.id}\`

      // ⚠️ *Note:* This withdrawal request is currently being processed by another admin.

      // 🔄 *Status:* Please wait for the process to complete.
      // `;

      //       // Gửi tin nhắn mới thay vì chỉnh sửa tin nhắn cũ
      //       if (TELEGRAM_CHAT_IDS.length > 0) {
      //         try {
      //           for (const chatId of TELEGRAM_CHAT_IDS) {
      //             // Gửi tin nhắn mới
      //             await this.telegramBotService.sendMessage(
      //               chatId,
      //               lockFailedMessage,
      //               {
      //                 parse_mode: 'Markdown',
      //               },
      //             );
      //           }
      //         } catch (telegramError) {
      //           this.logger.error(`[WalletService] Failed to send lock failed message to Telegram: ${telegramError.message}`, telegramError.stack);
      //         }

      return;
    }

    const user = await this.usersService.findOne(transaction.userId);
    const usdtToken = await this.tokenService.findBySymbol('USDT');
    if (!usdtToken) {
      // Giải phóng khóa nếu có lỗi
      await this.releaseTransactionLock(transaction.id);

      throw new NotFoundException({
        message: { en: 'USDT token not found' },
        code: 'USDT_TOKEN_NOT_FOUND',
      });
    }

    const wmToken = await this.tokenService.findBySymbol('WM');
    if (!wmToken) {
      // Giải phóng khóa nếu có lỗi
      await this.releaseTransactionLock(transaction.id);

      throw new NotFoundException({
        message: { en: 'WM token not found' },
        code: 'WM_TOKEN_NOT_FOUND',
      });
    }

    const adminApprovalRunner = this.dataSource.createQueryRunner();
    await adminApprovalRunner.connect();
    await adminApprovalRunner.startTransaction();

    // Kiểm tra lại trạng thái hiện tại của giao dịch
    const currentTransaction = await adminApprovalRunner.manager.findOne(Transaction, {
      where: { id: transaction.id }
    });

    if (!currentTransaction) {
      await this.releaseTransactionLock(transaction.id);
      throw new Error(`Transaction ${transaction.id} not found`);
    }

    if (currentTransaction.status !== TransactionStatus.PENDING) {
      this.logger.warn(`[handleWithdrawalRejection] Transaction ${transaction.id} is not in PENDING state anymore, current state: ${currentTransaction.status}`);

      //       const alreadyProcessedMessage = `⚠️ *WITHDRAWAL ALREADY PROCESSED*

      // 👤 *User:* \`${user.wallet || 'N/A'}\`
      // 🆔 *ID:* \`${user.referralCode} | ${user.username}\`
      // 💰 *Amount:* \`${Math.abs(transaction.amount)} USDT\`
      // 👛 *Wallet:* \`${transaction.recipientAddress}\`
      // 📝 *Transaction ID:* \`${transaction.id}\`
      // ⏰ *Time:* \`${new Date().toISOString()}\`

      // 🔄 *Current Status:* ${currentTransaction.status}

      // ⚠️ *Note:* This withdrawal request has already been processed.

      // 👨‍💼 *Support Reference:* \`${transaction.id}\`
      // `;

      // Gửi tin nhắn mới thay vì chỉnh sửa tin nhắn cũ
      // if (TELEGRAM_CHAT_IDS.length > 0) {
      //   try {
      //     for (const chatId of TELEGRAM_CHAT_IDS) {
      //       // Gửi tin nhắn mới
      //       await this.telegramBotService.sendMessage(
      //         chatId,
      //         alreadyProcessedMessage,
      //         {
      //           parse_mode: 'Markdown',
      //         },
      //       );
      //     }
      //   } catch (telegramError) {
      //     this.logger.error(`[WalletService] Failed to send already processed message to Telegram: ${telegramError.message}`, telegramError.stack);
      //   }
      // }

      await this.releaseTransactionLock(transaction.id);
      return;
    }

    try {
      const now = new Date();
      await this.transactionRepository.update(transaction.id, {
        status: TransactionStatus.CANCELLED,
        transactionAt: now,
        updatedAt: now,
        note: `Withdrawal to ${transaction.recipientAddress} (-${transaction.amount} USDT)`,
      });

      await this.transactionRepository.save({
        walletId: transaction.walletId,
        userId: user.id,
        tokenId: wmToken.id,
        type: TransactionType.WITHDRAWAL,
        amount: Math.abs(transaction.amountFee),
        status: TransactionStatus.COMPLETED,
        note: `Refund withdrawal fee to ${user.wallet} (+${Math.abs(transaction.amountFee)} WM)`,
        recipientAddress: user.wallet,
        reference: transaction.id,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      // Hoàn lại tiền
      const wallet = await adminApprovalRunner.manager.findOne(Wallet, { where: { userId: transaction.userId } });
      await this.updateTokenBalanceWithQueryRunner(adminApprovalRunner, wallet, usdtToken, 'usdtBalance', transaction, Math.abs(transaction.amount), 'add', now);
      await this.updateTokenBalanceWithQueryRunner(adminApprovalRunner, wallet, wmToken, 'wmBalance', transaction, Math.abs(transaction.amountFee), 'add', now);
      await adminApprovalRunner.commitTransaction();

      // Giải phóng khóa sau khi hoàn thành
      await this.releaseTransactionLock(transaction.id);

      const rejectedMessage = `🔴 *WITHDRAWAL REJECTED*

👤 *User:* \`${user.wallet || 'N/A'}\`
🆔 *ID:* \`${user.referralCode} | ${user.username}\`
💰 *Amount:* \`${Math.abs(transaction.amount)} USDT\`
💰 *Fee:* \`${Math.abs(transaction.amountFee)} WM\`
👛 *Wallet:* \`${transaction.recipientAddress}\`
📝 *Transaction ID:* \`${transaction.id}\`
⏰ *Time:* \`${new Date().toISOString()}\`

❌ *Status:* Request has been rejected
📋 *Reason:* Admin rejected the withdrawal request

💡 *Note:*
• No funds have been deducted
• User can submit a new request
• Please verify requirements before resubmitting

👥 *Support:*
• Contact support if you need assistance
• Reference ID: \`${transaction.id}\`

⏱️ *Processing Time:* ${moment(new Date()).diff(moment(transaction.createdAt), 'minutes')} minutes`;

      // Gửi tin nhắn mới thay vì chỉnh sửa tin nhắn cũ
      try {
        await this.telegramBotService.editMessageText(
          rejectedMessage,
          {
            chat_id: msg.chat.id,
            message_id: msg.message_id,
            parse_mode: 'Markdown',
            disable_web_page_preview: true,
          }
        );
      } catch (telegramError) {
        this.logger.error(`[WalletService] Failed to send rejection message to Telegram: ${telegramError.message}`, telegramError.stack);
      }
    } catch (error) {
      await adminApprovalRunner.rollbackTransaction();
      this.logger.error(`[WalletService] DB error during admin rejection: ${error.message}`, error.stack);

      // Giải phóng khóa trong trường hợp lỗi
      await this.releaseTransactionLock(transaction.id);

      const systemErrorMessage = `🚨 *SYSTEM ERROR - REJECTION FAILED*

👤 *User:* \`${user.wallet || 'N/A'}\`
🆔 *ID:* \`${user.referralCode} | ${user.username}\`
💰 *Amount:* \`${Math.abs(transaction.amount)} USDT\`
💰 *Fee:* \`${Math.abs(transaction.amountFee)} WM\`
👛 *Wallet:* \`${transaction.recipientAddress}\`
📝 *Transaction ID:* \`${transaction.id}\`
⏰ *Time:* \`${new Date().toISOString()}\`

❌ *Error Details:*
\`\`\`
${error.message}
\`\`\`

⚠️ *System Status:* Database error during rejection
📋 *Impact:* Withdrawal rejection could not be processed
🔄 *State:* Transaction status unchanged

🔧 *Required Actions:*
1. Check database connection
2. Verify transaction status
3. Manual status update may be needed
4. Monitor system stability

💡 *Technical Notes:*
• Database transaction rolled back
• No changes were made to user balance
• Manual verification required

👨‍💻 *Support Reference:* \`${transaction.id}\`
🆘 *Priority:* Medium - Needs attention`;

      await this.telegramBotService.editMessageText(
        systemErrorMessage,
        {
          chat_id: msg.chat.id,
          message_id: msg.message_id,
          parse_mode: 'Markdown',
          disable_web_page_preview: true,
        }
      );
    } finally {
      await adminApprovalRunner.release();
    }
  }

  /**
   * Chuyển WM từ người dùng sang người dùng khác
   */
  async transferWm(userId: string, transferWmDto: TransferWmDto) {
    const { amount, referralCode, walletType } = transferWmDto;

    if (walletType === 'available') {
      // Kiểm tra đầu vào
      if (!amount || amount < 10) { // Số lượng tối thiểu là 10 WM
        this.logger.warn(`[transferWm] Số lượng ${amount} không hợp lệ`);
        throw new BadRequestException({
          message: { en: 'Amount must be greater than 10 WM' },
          code: 'INVALID_AMOUNT'
        });
      }

      if (!referralCode) {
        this.logger.warn(`[transferWm] Mã giới thiệu không hợp lệ`);
        throw new BadRequestException({
          message: { en: 'Referral code is required' },
          code: 'REFERRAL_CODE_REQUIRED'
        });
      }

      // Lấy thông tin Telegram
      const telegramChannelIdConfig = await this.systemConfigService.findByKey('TELEGRAM_CHANNEL_ID');
      const TELEGRAM_CHAT_ID = telegramChannelIdConfig?.value;
      if (!TELEGRAM_CHAT_ID) {
        this.logger.warn('[transferWm] ID kênh Telegram không được cấu hình');
      }

      // 1. Lấy thông tin user và ví
      const user = await this.usersService.findOne(userId);
      if (!user) {
        throw new NotFoundException({
          message: { en: 'User not found' },
          code: 'USER_NOT_FOUND'
        });
      }

      if (!user.isKycCompleted) {
        this.logger.warn(`[transferWm] Người dùng ${userId} chưa hoàn thành KYC`);
        throw new BadRequestException({
          message: { en: 'KYC is not completed' },
          code: 'KYC_NOT_COMPLETED'
        });
      }

      const wallet = await this.findByUserId(userId);
      if (!wallet) {
        this.logger.warn(`[transferWm] Ví không được tìm thấy cho người dùng ${userId}`);
        throw new NotFoundException({
          message: { en: 'Wallet not found' },
          code: 'WALLET_NOT_FOUND'
        });
      }

      // 2. Lấy thông tin người nhận
      const toUser = await this.usersService.findByReferralCode(referralCode);
      if (!toUser) {
        this.logger.warn(`[transferWm] Mã giới thiệu không được tìm thấy`);
        throw new NotFoundException({
          message: { en: 'Referral code not found' },
          code: 'REFERRAL_CODE_NOT_FOUND'
        });
      }

      // Kiểm tra người dùng không tự chuyển cho chính mình
      if (toUser.id === userId) {
        this.logger.warn(`[transferWm] Không thể chuyển cho chính mình`);
        throw new BadRequestException({
          message: { en: 'Cannot transfer to yourself' },
          code: 'SELF_TRANSFER_NOT_ALLOWED'
        });
      }

      const toWallet = await this.findByUserId(toUser.id);
      if (!toWallet) {
        this.logger.warn(`[transferWm] Ví người nhận không được tìm thấy`);
        throw new NotFoundException({
          message: { en: 'Recipient wallet not found' },
          code: 'RECIPIENT_WALLET_NOT_FOUND'
        });
      }

      // 4. Kiểm tra token WM và số dư
      const wmToken = await this.tokenService.findBySymbol('WM');
      if (!wmToken) {
        this.logger.warn(`[transferWm] Token WM không được tìm thấy`);
        throw new NotFoundException({
          message: { en: 'WM token not found' },
          code: 'WM_TOKEN_NOT_FOUND'
        });
      }

      const wmBalance = await this.getWMBalance(userId);
      if (Number(wmBalance) < amount + amount*1/100) { // 1 % là phí chuyển khoản
        this.logger.warn(`[transferWm] Số dư WM không đủ cho người dùng ${userId}, số dư ${wmBalance}, số lượng ${amount}`);
        throw new BadRequestException({
          message: { en: `Insufficient WM balance. Available: ${wmBalance} WM` },
          code: 'INSUFFICIENT_WM_BALANCE',
          available: wmBalance,
          required: amount
        });
      }

      // 5. Bắt đầu transaction
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Sử dụng một giá trị thời gian nhất quán
        const now = new Date();

        // 6. Tạo giao dịch cho người gửi
        const transactionFrom = await queryRunner.manager.save(Transaction, {
          walletId: wallet.id,
          userId: user.id,
          tokenId: wmToken.id,
          type: TransactionType.TRANSFER,
          amount: -amount,
          status: TransactionStatus.COMPLETED,
          note: `Transfer WM to ${toUser.wallet} (-${amount} WM)`,
          recipientAddress: toWallet.address,
          senderAddress: wallet.address,
          transactionAt: now,
          createdAt: now,
          updatedAt: now,
        });

        await this.updateTokenBalanceWithQueryRunner(queryRunner, wallet, wmToken, 'wmBalance', transactionFrom, amount, 'subtract', now);

        // 6. Tạo giao dịch cho người gửi
        const amountFee = amount*1/100; // Phí giao dịch là 1%
        const transactionFromFee = await queryRunner.manager.save(Transaction, {
          walletId: wallet.id,
          userId: user.id,
          tokenId: wmToken.id,
          type: TransactionType.TRANSFER,
          amount: -amountFee,
          status: TransactionStatus.COMPLETED,
          note: `Transfer fee WM to ${toUser.wallet} (-${amountFee} WM)`,
          recipientAddress: toWallet.address,
          senderAddress: wallet.address,
          reference: transactionFrom.id,
          transactionAt: now,
          createdAt: now,
          updatedAt: now,
        });

        await this.updateTokenBalanceWithQueryRunner(queryRunner, wallet, wmToken, 'wmBalance', transactionFromFee, amountFee, 'subtract', now);

        // 7. Tạo giao dịch cho người nhận
        const transactionTo = await queryRunner.manager.save(Transaction, {
          walletId: toWallet.id,
          userId: toUser.id,
          tokenId: wmToken.id,
          type: TransactionType.TRANSFER,
          amount: amount,
          status: TransactionStatus.COMPLETED,
          note: `Received WM from ${user.wallet}`,
          recipientAddress: toWallet.address,
          senderAddress: wallet.address,
          transactionAt: now,
          createdAt: now,
          updatedAt: now,
        });

        await this.updateTokenBalanceWithQueryRunner(queryRunner, toWallet, wmToken, 'wmBalance', transactionTo, amount, 'add', now);

        await queryRunner.commitTransaction();

        // 9. Gửi thông báo Telegram
        try {
          const transferMessage = `💫 *WM TRANSFER COMPLETED*

👤 *From User:* \`${user.username}\`
👥 *To User:* \`${toUser.username}\`

💰 *Transfer Details:*
• Amount: \`${amount} WM\`
• Fee: \`${amountFee} WM\`
• Transaction ID: \`${transactionFrom.id}\`

👛 *Wallet Information:*
• From: \`${wallet.address}\`
• To: \`${toWallet.address}\`

⏰ *Time:* \`${moment(now).format('YYYY-MM-DD HH:mm:ss')}\`

✅ *Status:* Transfer completed successfully
📊 *Network:* Internal Transfer`;

          // Gửi thông báo đến tất cả admins
          await Promise.all(TELEGRAM_CHAT_IDS.map(chatId =>
            this.telegramBotService.sendMessage(
              chatId,
              transferMessage,
              {
                parse_mode: 'Markdown',
                disable_web_page_preview: true
              }
            ).catch(error => {
              this.logger.warn(`[transferWm] Failed to send notification to chat ${chatId}: ${error.message}`, {
                error: error.stack,
                chatId,
                transactionId: transactionFrom.id
              });
            })
          ))
        } catch (error) {
          this.logger.warn(`[transferWm] Failed to send notification to chat: ${error.message}`);
        }
        // 10. Gửi thông báo WebSocket
        this.socketGateway.server.emit('wm.transfer.completed', {
          fromUserId: user.id,
          toUserId: toUser.id,
          amount,
          transactionId: transactionFrom.id,
          timestamp: now
        });

        const updatedUser = await this.usersService.findOne(userId);

        // 11. Trả về kết quả
        return {
          user: updatedUser,
          success: true,
          transactionId: transactionFrom.id,
          fromUser: {
            id: user.id,
            username: user.username
          },
          toUser: {
            id: toUser.id,
            username: toUser.username
          },
          amount,
          timestamp: now,
          status: TransactionStatus.COMPLETED,
          message: { en: 'WM transfer completed successfully' }
        };
      } catch (error) {
        await queryRunner.rollbackTransaction();

        // Xử lý lỗi chi tiết hơn
        if (error instanceof BadRequestException ||
          error instanceof NotFoundException ||
          error instanceof ConflictException) {
          throw error; // Giữ nguyên lỗi đã xác định
        }

        throw new InternalServerErrorException({
          message: {
            en: `Failed to transfer WM. Please try again later.`,
          },
          code: 'WM_TRANSFER_FAILED'
        });
      } finally {
        // Đảm bảo luôn giải phóng queryRunner
        if (queryRunner) {
          await queryRunner.release();
        }
      }
    } else {
      // Kiểm tra đầu vào
      if (!amount || amount < 0) { // Số lượng tối thiểu là 10 WM
        this.logger.warn(`[transferWm] Số lượng ${amount} không hợp lệ`);
        throw new BadRequestException({
          message: { en: 'Amount must be greater than 0' },
          code: 'INVALID_AMOUNT'
        });
      }

      // Lấy thông tin Telegram
      const telegramChannelIdConfig = await this.systemConfigService.findByKey('TELEGRAM_CHANNEL_ID');
      const TELEGRAM_CHAT_ID = telegramChannelIdConfig?.value;
      if (!TELEGRAM_CHAT_ID) {
        this.logger.warn('[transferWm] ID kênh Telegram không được cấu hình');
      }

      // 1. Lấy thông tin user và ví
      const user = await this.usersService.findOne(userId);
      if (!user) {
        throw new NotFoundException({
          message: { en: 'User not found' },
          code: 'USER_NOT_FOUND'
        });
      }

      if (!user.isKycCompleted) {
        this.logger.warn(`[transferWm] Người dùng ${userId} chưa hoàn thành KYC`);
        throw new BadRequestException({
          message: { en: 'KYC is not completed' },
          code: 'KYC_NOT_COMPLETED'
        });
      }

      const wallet = await this.findByUserId(userId);
      if (!wallet) {
        this.logger.warn(`[transferWm] Ví không được tìm thấy cho người dùng ${userId}`);
        throw new NotFoundException({
          message: { en: 'Wallet not found' },
          code: 'WALLET_NOT_FOUND'
        });
      }

      // 4. Kiểm tra token WM và số dư
      const wmToken = await this.tokenService.findBySymbol('WM');
      if (!wmToken) {
        this.logger.warn(`[transferWm] Token WM không được tìm thấy`);
        throw new NotFoundException({
          message: { en: 'WM token not found' },
          code: 'WM_TOKEN_NOT_FOUND'
        });
      }

      const wmLockedBalance = wallet.wmLockedBalance;

      // Unlocked WM tối đa theo tỉ lệ KYC F1
      const totalF1Kyc = await this.usersService.countF1Kyc(userId);
      const maxUnlockedWM = wmLockedBalance * totalF1Kyc / 100;
      if (totalF1Kyc == 0 || maxUnlockedWM < amount) {
        this.logger.warn(`[transferWm] Số dư WM không đủ cho người dùng ${userId}, số dư ${wmLockedBalance}, số lượng ${amount}`);
        throw new BadRequestException({
          message: { en: `Insufficient WM balance.` },
          code: 'INSUFFICIENT_WM_BALANCE'
        });
      }


      // 5. Bắt đầu transaction
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Sử dụng một giá trị thời gian nhất quán
        const now = new Date();

        // 6. Tạo giao dịch cho người gửi
        const transactionFrom = await queryRunner.manager.save(Transaction, {
          walletId: wallet.id,
          userId: user.id,
          tokenId: wmToken.id,
          type: TransactionType.TRANSFER,
          amount: amount,
          status: TransactionStatus.COMPLETED,
          note: `Unlock WM +(${amount}WM)`,
          recipientAddress: wallet.address,
          senderAddress: wallet.address,
          transactionAt: now,
          createdAt: now,
          updatedAt: now,
        });

        await this.updateTokenBalanceWithQueryRunner(queryRunner, wallet, wmToken, 'wmBalance', transactionFrom, amount, 'add', now);
        await this.updateTokenBalanceWithQueryRunner(queryRunner, wallet, wmToken, 'wmLockedBalance', transactionFrom, amount, 'subtract', now);

        await queryRunner.commitTransaction();

        // 9. Gửi thông báo Telegram
        try {
          const transferMessage = `💫 *WM TRANSFER COMPLETED*

👤 *From User:* \`${user.username}\`
👥 *To User:* \`${user.username}\`

💰 *Transfer Details:*
• Amount: \`${amount} WM\`
• Transaction ID: \`${transactionFrom.id}\`

👛 *Wallet Information:*
• From: \`${wallet?.address}\`
• To: \`${wallet?.address}\`

⏰ *Time:* \`${moment(now).format('YYYY-MM-DD HH:mm:ss')}\`

✅ *Status:* Transfer completed successfully
📊 *Network:* Internal Transfer`;

          // Gửi thông báo đến tất cả admins
          await Promise.all(
            TELEGRAM_CHAT_IDS.map(chatId =>
              this.telegramBotService.sendMessage(
                chatId,
                transferMessage,
                {
                  parse_mode: 'Markdown',
                  disable_web_page_preview: true
                }
              ).catch(error => {
                this.logger.warn(`[transferWm] Failed to send notification to chat ${chatId}: ${error.message}`, {
                  error: error.stack,
                  chatId,
                  transactionId: transactionFrom.id
                });
              })
            )
          );
        } catch (error) {
          this.logger.warn(`[transferWm] Failed to send notification to chat: ${error.message}`);
        }

        // 10. Gửi thông báo WebSocket
        this.socketGateway.server.emit('wm.transfer.completed', {
          fromUserId: user.id,
          toUserId: user.id,
          amount,
          transactionId: transactionFrom.id,
          timestamp: now
        });

        const updatedUser = await this.usersService.findOne(userId);

        // 11. Trả về kết quả
        return {
          user: updatedUser,
          success: true,
          transactionId: transactionFrom.id,
          fromUser: {
            id: user.id,
            username: user.username
          },
          toUser: {
            id: user.id,
            username: user.username
          },
          amount,
          timestamp: now,
          status: TransactionStatus.COMPLETED,
          message: { en: 'WM transfer completed successfully' }
        };
      } catch (error) {
        await queryRunner.rollbackTransaction();

        // Xử lý lỗi chi tiết hơn
        if (error instanceof BadRequestException ||
          error instanceof NotFoundException ||
          error instanceof ConflictException) {
          throw error; // Giữ nguyên lỗi đã xác định
        }

        throw new InternalServerErrorException({
          message: {
            en: `Failed to transfer WM. Please try again later.`,
          },
          code: 'WM_TRANSFER_FAILED'
        });
      } finally {
        // Đảm bảo luôn giải phóng queryRunner
        if (queryRunner) {
          await queryRunner.release();
        }
      }
    }
  }

  /**
   * Điều chỉnh số dư WM cho người dùng
   */
  async adjustWm(userId: string, adjustWmDto: AdjustWmDto) {
    const { amount, type, note } = adjustWmDto;

    // Kiểm tra đầu vào
    if (!amount || amount <= 0) { // Số lượng tối thiểu là 1 WM
      this.logger.warn(`[adjustWm] Số lượng ${amount} không hợp lệ`);
      throw new BadRequestException({
        message: { en: 'Amount must be greater than 10 WM' },
        code: 'INVALID_AMOUNT'
      });
    }

    // Lấy thông tin user và ví
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException({
        message: { en: 'User not found' },
        code: 'USER_NOT_FOUND'
      });
    }

    const wallet = await this.findByUserId(userId);
    if (!wallet) {
      this.logger.warn(`[adjustWm] Ví không được tìm thấy cho người dùng ${userId}`);
      throw new NotFoundException({
        message: { en: 'Wallet not found' },
        code: 'WALLET_NOT_FOUND'
      });
    }

    // Kiểm tra token WM và số dư
    const wmToken = await this.tokenService.findBySymbol('WM');
    if (!wmToken) {
      this.logger.warn(`[adjustWm] Token WM không được tìm thấy`);
      throw new NotFoundException({
        message: { en: 'WM token not found' },
        code: 'WM_TOKEN_NOT_FOUND'
      });
    }

    const wmBalance = await this.getWMBalance(userId);
    if (type == 'subtract' && Number(wmBalance) < amount) {
      this.logger.warn(`[adjustWm] Số dư WM không đủ cho người dùng ${userId}, số dư ${wmBalance}, số lượng ${amount}`);
      throw new BadRequestException({
        message: { en: `Insufficient WM balance.` },
        code: 'INSUFFICIENT_WM_BALANCE',
      });
    }

    // Bắt đầu transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Sử dụng một giá trị thời gian nhất quán
      const now = new Date();

      // Tạo giao dịch cho người gửi
      const adjustAmount = type == 'add' ? amount : -amount;
      const adjustNote = note || type == 'add' ? `Adjust WM to ${user.wallet} (+${amount} WM)` : `Adjust WM to ${user.wallet} (-${amount} WM)`;

      const transaction = await queryRunner.manager.save(Transaction, {
        walletId: wallet.id,
        userId: user.id,
        tokenId: wmToken.id,
        type: type == 'add' ? TransactionType.DEPOSIT : TransactionType.WITHDRAWAL,
        amount: adjustAmount,
        status: TransactionStatus.COMPLETED,
        note: adjustNote,
        recipientAddress: wallet.address,
        senderAddress: wallet.address,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      await this.updateTokenBalanceWithQueryRunner(queryRunner, wallet, wmToken, 'wmBalance', transaction, amount, type, now);

      await queryRunner.commitTransaction();

      // Gửi thông báo WebSocket
      this.socketGateway.server.emit('wm.adjust.completed', {
        fromUserId: user.id,
        toUserId: user.id,
        amount,
        transactionId: transaction.id,
        timestamp: now
      });

      // Trả về kết quả
      return {
        fromUserId: user.id,
        toUserId: user.id,
        amount,
        transactionId: transaction.id,
        timestamp: now
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();

      // Xử lý lỗi chi tiết hơn
      if (error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException) {
        throw error; // Giữ nguyên lỗi đã xác định
      }

      throw new InternalServerErrorException({
        message: {
          en: `Failed to adjust WM. Please try again later.`,
        },
        code: 'WM_ADJUST_FAILED'
      });
    } finally {
      if (queryRunner) {
        await queryRunner.release();
      }
    }
  }

  /**
   * Tự động mining hằng ngày cho các tài khoản có priority purchase
   * Chạy vào lúc 0h45 phút hằng ngày
   * Sử dụng mineWm để đảm bảo tất cả logic nghiệp vụ (bao gồm thưởng người giới thiệu)
   */
  @Cron('45 0 * * *') // Chạy vào 0h45 phút hằng ngày
  async autoMiningForPriorityUsers(): Promise<void> {
    this.logger.log('[autoMiningForPriorityUsers] Bắt đầu tự động mining cho các tài khoản có priority purchase...');

    try {
      const now = getCurrentTime();
      const today = moment(now).format('YYYY-MM-DD');

      // 1. Tối ưu query để chỉ lấy users cần mining
      const priorityUsersQuery = `
        SELECT u.id
        FROM users u
        WHERE u.firstDepositTime IS NOT NULL
        AND (u.lastMiningTime IS NULL OR DATE(u.lastMiningTime) < ?)
      `;

      const priorityUserIds = await this.dataSource.query(priorityUsersQuery, [today]);

      if (!priorityUserIds || priorityUserIds.length === 0) {
        this.logger.log('[autoMiningForPriorityUsers] Không có user nào cần mining hôm nay');
        return;
      }

      this.logger.log(`[autoMiningForPriorityUsers] Tìm thấy ${priorityUserIds.length} user cần mining`);

      // 2. Xử lý theo batch để tối ưu hiệu năng
      const BATCH_SIZE = 50; // Giảm batch size vì mineWm phức tạp hơn
      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < priorityUserIds.length; i += BATCH_SIZE) {
        const batch = priorityUserIds.slice(i, i + BATCH_SIZE);

        // Xử lý batch với Promise.allSettled để không bị block bởi lỗi
        const batchPromises = batch.map(async (userRow) => {
          try {
            await this.mineWm(userRow.id);
            return { success: true, userId: userRow.id };
          } catch (error) {
            // Chỉ log lỗi không phải "Already mined today"
            if (!error.message?.includes('already mined today') &&
                !error.message?.includes('ALREADY_MINED_TODAY')) {
              this.logger.error(`[autoMiningForPriorityUsers] Lỗi mining user ${userRow.id}: ${error.message}`);
            }
            return { success: false, userId: userRow.id, error: error.message };
          }
        });

        const batchResults = await Promise.allSettled(batchPromises);

        // Đếm kết quả
        batchResults.forEach((result) => {
          if (result.status === 'fulfilled') {
            if (result.value.success) {
              successCount++;
            } else {
              errorCount++;
            }
          } else {
            errorCount++;
            this.logger.error(`[autoMiningForPriorityUsers] Promise rejected: ${result.reason}`);
          }
        });

        // Delay giữa các batch để tránh quá tải
        if (i + BATCH_SIZE < priorityUserIds.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Log tiến độ mỗi 10 batch
        if ((i / BATCH_SIZE + 1) % 10 === 0) {
          this.logger.log(`[autoMiningForPriorityUsers] Đã xử lý ${i + BATCH_SIZE}/${priorityUserIds.length} users`);
        }
      }

      this.logger.log(`[autoMiningForPriorityUsers] Hoàn thành auto mining. Thành công: ${successCount}, Lỗi: ${errorCount}`);

    } catch (error) {
      this.logger.error(`[autoMiningForPriorityUsers] Lỗi trong quá trình auto mining: ${error.message}`, error.stack);
    }
  }

  /**
   * Transfer USDT từ ví A sang ví B
   * @param fromUserId ID người gửi
   * @param transferUsdtDto Thông tin transfer
   * @returns Kết quả transfer
   */
  async transferUsdt(fromUserId: string, transferUsdtDto: TransferUsdtDto) {
    this.logger.log(`[transferUsdt] Bắt đầu chuyển ${transferUsdtDto.amount} USDT từ user ${fromUserId} đến ${transferUsdtDto.recipient}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const now = getCurrentTime();

      // 1. Lấy thông tin người gửi
      const fromUser = await queryRunner.manager.findOne(User, {
        where: { id: fromUserId }
      });
      if (!fromUser) {
        throw new NotFoundException({
          message: { en: 'Sender not found' },
          code: 'SENDER_NOT_FOUND'
        });
      }

      const fromWallet = await queryRunner.manager.findOne(Wallet, {
        where: { userId: fromUserId }
      });
      if (!fromWallet) {
        throw new NotFoundException({
          message: { en: 'Sender wallet not found' },
          code: 'SENDER_WALLET_NOT_FOUND'
        });
      }

      // 2. Tìm người nhận bằng wallet address hoặc referralCode
      let toUser: User;
      let toWallet: Wallet;

      // Kiểm tra xem recipient có phải là wallet address không (bắt đầu bằng 0x)
      if (transferUsdtDto.recipient.startsWith('0x')) {
        // Tìm theo wallet address
        toWallet = await queryRunner.manager.findOne(Wallet, {
          where: { address: transferUsdtDto.recipient }
        });
        if (!toWallet) {
          throw new NotFoundException({
            message: { en: 'Recipient wallet not found' },
            code: 'RECIPIENT_WALLET_NOT_FOUND'
          });
        }
        toUser = await queryRunner.manager.findOne(User, {
          where: { id: toWallet.userId }
        });
      } else {
        // Tìm theo referralCode
        toUser = await queryRunner.manager.findOne(User, {
          where: { referralCode: transferUsdtDto.recipient }
        });
        if (!toUser) {
          throw new NotFoundException({
            message: { en: 'Recipient not found by referral code' },
            code: 'RECIPIENT_NOT_FOUND'
          });
        }
        toWallet = await queryRunner.manager.findOne(Wallet, {
          where: { userId: toUser.id }
        });
        if (!toWallet) {
          throw new NotFoundException({
            message: { en: 'Recipient wallet not found' },
            code: 'RECIPIENT_WALLET_NOT_FOUND'
          });
        }
      }

      // 3. Kiểm tra không thể chuyển cho chính mình
      if (fromUserId === toUser.id) {
        throw new BadRequestException({
          message: { en: 'Cannot transfer to yourself' },
          code: 'CANNOT_TRANSFER_TO_SELF'
        });
      }

      // 4. Kiểm tra số dư USDT của người gửi
      const transferAmount = new BigNumber(transferUsdtDto.amount);
      const fromBalance = new BigNumber(fromWallet.usdtBalance);

      if (fromBalance.lt(transferAmount)) {
        throw new BadRequestException({
          message: { en: 'Insufficient USDT balance' },
          code: 'INSUFFICIENT_USDT_BALANCE'
        });
      }

      // 5. Lấy token USDT
      const usdtToken = await this.tokenService.findBySymbol('USDT');
      if (!usdtToken) {
        throw new NotFoundException({
          message: { en: 'USDT token not found' },
          code: 'USDT_TOKEN_NOT_FOUND'
        });
      }

      // 6. Tạo transaction rút tiền từ người gửi
      const withdrawTransaction = queryRunner.manager.create(Transaction, {
        walletId: fromWallet.id,
        userId: fromUserId,
        tokenId: usdtToken.id,
        type: TransactionType.WITHDRAWAL,
        amount: -transferAmount.toNumber(),
        status: TransactionStatus.COMPLETED,
        recipientAddress: toWallet.address,
        note: transferUsdtDto.note || `Transfer ${transferAmount.toFixed(2)} USDT to ${toUser.referralCode} (-${transferAmount.toFixed(2)}USDT)`,
        transactionAt: now,
        createdAt: now,
        updatedAt: now
      });

      const savedWithdrawTransaction = await queryRunner.manager.save(Transaction, withdrawTransaction);

      // 7. Cập nhật số dư người gửi
      await this.updateTokenBalanceWithQueryRunner(
        queryRunner,
        fromWallet,
        usdtToken,
        'usdtBalance',
        savedWithdrawTransaction,
        transferAmount.toNumber(),
        'subtract',
        now
      );

      // 8. Tạo transaction nạp tiền cho người nhận
      const depositTransaction = queryRunner.manager.create(Transaction, {
        walletId: toWallet.id,
        userId: toUser.id,
        tokenId: usdtToken.id,
        type: TransactionType.DEPOSIT,
        amount: transferAmount.toNumber(),
        status: TransactionStatus.COMPLETED,
        senderAddress: fromWallet.address,
        reference: savedWithdrawTransaction.id,
        note: transferUsdtDto.note || `Receive ${transferAmount.toFixed(2)} USDT from ${fromUser.referralCode} (+${transferAmount.toFixed(2)}USDT)`,
        transactionAt: now,
        createdAt: now,
        updatedAt: now
      });

      const savedDepositTransaction = await queryRunner.manager.save(Transaction, depositTransaction);

      // 9. Cập nhật số dư người nhận
      await this.updateTokenBalanceWithQueryRunner(
        queryRunner,
        toWallet,
        usdtToken,
        'usdtBalance',
        savedDepositTransaction,
        transferAmount.toNumber(),
        'add',
        now
      );

      // 10. Commit transaction
      await queryRunner.commitTransaction();

      this.logger.log(`[transferUsdt] Chuyển thành công ${transferAmount.toFixed(2)} USDT từ ${fromUser.referralCode} đến ${toUser.referralCode}`);

      return {
        success: true,
        amount: transferAmount.toNumber(),
        fromUser: {
          id: fromUser.id,
          referralCode: fromUser.referralCode,
          newBalance: new BigNumber(fromWallet.usdtBalance).minus(transferAmount).toNumber()
        },
        toUser: {
          id: toUser.id,
          referralCode: toUser.referralCode,
          newBalance: new BigNumber(toWallet.usdtBalance).plus(transferAmount).toNumber()
        },
        withdrawTransaction: savedWithdrawTransaction,
        depositTransaction: savedDepositTransaction,
        message: `Successfully transferred ${transferAmount.toFixed(2)} USDT`
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`[transferUsdt] Lỗi chuyển USDT: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
