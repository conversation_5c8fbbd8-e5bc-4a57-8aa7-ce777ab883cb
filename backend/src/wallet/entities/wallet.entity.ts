import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToOne, OneToMany, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Transaction } from './transaction.entity';
import { User } from '../../users/entities/user.entity';

@Entity('wallets')
export class Wallet {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'Wallet ID' })
  id: string;

  @ApiProperty({ description: 'User ID' })
  @Column()
  userId: string;

  @OneToOne(() => User, user => user.web3Wallet)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ApiProperty({ description: 'Wallet address' })
  @Column()
  address: string;

  @ApiProperty({ description: 'Wallet address', nullable: true })
  @Column({ nullable: true })
  addressV1: string;

  @ApiProperty({ description: 'Wallet type (e.g., WEB3)' })
  @Column({ default: 'WEB3' })
  type: string;

  @ApiProperty({ description: 'WM token balance' })
  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  wmBalance: number;

  @ApiProperty({ description: 'WM token balance (backup)' })
  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  wmBalanceBackup: number;

  @ApiProperty({ description: 'WM token locked balance' })
  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  wmLockedBalance: number;

  @ApiProperty({ description: 'WM token locked balance (backup)' })
  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  wmLockedBalanceBackup: number;

  @ApiProperty({ description: 'USDT balance' })
  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  usdtBalance: number;

  @ApiProperty({ description: 'USDT balance (backup)' })
  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  usdtBalanceBackup: number;

  @ApiProperty({ description: 'Is wallet active' })
  @Column({ default: true })
  isActive: boolean;

  @ApiProperty({ description: 'Encrypted private key' })
  @Column({ type: 'text', nullable: true })
  encryptedPrivateKey: string;

  @OneToMany(() => Transaction, transaction => transaction.wallet)
  transactions: Transaction[];

  @CreateDateColumn()
  @ApiProperty({ description: 'Wallet creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Wallet last update timestamp' })
  updatedAt: Date;
}