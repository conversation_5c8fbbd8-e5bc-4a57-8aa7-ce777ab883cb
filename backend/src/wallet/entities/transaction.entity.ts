import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Wallet } from './wallet.entity';
import { User } from '../../users/entities/user.entity';
import { Token } from '../../token/entities/token.entity';
import { TransactionType, TransactionStatus } from '../../common/enums/transaction.enum';

@Entity('transactions')
export class Transaction {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'Transaction ID' })
  id: string;

  @Column()
  @ApiProperty({ description: 'Wallet ID' })
  walletId: string;

  @ManyToOne(() => Wallet, wallet => wallet.transactions)
  @JoinColumn({ name: 'walletId' })
  wallet: Wallet;

  @Column()
  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ManyToOne('User')
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  @ApiProperty({ description: 'Token ID' })
  tokenId: string;

  @ManyToOne(() => Token)
  @JoinColumn({ name: 'tokenId' })
  token: Token;

  @ApiProperty({ description: 'Transaction type', enum: TransactionType })
  @Column({ type: 'enum', enum: TransactionType })
  type: TransactionType;

  @ApiProperty({ description: 'Transaction amount' })
  @Column({ type: 'decimal', precision: 18, scale: 8 })
  amount: number;

  @ApiProperty({ description: 'Transaction amount fee' })
  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  amountFee: number;

  @ApiProperty({ description: 'Transaction hash' })
  @Column({ nullable: true })
  txHash: string;

  @ApiProperty({ description: 'Transaction status', enum: TransactionStatus })
  @Column({ type: 'enum', enum: TransactionStatus, default: TransactionStatus.PENDING })
  status: TransactionStatus;

  @ApiProperty({ description: 'Transaction reference (e.g., task ID, purchase ID)' })
  @Column({ nullable: true })
  reference: string;

  @ApiProperty({ description: 'Transaction note' })
  @Column({ nullable: true })
  note: string;

  @ApiProperty({ description: 'Recipient wallet address for transfers' })
  @Column({ nullable: true })
  recipientAddress: string;

  @ApiProperty({ description: 'Sender wallet address for transfers' })
  @Column({ nullable: true })
  senderAddress: string;

  @ApiProperty({ description: 'Transaction at' })
  @Column({ nullable: true })
  transactionAt: Date;

  @CreateDateColumn()
  @ApiProperty({ description: 'Transaction creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Transaction last update timestamp' })
  updatedAt: Date;
} 