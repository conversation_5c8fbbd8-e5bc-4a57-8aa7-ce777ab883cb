import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('transaction_locks')
export class TransactionLock {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'Lock ID' })
  id: string;

  @Column({ unique: true })
  @ApiProperty({ description: 'Transaction ID' })
  transactionId: string;

  @Column()
  @ApiProperty({ description: 'Process ID' })
  processId: string;

  @Column({ default: true })
  @ApiProperty({ description: 'Is lock active' })
  isActive: boolean;

  @CreateDateColumn()
  @ApiProperty({ description: 'Lock creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Lock last update timestamp' })
  updatedAt: Date;
}
