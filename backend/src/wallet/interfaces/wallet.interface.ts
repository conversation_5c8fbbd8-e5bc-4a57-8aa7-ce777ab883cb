export interface IWallet {
  id: string;
  userId: string;
  address: string;
  type: string;
  balance: number;
  isActive: boolean;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICreateWalletParams {
  userId: string;
  type: string;
  balance: number;
  address: string;
  isActive?: boolean;
  metadata?: Record<string, any>;
}

export interface IUpdateWalletParams {
  balance?: number;
  isActive?: boolean;
  metadata?: Record<string, any>;
} 