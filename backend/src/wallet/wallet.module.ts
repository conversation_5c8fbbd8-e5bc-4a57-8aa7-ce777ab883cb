import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WalletService } from './wallet.service';
import { TransactionExpiryService } from './transaction-expiry.service';
import { WalletController } from './wallet.controller';
import { Wallet } from './entities/wallet.entity';
import { Transaction } from './entities/transaction.entity';
import { TransactionLock } from './entities/transaction-lock.entity';
import { UsersModule } from '../users/users.module';
import { TokenModule } from '../token/token.module';
import { CommonModule } from '../common/common.module';
import { SocketModule } from '../socket/socket.module';
import { Web3Service } from 'src/common/services/web3.service';
import { HttpModule } from '@nestjs/axios';
import { ApiKeyModule } from '../auth/api-key.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Wallet, Transaction, TransactionLock]),
    forwardRef(() => UsersModule),
    TokenModule,
    HttpModule,
    CommonModule,
    SocketModule,
    forwardRef(() => ApiKeyModule),
  ],
  controllers: [WalletController],
  providers: [WalletService, Web3Service, TransactionExpiryService],
  exports: [WalletService, Web3Service],
})
export class WalletModule {}