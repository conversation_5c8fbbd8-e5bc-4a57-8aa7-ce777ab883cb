import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan } from 'typeorm';
import { Transaction } from './entities/transaction.entity';
import { TransactionStatus, TransactionType } from '../common/enums/transaction.enum';
import { WalletService } from './wallet.service';
import { TokenService } from '../token/token.service';
import { Wallet } from './entities/wallet.entity';
import { TelegramBotService } from '../common/services/telegram-bot.service';
import { TELEGRAM_CHAT_IDS } from '../common/constants/telegram.constants';
import moment from 'moment-timezone';
import { getCurrentTime } from '../common/utils/date.utils';

@Injectable()
export class TransactionExpiryService {
  private readonly logger = new Logger(TransactionExpiryService.name);

  constructor(
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
    private readonly walletService: WalletService,
    private readonly tokenService: TokenService,
    private readonly telegramBotService: TelegramBotService,
  ) {
    // Ghi log khi service được khởi tạo
    this.logger.log('TransactionExpiryService initialized');
  }

  /**
   * Chạy mỗi phút để tự động hủy các giao dịch rút tiền PENDING quá 24h
   */
  @Cron(CronExpression.EVERY_HOUR)
  async handlePendingWithdrawals() {
    await this.checkAndCancelPendingWithdrawals();
  }

  /**
   * Phương thức có thể gọi thủ công để tự động hủy các giao dịch rút tiền PENDING quá hạn
   * @param hours Số giờ để xác định giao dịch quá hạn (mặc định: 24)
   */
  async checkAndCancelPendingWithdrawals(hours: number = 24) {
    this.logger.log(`Checking for pending withdrawal transactions older than ${hours} hours...`);

    try {
      // Tính thời gian hết hạn dựa trên số giờ
      const now = getCurrentTime();
      const expiryTime = new Date(now);
      expiryTime.setHours(expiryTime.getHours() - hours);

      // Tìm tất cả giao dịch rút tiền PENDING đã tạo trước thời gian hết hạn
      const expiredTransactions = await this.transactionRepository.find({
        where: {
          type: TransactionType.WITHDRAWAL,
          status: TransactionStatus.PENDING,
          createdAt: LessThan(expiryTime),
        },
        relations: ['user'],
      });

      this.logger.log(`Found ${expiredTransactions.length} pending withdrawal transactions older than ${hours} hours`);

      // Xử lý từng giao dịch hết hạn
      for (const transaction of expiredTransactions) {
        await this.cancelExpiredTransaction(transaction);
      }

      this.logger.log(`Auto-cancellation of pending withdrawals completed successfully. Processed ${expiredTransactions.length} transactions.`);
    } catch (error) {
      this.logger.error(`Error auto-cancelling pending withdrawals: ${error.message}`, error.stack);
    }
  }

  /**
   * Hủy giao dịch hết hạn và hoàn lại tiền cho người dùng
   */
  private async cancelExpiredTransaction(transaction: Transaction) {
    this.logger.log(`Auto-cancelling expired transaction ${transaction.id}`);

    const queryRunner = this.walletService.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Lấy thông tin token
      const token = await this.tokenService.findOne(transaction.tokenId);
      if (!token) {
        throw new Error(`Token with ID ${transaction.tokenId} not found`);
      }

      const wmToken = await this.tokenService.findBySymbol('WM');
      if (!wmToken) {
        throw new Error(`WM token not found`);
      }

      // Lấy thông tin ví
      const wallet = await queryRunner.manager.findOne(Wallet, {
        where: { id: transaction.walletId }
      });
      if (!wallet) {
        throw new Error(`Wallet with ID ${transaction.walletId} not found`);
      }

      const now = getCurrentTime();

      // Cập nhật trạng thái giao dịch thành CANCELLED
      await queryRunner.manager.update(Transaction, transaction.id, {
        status: TransactionStatus.CANCELLED,
        updatedAt: now,
        note: `${transaction.note} (Tự động hủy sau 24 giờ)`,
      });

      await queryRunner.manager.save(Transaction, {
        walletId: transaction.walletId,
        userId: transaction.userId,
        tokenId: wmToken.id,
        type: TransactionType.WITHDRAWAL,
        amount: Math.abs(transaction.amountFee),
        status: TransactionStatus.COMPLETED,
        note: `Refund withdrawal fee to ${wallet.address} (+${Math.abs(transaction.amountFee)} WM)`,
        recipientAddress: wallet.address,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      // Hoàn lại tiền cho người dùng
      await this.walletService.updateTokenBalanceWithQueryRunner(
        queryRunner,
        wallet,
        token,
        'usdtBalance',
        transaction,
        Math.abs(transaction.amount),
        'add',
        now
      );

      await this.walletService.updateTokenBalanceWithQueryRunner(
        queryRunner,
        wallet,
        wmToken,
        'wmBalance',
        transaction,
        Math.abs(transaction.amountFee),
        'add',
        now
      );

      // Commit transaction
      await queryRunner.commitTransaction();

      // Gửi thông báo đến Telegram
      await this.sendExpiryNotification(transaction);

      this.logger.log(`Successfully auto-cancelled transaction ${transaction.id} and refunded ${Math.abs(transaction.amount)} USDT`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error cancelling expired transaction ${transaction.id}: ${error.message}`, error.stack);
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Gửi thông báo về giao dịch tự động hủy đến Telegram
   */
  private async sendExpiryNotification(transaction: Transaction) {
    if (TELEGRAM_CHAT_IDS.length < 1) {
      this.logger.warn('No Telegram chat IDs configured, skipping notification');
      return;
    }

    const expiryMessage = `⏰ *WITHDRAWAL AUTO-CANCELLED*

📝 *Transaction ID:* \`${transaction.id}\`
💰 *Amount:* \`${Math.abs(transaction.amount)} USDT\`
👛 *Wallet:* \`${transaction.recipientAddress}\`
⏱️ *Created:* \`${moment(transaction.createdAt).format('YYYY-MM-DD HH:mm:ss')}\`
⌛ *Cancelled:* \`${moment(getCurrentTime()).format('YYYY-MM-DD HH:mm:ss')}\`

✅ *Action Taken:* Transaction automatically cancelled after 24 hours
💸 *Funds:* Returned to user's balance

🔄 *Status:* CANCELLED
`;

    try {
      for (const chatId of TELEGRAM_CHAT_IDS) {
        await this.telegramBotService.sendMessage(
          chatId,
          expiryMessage,
          {
            parse_mode: 'Markdown',
          },
        );
      }
      this.logger.log(`Auto-cancellation notification sent for transaction ${transaction.id}`);
    } catch (error) {
      this.logger.error(`Failed to send auto-cancellation notification: ${error.message}`, error.stack);
    }
  }
}
