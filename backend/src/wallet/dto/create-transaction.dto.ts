import { ApiProperty } from '@nestjs/swagger';
import { TransactionType, TransactionStatus } from '../../common/enums/transaction.enum';
import { IsUUID, IsNumber, IsEnum, IsString, IsOptional } from 'class-validator';

export class CreateTransactionDto {
  @ApiProperty({ description: 'Wallet ID' })
  @IsUUID()
  walletId: string;

  @ApiProperty({ description: 'User ID', required: false })
  @IsUUID()
  @IsOptional()
  userId?: string;

  @ApiProperty({ description: 'Token ID' })
  @IsUUID()
  tokenId: string;

  @ApiProperty({ description: 'Transaction type', enum: TransactionType })
  @IsEnum(TransactionType)
  type: TransactionType;

  @ApiProperty({ description: 'Transaction amount' })
  @IsNumber()
  amount: number;

  @ApiProperty({ description: 'Transaction status', enum: TransactionStatus, required: false })
  @IsEnum(TransactionStatus)
  @IsOptional()
  status?: TransactionStatus;

  @ApiProperty({ description: 'Transaction reference (e.g., task ID, purchase ID)', required: false })
  @IsString()
  @IsOptional()
  reference?: string;

  @ApiProperty({ description: 'Transaction note', required: false })
  @IsString()
  @IsOptional()
  note?: string;

  @ApiProperty({ description: 'Recipient wallet address for transfers', required: false })
  @IsString()
  @IsOptional()
  recipientAddress?: string;

  @ApiProperty({ description: 'Sender wallet address for transfers', required: false })
  @IsString()
  @IsOptional()
  senderAddress?: string;

  @ApiProperty({ description: 'Transaction hash', required: false })
  @IsString()
  @IsOptional()
  txHash?: string;
} 