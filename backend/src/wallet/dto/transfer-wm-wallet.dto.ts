import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsBoolean, IsObject, IsString } from 'class-validator';

export class TransferWmDto {
  @ApiProperty({ description: 'Amount WM to transfer' })
  @IsNumber()
  amount: number;

  @ApiProperty({ description: 'Wallet address to transfer' })
  @IsString()
  referralCode: string;

  @ApiProperty({ description: 'Wallet type to transfer' })
  @IsString()
  walletType: 'available' | 'freeze';
} 