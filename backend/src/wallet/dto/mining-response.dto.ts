import { ApiProperty } from '@nestjs/swagger';
import { UserRank } from '../../common/enums/user.enum';

export class MiningResponseDto {
  @ApiProperty({ description: 'Whether the mining operation was successful' })
  success: boolean;

  @ApiProperty({ description: 'Amount of WM tokens mined' })
  amount: number;

  @ApiProperty({ description: 'New WM balance after mining' })
  newBalance: string;

  @ApiProperty({ description: 'Total amount of WM tokens mined by the user' })
  totalMined: number;

  @ApiProperty({ description: 'User rank after mining', enum: UserRank })
  rank: UserRank;

  @ApiProperty({ description: 'Current mining difficulty' })
  difficulty: number;

  @ApiProperty({ description: 'Next mining difficulty threshold' })
  nextDifficulty: number;

  @ApiProperty({ description: 'Referral bonus amount (if applicable)' })
  referralBonus: number;

  @ApiProperty({ description: 'Last mining timestamp' })
  lastMiningTime: Date;

  @ApiProperty({ description: 'Current mining multiplier' })
  miningMultiplier: number;

  @ApiProperty({ description: 'Daily mining rate (WM per day)' })
  dailyMiningRate: number;

  @ApiProperty({ description: 'Bonus mining from referrals (25% of F1 daily)' })
  referralMiningBonus: number;

  @ApiProperty({ description: 'Whether user has lightning bolt active' })
  hasLightningBolt: boolean;

  @ApiProperty({ description: 'Success message' })
  message: string;
} 