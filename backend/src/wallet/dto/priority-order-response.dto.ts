import { ApiProperty } from '@nestjs/swagger';
import { TransactionStatus } from '../../common/enums/transaction.enum';

export class PriorityOrderResponseDto {
  @ApiProperty({ description: 'Order ID' })
  orderId: string;

  @ApiProperty({ description: 'USDT receiver address' })
  receiverAddress: string;

  @ApiProperty({ description: 'Amount to pay in USDT' })
  amount: number;

  @ApiProperty({ description: 'Order status', enum: TransactionStatus })
  status: TransactionStatus;

  @ApiProperty({ description: 'QR code data URL' })
  qrCode: string;
} 