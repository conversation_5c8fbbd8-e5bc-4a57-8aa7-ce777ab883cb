import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsBoolean, IsObject, IsString } from 'class-validator';

export class AdjustWmDto {
  @ApiProperty({ description: 'Amount WM to adjust' })
  @IsNumber()
  amount: number;

  @ApiProperty({ description: 'Type to adjust' })
  @IsString()
  type: 'add' | 'subtract';

  @ApiProperty({ description: 'Type to adjust' })
  @IsString()
  note?: string;
} 