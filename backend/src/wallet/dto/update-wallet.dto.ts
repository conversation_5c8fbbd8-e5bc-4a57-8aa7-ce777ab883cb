import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsBoolean, IsObject } from 'class-validator';

export class UpdateWalletDto {
  @ApiProperty({ description: 'WM token balance' })
  @IsNumber()
  @IsOptional()
  wmBalance?: number;

  @ApiProperty({ description: 'USDT balance' })
  @IsNumber()
  @IsOptional()
  usdtBalance?: number;

  @ApiProperty({ description: 'Is wallet active' })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({ description: 'Additional metadata' })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
} 