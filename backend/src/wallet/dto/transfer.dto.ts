import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsOptional, Min } from 'class-validator';

export class TransferDto {
  @ApiProperty({ description: 'Source wallet ID' })
  @IsString()
  fromWalletId: string;

  @ApiProperty({ description: 'Recipient wallet address' })
  @IsString()
  toAddress: string;

  @ApiProperty({ description: 'Amount to transfer in WM tokens' })
  @IsNumber()
  @Min(0.00000001)
  amount: number;

  @ApiProperty({ description: 'Transfer note', required: false })
  @IsString()
  @IsOptional()
  note?: string;
} 