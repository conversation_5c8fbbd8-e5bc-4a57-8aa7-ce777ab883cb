import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, IsOptional, Min } from 'class-validator';

export class TransferUsdtDto {
  @ApiProperty({ 
    description: 'Amount USDT to transfer',
    example: 100.50,
    minimum: 0.01
  })
  @IsNumber()
  @Min(0.01, { message: 'Amount must be at least 0.01 USDT' })
  amount: number;

  @ApiProperty({ 
    description: 'Recipient wallet address or referral code',
    example: '****************************************** or ABC123'
  })
  @IsString()
  recipient: string;

  @ApiProperty({ 
    description: 'Transfer note/memo',
    example: 'Payment for services',
    required: false
  })
  @IsString()
  @IsOptional()
  note?: string;
}
