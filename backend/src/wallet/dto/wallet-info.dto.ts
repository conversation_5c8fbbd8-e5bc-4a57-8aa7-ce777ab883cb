import { ApiProperty } from '@nestjs/swagger';

export class TokenBalanceDto {
  @ApiProperty({ example: 'USDT', description: 'Token symbol' })
  symbol: string;

  @ApiProperty({ example: '1000.00', description: 'Token balance' })
  balance: string;

  @ApiProperty({ example: '0x...', description: 'Token contract address' })
  contractAddress: string;

  @ApiProperty({ example: 18, description: 'Token decimals' })
  decimals: number;
}

export class WalletInfoDto {
  @ApiProperty({ example: '0x...', description: 'Wallet address' })
  address: string;

  @ApiProperty({ example: 'USDT', description: 'Default currency' })
  defaultCurrency: string;

  @ApiProperty({ type: [TokenBalanceDto], description: 'List of token balances' })
  tokens: TokenBalanceDto[];

  @ApiProperty({ example: true, description: 'Whether the wallet is active' })
  isActive: boolean;
} 