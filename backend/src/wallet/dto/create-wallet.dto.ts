import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsOptional, IsBoolean, IsObject } from 'class-validator';

export class CreateWalletDto {
  @ApiProperty({ description: 'User ID' })
  @IsString()
  userId: string;

  @ApiProperty({ description: 'Wallet address' })
  @IsString()
  address: string;

  @ApiProperty({ description: 'Wallet type (e.g., WEB3)' })
  @IsString()
  type: string;

  @ApiProperty({ description: 'Initial WM balance', default: 0 })
  @IsNumber()
  @IsOptional()
  wmBalance?: number;

  @ApiProperty({ description: 'Initial USDT balance', default: 0 })
  @IsNumber()
  @IsOptional()
  usdtBalance?: number;

  @ApiProperty({ description: 'Is wallet active', default: true })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({ description: 'Additional metadata', required: false })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
} 