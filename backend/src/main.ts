import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import { CustomLoggerService } from './common/logger/logger.service';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';
import * as fs from 'fs';
import * as path from 'path';
import cookieParser from 'cookie-parser';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { ConfigService } from '@nestjs/config';

async function bootstrap() {
  // Log environment info
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('Current working directory:', process.cwd());

  const app = await NestFactory.create(AppModule, {
    logger: false, // Disable default logger
  });

  // Use cookie-parser middleware
  app.use(cookieParser());

  // Sử dụng custom logger
  const logger = app.get(CustomLoggerService);
  app.useLogger(logger);

  // Log environment variables loading
  logger.log(`Loading environment for ${process.env.NODE_ENV} mode`);
  logger.log(`USDT_CONTRACT_ADDRESS: ${process.env.USDT_CONTRACT_ADDRESS}`);
  logger.log(`BSC_WEBSOCKET_URL: ${process.env.BSC_WEBSOCKET_URL}`);

  // Thiết lập global prefix cho API
  app.setGlobalPrefix('api/v1');

  // Thiết lập validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // Áp dụng TransformInterceptor cho tất cả các route
  app.useGlobalInterceptors(new TransformInterceptor());

  // Thiết lập CORS
  app.enableCors({
    origin: process.env.NODE_ENV === 'production' ? ['https://worldmall.app'] : ['http://localhost:3000'],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
    allowedHeaders: [
      'Content-Type',
      'Accept',
      'Authorization',
      'X-Requested-With',
      'Origin',
      'Access-Control-Allow-Origin',
      'Access-Control-Allow-Headers',
      'Access-Control-Allow-Methods',
      'Access-Control-Allow-Credentials'
    ],
    exposedHeaders: ['Set-Cookie', 'Authorization'],
    preflightContinue: false,
    optionsSuccessStatus: 204
  });


  // Thiết lập Swagger
  if (process.env.NODE_ENV === 'development') {
    const config = new DocumentBuilder()
      .setTitle('WorldMall Crypto Platform API')
      .setDescription('API documentation for WorldMall Crypto Platform')
      .setVersion('1.0')
      .addTag('auth', 'Authentication endpoints')
      .addTag('users', 'User management endpoints')
      .addTag('wallet', 'Wallet management endpoints')
      .addTag('token', 'Token management endpoints')
      .addBearerAuth()
      .addServer('http://localhost:5000', 'Local development')
      .addServer('https://api.worldmall.crypto', 'Production')
      .build();

    const document = SwaggerModule.createDocument(app, config);

    // Save Swagger JSON
    const swaggerJson = JSON.stringify(document, null, 2);
    fs.writeFileSync(path.join(process.cwd(), 'swagger.json'), swaggerJson);

    SwaggerModule.setup('api-docs', app, document);
  }

  const configService = app.get(ConfigService);
  app.useGlobalFilters(new HttpExceptionFilter(configService));

  const port = process.env.PORT || 5000;
  await app.listen(port, '0.0.0.0');
  logger.log(`[Application] is running on port: ${port}`);
  logger.log(`[Application] Environment: ${process.env.NODE_ENV}`);
  logger.log(`[Application] CORS origin: ${process.env.NODE_ENV === 'production' ? 'https://worldmall.app' : 'http://localhost:3000'}`);
  logger.log(`[Application] API prefix: /api/v1`);
  logger.log(`[Application] Swagger documentation is available at: http://localhost:${port}/api-docs`);
}

bootstrap().catch(err => {
  console.error('[Application] Failed to start application:', err);
  process.exit(1);
});
