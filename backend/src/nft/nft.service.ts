import {
    BadRequestException,
    Injectable,
    InternalServerErrorException,
    Logger,
    NotFoundException,
    OnModuleInit
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { BigNumber } from 'bignumber.js';
import moment from 'moment-timezone';
import { TransactionStatus, TransactionType } from 'src/common/enums/transaction.enum';
import { StakingStatus, StakingTransaction } from 'src/staking/entities/staking-transaction.entity';
import { Transaction } from 'src/wallet/entities/transaction.entity';
import { DataSource, IsNull, Repository } from 'typeorm';
import { SystemConfigService } from '../common/services/system-config.service';
import { TokenService } from '../token/token.service';
import { UsersService } from '../users/users.service';
import { WalletService } from '../wallet/wallet.service';
import { NFTOrder, OrderStatus, OrderType } from './entities/nft-order.entity';
import { NFTSessionStatus } from './entities/nft-trading-session.entity';
import { NFT, NFTStatus, NFTType } from './entities/nft.entity';
import { TradingSessionService } from './services/trading-session.service';

interface NFTConfig {
    type: NFTType;
    price: number;
    gasFee: number;
    quantity: string;
}

@Injectable()
export class NFTService implements OnModuleInit {
    private readonly logger = new Logger(NFTService.name);
    private readonly nftConfigs: Map<NFTType, NFTConfig> = new Map();

    constructor(
        @InjectRepository(NFT)
        private readonly nftRepository: Repository<NFT>,
        @InjectRepository(NFTOrder)
        private readonly orderRepository: Repository<NFTOrder>,
        private readonly usersService: UsersService,
        private readonly tokenService: TokenService,
        private readonly walletService: WalletService,
        private readonly systemConfigService: SystemConfigService,
        private readonly tradingSessionService: TradingSessionService,
        private readonly dataSource: DataSource,
    ) {
    }

    private async loadNFTConfig(type: NFTType): Promise<NFTConfig> {
        const typeKey = type.toString();
        const [priceConfig, gasFeeConfig, quantityConfig] = await Promise.all([
            this.systemConfigService.findByKey(`NFT_${typeKey}_PRICE`),
            this.systemConfigService.findByKey(`NFT_${typeKey}_GAS_FEE`),
            this.systemConfigService.findByKey(`NFT_${typeKey}_QUANTITY`)
        ]);

        return {
            type,
            price: parseFloat(priceConfig?.value || '0'),
            gasFee: parseFloat(gasFeeConfig?.value || '0'),
            quantity: quantityConfig?.value || '0',
        };
    }

    private async initializeNFTConfigs(): Promise<void> {
        const nftTypes = [NFTType.PHOENIX, NFTType.SPIRIT_TURTLE, NFTType.UNICORN, NFTType.DRAGON];

        await Promise.all(
            nftTypes.map(async (type) => {
                const config = await this.loadNFTConfig(type);
                this.nftConfigs.set(type, config);
            })
        );
    }

    private createNFTEntity(config: NFTConfig): NFT {
        return this.nftRepository.create({
            type: config.type,
            currentPrice: config.price,
            initialPrice: config.price,
            gasFee: config.gasFee,
            status: NFTStatus.LISTED,
            quantity: config.quantity,
            ownerId: null,
            owner: null,
            createdAt: moment().tz('Asia/Dubai').toDate(),
            updatedAt: moment().tz('Asia/Dubai').toDate(),
        });
    }

    private async initializeDefaultNFTs(): Promise<void> {
        const existingNFTs = await this.nftRepository.find();
        if (existingNFTs.length > 0) return;

        const nftEntities = Array.from(this.nftConfigs.values()).map(config =>
            this.createNFTEntity(config)
        );

        await this.nftRepository.save(nftEntities);
    }

    /**
     * Khởi tạo NFT Service
     */
    async onModuleInit(): Promise<void> {
        try {
            await this.initializeNFTConfigs();
            await this.initializeDefaultNFTs();
        } catch (error) {
            this.logger.error('[NFTService][onModuleInit] Lỗi khởi tạo NFT Service', error);
            throw error;
        }
    }

    /**
     * Lấy toàn bộ danh sách NFT
     * @returns Danh sách toàn bộ NFT
     */
    async getAllNFTs(): Promise<NFT[]> {
        return this.nftRepository.find({order: {createdAt: 'ASC'}});
    }

    /**
     * Lấy danh sách NFT đang được gửi bán
     * @returns Danh sách NFT đang được gửi bán
     */
    async getListedNFTs(): Promise<NFT[]> {
        return this.nftRepository.find({
            where: {
                status: NFTStatus.LISTED,
            },
            order: {
                currentPrice: 'DESC'
            }
        });
    }

    /**
     * Lấy danh sách NFT của người dùng
     * @param userId - ID của người dùng
     * @returns Danh sách NFT của người dùng
     */
    async getMyNFTs(userId: string) {
        return this.nftRepository.find({where: {ownerId: userId}, order: {createdAt: 'ASC'}});
    }

    /**
     * Listing NFT - Gửi bán NFT (Cập nhật trạng thái AVAILABLE -> LISTED)
     * @param userId - ID của người dùng
     * @param nftId - ID của NFT
     * @returns NFT đã được gửi bán
     */
    async listingNFT(userId: string, nftId: string, quantity: string) {

        // 1. Lấy thông tin NFT
        const nft = await this.nftRepository.findOne({where: {id: nftId}});
        if (!nft) {
            this.logger.debug(`[NFTService][listingNFT] Không tìm thấy NFT ${nftId}`);
            throw new NotFoundException({
                message: { en: 'NFT not found' },
                code: 'NFT_NOT_FOUND'
            });
        }

        if (nft.ownerId !== userId) {
            this.logger.debug(`[NFTService][listingNFT] Người dùng ${userId} không phải là chủ sở hữu NFT ${nftId}`);
            throw new BadRequestException('You are not the owner of this NFT');
        }

        if ((new BigNumber(nft.quantity)).lte(0)) {
            this.logger.debug(`[NFTService][listingNFT] NFT ${nftId} đã hết hàng`);
            throw new BadRequestException('NFT is out of stock');
        }

        const currentQuantity = new BigNumber(quantity);
        if ((new BigNumber(nft.quantity)).lt(currentQuantity)) {
            this.logger.debug(`[NFTService][listingNFT] NFT ${nftId} không đủ số lượng để gửi bán`);
            throw new BadRequestException('Insufficient quantity to list');
        }

        // Kiểm tra xem có đủ phí gas để gửi bán NFT (0.5% số lượng NFT)
        const wmUsdRateConfig = await this.systemConfigService.findByKey('WM_USD_RATE');
        const wmUsdRate = Number(wmUsdRateConfig?.value || 0);

        if (wmUsdRate <= 0) {
            throw new BadRequestException({
                message: {
                    en: 'Invalid WM rate configuration',
                },
                code: 'INVALID_WM_USD_RATE',
            });
        }

        const tokenWM = await this.tokenService.findBySymbol('WM');
        const wallet = await this.walletService.findByUserId(userId);

        const wmBalance = new BigNumber(wallet.wmBalance); // Số dư WM
        const totalGasFeeInWM = new BigNumber(quantity)
            .multipliedBy(nft.currentPrice) // Giá trị NFT (USDT ≈ USD)
            .multipliedBy(0.005) // Phí gas 0.5% (USDT)
            .dividedBy(wmUsdRate); // Quy đổi sang WM (1 USDT = wmUsdRate WM)

        if (wmBalance.lt(totalGasFeeInWM)) {
            throw new BadRequestException(
                {
                    message: {
                        en: 'Insufficient WM balance for gas fee',
                    },
                    code: 'INSUFFICIENT_WM_BALANCE',
                });
        }

        const now = moment().tz('Asia/Dubai').toDate();

        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            const currentQuantity = new BigNumber(quantity);
            nft.quantity = currentQuantity.minus(quantity).toString();
            await queryRunner.manager.save(nft);

            // Tạo đơn hàng nft_orders loại SELL
            const order = queryRunner.manager.create(NFTOrder, {
                orderType: OrderType.SELL,
                traderId: userId,
                nftId: nftId,
                nftType: nft.type,
                nftPrice: nft.currentPrice,
                status: OrderStatus.PENDING,
                gasFee: nft.gasFee,
                gasFeePercentage: 100,
                quantity: currentQuantity.toString(),
                soldQuantity: '0',
                remainingQuantity: currentQuantity.toString(),
                createdAt: now,
                updatedAt: now,
            });

            await queryRunner.manager.save(order);

            // Tạo giao dịch (phí gas WM)
            const transaction = queryRunner.manager.create(Transaction, {
                userId: userId,
                walletId: wallet.id,
                type: TransactionType.NFT_PURCHASE,
                status: TransactionStatus.COMPLETED,
                amount: -totalGasFeeInWM.toNumber(),
                tokenId: tokenWM.id,
                reference: order.id,
                note: `Gas fee for listing NFT ${nft.type} (-${totalGasFeeInWM.toNumber()}WM)`,
                transactionAt: now,
                createdAt: now,
                updatedAt: now,
            });

            const savedTransaction = await queryRunner.manager.save(Transaction, transaction);

            await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, wallet, tokenWM, 'wmBalance', savedTransaction, totalGasFeeInWM.toNumber(), 'subtract', now);

            this.logger.debug(`[NFTService][listingNFT] Đã cập nhật NFT: ${nft}, đơn hàng: ${order}`);
            await queryRunner.commitTransaction();
            return nft;
        } catch (error) {
            this.logger.error(`[NFTService][listingNFT] Lỗi khi gửi bán NFT: ${error.message}`);
            await queryRunner.rollbackTransaction();
            throw new InternalServerErrorException(`Something went wrong, please try again later`);
        } finally {
            await queryRunner.release();
        }
    }

    /**
     * Lấy danh sách NFT đang được gửi bán theo phiên giao dịch
     * @param sessionId - ID của phiên giao dịch
     * @returns Danh sách NFT đang được gửi bán theo phiên giao dịch
     */
    async getListedBySession(sessionId: string): Promise<NFT[]> {
        const session = await this.tradingSessionService.getSessionById(sessionId);
        if (!session) {
            this.logger.debug(`[NFTService][getListedBySession] Không tìm thấy phiên giao dịch ${sessionId}`);
            throw new NotFoundException('Trading session not found');
        }

        // Lấy danh sách NFT đang được gửi bán trước thời gian phiên diễn ra gồm danh sách NFTStatus.LISTED và danh sách nftId có trong đơn hàng PENDING
        const listedNFTs = await this.nftRepository.find({where: {status: NFTStatus.LISTED, ownerId: IsNull()}});
        return listedNFTs;
    }

    /**
     * Tạo đơn hàng NFT (Schedule)
     * @param sessionId - ID của phiên giao dịch
     * @param userId - ID của người dùng
     * @param nftType - Loại NFT
     * @returns Đơn hàng NFT đã được tạo
     */

    async scheduleNFT(sessionId: string, userId: string, nftId: string): Promise<NFTOrder> {

        // 1. Lấy thông tin người dùng
        const user = await this.usersService.findById(userId);
        if (!user) {
            this.logger.debug(`[NFTService][scheduleNFT] Không tìm thấy người dùng ${userId}`);
            throw new NotFoundException(
                {
                    message: {
                        en: 'User not found',
                    },
                    code: 'USER_NOT_FOUND',
                });
        }

        // 1. Lấy thông tin phiên giao dịch
        const session = await this.tradingSessionService.getSessionById(sessionId);
        if (!session || session.status === NFTSessionStatus.OPEN) {
            this.logger.debug(`[NFTService][scheduleNFT] Phiên giao dịch ${sessionId} không tồn tại hoặc đang mở`);
            throw new BadRequestException(
            {
                message: {
                    en: 'Trading session is not exist or is open',
                },
                code: 'TRADING_SESSION_NOT_FOUND',
            });
        }

        // 2. Lấy thông tin NFT
        const nft = await this.nftRepository.findOne({where: {id: nftId}});
        if (!nft) {
            this.logger.debug(`[NFTService][scheduleNFT] Không tìm thấy NFT ${nftId}`);
            throw new NotFoundException(
                {
                    message: {
                        en: 'NFT not found',
                    },
                    code: 'NFT_NOT_FOUND',
                });
        }

        // 3. Lấy thông tin ví người dùng
        const wallet = await this.walletService.findByUserId(userId);
        if (!wallet) {
            this.logger.debug(`[NFTService][scheduleNFT] Không tìm thấy ví người dùng ${userId}`);
            throw new NotFoundException(
                {
                    message: {
                        en: 'User wallet not found',
                    },
                    code: 'USER_WALLET_NOT_FOUND',
                });
        }

        // 4. Lấy thông tin tỉ giá token WM
        const wmUsdRateConfig = await this.systemConfigService.findByKey('WM_USD_RATE');
        const wmUsdRate = Number(wmUsdRateConfig?.value || 0);

        if (wmUsdRate <= 0) {
            throw new BadRequestException(
                {
                    message: {
                        en: 'Invalid WM rate configuration',
                    },
                    code: 'INVALID_WM_USD_RATE',
                });
        }

        const tokenWM = await this.tokenService.findBySymbol('WM');
        const tokenUSDT = await this.tokenService.findBySymbol('USDT');

        const wmBalance = new BigNumber(wallet.wmBalance);
        const usdtBalance = new BigNumber(wallet.usdtBalance);

        // 5. Kiểm tra số dư WM cho phí gas và phí mua NFT
        const gasFeeInWM = new BigNumber(nft.gasFee).dividedBy(wmUsdRate); // Quy đổi USDT sang WM
        if (wmBalance.lt(gasFeeInWM)) {
            this.logger.debug(`[NFTService][scheduleNFT] Không đủ số dư WM cho phí gas và phí mua NFT ${nft.gasFee} - WM: ${wmBalance}`);
            throw new BadRequestException(
                {
                    message: {
                        en: 'Insufficient WM balance for gas fee and purchase',
                    },
                    code: 'INSUFFICIENT_WM_BALANCE',
                });
        }

        // 6. Kiểm tra đủ số dư USDT để mua
        if (usdtBalance.lt(nft.currentPrice)) {
            this.logger.debug(`[NFTService][scheduleNFT] Không đủ số dư USDT để mua ${nft.currentPrice}- USDT: ${usdtBalance}`);
            throw new BadRequestException(
                {
                    message: {
                        en: 'Insufficient USDT balance for purchase',
                    },
                    code: 'INSUFFICIENT_USDT_BALANCE',
                });
        }

        // 6. Bắt đầu giao dịch
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        const now = moment().tz('Asia/Dubai').toDate();

        try {
            // 25.05
            /**
             * Muốn tham gia mua bán NFT thì phải mua 1 gói global Mining tương đương
                Ví dụ Muốn mua bán NFT 1000$ thì phải mua 1 gói global mining 1000$
                Muốn mua bán NFT 10.000$ thì phải tham gia gói global mining 10.000$
             */

            // Lấy tất cả các gói staking đang hoạt động của user
            const stakingTransactions = await queryRunner.manager.find(StakingTransaction, {
                where: { userId, status: StakingStatus.ACTIVE },
            });

            // Lấy tất cả các đơn hàng đang chờ của user
            const nftOrders = await queryRunner.manager.find(NFTOrder, {
                where: { traderId: userId, status: OrderStatus.PENDING, orderType: OrderType.BUY },
            });

            // Tính tổng số tiền đang staking và tổng số tiền đang đặt trước
            const totalStakingAmount = stakingTransactions.reduce((acc, staking) => acc.plus(staking.amount), new BigNumber(0));
            const totalNFTOrderAmount = nftOrders.reduce((acc, order) => acc.plus(order.nftPrice).multipliedBy(order.quantity), new BigNumber(0));
            const totalAmount = totalNFTOrderAmount.plus(nft.currentPrice);

            // Kiểm tra số dư staking <= số tiền đặt trước + số tiền đặt trước mới
            if (totalStakingAmount.lt(totalAmount)) {
                this.logger.debug(`[NFTService][scheduleNFT] Không đủ số dư staking để mua ${totalAmount}- Staking: ${totalStakingAmount}`);
                throw new BadRequestException(
                    {
                        message: {
                            en: 'Insufficient staking balance for purchase',
                        },
                        code: 'INSUFFICIENT_STAKING_BALANCE',
                    });
            }

            // 7. Tạo đơn hàng đặt trước
            const order = queryRunner.manager.create(NFTOrder, {
                orderType: OrderType.BUY,
                traderId: userId,
                nftId,
                nftType: nft.type,
                nftPrice: nft.currentPrice,
                status: OrderStatus.PENDING,
                sessionType: session.type,
                sessionId: session.id,
                session: session,
                gasFee: nft.gasFee,
                gasFeePercentage: 100,
                sessionTime: session.openTime,
                quantity: '1',
                remainingQuantity: '1',
                createdAt: now,
                updatedAt: now,
                isScheduled: true, // Đặt trước
                isBuy: true, // Đặt trước
                isPriorityBuy: user.isPriorityBuy,
            });

            // 8. Lưu đơn hàng
            const savedOrder = await queryRunner.manager.save(order);

            // 10. Tạo giao dịch (USDT)
            const transactionUSDT = queryRunner.manager.create(Transaction, {
                userId: userId,
                walletId: wallet.id,
                type: TransactionType.NFT_PURCHASE,
                status: TransactionStatus.COMPLETED,
                amount: -Number(nft.currentPrice),
                tokenId: tokenUSDT.id,
                reference: order.id,
                note: `Buy NFT ${nft.type} with session ${sessionId} (-${nft.currentPrice}USDT)`,
                transactionAt: now,
                createdAt: now,
                updatedAt: now,
            });

            const savedTransactionUSDT = await queryRunner.manager.save(Transaction, transactionUSDT);

            await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, wallet, tokenUSDT, 'usdtBalance', savedTransactionUSDT, nft.currentPrice, 'subtract', now);

            // 9. Tạo giao dịch (phí gas WM)
            const transaction = queryRunner.manager.create(Transaction, {
                userId: userId,
                walletId: wallet.id,
                type: TransactionType.NFT_PURCHASE,
                status: TransactionStatus.COMPLETED,
                amount: -gasFeeInWM.toNumber(),
                tokenId: tokenWM.id,
                reference: order.id,
                note: `Gas fee for schedule NFT ${nft.type} with session ${sessionId} (-${gasFeeInWM.toNumber()}WM)`,
                transactionAt: now,
                createdAt: now,
                updatedAt: now,
            });

            const savedTransaction = await queryRunner.manager.save(Transaction, transaction);

            // 10. Cập nhật số dư WM
            await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, wallet, tokenWM, 'wmBalance', savedTransaction, gasFeeInWM.toNumber(), 'subtract', now);

            await queryRunner.commitTransaction();

            return savedOrder;
        } catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`[NFTService][scheduleNFT] Lỗi khi tạo đơn hàng NFT cho ${userId}, nftId: ${nftId}, sessionId: ${sessionId}: ${error.message}`);

            // Re-throw business logic errors (BadRequestException, NotFoundException)
            if (error instanceof BadRequestException || error instanceof NotFoundException) {
                throw error;
            }

            // Only wrap unexpected errors as InternalServerErrorException
            throw new InternalServerErrorException(
                {
                message: {
                    en: 'Failed to create NFT order',
                },
                code: 'CREATE_NFT_ORDER_FAILED',
            });
        } finally {
            await queryRunner.release();
        }
    }

    /**
     * Tạo đơn hàng NFT
     * @param sessionId - ID của phiên giao dịch
     * @param userId - ID của người dùng
     * @param nftType - Loại NFT
     * @returns Đơn hàng NFT đã được tạo
     */

    async orderNFT(sessionId: string, userId: string, nftId: string): Promise<NFTOrder> {
        // 1. Lấy thông tin người dùng
        const user = await this.usersService.findById(userId);
        if (!user) {
            this.logger.debug(`[NFTService][orderNFT] Không tìm thấy người dùng ${userId}`);
            throw new NotFoundException({
                message: {
                  en: 'User not found',
                },
                code: 'USER_NOT_FOUND',
              });
        }

        // 1. Lấy thông tin phiên giao dịch
        const session = await this.tradingSessionService.getSessionById(sessionId);
        if (!session || session.status !== NFTSessionStatus.OPEN) {
            this.logger.debug(`[NFTService][orderNFT] Phiên giao dịch ${sessionId} không tồn tại hoặc đang mở`);
            throw new BadRequestException({
                message: {
                en: 'Trading session is not exist or is not open',
                },
                code: 'TRADING_SESSION_NOT_FOUND',
            });
        }

        // 2. Lấy thông tin NFT
        const nft = await this.nftRepository.findOne({where: {id: nftId}});
        if (!nft) {
            this.logger.debug(`[NFTService][orderNFT] Không tìm thấy NFT ${nftId}`);
            throw new NotFoundException({
                message: {
                    en: 'NFT not found',
                },
                code: 'NFT_NOT_FOUND',
            });
        }

        // 3. Lấy thông tin ví người dùng
        const wallet = await this.walletService.findByUserId(userId);
        if (!wallet) {
            this.logger.debug(`[NFTService][orderNFT] Không tìm thấy ví người dùng ${userId}`);
            throw new NotFoundException({
                message: {
                    en: 'User wallet not found',
                },
                code: 'USER_WALLET_NOT_FOUND',
            });
        }

        // 4. Lấy thông tin tỉ giá token WM
        const wmUsdRateConfig = await this.systemConfigService.findByKey('WM_USD_RATE');
        const wmUsdRate = Number(wmUsdRateConfig?.value || 0);

        if (wmUsdRate <= 0) {
            throw new BadRequestException({
                message: {
                    en: 'Invalid WM rate configuration',
                },
                code: 'INVALID_WM_USD_RATE',
            });
        }

        const tokenWM = await this.tokenService.findBySymbol('WM');
        const tokenUSDT = await this.tokenService.findBySymbol('USDT');

        const wmBalance = new BigNumber(wallet.wmBalance);
        const usdtBalance = new BigNumber(wallet.usdtBalance);

        // 5. Kiểm tra số dư WM cho phí gas
        const gasFeeInWM = new BigNumber(nft.gasFee).dividedBy(wmUsdRate); // Quy đổi USDT sang WM
        if (wmBalance.lt(gasFeeInWM)) {
            this.logger.debug(`[NFTService][orderNFT] Không đủ số dư WM cho phí gas ${gasFeeInWM} - WM: ${wmBalance}`);
            throw new BadRequestException(
            {
                message: {
                    en: 'Insufficient WM balance for gas fee',
                },
                code: 'INSUFFICIENT_WM_BALANCE',
            });
        }

        // 6. Kiểm tra đủ số dư USDT để mua
        if (usdtBalance.lt(nft.currentPrice)) {
            this.logger.debug(`[NFTService][orderNFT] Không đủ số dư USDT để mua ${nft.currentPrice}- USDT: ${usdtBalance}`);
            throw new BadRequestException(
                {
                message: {
                    en: 'Insufficient USDT balance for purchase',
                },
                code: 'INSUFFICIENT_USDT_BALANCE',
            });
        }

        // 7. Bắt đầu giao dịch
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        const now = moment().tz('Asia/Dubai').toDate();

        try {
            // 25.05
            /**
             * Muốn tham gia mua bán NFT thì phải mua 1 gói global Mining tương đương
                Ví dụ Muốn mua bán NFT 1000$ thì phải mua 1 gói global mining 1000$
                Muốn mua bán NFT 10.000$ thì phải tham gia gói global mining 10.000$
             */

            // Lấy tất cả các gói staking đang hoạt động của user
            const stakingTransactions = await queryRunner.manager.find(StakingTransaction, {
                where: { userId, status: StakingStatus.ACTIVE },
            });

            // Lấy tất cả các đơn hàng đang chờ của user
            const nftOrders = await queryRunner.manager.find(NFTOrder, {
                where: { traderId: userId, status: OrderStatus.PENDING, orderType: OrderType.BUY },
            });

            // Tính tổng số tiền đang staking và tổng số tiền đang đặt trước
            const totalStakingAmount = stakingTransactions.reduce((acc, staking) => acc.plus(staking.amount), new BigNumber(0));
            const totalNFTOrderAmount = nftOrders.reduce((acc, order) => acc.plus(order.nftPrice).multipliedBy(order.quantity), new BigNumber(0));
            const totalAmount = totalNFTOrderAmount.plus(nft.currentPrice);

            // Kiểm tra số dư staking <= số tiền đặt trước + số tiền đặt trước mới
            if (totalStakingAmount.lt(totalAmount)) {
                this.logger.debug(`[NFTService][scheduleNFT] Không đủ số dư staking để mua ${totalAmount}- Staking: ${totalStakingAmount}`);
                throw new BadRequestException({
                    message: {
                        en: 'Insufficient staking balance for purchase',
                    },
                    code: 'INSUFFICIENT_STAKING_BALANCE',
                });
            }

            // 8. Tạo đơn hàng
            const order = queryRunner.manager.create(NFTOrder, {
                orderType: OrderType.BUY,
                traderId: userId,
                nftId,
                nftType: nft.type,
                nftPrice: nft.currentPrice,
                status: OrderStatus.PENDING,
                sessionType: session.type,
                sessionId: session.id,
                session: session,
                gasFee: nft.gasFee,
                gasFeePercentage: 100,
                sessionTime: session.openTime,
                quantity: '1',
                createdAt: now,
                updatedAt: now,
                isScheduled: false, // Mua trực tiếp
                isBuy: true, // Mua trực tiếp
                isPriorityBuy: user.isPriorityBuy,
            });

            // 9. Lưu đơn hàng đặt trước
            await queryRunner.manager.save(order);

            // 10. Tạo giao dịch (USDT)
            const transactionUSDT = queryRunner.manager.create(Transaction, {
                userId: userId,
                walletId: wallet.id,
                type: TransactionType.NFT_PURCHASE,
                status: TransactionStatus.COMPLETED,
                amount: -Number(nft.currentPrice),
                tokenId: tokenUSDT.id,
                reference: order.id,
                note: `Buy NFT ${nft.type} with session ${sessionId} (-${nft.currentPrice}USDT)`,
                transactionAt: now,
                createdAt: now,
                updatedAt: now,
            });

            const savedTransactionUSDT = await queryRunner.manager.save(Transaction, transactionUSDT);

            await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, wallet, tokenUSDT, 'usdtBalance', savedTransactionUSDT, nft.currentPrice, 'subtract', now);

            const transactionWM = queryRunner.manager.create(Transaction, {
                userId: userId,
                walletId: wallet.id,
                type: TransactionType.NFT_PURCHASE,
                status: TransactionStatus.COMPLETED,
                amount: -gasFeeInWM.toNumber(),
                tokenId: tokenWM.id,
                reference: order.id,
                note: `Gas fee for buy NFT ${nft.type} with session ${sessionId} (-${gasFeeInWM.toNumber()}WM)`,
                transactionAt: now,
                createdAt: now,
                updatedAt: now,
            });

            const savedTransactionWM = await queryRunner.manager.save(Transaction, transactionWM);

            // 11. Cập nhật số dư WM
            await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, wallet, tokenWM, 'wmBalance', savedTransactionWM, gasFeeInWM.toNumber(), 'subtract', now);

            await queryRunner.commitTransaction();
            return order;
        } catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`[NFTService][orderNFT] Lỗi khi tạo đơn hàng NFT: ${error.message}`);

            // Re-throw business logic errors (BadRequestException, NotFoundException)
            if (error instanceof BadRequestException || error instanceof NotFoundException) {
                throw error;
            }

            // Only wrap unexpected errors as InternalServerErrorException
            throw new InternalServerErrorException(
                {
                message: {
                    en: 'Failed to create NFT order',
                },
                code: 'CREATE_NFT_ORDER_FAILED',
            });
        } finally {
            await queryRunner.release();
        }
    }

    /**
     * Danh sách đơn hàng của tôi
     * @param userId - ID của người dùng
     * @returns Danh sách đơn hàng của tôi
     */
    async getMyOrders(userId: string): Promise<NFTOrder[]> {
        // Sắp xếp theo thời gian tạo đơn hàng
        return this.orderRepository.find({where: {traderId: userId}, order: {createdAt: 'DESC'}});
    }
}