import { Controller, Get, Post, Body, Param, UseGuards, Query, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { TradingSessionService } from './services/trading-session.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiResponseDto } from 'src/common/dto/api-response.dto';
import { NFTTradingSession } from './entities/nft-trading-session.entity';

@ApiTags('Trading Sessions')
@Controller('trading-sessions')
@UseGuards(JwtAuthGuard)
export class TradingSessionController {
  constructor(private readonly tradingSessionService: TradingSessionService) { }

  /**
   * L<PERSON>y tất cả phiên giao dịch
   * @returns Tất cả phiên giao dịch
   */
  @Get()
  @ApiOperation({ summary: 'Get all trading sessions' })
  @ApiResponse({ status: 200, description: 'Returns all trading sessions' })
  async getSessions(): Promise<ApiResponseDto<NFTTradingSession[]>> {
    const sessions = await this.tradingSessionService.getSessions();
    return ApiResponseDto.success(sessions, 'All trading sessions retrieved successfully');
  }

  /**
   * Lấy phiên giao dịch hiện tại
   * @returns Phiên giao dịch hiện tại
   */
  @Get('current')
  @ApiOperation({ summary: 'Get current trading session' })
  @ApiResponse({ status: 200, description: 'Returns current trading session if exists' })
  async getCurrentSession(): Promise<ApiResponseDto<NFTTradingSession>> {
    const session = await this.tradingSessionService.getCurrentSession();
    return ApiResponseDto.success(session, 'Current trading session retrieved successfully');
  }

  /**
   * Lấy phiên giao dịch hôm nay
   * @returns Phiên giao dịch hôm nay
   */
  @Get('today')
  @ApiOperation({ summary: 'Get today\'s trading sessions' })
  @ApiResponse({ status: 200, description: 'Returns all trading sessions for today' })
  async getTodaySessions(): Promise<ApiResponseDto<NFTTradingSession[]>> {
    const sessions = await this.tradingSessionService.getTodaySessions();
    return ApiResponseDto.success(sessions, 'Today trading sessions retrieved successfully');
  }

  /**
   * Lấy thống kê phiên giao dịch
   * @returns Thống kê phiên giao dịch
   */
  @Get('statistics')
  @ApiOperation({ summary: 'Get trading session statistics' })
  @ApiResponse({ status: 200, description: 'Returns trading statistics for the specified date range' })
  async getStatistics(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string
  ): Promise<ApiResponseDto<{ totalSessions: number; totalOrders: number; totalMatched: number; totalVolume: number; averageMatchRate: number }>> {
    const sessions = await this.tradingSessionService.getSessionStatistics(
      new Date(startDate),
      new Date(endDate)
    );
    return ApiResponseDto.success(sessions, 'Trading session statistics retrieved successfully');
  }

  /**
   * Đặt hẹn mua phiên giao dịch
   * @param req - Request object
   * @param sessionId - ID của phiên giao dịch  
   * @returns 
   */
  @Post(':id/schedule')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Schedule for a trading session' })
  @ApiResponse({ status: 201, description: 'Successfully scheduled for session' })
  async scheduleForSession(
    @Request() req,
    @Param('id') sessionId: string,
    @Body() body: { amount: number }
  ): Promise<ApiResponseDto<void>>  {
    await this.tradingSessionService.scheduleForSession(req.user.id, sessionId, body.amount);
    return ApiResponseDto.success(null, 'Session scheduled successfully');
  }

  /**
   * Đóng phiên giao dịch
   * @param req - Request object
   * @param sessionId - ID của phiên giao dịch  
   * @returns 
   */
  @Post(':id/open')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Open a trading session' })
  @ApiResponse({ status: 201, description: 'Successfully opened session' })
  async openSession(
    @Request() req,
    @Param('id') sessionId: string
  ): Promise<ApiResponseDto<void>>  {
    await this.tradingSessionService.openSession(sessionId);
    return ApiResponseDto.success(null, 'Session opened successfully');
  }

  /**
   * Đóng phiên giao dịch
   * @param req - Request object
   * @param sessionId - ID của phiên giao dịch  
   * @returns 
   */
  @Post(':id/close')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Close a trading session' })
  @ApiResponse({ status: 201, description: 'Successfully closed session' })
  async closeSession(
    @Request() req,
    @Param('id') sessionId: string
  ): Promise<ApiResponseDto<void>>  {
    await this.tradingSessionService.closeSession(sessionId);
    return ApiResponseDto.success(null, 'Session closed successfully');
  }
} 