import { Controller, Get, Post, Body, Param, UseGuards, Request, Query, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { NFTService } from './nft.service';
import { NFT, NFTType, NFTStatus } from './entities/nft.entity';
import { NFTOrder } from './entities/nft-order.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiResponseDto } from '../common/dto/api-response.dto';

@ApiTags('nfts')
@Controller('nfts')
export class NFTController {
  constructor(private readonly nftService: NFTService) {}

  /**
   * Lấy toàn bộ danh sách NFT
   * @returns Danh sách toàn bộ NFT
   */
  @Get()
  @ApiOperation({ summary: 'Get all NFTs' })
  @ApiResponse({ status: 200, description: 'Return all NFTs', type: ApiResponseDto })
  async getAllNFTs(): Promise<ApiResponseDto<NFT[]>> {
    const nfts = await this.nftService.getAllNFTs();
    return ApiResponseDto.success(nfts, 'NFTs listed successfully');
  }
  
  /**
   * Lấy danh sách NFT của người dùng
   * @param req - Request object
   * @returns Danh sách NFT của người dùng
   */
  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get my NFTs' })
  @ApiResponse({ status: 200, description: 'Return my NFTs', type: ApiResponseDto })
  @ApiResponse({ status: 404, description: 'NFTs not found' })
  async myNFT(@Request() req) {
    const userId = req.user.id;
    return await this.nftService.getMyNFTs(userId); 
  }

  /**
   * Lấy danh sách NFT đang được gửi bán
   * @returns Danh sách NFT đang được gửi bán
   */
  @Get('listed')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all LISTED NFTs' })
  @ApiResponse({ status: 200, description: 'Return all LISTED NFTs', type: ApiResponseDto })
  @ApiResponse({ status: 404, description: 'NFTs not found' })
  async getListedNFTs(): Promise<ApiResponseDto<NFT[]>> {
    const nfts = await this.nftService.getListedNFTs();
    return ApiResponseDto.success(nfts, 'NFTs listed successfully');
  }

  /**
   * Lấy danh sách NFT đang được gửi bán
   * @returns Danh sách NFT đang được gửi bán
   */
  @Get('sessions/:sessionId/listed')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all LISTED NFTs' })
  @ApiResponse({ status: 200, description: 'Return all LISTED NFTs', type: ApiResponseDto })
  @ApiResponse({ status: 404, description: 'NFTs not found' })
  async getListedBySession(@Param('sessionId') sessionId: string): Promise<ApiResponseDto<NFT[]>> {
    const nfts = await this.nftService.getListedBySession(sessionId);
    return ApiResponseDto.success(nfts, 'NFTs listed successfully');
  }

  /**
   * Gửi bán NFT
   * @param req - Request object
   * @param nftId - ID của NFT
   * @returns NFT đã được gửi bán
   */ 
  @Post(':nftId/listing')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Listing NFT' })
  @ApiResponse({ status: 201, description: 'NFT listed successfully', type: ApiResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid request or insufficient balance' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  async listingNFT(
    @Request() req,
    @Param('nftId') nftId: string,
    @Body() body: { quantity: string }
  ): Promise<ApiResponseDto<NFT>> {
    const userId = req.user.id;
    const nft = await this.nftService.listingNFT(userId, nftId, body.quantity);
    return ApiResponseDto.success(nft, 'NFT listed successfully');
  }

  /**
   * Đặt đơn hàng NFT (schedule)
   * @param req - Request object
   * @param sessionId - ID của phiên giao dịch
   * @param nftId - ID của NFT
   * @returns Đơn hàng NFT đã được đặt
   */
  @Post(':nftId/session/:sessionId/schedule')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Schedule NFT in trading session' })
  @ApiResponse({ status: 201, description: 'NFT scheduled successfully', type: ApiResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid request or insufficient balance' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  async scheduleNFT(
    @Request() req,
    @Param('sessionId') sessionId: string,
    @Param('nftId') nftId: string,
  ) {
    const userId = req.user.id;
    return await this.nftService.scheduleNFT(sessionId, userId, nftId);
  }

  /**
   * Đặt đơn hàng NFT
   * @param req - Request object
   * @param sessionId - ID của phiên giao dịch
   * @param nftId - ID của NFT
   * @returns Đơn hàng NFT đã được đặt
   */
  @Post(':nftId/session/:sessionId/order')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Buy NFT in trading session' })
  @ApiResponse({ status: 201, description: 'NFT bought successfully', type: ApiResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid request or insufficient balance' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  async orderNFT(
    @Request() req,
    @Param('sessionId') sessionId: string,
    @Param('nftId') nftId: string,
  ) {
    const userId = req.user.id;
    return await this.nftService.orderNFT(sessionId, userId, nftId);
  }

  /**
   * Danh sách đơn hàng của tôi
   * @param req - Request object
   * @returns Danh sách đơn hàng của tôi
   */
  @Get('orders')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get my orders' })
  @ApiResponse({ status: 200, description: 'Return my orders', type: ApiResponseDto })
  @ApiResponse({ status: 404, description: 'Orders not found' })
  async getMyOrders(
    @Request() req,
  ): Promise<ApiResponseDto<NFTOrder[]>> {
    const userId = req.user.id;
    const orders = await this.nftService.getMyOrders(userId);
    return ApiResponseDto.success(orders, 'Orders listed successfully');
  }
} 