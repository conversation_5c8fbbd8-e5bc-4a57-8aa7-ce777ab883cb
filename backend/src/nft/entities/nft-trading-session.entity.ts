import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { NFTScheduling } from './nft-scheduling.entity';

export enum NFTSessionType {
  MORNING = 'MORNING',
  EVENING = 'EVENING',
  MIDDLE = 'MIDDLE'
}

export enum NFTSessionStatus {
  PENDING = 'PENDING',
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  COMPLETED = 'COMPLETED'
}

@Entity('nft_trading_sessions')
export class NFTTradingSession {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'Session ID' })
  id: string;

  @Column({
    type: 'enum',
    enum: NFTSessionType
  })
  @ApiProperty({ description: 'Session type', enum: NFTSessionType })
  type: NFTSessionType;

  @Column({ type: 'date' })
  @ApiProperty({ description: 'Session date' })
  date: Date;

  @Column({
    type: 'enum',
    enum: NFTSessionStatus,
    default: NFTSessionStatus.PENDING
  })
  @ApiProperty({ description: 'Session status', enum: NFTSessionStatus })
  status: NFTSessionStatus;

  @Column({ type: 'timestamp' })
  @ApiProperty({ description: 'Session open time' })
  openTime: Date;

  @Column({ type: 'timestamp' })
  @ApiProperty({ description: 'Session close time' })
  closeTime: Date;

  @Column({ type: 'int', default: 0 })
  @ApiProperty({ description: 'Total orders in session' })
  totalOrders: number;

  @Column({ type: 'int', default: 0 })
  @ApiProperty({ description: 'Matched orders in session' })
  matchedOrders: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Total trading volume in session' })
  totalVolume: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Max available buy volume in session' })
  maxAvailableBuyVolume: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Max available sell volume in session' })
  maxAvailableSellVolume: number;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({ description: 'Session notes/remarks', required: false })
  notes?: string;

  @CreateDateColumn()
  @ApiProperty({ description: 'Record creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Record last update timestamp' })
  updatedAt: Date;

  @Column({ default: 0 })
  totalSchedules: number;

  @OneToMany(() => NFTScheduling, scheduling => scheduling.session)
  schedules: NFTScheduling[];
} 