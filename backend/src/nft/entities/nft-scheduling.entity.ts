import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { NFTTradingSession } from './nft-trading-session.entity';
import { User } from 'src/users/entities/user.entity';

export enum SchedulingStatus {
    PENDING = 'PENDING',
    COMPLETED = 'COMPLETED',
    CANCELLED = 'CANCELLED'
}

@Entity('nft_scheduling')
export class NFTScheduling {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    userId: string;

    @Column()
    sessionId: string;

    @Column('decimal', { precision: 10, scale: 2 })
    amountUSDT: number;

    @Column('decimal', { precision: 10, scale: 2 })
    fee: number;

    @Column({
        type: 'enum',
        enum: SchedulingStatus,
        default: SchedulingStatus.PENDING
    })
    status: SchedulingStatus;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @ManyToOne(() => User)
    @JoinColumn({ name: 'userId' })
    user: User;

    @ManyToOne(() => NFTTradingSession)
    @JoinColumn({ name: 'sessionId' })
    session: NFTTradingSession;
}