import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../users/entities/user.entity';

export enum NFTType {
  PHOENIX = 'PHOENIX',
  SPIRIT_TURTLE = 'SPIRIT_TURTLE',
  UNICORN = 'UNICORN',
  DRAGON = 'DRAGON'
}

export enum NFTStatus {
  AVAILABLE = 'AVAILABLE',
  LISTED = 'LISTED',
  SOLD = 'SOLD'
}

@Entity('nfts')
export class NFT {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'NFT ID' })
  id: string;

  @Column({ type: 'enum', enum: NFTType })
  @ApiProperty({ description: 'NFT Type', enum: NFTType })
  type: NFTType;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  @ApiProperty({ description: 'Current price in USDT' })
  currentPrice: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  @ApiProperty({ description: 'Initial price in USDT' })
  initialPrice: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  @ApiProperty({ description: 'Gas fee in WM tokens' })
  gasFee: number;

  @Column({ type: 'decimal', precision: 20, scale: 10, default: 0 })
  @ApiProperty({ description: 'Quantity of NFT' })
  quantity: string;

  @Column({ type: 'enum', enum: NFTStatus, default: NFTStatus.AVAILABLE })
  @ApiProperty({ description: 'NFT Status', enum: NFTStatus })
  status: NFTStatus;

  @Column({ name: 'owner_id', nullable: true })
  @ApiProperty({ description: 'Current owner ID', required: false })
  ownerId?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'owner_id' })
  @ApiProperty({ description: 'Current owner', type: () => User, required: false })
  owner: User;

  @CreateDateColumn()
  @ApiProperty({ description: 'NFT creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'NFT last update timestamp' })
  updatedAt: Date;
} 