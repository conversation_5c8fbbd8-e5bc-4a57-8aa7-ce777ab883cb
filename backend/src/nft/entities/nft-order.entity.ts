import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../users/entities/user.entity';
import { NFT, NFTType } from './nft.entity';
import { NFTSessionType, NFTTradingSession } from './nft-trading-session.entity';

export enum OrderStatus {
  PENDING = 'PENDING',
  MATCHED = 'MATCHED',
  CANCELLED = 'CANCELLED'
}

export enum OrderType {
  BUY = 'BUY',
  SELL = 'SELL'
}

@Entity('nft_orders')
export class NFTOrder {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'Order ID' })
  id: string;

  @Column({ type: 'enum', enum: OrderType })
  @ApiProperty({ description: 'Order type', enum: OrderType })
  orderType: OrderType;

  @Column()
  @ApiProperty({ description: 'Trader ID' })
  traderId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'traderId' })
  trader: User;

  @Column({ type: 'enum', enum: NFTType })
  @ApiProperty({ description: 'NFT Type requested', enum: NFTType })
  nftType: NFTType;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Matched NFT ID', required: false })
  nftId?: string;

  @ManyToOne(() => NFT, { nullable: true })
  @JoinColumn({ name: 'nftId' })
  nft?: NFT;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  @ApiProperty({ description: 'NFT price' })
  nftPrice: number;

  @Column({ type: 'decimal', precision: 20, scale: 10, default: 0 })
  @ApiProperty({ description: 'Quantity of NFT' })
  quantity: string;

  @Column({ type: 'decimal', precision: 20, scale: 10, default: 0 })
  @ApiProperty({ description: 'Quantity of NFT sold' })
  soldQuantity: string;

  @Column({ type: 'decimal', precision: 20, scale: 10, default: 0 })
  @ApiProperty({ description: 'Quantity of NFT remaining' })
  remainingQuantity: string;

  @Column({ type: 'bigint', default: 0 })
  @ApiProperty({ description: 'Quantity of NFT trade sessions' })
  tradeSessions: number;

  @Column({ type: 'enum', enum: OrderStatus, default: OrderStatus.PENDING })
  @ApiProperty({ description: 'Order status', enum: OrderStatus })
  status: OrderStatus;

  @Column({ type: 'enum', enum: NFTSessionType, nullable: true })
  @ApiProperty({ description: 'Trading session', enum: NFTSessionType, required: false })
  sessionType?: NFTSessionType;

  @Column({ type: 'uuid', nullable: true })
  @ApiProperty({ description: 'Trading session ID', required: false })
  sessionId?: string;
  
  @ManyToOne(() => NFTTradingSession, { nullable: true })
  @JoinColumn({ name: 'sessionId' })
  session?: NFTTradingSession;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  @ApiProperty({ description: 'Gas fee in WM tokens' })
  gasFee: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 100 })
  @ApiProperty({ description: 'Gas fee percentage (100%, 90%, or 80%)' })
  gasFeePercentage: number;

  @Column({ type: 'timestamp', nullable: true })
  @ApiProperty({ description: 'Scheduled session timestamp', required: false })
  sessionTime?: Date;

  @Column({ type: 'timestamp', nullable: true })
  @ApiProperty({ description: 'When order was matched', required: false })
  matchedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  @ApiProperty({ description: 'When order was completed', required: false })
  completedAt?: Date;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether the seller has been paid' })
  sellerPaid: boolean;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether the NFT has been transferred to the buyer' })
  isTransferred: boolean;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether the order is scheduled' })
  isScheduled: boolean;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether the order is buy order' })
  isBuy: boolean;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({ description: 'Whether the order is priority buy order' })
  isPriorityBuy: boolean;

  @CreateDateColumn()
  @ApiProperty({ description: 'Order creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Order last update timestamp' })
  updatedAt: Date;
} 