import { NFTSessionType, NFTSessionStatus } from '../entities/nft-trading-session.entity';

export interface NFTSessionData {
  id: string;
  type: NFTSessionType;
  status: NFTSessionStatus;
  openTime: Date;
  closeTime: Date;
  date: Date;
  totalOrders: number;
  matchedOrders: number;
  totalVolume: number;
  totalSchedules: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface NFTSessionJob {
  sessionId: string;
  type: 'open' | 'close' | 'countdown';
  scheduledTime: Date;
}

export interface NFTSessionUpdate {
  type: 'statistics' | 'status';
  data: {
    totalOrders?: number;
    matchedOrders?: number;
    totalVolume?: number;
    status?: NFTSessionStatus;
  };
}

export interface NFTSessionCountdownData {
  sessionId: string;
  timeLeft: string;
  timeLeftInHours: number;
  timeLeftInMinutes: number;
  timeLeftInSeconds: number;
  shouldShow: boolean;
  status: NFTSessionStatus;
  currentTime: string;
  openTime: string;
  closeTime: string;
  timezone: string;
} 

export interface UserWManagerStats {
  id: string;
  isKycCompleted: boolean;
  currentRank: string | null;
  totalVolume: number;
  kycF1Count: number;
  branchCounts: {
    W1: number;
    W2: number;
    W3: number;
    W4: number;
    W5: number;
  };
}