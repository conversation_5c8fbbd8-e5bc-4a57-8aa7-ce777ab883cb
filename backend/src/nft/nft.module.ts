import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NFT } from './entities/nft.entity';
import { NFTOrder } from './entities/nft-order.entity';
import { NFTTradingSession } from './entities/nft-trading-session.entity';
import { NFTService } from './nft.service';
import { NFTController } from './nft.controller';
import { TradingSessionService } from './services/trading-session.service';
import { TradingSessionController } from './trading-session.controller';
import { UsersModule } from '../users/users.module';
import { TokenModule } from '../token/token.module';
import { WalletModule } from '../wallet/wallet.module';
import { CommonModule } from '../common/common.module';
import { BullModule } from '@nestjs/bull';
import { SessionManagerService } from './services/session-manager.service';
import { Wallet } from '../wallet/entities/wallet.entity';
import { Transaction } from 'src/wallet/entities/transaction.entity';
import { User } from 'src/users/entities/user.entity';
import { StakingTransaction } from 'src/staking/entities/staking-transaction.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, NFT, NFTOrder, NFTTradingSession, Wallet, Transaction, StakingTransaction]),
    UsersModule,
    TokenModule,
    WalletModule,
    CommonModule,
    BullModule.registerQueue({
      name: 'session-queue',
    }),
  ],
  controllers: [NFTController, TradingSessionController],
  providers: [NFTService, TradingSessionService, SessionManagerService],
  exports: [NFTService, TradingSessionService, SessionManagerService]
})
export class NFTModule {} 