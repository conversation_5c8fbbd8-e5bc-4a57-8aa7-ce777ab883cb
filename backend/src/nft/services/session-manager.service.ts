import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import moment from 'moment-timezone';
import { NFTTradingSession, NFTSessionStatus } from '../entities/nft-trading-session.entity';
import { RedisService } from '../../common/services/redis.service';
import { SystemConfigService } from '../../common/services/system-config.service';
import {
  NFTSessionData,
  NFTSessionCountdownData
} from '../interfaces/session-data.interface';
import { WebSocketGateway, WebSocketServer } from '@nestjs/websockets';
import { Server } from 'socket.io';
import { MoreThanOrEqual } from 'typeorm';

@Injectable()
@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class SessionManagerService implements OnModuleInit {
  private readonly logger = new Logger(SessionManagerService.name);
  private timezone: string;

  @WebSocketServer()
  server: Server;

  constructor(
    @InjectRepository(NFTTradingSession)
    private readonly sessionRepository: Repository<NFTTradingSession>,
    private readonly systemConfigService: SystemConfigService,
    @InjectQueue('session-queue') private readonly sessionQueue: Queue,
  ) { }

  /**
   * Khởi tạo SessionManagerService
   */
  async onModuleInit() {
    try {
      // 1. Lấy thông tin timezone
      const timezoneConfig = await this.systemConfigService.findByKey('NFT_TRADING_SESSION_TIMEZONE');
      this.timezone = timezoneConfig?.value || 'Asia/Dubai';

      // 2. Khởi tạo queue processors với xử lý lỗi
      await this.setupQueueProcessors().catch(error => {
        this.logger.error('[SessionManagerService] Lỗi thiết lập hàng đợi:', error);
      });

      // 3. Khởi tạo countdown cho các phiên giao dịch đang hoạt động
      await this.initializeActiveSessionCountdowns();

      this.logger.log('[SessionManagerService] SessionManagerService đã được khởi tạo thành công');
    } catch (error) {
      this.logger.error('[SessionManagerService] Lỗi khởi tạo SessionManagerService:', error);
    }
  }

  /**
   * Khởi tạo countdown cho các phiên giao dịch đang hoạt động
   */
  private async initializeActiveSessionCountdowns() {
    try {
      const now = moment().tz(this.timezone);

      // 1. Tìm tất cả các phiên giao dịch OPEN
      const activeSessions = await this.sessionRepository.find({
        where: {
          status: NFTSessionStatus.OPEN,
          closeTime: MoreThanOrEqual(now.toDate())
        }
      });

      // 5. Bắt đầu countdown ngay lập tức cho mỗi phiên giao dịch OPEN
      for (const session of activeSessions) {
        // 6. Bắt đầu xử lý countdown ngay lập tức thay vì lập lịch
        await this.processCountdown(session.id);

        // 7. Lập lịch các công việc khác (như đóng phiên) nếu cần
        const closeTime = moment(session.closeTime).tz(this.timezone);
        if (closeTime.isAfter(now)) {
          await this.sessionQueue.add(
            'close',
            { sessionId: session.id },
            {
              delay: closeTime.diff(now),
              jobId: `close:${session.id}`,
            }
          );
        }
      }
      this.logger.log('[SessionManagerService] Countdown cho các phiên giao dịch đang hoạt động đã được khởi tạo thành công');
    } catch (error) {
      this.logger.error('Lỗi khởi tạo countdown cho các phiên giao dịch đang hoạt động:', error);
    }
  }

  /**
   * Khởi tạo queue processors
   */
  private async setupQueueProcessors() {
    try {
      // 1. Xử lý countdown jobs
      this.sessionQueue.process('countdown', async (job) => {
        const { sessionId } = job.data;
        await this.processCountdown(sessionId);
      });

      // 2. Xử lý countdown jobs
      this.sessionQueue.process('open', async (job) => {
        const { sessionId } = job.data;
        await this.openSession(sessionId);
      });

      // 3. Xử lý countdown jobs
      this.sessionQueue.process('close', async (job) => {
        const { sessionId } = job.data;
        await this.closeSession(sessionId);
      });

      // 4. Lấy trạng thái queue
      const queueStatus = await this.sessionQueue.getJobCounts();

      this.logger.debug('[SessionManagerService] Thiết lập hàng đợi thành công:', queueStatus);
    } catch (error) {
      this.logger.error('[SessionManagerService] Lỗi thiết lập hàng đợi:', error);
      throw error;
    }
  }

  /**
   * Khởi tạo phiên giao dịch
   * @param session - Phiên giao dịch
   */
  async initializeSession(session: NFTTradingSession) {
    try {
      // 2. Lập lịch các công việc
      await this.scheduleSessionJobs(session);

      this.logger.log(`[SessionManagerService] Phiên giao dịch ${session.id} đã được khởi tạo thành công`);
    } catch (error) {
      this.logger.error(`[SessionManagerService] Lỗi khởi tạo phiên giao dịch ${session.id}:`, error);
      throw error;
    }
  }

  /**
   * Lập lịch các công việc
   * @param session - Phiên giao dịch
   */
  private async scheduleSessionJobs(session: NFTTradingSession) {
    const now = moment().tz(this.timezone);
    const openTime = moment(session.openTime).tz(this.timezone);
    const closeTime = moment(session.closeTime).tz(this.timezone);
    const countdownTime = openTime.clone().subtract(1, 'minutes');

    // 4. Lập lịch countdown cho các phiên giao dịch PENDING
    if (session.status === NFTSessionStatus.PENDING && countdownTime.isAfter(now)) {
      await this.sessionQueue.add(
        'countdown',
        { sessionId: session.id },
        {
          delay: countdownTime.diff(now),
          jobId: `countdown:${session.id}`,
        }
      );
    }

    // 5. Lập lịch countdown cho các phiên giao dịch OPEN
    if (session.status === NFTSessionStatus.OPEN && closeTime.isAfter(now)) {
      await this.sessionQueue.add(
        'countdown',
        { sessionId: session.id },
        {
          delay: 0, // Start immediately
          jobId: `countdown:${session.id}`,
        }
      );
    }

    // 6. Lập lịch countdown cho các phiên giao dịch OPEN
    if (openTime.isAfter(now)) {
      await this.sessionQueue.add(
        'open',
        { sessionId: session.id },
        {
          delay: openTime.diff(now),
          jobId: `open:${session.id}`,
        }
      );
    }

    // 7. Lập lịch countdown cho các phiên giao dịch OPEN
    if (closeTime.isAfter(now)) {
      await this.sessionQueue.add(
        'close',
        { sessionId: session.id },
        {
          delay: closeTime.diff(now),
          jobId: `close:${session.id}`,
        }
      );
    }

    const queueStatus = await this.sessionQueue.getJobCounts();
    this.logger.debug(`[SessionManagerService] Trạng thái hàng đợi sau khi lập lịch:`, queueStatus);
  }

  /**
   * Xử lý countdown
   * @param sessionId - ID của phiên giao dịch
   */
  private async processCountdown(sessionId: string) {
    const sessionData = await this.getSessionData(sessionId);
    if (!sessionData) {
      this.logger.warn(`[SessionManagerService] Không tìm thấy dữ liệu phiên giao dịch cho xử lý countdown: ${sessionId}`);
      return;
    }

    // 1. Xóa bất kỳ khoản thời gian tồn tại
    const existingInterval = this.countdownIntervals.get(sessionId);
    if (existingInterval) {
      clearInterval(existingInterval);
      this.countdownIntervals.delete(sessionId);
    }

    // 2. Bắt đầu countdown interval
    const countdownInterval = setInterval(async () => {
      try {
        const countdownData = await this.calculateCountdown(sessionData);
        const now = moment().tz(this.timezone);
        const closeTime = moment(sessionData.closeTime).tz(this.timezone);

        // 3. Kiểm tra nếu đã quá thời gian đóng phiên
        if (false && now.isAfter(closeTime)) {
          clearInterval(countdownInterval);
          this.countdownIntervals.delete(sessionId);
          return;
        }

        // 4. Emit countdown cho cả PENDING (5 phút cuối) và OPEN
        if ((sessionData.status === NFTSessionStatus.PENDING && countdownData.shouldShow) ||
          sessionData.status === NFTSessionStatus.OPEN) {

          // 5. Emit countdown cho tất cả các client
          this.server.emit('session:countdown', {
            sessionId: countdownData.sessionId,
            timeLeft: countdownData.timeLeft,
            timeLeftInSeconds: countdownData.timeLeftInSeconds,
            timeLeftInMinutes: countdownData.timeLeftInMinutes,
            timeLeftInHours: countdownData.timeLeftInHours,
            status: countdownData.status,
            shouldEmit: true,
            currentTime: countdownData.currentTime
          });
        }
      } catch (error) {
        this.logger.error(`[SessionManagerService] Lỗi xử lý countdown cho phiên giao dịch ${sessionId}:`, error);
        clearInterval(countdownInterval);
        this.countdownIntervals.delete(sessionId);
      }
    }, 1000);

    // 6. Lưu tham chiếu interval
    this.countdownIntervals.set(sessionId, countdownInterval);

    // 7. Xóa interval sau khi phiên giao dịch đóng
    const sessionCloseTime = moment(sessionData.closeTime).tz(this.timezone);
    const timeUntilClose = sessionCloseTime.diff(moment().tz(this.timezone));

    if (timeUntilClose > 0) {
      setTimeout(() => {
        clearInterval(countdownInterval);
        this.countdownIntervals.delete(sessionId);
      }, timeUntilClose);
    }
  }

  /**
   * Tính toán countdown
   * @param session - Phiên giao dịch
   * @returns Countdown data
   */
  private async calculateCountdown(session: NFTSessionData): Promise<NFTSessionCountdownData> {
    const now = moment().tz(this.timezone);
    const openTime = moment(session.openTime).tz(this.timezone);
    const closeTime = moment(session.closeTime).tz(this.timezone);

    let timeLeft = '';
    let timeLeftInSeconds = 0;
    let timeLeftInMinutes = 0;
    let timeLeftInHours = 0;
    let shouldShow = false;

    if (session.status === NFTSessionStatus.PENDING) {
      const diffToOpen = openTime.diff(now);
      if (diffToOpen <= 1 * 60 * 1000 && diffToOpen > 0) {
        const duration = moment.duration(diffToOpen);
        timeLeft = `${duration.minutes()}m ${duration.seconds()}s`;
        timeLeftInSeconds = duration.seconds();
        timeLeftInMinutes = duration.minutes();
        timeLeftInHours = duration.hours();
        shouldShow = true;
      }
    } else if (session.status === NFTSessionStatus.OPEN) {
      const diffToClose = closeTime.diff(now);
      if (diffToClose > 0) {
        const duration = moment.duration(diffToClose);
        const hours = Math.floor(duration.asHours());
        timeLeft = hours > 0
          ? `${hours}h ${duration.minutes()}m ${duration.seconds()}s`
          : `${duration.minutes()}m ${duration.seconds()}s`;
        timeLeftInSeconds = duration.seconds();
        timeLeftInMinutes = duration.minutes();
        timeLeftInHours = duration.hours();
        shouldShow = true;
      }
    }

    return {
      sessionId: session.id,
      timeLeft,
      timeLeftInSeconds,
      timeLeftInMinutes,
      timeLeftInHours,
      shouldShow,
      status: session.status,
      currentTime: now.format(),
      openTime: openTime.format(),
      closeTime: closeTime.format(),
      timezone: this.timezone
    };
  }

  /**
   * Mở phiên giao dịch
   * @param sessionId - ID của phiên giao dịch
   */
  public async openSession(sessionId: string) {
    this.logger.debug(`[SessionManagerService] Mở phiên giao dịch ${sessionId} ...`);

    try {
      const sessionData = await this.getSessionData(sessionId);
      if (!sessionData || sessionData.status !== NFTSessionStatus.PENDING) {
        this.logger.debug(`[SessionManagerService] Phiên giao dịch ${sessionId} không thể được mở - trạng thái không hợp lệ: ${sessionData?.status}`);
        return;
      }

      // 1. Cập nhật trạng thái phiên giao dịch trong cơ sở dữ liệu
      await this.sessionRepository.update(sessionId, {
        status: NFTSessionStatus.OPEN
      });

      // 2. Cập nhật cache
      sessionData.status = NFTSessionStatus.OPEN;

      // 3. Chuẩn bị dữ liệu sự kiện socket
      const eventData = {
        sessionId,
        type: sessionData.type,
        openTime: sessionData.openTime,
        closeTime: sessionData.closeTime
      };

      // 4. Emit sự kiện phiên giao dịch đã mở
      this.server.emit('session:opened', eventData);

      // 5. Bắt đầu countdown cho phiên giao dịch OPEN
      await this.processCountdown(sessionId);

      this.logger.log(`[SessionManagerService] Phiên giao dịch ${sessionId} đã được mở thành công`);
    } catch (error) {
      this.logger.error(`[SessionManagerService] Lỗi mở phiên giao dịch ${sessionId}:`, error);
      throw error;
    } finally {
    }
  }

  /**
   * Đóng phiên giao dịch
   * @param sessionId - ID của phiên giao dịch
   */
  public async closeSession(sessionId: string) {
    this.logger.debug(`[SessionManagerService] Đóng phiên giao dịch ${sessionId}...`);
    try {
      const sessionData = await this.getSessionData(sessionId);
      if (!sessionData || sessionData.status !== NFTSessionStatus.OPEN) {
        this.logger.debug(`[SessionManagerService] Phiên giao dịch ${sessionId} không thể được đóng - trạng thái không hợp lệ: ${sessionData?.status}`);
        return;
      }

      // 1. Cập nhật trạng thái phiên giao dịch trong cơ sở dữ liệu
      await this.sessionRepository.update(sessionId, {
        status: NFTSessionStatus.CLOSED
      });

      // 2. Cập nhật cache
      sessionData.status = NFTSessionStatus.CLOSED;

      // 3. Dừng interval countdown nếu đang chạy
      const countdownInterval = this.countdownIntervals.get(sessionId);
      if (countdownInterval) {
        clearInterval(countdownInterval);
        this.countdownIntervals.delete(sessionId);
      }

      // 3. Chuẩn bị dữ liệu sự kiện socket
      const eventData = {
        sessionId,
        type: sessionData.type,
        closeTime: sessionData.closeTime,
        statistics: {
          totalOrders: sessionData.totalOrders,
          matchedOrders: sessionData.matchedOrders,
          totalVolume: sessionData.totalVolume
        }
      };

      // 4. Emit sự kiện phiên giao dịch đã đóng
      this.server.emit('session:closed', eventData);

      this.logger.log(`[SessionManagerService] Phiên giao dịch ${sessionId} đã được đóng thành công`);
    } catch (error) {
      this.logger.error(`[SessionManagerService] Lỗi đóng phiên giao dịch ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Lấy dữ liệu phiên giao dịch
   * @param sessionId - ID của phiên giao dịch
   * @returns Dữ liệu phiên giao dịch
   */
  private async getSessionData(sessionId: string) {
    return await this.sessionRepository.findOne({ where: { id: sessionId } });
  }

  /**
   * Cập nhật thống kê phiên giao dịch
   * @param sessionId - ID của phiên giao dịch
   * @param update - Dữ liệu cập nhật
   */
  async updateSessionStatistics(sessionId: string, update: {
    totalOrders?: number;
    matchedOrders?: number;
    totalVolume?: number;
    totalSchedules?: number;
  }) {
    try {
      const sessionData = await this.getSessionData(sessionId);
      if (!sessionData) return;

      // 1. Cập nhật thống kê
      if (update.totalOrders !== undefined) {
        sessionData.totalOrders = update.totalOrders;
      }
      if (update.matchedOrders !== undefined) {
        sessionData.matchedOrders = update.matchedOrders;
      }
      if (update.totalVolume !== undefined) {
        sessionData.totalVolume = update.totalVolume;
      }
      if (update.totalSchedules !== undefined) {
        sessionData.totalSchedules = update.totalSchedules;
      }

      // 3. Cập nhật cơ sở dữ liệu
      await this.sessionRepository.update(sessionId, update);

      // 4. Emit sự kiện cập nhật thống kê
      this.server.to(`session:${sessionId}`).emit('session:statistics', {
        sessionId,
        totalOrders: sessionData.totalOrders,
        matchedOrders: sessionData.matchedOrders,
        totalVolume: sessionData.totalVolume,
        totalSchedules: sessionData.totalSchedules
      });
    } catch (error) {
      this.logger.error(`[SessionManagerService] Lỗi cập nhật thống kê phiên giao dịch ${sessionId}:`, error);
      throw error;
    }
  }

  // 8. Lưu tham chiếu interval
  private countdownIntervals: Map<string, NodeJS.Timeout> = new Map();
} 