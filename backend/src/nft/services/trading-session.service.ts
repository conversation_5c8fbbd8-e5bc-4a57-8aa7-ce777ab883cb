import { Injectable, Logger, OnModuleInit, NotFoundException, BadRequestException, InternalServerErrorException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual, MoreThanOrEqual, Between, In, Not, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, IsNull, Like, Equal, And } from 'typeorm';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import moment from 'moment-timezone';
import { NFTTradingSession, NFTSessionType, NFTSessionStatus } from '../entities/nft-trading-session.entity';
import { WebSocketGateway, WebSocketServer } from '@nestjs/websockets';
import { Server } from 'socket.io';
import { SystemConfigService } from '../../common/services/system-config.service';
import { SessionManagerService } from './session-manager.service';
import { <PERSON>ronJob } from 'cron';
import { DataSource } from 'typeorm';
import { TokenService } from 'src/token/token.service';
import { WalletService } from 'src/wallet/wallet.service';
import { TransactionStatus, TransactionType } from 'src/common/enums/transaction.enum';
import { NFTScheduling, SchedulingStatus } from '../entities/nft-scheduling.entity';
import { NFTOrder, OrderStatus, OrderType } from '../entities/nft-order.entity';
import { NFT, NFTStatus, NFTType } from '../entities/nft.entity';
import { Wallet } from 'src/wallet/entities/wallet.entity';
import { User } from 'src/users/entities/user.entity';
import { Transaction } from 'src/wallet/entities/transaction.entity';
import { Token } from 'src/token/entities/token.entity';
import { BigNumber } from 'bignumber.js';
import { WManagerRank } from 'src/common/enums/user.enum';
import { UserWManagerStats } from '../interfaces/session-data.interface';
import {getCurrentTime} from "../../common/utils/date.utils";

@Injectable()
@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class TradingSessionService implements OnModuleInit {
  private readonly logger = new Logger(TradingSessionService.name);
  private timezone: string;
  private sessionDuration: number;
  private morningStartTime: string;
  private middleStartTime: string;
  private eveningStartTime: string;
  private daysToCreate: number;
  private schedulingFeeRate: number;

  @WebSocketServer()
  server: Server;

  constructor(
    @InjectRepository(NFTTradingSession)
    private readonly sessionRepository: Repository<NFTTradingSession>,
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
    private readonly systemConfigService: SystemConfigService,
    private readonly sessionManager: SessionManagerService,
    private readonly tokenService: TokenService,
    private readonly walletService: WalletService,
    private readonly dataSource: DataSource,
    private schedulerRegistry: SchedulerRegistry,
  ) { }

  async onModuleInit() {
    this.logger.log('[TradingSessionService][onModuleInit] Khởi tạo TradingSessionService...');

    try {
      // Load timezone from config
      const timezoneConfig = await this.systemConfigService.findByKey('NFT_TRADING_SESSION_TIMEZONE');
      this.timezone = timezoneConfig?.value || 'Asia/Dubai';

      // Load morning start time from config
      const morningStartTimeConfig = await this.systemConfigService.findByKey('NFT_TRADING_SESSION_MORNING_START_TIME');
      this.morningStartTime = morningStartTimeConfig?.value || '06:00';

      // Load middle start time from config
      const middleStartTimeConfig = await this.systemConfigService.findByKey('NFT_TRADING_SESSION_MIDDLE_START_TIME');
      this.middleStartTime = middleStartTimeConfig?.value || '11:00';

      // Load evening start time from config
      const eveningStartTimeConfig = await this.systemConfigService.findByKey('NFT_TRADING_SESSION_EVENING_START_TIME');
      this.eveningStartTime = eveningStartTimeConfig?.value || '18:00';

      // Load session duration from config
      const durationConfig = await this.systemConfigService.findByKey('NFT_TRADING_SESSION_DURATION');
      this.sessionDuration = parseInt(durationConfig?.value || '60', 10);

      // Load number of days to create sessions from config
      const daysToCreateConfig = await this.systemConfigService.findByKey('NFT_TRADING_SESSION_DAYS_TO_CREATE');
      this.daysToCreate = parseInt(daysToCreateConfig?.value || '30', 30);

      const schedulingFeeConfig = await this.systemConfigService.findByKey('NFT_SCHEDULING_FEE');
      this.schedulingFeeRate = parseFloat(schedulingFeeConfig?.value || '1');

      // Setup dynamic cron jobs
      await this.setupSessionCronJobs();

      // Check and create today's sessions
      await this.checkAndCreateTodaySessions();

      this.logger.log('[TradingSessionService][onModuleInit] Khởi tạo TradingSessionService thành công');
    } catch (error) {
      this.logger.error('[TradingSessionService][onModuleInit] Lỗi khởi tạo TradingSessionService:', error);
      throw error;
    }
  }

  private async setupSessionCronJobs() {
    this.logger.log('[TradingSessionService][setupSessionCronJobs] Thiết lập cron jobs cho phiên giao dịch...');

    try {
      this.schedulerRegistry.deleteCronJob('openMorningSession');
      this.schedulerRegistry.deleteCronJob('closeMorningSession');
      this.schedulerRegistry.deleteCronJob('openMiddleSession');
      this.schedulerRegistry.deleteCronJob('closeMiddleSession');
      this.schedulerRegistry.deleteCronJob('openEveningSession');
      this.schedulerRegistry.deleteCronJob('closeEveningSession');
    } catch (error) {
      this.logger.debug('[TradingSessionService][setupSessionCronJobs] Không có cron jobs để xóa');
    }

    // Parse configured start times
    const [morningStartHour, morningStartMinute] = this.morningStartTime.split(':').map(Number);
    const [middleStartHour, middleStartMinute] = this.middleStartTime.split(':').map(Number);
    const [eveningStartHour, eveningStartMinute] = this.eveningStartTime.split(':').map(Number);

    // Calculate close times based on session duration
    const morningCloseTime = moment()
      .set('hour', morningStartHour)
      .set('minute', morningStartMinute)
      .add(this.sessionDuration, 'minutes');

    const middleCloseTime = moment()
        .set('hour', middleStartHour)
        .set('minute', middleStartMinute)
        .add(this.sessionDuration, 'minutes');

    const eveningCloseTime = moment()
      .set('hour', eveningStartHour)
      .set('minute', eveningStartMinute)
      .add(this.sessionDuration, 'minutes');

    const morningCloseHour = morningCloseTime.hour();
    const morningCloseMinute = morningCloseTime.minute();
    const middleCloseHour = middleCloseTime.hour();
    const middleCloseMinute = middleCloseTime.minute();
    const eveningCloseHour = eveningCloseTime.hour();
    const eveningCloseMinute = eveningCloseTime.minute();

    // Create cron jobs with configurable times
    const openMorningJob = new CronJob(
      `0 ${morningStartMinute} ${morningStartHour} * * *`,
      () => this.handleOpenSession(),
      null,
      false,
      this.timezone
    );

    const closeMorningJob = new CronJob(
      `0 ${morningCloseMinute} ${morningCloseHour} * * *`,
      () => this.handleCloseSession(),
      null,
      false,
      this.timezone
    );

    const openMiddleJob = new CronJob(
        `0 ${middleStartMinute} ${middleStartHour} * * *`,
        () => this.handleOpenSession(),
        null,
        false,
        this.timezone
    );

    const closeMiddleJob = new CronJob(
        `0 ${middleCloseMinute} ${middleCloseHour} * * *`,
        () => this.handleCloseSession(),
        null,
        false,
        this.timezone
    );

    const openEveningJob = new CronJob(
      `0 ${eveningStartMinute} ${eveningStartHour} * * *`,
      () => this.handleOpenSession(),
      null,
      false,
      this.timezone
    );

    const closeEveningJob = new CronJob(
      `0 ${eveningCloseMinute} ${eveningCloseHour} * * *`,
      () => this.handleCloseSession(),
      null,
      false,
      this.timezone
    );

    // Add jobs to registry
    this.schedulerRegistry.addCronJob('openMorningSession', openMorningJob);
    this.schedulerRegistry.addCronJob('closeMorningSession', closeMorningJob);
    this.schedulerRegistry.addCronJob('openMiddleSession', openMiddleJob);
    this.schedulerRegistry.addCronJob('closeMiddleSession', closeMiddleJob);
    this.schedulerRegistry.addCronJob('openEveningSession', openEveningJob);
    this.schedulerRegistry.addCronJob('closeEveningSession', closeEveningJob);

    // Start the jobs
    openMorningJob.start();
    closeMorningJob.start();
    openMiddleJob.start();
    closeMiddleJob.start();
    openEveningJob.start();
    closeEveningJob.start();

    this.logger.log('[TradingSessionService][setupSessionCronJobs] Thiết lập cron jobs cho phiên giao dịch thành công');
  }

  @Cron('30 1 * * *') // Chạy vào 1:30 AM mỗi ngày theo đề xuất
  private async handleDailyTask() {
    this.logger.log('[TradingSessionService][handleDailyTask] Reset dữ liệu cho ngày mới...');
    await this.resetTotalVolumeAndTotalEarnings();
    await this.resetTotalVolumeAndTotalEarningsPerDay();
    this.logger.log('[TradingSessionService][handleDailyTask] Reset dữ liệu cho ngày mới thành công');
  }

  //@Cron('30 0 * * *') // Chạy vào 0:30 AM mỗi ngày - tăng giá NFT trước khi tạo phiên
  private async handleDailyNFTPriceIncrease() {
    this.logger.log('[TradingSessionService][handleDailyNFTPriceIncrease] Bắt đầu tăng giá NFT hằng ngày 8%...');

    try {
      // Lấy tỷ lệ tăng giá từ config (mặc định 8%)
      const growthRateConfig = await this.systemConfigService.findByKey('NFT_GROWTH_RATE');
      const growthRate = parseFloat(growthRateConfig?.value || '8') / 100; // Convert % to decimal

      // Lấy ngưỡng reset giá từ config (mặc định $1000)
      const resetThresholdConfig = await this.systemConfigService.findByKey('NFT_SPLIT_THRESHOLD');
      const resetThreshold = parseFloat(resetThresholdConfig?.value || '1000');

      // Lấy tất cả NFT đang listing (owner_id = null)
      const listingNFTs = await this.dataSource.query(
        'SELECT id, type, currentPrice, initialPrice FROM nfts WHERE owner_id IS NULL'
      );

      let updatedCount = 0;
      let resetCount = 0;

      for (const nft of listingNFTs) {
        try {
          const currentPrice = new BigNumber(nft.currentPrice);
          const initialPrice = new BigNumber(nft.initialPrice);
          const threshold = new BigNumber(resetThreshold);

          let newPrice: BigNumber;
          let action: string;

          // Kiểm tra nếu giá hiện tại > ngưỡng reset
          if (currentPrice.gt(threshold)) {
            // Reset về giá ban đầu
            newPrice = initialPrice;
            action = `RESET to initial price`;
            resetCount++;
          } else {
            // Tăng giá 8%
            newPrice = currentPrice.multipliedBy(new BigNumber(1).plus(growthRate));
            action = `+${(growthRate * 100).toFixed(1)}%`;
            updatedCount++;
          }

          // Cập nhật giá mới - sử dụng toFixed(2) để đảm bảo 2 chữ số thập phân
          await this.dataSource.query(
            'UPDATE nfts SET currentPrice = ?, updatedAt = ? WHERE id = ?',
            [newPrice.toFixed(2), new Date(), nft.id]
          );

          this.logger.log(`[TradingSessionService][handleDailyNFTPriceIncrease] ${nft.type}: $${currentPrice.toFixed(2)} → $${newPrice.toFixed(2)} (${action})`);

        } catch (error) {
          this.logger.error(`[TradingSessionService][handleDailyNFTPriceIncrease] Lỗi cập nhật NFT ${nft.id}: ${error.message}`);
        }
      }

      this.logger.log(`[TradingSessionService][handleDailyNFTPriceIncrease] Hoàn thành tăng giá NFT. Tăng giá: ${updatedCount}, Reset: ${resetCount}`);

    } catch (error) {
      this.logger.error(`[TradingSessionService][handleDailyNFTPriceIncrease] Lỗi trong quá trình tăng giá NFT: ${error.message}`, error.stack);
    }
  }

  @Cron('0 1 * * *') // Chạy vào 1:00 AM mỗi ngày theo đề xuất
  private async checkAndCreateTodaySessions() {
    this.logger.log(`[TradingSessionService][checkAndCreateTodaySessions] Kiểm tra và tạo phiên giao dịch cho ${this.daysToCreate} ngày tiếp theo nếu cần...`);
    const today = moment().tz(this.timezone).startOf('day');

    try {
      // Check sessions for next x days
      for (let i = 0; i < this.daysToCreate; i++) {
        const targetDate = today.clone().add(i, 'days');

        // Check if sessions exist for this date
        const existingMorningSession = await this.sessionRepository.find({
          where: {
            date: targetDate.toDate(),
            type: NFTSessionType.MORNING
          }
        });

        const existingMiddleSession = await this.sessionRepository.find({
          where: {
            date: targetDate.toDate(),
            type: NFTSessionType.MIDDLE
          }
        });

        const existingEveningSession = await this.sessionRepository.find({
          where: {
            date: targetDate.toDate(),
            type: NFTSessionType.EVENING
          }
        });

        if (existingMorningSession.length === 0) {
          // Create morning session
          const [morningStartHour, morningStartMinute] = this.morningStartTime.split(':').map(Number);
          const morningSession = await this.sessionRepository.save({
            type: NFTSessionType.MORNING,
            date: targetDate.toDate(),
            openTime: targetDate.clone().add(morningStartHour, 'hours').add(morningStartMinute, 'minutes').toDate(),
            closeTime: targetDate.clone().add(morningStartHour, 'hours').add(morningStartMinute, 'minutes').add(this.sessionDuration, 'minutes').toDate(),
            status: NFTSessionStatus.PENDING
          });

          // Initialize morning session with SessionManager
          await this.sessionManager.initializeSession(morningSession);
        }

        if (existingMiddleSession.length === 0) {
          // Create middle session
          const [middleStartHour, middleStartMinute] = this.middleStartTime.split(':').map(Number);
          const middleSession = await this.sessionRepository.save({
            type: NFTSessionType.MIDDLE,
            date: targetDate.toDate(),
            openTime: targetDate.clone().add(middleStartHour, 'hours').add(middleStartMinute, 'minutes').toDate(),
            closeTime: targetDate.clone().add(middleStartHour, 'hours').add(middleStartMinute, 'minutes').add(this.sessionDuration, 'minutes').toDate(),
            status: NFTSessionStatus.PENDING
          });

          // Initialize middle session with SessionManager
          await this.sessionManager.initializeSession(middleSession);
        }

        if (existingEveningSession.length === 0) {
          // Create evening session
          const [eveningStartHour, eveningStartMinute] = this.eveningStartTime.split(':').map(Number);
          const eveningSession = await this.sessionRepository.save({
            type: NFTSessionType.EVENING,
            date: targetDate.toDate(),
            openTime: targetDate.clone().add(eveningStartHour, 'hours').add(eveningStartMinute, 'minutes').toDate(),
            closeTime: targetDate.clone().add(eveningStartHour, 'hours').add(eveningStartMinute, 'minutes').add(this.sessionDuration, 'minutes').toDate(),
            status: NFTSessionStatus.PENDING
          });

          // Initialize evening session with SessionManager
          await this.sessionManager.initializeSession(eveningSession);
        }
      }

      this.logger.log(`[TradingSessionService][checkAndCreateTodaySessions] Hoàn tất kiểm tra và tạo phiên giao dịch cho ${this.daysToCreate} ngày tiếp theo`);
    } catch (error) {
      this.logger.error('[TradingSessionService][checkAndCreateTodaySessions] Lỗi tạo phiên giao dịch:', error);
      throw error;
    }
  }

  // Handler methods for cron jobs
  private async handleOpenSession(): Promise<void> {
    this.logger.log('[TradingSessionService][handleOpenSession] Mở phiên giao dịch...');
    try {
      const currentSession = await this.getCurrentSession();
      if (currentSession && currentSession.status === NFTSessionStatus.PENDING) {
        await this.sessionManager.openSession(currentSession.id);
        await this.resetTotalVolumeAndTotalEarnings();
      }
    } catch (error) {
      this.logger.error('[TradingSessionService][handleOpenSession] Lỗi mở phiên giao dịch:', error);
    }
  }

  private async handleCloseSession(): Promise<void> {
    this.logger.log('[TradingSessionService][handleCloseSession] Đóng phiên giao dịch...');
    try {
      // Lấy session đang mở
      const openSession = await this.sessionRepository.findOne({
        where: {
          status: NFTSessionStatus.OPEN
        }
      });

      await this.sessionManager.closeSession(openSession.id);

      await this.processCloseSession(openSession.id);

      this.logger.log('[TradingSessionService][handleCloseSession] Đóng phiên giao dịch thành công');
    } catch (error) {
      this.logger.error('[TradingSessionService][handleCloseSession] Lỗi đóng phiên giao dịch:', error);
    }
  }
  // Get today's sessions
  async getTodaySessions(): Promise<NFTTradingSession[]> {
    try {
      const today = moment().tz(this.timezone).startOf('day');
      const tomorrow = today.clone().add(1, 'day');

      const sessions = await this.sessionRepository.find({
        where: {
          date: Between(today.toDate(), tomorrow.toDate())
        },
        order: {
          openTime: 'ASC'
        }
      });

      return sessions;
    } catch (error) {
      this.logger.error('[TradingSessionService][getTodaySessions] Lỗi lấy phiên giao dịch hôm nay:', error);
      throw error;
    }
  }

  // Get all sessions
  async getSessions(): Promise<NFTTradingSession[]> {
    try {
      const currentDate = moment().tz(this.timezone).startOf('day').toDate();
      const currentDateTime = moment().tz(this.timezone).toDate();

      const sessions = await this.sessionRepository.find({
        where: [
          { status: NFTSessionStatus.OPEN },
          {
            status: NFTSessionStatus.PENDING,
            openTime: MoreThanOrEqual(currentDate),
            closeTime: MoreThanOrEqual(currentDateTime),
          }
        ],
        order: {
          openTime: 'ASC'
        }
      });

      return sessions;
    } catch (error) {
      this.logger.error('[TradingSessionService][getSessions] Lỗi lấy tất cả phiên giao dịch:', error);
      throw error;
    }
  }

  // Get current active session
  async getCurrentSession(): Promise<NFTTradingSession | null> {
    try {
      const now = moment().tz(this.timezone);
      const today = now.clone().startOf('day');
      const endOfDay = now.clone().endOf('day');

      const session = await this.sessionRepository.findOne({
        where: {
          date: Between(today.toDate(), endOfDay.toDate()),
          openTime: LessThanOrEqual(now.toDate()),
          closeTime: MoreThanOrEqual(now.toDate()),
        }
      });

      return session;
    } catch (error) {
      this.logger.error('[TradingSessionService][getCurrentSession] Lỗi lấy phiên giao dịch hiện tại:', error);
      throw error;
    }
  }

  // Check if trading is currently allowed
  async isSessionOpen(): Promise<boolean> {
    try {
      const currentSession = await this.getCurrentSession();
      return currentSession?.status === NFTSessionStatus.OPEN;
    } catch (error) {
      if (error instanceof NotFoundException) {
        return false;
      }
      throw error;
    }
  }

  // Get session statistics for a date range
  async getSessionStatistics(startDate: Date, endDate: Date) {
    try {
      const sessions = await this.sessionRepository.find({
        where: {
          date: Between(startDate, endDate)
        }
      });

      const totalOrders = sessions.reduce((sum, session) => sum + session.totalOrders, 0);
      const totalMatched = sessions.reduce((sum, session) => sum + session.matchedOrders, 0);
      const totalVolume = sessions.reduce((sum, session) => sum + Number(session.totalVolume), 0);

      return {
        totalSessions: sessions.length,
        totalOrders,
        totalMatched,
        totalVolume,
        averageMatchRate: totalOrders > 0 ? (totalMatched / totalOrders) * 100 : 0
      };
    } catch (error) {
      this.logger.error('[TradingSessionService][getSessionStatistics] Lỗi lấy thống kê phiên giao dịch:', error);
      throw error;
    }
  }

  // Update session statistics when order is created
  async incrementTotalOrders(sessionId: string): Promise<void> {
    try {
      await this.sessionManager.updateSessionStatistics(sessionId, {
        totalOrders: (await this.sessionRepository.findOne({ where: { id: sessionId } }))?.totalOrders + 1
      });
    } catch (error) {
      this.logger.error(`[TradingSessionService][incrementTotalOrders] Lỗi tăng tổng đơn hàng cho phiên ${sessionId}:`, error);
      throw error;
    }
  }

  // Update session statistics when order is matched
  async incrementMatchedOrders(sessionId: string, orderVolume: number): Promise<void> {
    try {
      const session = await this.sessionRepository.findOne({ where: { id: sessionId } });
      if (!session) return;

      await this.sessionManager.updateSessionStatistics(sessionId, {
        matchedOrders: session.matchedOrders + 1,
        totalVolume: Number(session.totalVolume) + orderVolume
      });
    } catch (error) {
      this.logger.error(`[TradingSessionService][incrementMatchedOrders] Lỗi tăng tổng đơn hàng cho phiên ${sessionId}:`, error);
      throw error;
    }
  }

  async scheduleForSession(userId: string, sessionId: string, amountUSDT: number): Promise<void> {
    this.logger.debug(`[TradingSessionService][scheduleForSession] Xử lý đặt hẹn phiên ${sessionId} cho người dùng ${userId} với số tiền ${amountUSDT}`);

    const schedulingFee = (amountUSDT / 100) * this.schedulingFeeRate;

    const wallet = await this.walletService.findByUserId(userId);

    const session = await this.sessionRepository.findOne({
      where: { id: sessionId }
    });

    if (!session) {
      this.logger.debug(`[TradingSessionService][scheduleForSession] Phiên ${sessionId} không tồn tại`);
      throw new NotFoundException('Session not found');
    }

    // Kiểm tra trạng thái phiên
    if (session.status !== NFTSessionStatus.PENDING) {
      this.logger.debug(`[TradingSessionService][scheduleForSession] Phiên ${sessionId} không đang chờ`);
      throw new BadRequestException('Can only schedule for pending sessions');
    }

    // Kiểm tra xem user đã đặt lịch cho phiên này chưa
    const existingScheduling = await this.dataSource
      .getRepository(NFTScheduling)
      .findOne({
        where: {
          userId,
          sessionId,
          status: Not(SchedulingStatus.CANCELLED)
        }
      });

    if (existingScheduling) {
      this.logger.debug(`[TradingSessionService][scheduleForSession] Người dùng ${userId} đã đặt hẹn cho phiên ${sessionId}`);
      throw new BadRequestException('You have already scheduled for this session');
    }

    // Xử lý phí đặt hẹn
    const wmToken = await this.tokenService.findBySymbol('WM');
    const wmBalance = await this.tokenService.findTokenByUserIdAndId(userId, wmToken.id);

    if (wmBalance.availableBalance < schedulingFee) {
      this.logger.debug(`[TradingSessionService][scheduleForSession] Người dùng ${userId} không có đủ WM balance để đặt hẹn phiên ${sessionId}`);
      throw new BadRequestException(`Insufficient WM balance for scheduling fee (${schedulingFee} WM required)`);
    }

    // Sử dụng transaction để đảm bảo tính nhất quán
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const now = getCurrentTime();
      const user = await queryRunner.manager.findOne(User, { where: { id: userId } });

      // Ghi nhận transaction
      const transaction = queryRunner.manager.create(Transaction, {
        walletId: user.web3WalletId,
        userId,
        tokenId: wmToken.id,
        type: TransactionType.SCHEDULING_FEE,
        amount: -schedulingFee,
        reference: sessionId,
        status: TransactionStatus.COMPLETED,
        note: `Scheduling fee for session ${sessionId} -${schedulingFee}WM`,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      const savedTransaction = await queryRunner.manager.save(Transaction, transaction);

      // Trừ phí đặt hẹn
      await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, wallet, wmToken, 'wmBalance', savedTransaction, schedulingFee, 'subtract', now);

      // Tạo bản ghi đặt hẹn
      const scheduling = await queryRunner.manager.save(NFTScheduling, {
        userId,
        sessionId,
        status: SchedulingStatus.PENDING,
        amountUSDT,
        fee: schedulingFee,
        createdAt: now,
        updatedAt: now
      });

      // Cập nhật số lượng đặt hẹn trong session
      await queryRunner.manager.update(
        NFTTradingSession,
        { id: sessionId },
        {
          totalSchedules: () => 'totalSchedules + 1',
          updatedAt: now
        }
      );

      await queryRunner.commitTransaction();

      // Emit event thông báo đặt hẹn thành công
      this.server.to(`user:${userId}`).emit('session:scheduled', {
        sessionId,
        schedulingId: scheduling.id,
        fee: schedulingFee
      });

      this.logger.debug(`[TradingSessionService][scheduleForSession] Đã đặt hẹn phiên ${sessionId} cho người dùng ${userId} với số tiền ${amountUSDT}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`[TradingSessionService][scheduleForSession] Lỗi đặt hẹn phiên ${sessionId}:`, error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async openSession(sessionId: string): Promise<void> {
    this.logger.debug(`[TradingSessionService][openSession] Mở phiên ${sessionId}`);
    await this.sessionManager.openSession(sessionId);

    await this.resetTotalVolumeAndTotalEarnings();

    this.logger.debug(`[TradingSessionService][openSession] Phiên ${sessionId} đã được mở thành công`);
  }

  async resetTotalVolumeAndTotalEarnings() {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await queryRunner.manager.query(`
        UPDATE users
        SET totalVolumeSession = 0, totalEarningsSession = 0, totalSellVolumeSession = 0
      `);

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
  }

  async resetTotalVolumeAndTotalEarningsPerDay() {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await queryRunner.manager.query(`
        UPDATE users
        SET totalVolumePerDay = 0, totalEarningsPerDay = 0, totalSellVolumePerDay = 0
      `);

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
  }

  async summarizeUplineTotalVolume(queryRunner: QueryRunner, buyerTotalVolumeMap: Map<string, BigNumber>) {
    try {
      for (const [buyerId, volume] of buyerTotalVolumeMap.entries()) {
        // Lấy thông tin trader
        const trader = await queryRunner.manager.findOne(User, {
          where: { id: buyerId },
          select: ['id', 'path', 'referredBy', 'isRoot', 'totalVolume', 'totalVolumePerDay', 'totalVolumeSession']
        });

        if (!trader) {
          continue;
        }

        // Xử lý upline IDs từ path
        let uplineIds: string[] = [];

        if (trader.isRoot) {
          trader.totalVolume = new BigNumber(trader.totalVolume || 0).plus(volume).toNumber();
          trader.totalVolumePerDay = new BigNumber(trader.totalVolumePerDay || 0).plus(volume).toNumber();
          trader.totalVolumeSession = new BigNumber(trader.totalVolumeSession || 0).plus(volume).toNumber();

          await queryRunner.manager.save(trader);
          continue;
        }

        if (!trader.path) {
          continue;
        }

        uplineIds = trader.path.split('.').filter(id => id !== trader.id);

        if (uplineIds.length === 0) {
          continue;
        }

        await queryRunner.manager.transaction(async manager => {
          await manager.query(`
            UPDATE users
            SET totalVolume = COALESCE(totalVolume, 0) + ?,
            totalVolumePerDay = COALESCE(totalVolumePerDay, 0) + ?,
            totalVolumeSession = COALESCE(totalVolumeSession, 0) + ?
            WHERE id IN (?)
          `, [volume.toNumber(), volume.toNumber(), volume.toNumber(), uplineIds]);
        });

        this.logger.debug(`[TradingSessionService][summarizeUplineTotalVolume] Tổng trước ${trader.id} - ${trader.totalVolumePerDay}`);

        trader.totalVolume = new BigNumber(trader.totalVolume || 0).plus(volume).toNumber();
        trader.totalVolumePerDay = new BigNumber(trader.totalVolumePerDay || 0).plus(volume).toNumber();
        trader.totalVolumeSession = new BigNumber(trader.totalVolumeSession || 0).plus(volume).toNumber();

        this.logger.debug(`[TradingSessionService][summarizeUplineTotalVolume] Tổng sau ${trader.id} - ${trader.totalVolumePerDay}`);

        await queryRunner.manager.save(trader);

        this.logger.debug(`[TradingSessionService][summarizeUplineTotalVolume] Trader ${trader.id} có referredBy ${trader.referredBy}`);
        if (trader.referredBy) {
          const referral = await queryRunner.manager.findOne(User, { where: { id: trader.referredBy } });
          if (referral) {
            referral.hasF1BuySession = true;
            await queryRunner.manager.save(referral);
          }
        }
      }
    } catch (error) {
      this.logger.error('[TradingSessionService][summarizeUplineTotalVolume] Lỗi khi cập nhật volume cho upline:', error);
      throw error;
    }
  }

  async summarizeUplineTotalSellVolume(queryRunner: QueryRunner, sellerTotalVolumeMap: Map<string, BigNumber>) {
    try {
      for (const [sellerId, volume] of sellerTotalVolumeMap.entries()) {
        // Lấy thông tin trader
        const seller = await queryRunner.manager.findOne(User, {
          where: { id: sellerId },
          select: ['id', 'path', 'referredBy', 'isRoot', 'totalSellVolume', 'totalSellVolumePerDay', 'totalSellVolumeSession']
        });

        if (!seller) {
          continue;
        }

        // Xử lý upline IDs từ path
        let uplineIds: string[] = [];

        if (seller.isRoot) {
          seller.totalSellVolume = new BigNumber(seller.totalSellVolume || 0).plus(volume).toNumber();
          seller.totalSellVolumePerDay = new BigNumber(seller.totalSellVolumePerDay || 0).plus(volume).toNumber();
          seller.totalSellVolumeSession = new BigNumber(seller.totalSellVolumeSession || 0).plus(volume).toNumber();

          await queryRunner.manager.save(seller);
          continue;
        }

        if (!seller.path) {
          continue;
        }

        uplineIds = seller.path.split('.').filter(id => id !== seller.id);

        if (uplineIds.length === 0) {
          continue;
        }

        await queryRunner.manager.transaction(async manager => {
          await manager.query(`
            UPDATE users
            SET totalSellVolume = COALESCE(totalSellVolume, 0) + ?,
            totalSellVolumePerDay = COALESCE(totalSellVolumePerDay, 0) + ?,
            totalSellVolumeSession = COALESCE(totalSellVolumeSession, 0) + ?
            WHERE id IN (?)
          `, [volume.toNumber(), volume.toNumber(), volume.toNumber(), uplineIds]);
        });

        this.logger.debug(`[TradingSessionService][summarizeUplineTotalSellVolume] Tổng trước ${seller.id} - ${seller.totalVolumePerDay}`);

        seller.totalSellVolume = new BigNumber(seller.totalSellVolume || 0).plus(volume).toNumber();
        seller.totalSellVolumePerDay = new BigNumber(seller.totalSellVolumePerDay || 0).plus(volume).toNumber();
        seller.totalSellVolumeSession = new BigNumber(seller.totalSellVolumeSession || 0).plus(volume).toNumber();

        this.logger.debug(`[TradingSessionService][summarizeUplineTotalSellVolume] Tổng sau ${seller.id} - ${seller.totalVolumePerDay}`);

        await queryRunner.manager.save(seller);

        this.logger.debug(`[TradingSessionService][summarizeUplineTotalSellVolume] Trader ${seller.id} có referredBy ${seller.referredBy}`);
        if (seller.referredBy) {
          const referral = await queryRunner.manager.findOne(User, { where: { id: seller.referredBy } });
          if (referral) {
            referral.hasF1SellSession = true;
            await queryRunner.manager.save(referral);
          }
        }
      }
    } catch (error) {
      this.logger.error('[TradingSessionService][summarizeUplineTotalSellVolume] Lỗi khi cập nhật volume cho upline:', error);
      throw error;
    }
  }

  async summarizeWManagerRank(queryRunner: QueryRunner) {
    try {
      // 1. Lấy thông tin của tất cả users có volume >= 6000
      const query = `
            WITH user_base AS (
                SELECT
                    u.id,
                    u.isKycCompleted,
                    u.wManagerRank as currentRank,
                    u.totalVolumePerDay as totalVolume,
                    (
                        SELECT COUNT(*)
                        FROM users f1
                        WHERE f1.referredBy = u.id
                        AND f1.isKycCompleted = true
                    ) as kycF1Count
                FROM users u
                WHERE u.totalVolumePerDay >= 6000 AND (u.hasF1BuySession = true OR u.hasF1SellSession = true)
            )
            SELECT
                ub.*,
                JSON_OBJECT(
                'W1', (
                    SELECT COUNT(DISTINCT d.id)
                    FROM users f1
                    JOIN users d ON d.path LIKE CONCAT(f1.path, '%')
                    WHERE f1.referredBy = ub.id
                    AND d.wManagerRank = 'WM1'
                ),
                'W2', (
                    SELECT COUNT(DISTINCT d.id)
                    FROM users f1
                    JOIN users d ON d.path LIKE CONCAT(f1.path, '%')
                    WHERE f1.referredBy = ub.id
                    AND d.wManagerRank = 'WM2'
                ),
                'W3', (
                    SELECT COUNT(DISTINCT d.id)
                    FROM users f1
                    JOIN users d ON d.path LIKE CONCAT(f1.path, '%')
                    WHERE f1.referredBy = ub.id
                    AND d.wManagerRank = 'WM3'
                ),
                'W4', (
                    SELECT COUNT(DISTINCT d.id)
                    FROM users f1
                    JOIN users d ON d.path LIKE CONCAT(f1.path, '%')
                    WHERE f1.referredBy = ub.id
                    AND d.wManagerRank = 'WM4'
                ),
                'W5', (
                    SELECT COUNT(DISTINCT d.id)
                    FROM users f1
                    JOIN users d ON d.path LIKE CONCAT(f1.path, '%')
                    WHERE f1.referredBy = ub.id
                    AND d.wManagerRank = 'WM5'
                )
            ) as branchCounts
            FROM user_base ub
        `;

      const userStats: UserWManagerStats[] = await queryRunner.query(query);

      if (userStats.length === 0) {
        this.logger.debug('[TradingSessionService][summarizeWManagerRank] Không có user nào đủ điều kiện xét rank');
        return;
      }

      // 2. Xử lý từng user
      const rankUpdates: { id: string; oldRank: string; newRank: string }[] = [];
      for (const user of userStats) {
        if (!user.isKycCompleted) { // Bỏ qua nếu user chưa KYC
          continue;
        }

        const volume = user.totalVolume;
        const currentRank = user.currentRank || '';
        const branchCounts = user.branchCounts;

        let newRank = currentRank;

        // Check W5
        if (volume >= 200000 && currentRank === 'W4' && branchCounts['W4'] >= 2) {
          newRank = WManagerRank.WM5;
        }

        // Check W4
        else if (volume >= 100000 && currentRank === 'W3' && branchCounts['W3'] >= 2) {
          newRank = WManagerRank.WM4;
        }

        // Check W3
        else if (volume >= 40000 && currentRank === 'W2' && branchCounts['W2'] >= 2) {
          newRank = WManagerRank.WM3;
        }

        // Check W2
        else if (volume >= 15000 && currentRank === 'W1' && branchCounts['W1'] >= 2) {
          newRank = WManagerRank.WM2;
        }

        // Check W1
        else if (volume >= 6000 && currentRank === '' && user.kycF1Count >= 2) {
          newRank = WManagerRank.WM1;
        }

        if (newRank !== currentRank) {
          rankUpdates.push({
            id: user.id,
            oldRank: currentRank || 'No Rank',
            newRank: newRank as WManagerRank
          });
        }
      }

      // 3. Batch update ranks
      if (rankUpdates.length > 0) {
        await queryRunner.manager.transaction(async manager => {
          for (const update of rankUpdates) {
            await manager.update(User,
              { id: update.id },
              { wManagerRank: update.newRank as WManagerRank }
            );
            this.logger.debug(
              `[TradingSessionService][summarizeWManagerRank] Cập nhật rank của user ${update.id} từ ${update.oldRank} thành ${update.newRank}`
            );
          }
        });

        this.logger.debug(
          `[TradingSessionService][summarizeWManagerRank] Đã cập nhật rank cho ${rankUpdates.length} users`
        );
      }

      // 4. Cập nhật rank W6 với điều kiện có 2 F1 đạt cấp W5
      const queryUpdateShareholder = `
        UPDATE users u
        INNER JOIN (
          SELECT DISTINCT u2.id
          FROM users u2
          LEFT JOIN users f1 ON f1.referredBy = u2.id
          WHERE u2.wManagerRank = 'WM5'
          AND u2.isKycCompleted = true
          AND f1.wManagerRank = 'WM5'
          AND f1.isKycCompleted = true
          GROUP BY u2.id
          HAVING COUNT(DISTINCT f1.id) >= 2
        ) qualified ON u.id = qualified.id
        SET u.isShareholder = true;
      `;

      await queryRunner.query(queryUpdateShareholder);
    } catch (error) {
      this.logger.error('[TradingSessionService][summarizeWManagerRank] Lỗi khi tổng hợp rank WManager:', error);
      throw error;
    }
  }

  /**
   * // Từ vị trí seller, lấy theo path ngược lên để tìm W-Manager,
    // Nếu trên path mà chỉ có 1 W-Manager thì người đó được hưởng nguyên mức hoa hồng.
    // Nếu có cấp W-Manager thấp hơn thì thì người thấp hơn được hưởng hoa hồng trước, rồi đến cấp tiếp theo tương ứng với mức hoa hồng 0.5%
    // Tổng hoa hồng tối đa chỉ là 3% tổng bán của một trader
    // Ghi nhận hoa hồng này là: Ranking commission (Lưu vào giao dịch + cộng tiền tài khoản)

    // Ví dụ 1:
    // - Trader A bán 1000 USDT, có 1 W1 và 1 W2 trên path.
    // - W1 được hưởng 1% hoa hồng từ A.
    // - W2 được hưởng 0.5% hoa hồng từ A (Vì mức tối đa của W2 là 1.5%).
    // - Tổng hoa hồng tối đa là 3% từ A, do đó W1 và W2 chỉ được hưởng 1.5% tổng bán của A.
    // Ví dụ 2:
    // - Trader B bán 1000 USDT, có 1 W1 và 1 W3 trên path.
    // - W1 được hưởng 1% hoa hồng từ B.
    // - W3 được hưởng 1% hoa hồng từ B (Vì mức tối đa của W3 là 2%).
    // - Tổng hoa hồng tối đa là 3% từ B, do đó W1 và W3 chỉ được hưởng 2% tổng bán của B.

    // Ví dụ 3:
    // - Trader C bán 1000 USDT, có 1 W5 trên path.
    // - W5 được hưởng 3% hoa hồng từ C.
    // - Tổng hoa hồng tối đa là 3% từ C, do đó W5 chỉ được hưởng 3% tổng bán của C.

    // Mỗi W được nhận hoa hồng thì người giới thiệu ra W-Manager sẽ được nhận 20% tổng hoa hồng từ W đó.
    // Ví dụ:
    // - Trader D bán 1000 USDT, có 1 W1 và 1 W2 trên path.
    // - W1 được hưởng 1% hoa hồng từ D.
    // - W2 được hưởng 0.5% hoa hồng từ D.
    // - Người giới thiệu ra W1 là R, thì R được nhận 20% hoa hồng từ W1.
    // - Ghi nhận hoa hồng này là: Matching Bonus (Lưu vào giao dịch + cộng tiền tài khoản)
   * @param queryRunner
   * @param sellTotalVolumeMap
   * @param usdtToken
   * @param sessionId
   * @param now
   * @returns
   */
  async summarizeWManagerCommission(queryRunner: QueryRunner, sellTotalVolumeMap: Map<string, BigNumber>, usdtToken: Token, sessionId: string, now: Date) {
    // Định nghĩa tỷ lệ bằng points thay vì số thập phân
    const commissionRatePoints: Record<WManagerRank, number> = {
      [WManagerRank.WM1]: 50,   // 1% = 100 points (0.5% -> 50 points)
      [WManagerRank.WM2]: 75,   // 1.5% = 150 points (0.75% -> 75 points)
      [WManagerRank.WM3]: 100,   // 2% = 200 points (1% -> 100 points)
      [WManagerRank.WM4]: 125,   // 2.5% = 250 points (1.25% -> 125 points)
      [WManagerRank.WM5]: 150,   // 3% = 300 points (1.5% -> 150 points)
    };
    const RATE_DENOMINATOR = 10000;  // Mẫu số để tính rate
    const MATCHING_BONUS_NUMERATOR = 20; // 20%
    const MATCHING_BONUS_DENOMINATOR = 100;
    const rankingCommissionTransactions: Transaction[] = [];

    for (const [sellerId, volume] of sellTotalVolumeMap.entries()) {
      const seller = await queryRunner.manager.findOne(User, {
        where: { id: sellerId },
        select: ['id', 'path', 'isRoot', 'wManagerRank', 'referredBy'],
      });

      if (!seller || seller.isRoot) continue;

      const allUplineIds = seller.path.split('.');
      // Lấy tối đa 5 đời upline (bỏ qua chính seller - phần tử cuối cùng)
      const uplineIds = allUplineIds.slice(0, -1).slice(-5); // Lấy 5 phần tử cuối (gần nhất với seller)

      const uplines = await queryRunner.manager.find(User, {
        where: { id: In(uplineIds) },
        select: ['id', 'wManagerRank', 'web3WalletId', 'totalEarningsSession', 'totalEarningsPerDay', 'totalEarnings'],
      });

      const sortedUplines = uplines.sort((a, b) => {
        const aIndex = uplineIds.indexOf(a.id);
        const bIndex = uplineIds.indexOf(b.id);
        return bIndex - aIndex;
      });

      let lastWManagerRank = null;
      let lastWManagerCommission = new BigNumber(0);
      let processedWManagers = new Set<string>();

      for (const upline of sortedUplines) {
        if (upline.id === seller.referredBy || !upline.wManagerRank || processedWManagers.has(upline.id)) {
          continue;
        }

        const currentRankPoints = commissionRatePoints[upline.wManagerRank];
        if (!currentRankPoints) continue;

        let commission = new BigNumber(0);
        let isMatchingBonus = false;

        if (!lastWManagerRank) {
          // W-Manager đầu tiên - tính commission bằng points
          commission = new BigNumber(volume)
            .multipliedBy(currentRankPoints)
            .dividedBy(RATE_DENOMINATOR)
            .decimalPlaces(8);  // Làm tròn 8 chữ số thập phân

          lastWManagerRank = upline.wManagerRank;
          lastWManagerCommission = commission;
          isMatchingBonus = false;
        } else {
          const lastRankValue = parseInt(lastWManagerRank.replace('WM', ''));
          const currentRankValue = parseInt(upline.wManagerRank.replace('WM', ''));

          if (currentRankValue <= lastRankValue) {
            // Nếu rank thấp hơn hoặc bằng, chỉ nhận 20% của W trước đó
            commission = lastWManagerCommission
              .multipliedBy(MATCHING_BONUS_NUMERATOR)
              .dividedBy(MATCHING_BONUS_DENOMINATOR)
              .decimalPlaces(8);

            lastWManagerCommission = commission;
            isMatchingBonus = true;
          } else {
            // Tính chênh lệch points thay vì rate
            const pointsDiff = currentRankPoints - commissionRatePoints[lastWManagerRank];
            commission = new BigNumber(volume)
              .multipliedBy(pointsDiff)
              .dividedBy(RATE_DENOMINATOR)
              .decimalPlaces(8);

            lastWManagerRank = upline.wManagerRank;
            lastWManagerCommission = commission;
            isMatchingBonus = false;
          }
        }

        if (commission.gt(0)) {
          processedWManagers.add(upline.id);

          const wallet = await queryRunner.manager.findOne(Wallet, {
            where: { userId: upline.id }
          });

          const transaction = queryRunner.manager.create(Transaction, {
            userId: upline.id,
            walletId: wallet.id,
            tokenId: usdtToken.id,
            type: TransactionType.RANKING_COMMISSION,
            amount: commission.toNumber(),
            status: TransactionStatus.COMPLETED,
            reference: sellerId,
            note: isMatchingBonus
              ? `Matching Bonus (${upline.wManagerRank}) 20% from ${lastWManagerRank} commission of seller ${sellerId} in session ${sessionId} volume ${volume} (+${commission.toString()}USDT)`
              : `Ranking Commission (${upline.wManagerRank}) from seller ${sellerId} in session ${sessionId} volume ${volume} (+${commission.toString()}USDT)`,
            createdAt: now,
            updatedAt: now,
            transactionAt: now,
          });

          rankingCommissionTransactions.push(transaction);

          await this.walletService.updateTokenBalanceWithQueryRunner(
            queryRunner,
            wallet,
            usdtToken,
            'usdtBalance',
            transaction,
            commission.toNumber(),
            'add'
          );

          const currentEarningsSession = new BigNumber(upline.totalEarningsSession || 0);
          const currentEarningsPerDay = new BigNumber(upline.totalEarningsPerDay || 0);
          const currentEarnings = new BigNumber(upline.totalEarnings || 0);

          upline.totalEarningsSession = currentEarningsSession.plus(commission).toNumber();
          upline.totalEarningsPerDay = currentEarningsPerDay.plus(commission).toNumber();
          upline.totalEarnings = currentEarnings.plus(commission).toNumber();

          await queryRunner.manager.save(upline);
        }
      }
    }

    return rankingCommissionTransactions;
  }

  async processCloseSession(sessionId: string): Promise<NFTTradingSession> {

    // Lấy thông tin phiên hiện tại
    const session = await this.sessionRepository.findOne({ where: { id: sessionId, status: NFTSessionStatus.CLOSED } });
    if (!session) {
      this.logger.error(`[TradingSessionService] Phiên đã được xử lý ${sessionId} không tồn tại`);
      throw new NotFoundException('Session not found');
    }

    // Lấy tất cả đơn hàng (Buy/Sell)
    const allowedBuyOrders = await this.dataSource.getRepository(NFTOrder).find({
      where: { sessionId: sessionId, orderType: OrderType.BUY, status: OrderStatus.PENDING, isTransferred: false, isBuy: true }, // Có mua trực tiếp
      order: { isPriorityBuy: 'DESC', nftPrice: 'ASC' },
      relations: ['trader', 'nft'],
    });

    // Lấy tất cả đơn hàng bán (SELL) có isTransferred = true
    const allowedSellOrders = await this.dataSource.getRepository(NFTOrder).find({
      where: { orderType: OrderType.SELL, status: OrderStatus.PENDING, isTransferred: true }, // Lấy tất cả đơn được đăng bán đã đủ 24h
      order: { createdAt: 'ASC' },
      relations: ['trader', 'nft'],
    });

    // Lấy tất cả đơn hàng bán (SELL) có isTransferred = false
    const notAllowedSellOrders = await this.dataSource.getRepository(NFTOrder).find({
      where: { orderType: OrderType.SELL, status: OrderStatus.PENDING, isTransferred: false },
      order: { createdAt: 'ASC' },
      relations: ['trader', 'nft'],
    });

    // Lấy thông tin token WM và USDT
    const wmToken = await this.tokenService.findBySymbol('WM');
    const usdtToken = await this.tokenService.findBySymbol('USDT');

    // Lấy tham số tổng volume phiên (trong bảng cấu hình)
    const maxSessionVolumePercentConfig = await this.systemConfigService.findByKey('MAX_SESSION_VOLUME');
    const maxSessionVolumePercent = Number(maxSessionVolumePercentConfig?.value || 25) > 100 ? 100 : Number(maxSessionVolumePercentConfig?.value || 25);

    // Lấy tổng giá trị đặt trước
    const totalScheduledVolume = allowedBuyOrders.reduce((acc, order) => {
      return acc.plus(new BigNumber(order.nftPrice).multipliedBy(new BigNumber(order.quantity)));
    }, new BigNumber(0));

    this.logger.log(`[TradingSessionService] Tổng volume đặt trước phiên ${sessionId}: ${totalScheduledVolume}`);

    // Tính tổng volume phiên cho phép (Mặc định 25% tổng volume đặt trước)
    const maxSessionVolume = Number(new BigNumber(totalScheduledVolume).multipliedBy(new BigNumber(maxSessionVolumePercent)).dividedBy(new BigNumber(100)).toString());

    const wmUsdRateConfig = await this.systemConfigService.findByKey('WM_USD_RATE');
    const wmUsdRate = Number(wmUsdRateConfig?.value || 0);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const now = getCurrentTime();

      // Bước 1: Xử lý sở hữu NFT cho buyer sau khi phiên hoàn thành
      const buyerTotalVolumeMap = new Map<string, BigNumber>();
      let totalAllowedBuyVolume = 0;
      const orderIds = [];
      for (const order of allowedBuyOrders) {
        totalAllowedBuyVolume = new BigNumber(totalAllowedBuyVolume).plus(new BigNumber(order.nftPrice).multipliedBy(new BigNumber(order.quantity))).toNumber();
        if (new BigNumber(totalAllowedBuyVolume).gt(new BigNumber(maxSessionVolume))) {
          break;
        }

        let nft = await queryRunner.manager.findOne(NFT, {
          where: { ownerId: order.traderId, type: order.nftType },
        });

        if (!nft) {
          nft = queryRunner.manager.create(NFT, {
            ownerId: order.traderId,
            type: order.nftType,
            initialPrice: order.nftPrice,
            currentPrice: order.nftPrice,
            gasFee: order.gasFee,
            quantity: order.quantity,
            status: NFTStatus.AVAILABLE,
            owner: order.trader,
            createdAt: now,
            updatedAt: now,
          });
          await queryRunner.manager.save(nft);
        } else {
          nft.quantity = (new BigNumber(nft.quantity || 0)).plus(new BigNumber(order.quantity)).toString();
          nft.currentPrice = order.nftPrice;
          await queryRunner.manager.save(nft);
        }

        order.isTransferred = true;
        order.status = OrderStatus.MATCHED;
        order.matchedAt = now;
        await queryRunner.manager.save(order);

        orderIds.push(order.id);

        // Cập nhật tổng giao dịch của mỗi người dùng có tham gia mua
        const orderVolume = new BigNumber(order.nftPrice).multipliedBy(order.quantity);
        const currentVolume = buyerTotalVolumeMap.get(order.traderId) || new BigNumber(0);
        let totalVolume = currentVolume.plus(orderVolume);
        buyerTotalVolumeMap.set(order.traderId, totalVolume);
      }

      for (const order of allowedBuyOrders) {
        if (!orderIds.includes(order.id)) {
          // 10. Tạo giao dịch trả lại tiền (USDT và phí gas WM)
          order.isTransferred = true;
          order.status = OrderStatus.CANCELLED;
          order.matchedAt = now;
          await queryRunner.manager.save(order);

          if (order.trader?.web3WalletId) {
            const orderTraderWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: order.traderId } });
            const transactionUSDT = queryRunner.manager.create(Transaction, {
              userId: order.traderId,
              walletId: orderTraderWallet.id,
              type: TransactionType.NFT_PURCHASE,
              status: TransactionStatus.COMPLETED,
              amount: Number(order.nftPrice),
              tokenId: usdtToken.id,
              reference: order.id,
              note: `Refund USDT for buy NFT ${order.nftType} in session ${sessionId} (+${order.nftPrice}USDT)`,
              transactionAt: now,
              createdAt: now,
              updatedAt: now,
            });

            const savedTransactionUSDT = await queryRunner.manager.save(Transaction, transactionUSDT);

            await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, orderTraderWallet, usdtToken, 'usdtBalance', savedTransactionUSDT, order.nftPrice, 'add', now);

            // 11. Tạo giao dịch trả lại phí gas
            const gasFeeInWM = new BigNumber(order.gasFee).dividedBy(wmUsdRate); // Quy đổi USDT sang WM
            const transactionWM = queryRunner.manager.create(Transaction, {
              userId: order.traderId,
              walletId: orderTraderWallet.id,
              type: TransactionType.NFT_PURCHASE,
              status: TransactionStatus.COMPLETED,
              amount: gasFeeInWM.toNumber(),
              tokenId: wmToken.id,
              reference: order.id,
              note: `Refund gas fee for buy NFT ${order.nftType} in session ${sessionId} (+${gasFeeInWM.toNumber()}WM)`,
              transactionAt: now,
              createdAt: now,
              updatedAt: now,
            });

            const savedTransactionWM = await queryRunner.manager.save(Transaction, transactionWM);

            // 11. Cập nhật số dư WM và USDT
            await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, orderTraderWallet, wmToken, 'wmBalance', savedTransactionWM, gasFeeInWM.toNumber(), 'add', now);
          }
        }
      }

      // Bước 4: Xử lý đơn hàng bán (SELL) có isTransferred = true
      const sellTotalVolumeMap = new Map<string, BigNumber>();
      const sellTransactions: Transaction[] = [];
      const sellerIds = [];
      for (const order of allowedSellOrders) {
        order.status = OrderStatus.MATCHED;
        order.matchedAt = now;
        order.sessionType = session.type;
        order.sessionId = sessionId;
        order.sessionTime = session.openTime;
        await queryRunner.manager.save(order);

        // 5. Cập nhật tổng hoa hồng (phiên)
        const orderVolume = new BigNumber(order.nftPrice).multipliedBy(order.quantity);
        const total = orderVolume.multipliedBy(1.025); // Tổng gốc + lợi nhuận
        const profit = orderVolume.multipliedBy(0.025); // Tổng lợi nhuận

        const currentVolume = sellTotalVolumeMap.get(order.traderId) || new BigNumber(0);
        let totalVolume = currentVolume.plus(orderVolume);
        sellTotalVolumeMap.set(order.traderId, totalVolume);

        sellerIds.push(order.traderId);

        // 6. Trả tiền cho traderId
        if (order.trader?.web3WalletId) {
          const orderTraderWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: order.traderId } });
          const transaction = queryRunner.manager.create(Transaction, {
            walletId: orderTraderWallet.id,
            userId: order.traderId,
            tokenId: usdtToken.id,
            type: TransactionType.NFT_SALE,
            amount: total.toNumber(),
            status: TransactionStatus.COMPLETED,
            reference: order.id,
            note: `Sale NFT ${order.nftId} in session ${sessionId} price ${order.nftPrice} (+${total.toNumber()}USDT)`,
            transactionAt: now,
            createdAt: now,
            updatedAt: now,
          });

          sellTransactions.push(transaction);

          await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, orderTraderWallet, usdtToken, 'usdtBalance', transaction, total.toNumber(), 'add', now);
        }

        // 7. Tặng thêm dựa trên lợi nhuận
        let totalBonusPerProfit = new BigNumber(0);

        if (order.nftType === NFTType.SPIRIT_TURTLE) {
          // SPIRIT_TURTLE: 2.5% profit -> WM
          totalBonusPerProfit = profit
            .multipliedBy(0.025)  // 2.5% của profit
            .dividedBy(wmUsdRate);
        } else if (order.nftType === NFTType.UNICORN) {
          // UNICORN: 5% profit -> WM
          totalBonusPerProfit = profit
            .multipliedBy(0.05)   // 5% của profit
            .dividedBy(wmUsdRate);
        } else if (order.nftType === NFTType.DRAGON) {
          // DRAGON: 10% profit -> WM
          totalBonusPerProfit = profit
            .multipliedBy(0.1)    // 10% của profit
            .dividedBy(wmUsdRate);
        }

        if (order.trader?.web3WalletId) {
          const orderTraderWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: order.traderId } });
          const transactionBonusWM = queryRunner.manager.create(Transaction, {
            walletId: orderTraderWallet.id,
            userId: order.traderId,
            tokenId: wmToken.id,
            type: TransactionType.SALE_BONUS,
            amount: totalBonusPerProfit.toNumber(),
            status: TransactionStatus.COMPLETED,
            reference: order.id,
            note: `Bonus for sale NFT ${order.nftId} in session ${sessionId} price ${order.nftPrice} (+${totalBonusPerProfit.toNumber()}WM)`,
            createdAt: now,
            updatedAt: now,
            transactionAt: now,
          });

          sellTransactions.push(transactionBonusWM);

          await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, orderTraderWallet, wmToken, 'wmBalance', transactionBonusWM, totalBonusPerProfit.toNumber(), 'add', now);
        }

        // Trả 1% cho người giới thiệu (F1) với điều kiện người giới thiệu đã KYC
        const totalCommission = new BigNumber(orderVolume).multipliedBy(0.01);

        const referrer = await this.dataSource.getRepository(User).findOne({ where: { id: order.trader.referredBy } });
        if (referrer && referrer.isKycCompleted) {
          if (referrer.web3WalletId) {
            const referrerWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: referrer.id } });
            const transaction = queryRunner.manager.create(Transaction, {
              walletId: referrerWallet.id,
              userId: referrer.id,
              tokenId: usdtToken.id,
              type: TransactionType.DIRECT_COMMISSION,
              amount: totalCommission.toNumber(),
              status: TransactionStatus.COMPLETED,
              reference: order.id,
              note: `Direct commission for sale NFT ${order.nftId} in session ${sessionId} price ${order.nftPrice} (+${totalCommission.toNumber()}USDT)`,
              createdAt: now,
              updatedAt: now,
              transactionAt: now,
            });

            sellTransactions.push(transaction);

            // Cập nhật hoa hồng vào tài khoản (USDT)
            await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, referrerWallet, usdtToken, 'usdtBalance', transaction, totalCommission.toNumber(), 'add', now);
          }

          // Cập nhật tổng hoa hồng (phiên)
          const currentEarningsSession = new BigNumber(referrer.totalEarningsSession || 0);
          const currentEarningsPerDay = new BigNumber(referrer.totalEarningsPerDay || 0);
          const currentEarnings = new BigNumber(referrer.totalEarnings || 0);

          referrer.totalEarningsSession = currentEarningsSession.plus(totalCommission).toNumber();
          referrer.totalEarningsPerDay = currentEarningsPerDay.plus(totalCommission).toNumber();
          referrer.totalEarnings = currentEarnings.plus(totalCommission).toNumber();

          await queryRunner.manager.save(referrer);
        }
      }

      // Bước 5: Xử lý đơn hàng bán (SELL) có isTransferred = false (Vẫn tiếp tục PENDING)
      for (const order of notAllowedSellOrders) {
        if (!sellerIds.includes(order.traderId)) {
          order.isTransferred = true;
          order.matchedAt = now;
          await queryRunner.manager.save(order);
        }
      }

      // Bước 6.1: Cập nhật tổng mua cho upline
      await this.summarizeUplineTotalVolume(queryRunner, buyerTotalVolumeMap);

      // Bước 6.2: Cập nhật tông bán cho upline
      await this.summarizeUplineTotalSellVolume(queryRunner, sellTotalVolumeMap);

      // Bước 7:: Cập nhật rank WManager
      await this.summarizeWManagerRank(queryRunner);

      // Bước 8: Cập nhật hoa hồng cho W-Manager
      const rankingCommissionTransactions = await this.summarizeWManagerCommission(queryRunner, sellTotalVolumeMap, usdtToken, sessionId, now);

      // Bước 9: Cập nhật shareholder
      await this.summarizeShareholder(queryRunner, usdtToken, sessionId, now);

      // Lưu tất cả giao dịch

      if (sellTransactions.length > 0) {
        await queryRunner.manager.save(Transaction, sellTransactions);
      }

      if (rankingCommissionTransactions.length > 0) {
        await queryRunner.manager.save(Transaction, rankingCommissionTransactions);
      }

      session.status = NFTSessionStatus.COMPLETED;
      session.updatedAt = getCurrentTime();
      await queryRunner.manager.save(NFTTradingSession, session);

      await queryRunner.commitTransaction();

      return session;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`[TradingSessionService] Có lỗi xảy ra khi xử lý phiên ${sessionId}: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Some thing went wrong, please try again later`);
    } finally {
      await queryRunner.release();
    }
  }

  async summarizeShareholder(queryRunner: QueryRunner, usdtToken: Token, sessionId: string, now: Date) {
    this.logger.debug('[TradingSessionService][summarizeShareholder] Bắt đầu xử lý hoa hồng cho shareholders');

    try {
      // 1. Lấy tổng volume phiên của root user
      const rootVolumeResult = await queryRunner.query(`
            SELECT COALESCE(SUM(totalVolumeSession), 0) as totalRootVolume
            FROM users
            WHERE isRoot = true
            OR path IS NULL
            OR referredBy IS NULL
        `);

      const totalRootVolume = rootVolumeResult[0].totalRootVolume;

      if (totalRootVolume <= 0) {
        this.logger.debug('[TradingSessionService][summarizeShareholder] Không có volume để chia');
        return;
      }

      // 2. Đếm số lượng shareholders
      const shareholderCountResult = await queryRunner.query(`
            SELECT COUNT(*) as shareholderCount
            FROM users
            WHERE isShareholder = true
            AND isKycCompleted = true
        `);

      const shareholderCount = shareholderCountResult[0].shareholderCount;

      if (shareholderCount <= 0) {
        this.logger.debug('[TradingSessionService][summarizeShareholder] Không có shareholders để chia');
        return;
      }

      // 3. Tính commission cho mỗi shareholder (chia đều)
      const totalCommission = new BigNumber(totalRootVolume).multipliedBy(5).dividedBy(1000); // 0.5%
      const commissionPerShareholder = totalCommission.dividedBy(shareholderCount);

      // 4. Lấy danh sách shareholders
      const shareholders = await queryRunner.query(`
            SELECT u.id, u.web3WalletId, u.totalEarningsSession, u.totalEarningsPerDay, u.totalEarnings
            FROM users u
            WHERE u.isShareholder = true
            AND u.isKycCompleted = true
        `);

      // 5. Xử lý giao dịch cho từng shareholder
      for (const shareholder of shareholders) {
        const wallet = await queryRunner.manager.findOne(Wallet, {
          where: { userId: shareholder.id }
        });

        if (!wallet) continue;

        // Tạo giao dịch
        const transaction = queryRunner.manager.create(Transaction, {
          userId: shareholder.id,
          walletId: wallet.id,
          tokenId: usdtToken.id,
          type: TransactionType.CO_SHAREHOLDER_BONUS,
          amount: commissionPerShareholder.toNumber(),
          status: TransactionStatus.COMPLETED,
          reference: sessionId,
          note: `Co-Shareholder Bonus for session ${sessionId} volume ${totalRootVolume} (+${commissionPerShareholder.toString()}USDT)`,
          transactionAt: now,
          createdAt: now,
          updatedAt: now,
        });

        const savedTransaction = await queryRunner.manager.save(Transaction, transaction);

        // Cập nhật ví USDT
        await this.walletService.updateTokenBalanceWithQueryRunner(
          queryRunner,
          wallet,
          usdtToken,
          'usdtBalance',
          savedTransaction,
          commissionPerShareholder.toNumber(),
          'add',
          now
        );

        // Cập nhật tổng earnings
        const currentEarningsSession = new BigNumber(shareholder.totalEarningsSession || 0);
        const currentEarningsPerDay = new BigNumber(shareholder.totalEarningsPerDay || 0);
        const currentEarnings = new BigNumber(shareholder.totalEarnings || 0);

        await queryRunner.manager.update(User, shareholder.id, {
          totalEarningsSession: currentEarningsSession.plus(commissionPerShareholder).toNumber(),
          totalEarningsPerDay: currentEarningsPerDay.plus(commissionPerShareholder).toNumber(),
          totalEarnings: currentEarnings.plus(commissionPerShareholder).toNumber(),
          updatedAt: now
        });
      }

      this.logger.debug('[TradingSessionService][summarizeShareholder] Hoàn thành xử lý hoa hồng cho shareholders');
    } catch (error) {
      this.logger.error('[TradingSessionService][summarizeShareholder] Lỗi xử lý hoa hồng cho shareholders:', error);
      throw error;
    }
  }

  async closeSession(sessionId: string) {
    this.logger.debug(`[TradingSessionService] Đóng phiên ${sessionId}`);

    await this.sessionManager.closeSession(sessionId);
    this.logger.log(`[TradingSessionService] Phiên ${sessionId} đã được đóng thành công`);

    await this.processCloseSession(sessionId);
    this.logger.log(`[TradingSessionService] Phiên ${sessionId} đã được đóng thành công`);
  }

  async getSessionById(sessionId: string): Promise<NFTTradingSession> {
    return this.sessionRepository.findOne({ where: { id: sessionId } });
  }

  async manualRefundOrder(orderId: string, isUSDT: boolean, isWM: boolean, sessionId: string) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const order = await queryRunner.manager.findOne(NFTOrder, { where: { id: orderId }, relations: ['trader', 'nft'] });
    const usdtToken = await this.tokenService.findBySymbol('USDT');
    const wmToken = await this.tokenService.findBySymbol('WM');
    const now = moment().toDate();

    try {
      // 10. Tạo giao dịch trả lại tiền (USDT và phí gas WM)
      if (isUSDT) {

        const orderTraderWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: order.traderId } });
        const transactionUSDT = queryRunner.manager.create(Transaction, {
          userId: order.traderId,
          walletId: orderTraderWallet.id,
          type: TransactionType.NFT_PURCHASE,
          status: TransactionStatus.COMPLETED,
          amount: Number(order.nftPrice),
          tokenId: usdtToken.id,
          reference: order.id,
          note: `Refund USDT for buy NFT ${order.nftType} in session ${sessionId} (+${order.nftPrice}USDT)`,
          transactionAt: now,
          createdAt: now,
          updatedAt: now,
        });

        const savedTransactionUSDT = await queryRunner.manager.save(Transaction, transactionUSDT);

        // 11. Cập nhật số dư WM và USDT

        await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, orderTraderWallet, usdtToken, 'usdtBalance', savedTransactionUSDT, order.nftPrice, 'add', now);
      }

      if (isWM) {
        const orderTraderWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: order.traderId } });
        const transactionWM = queryRunner.manager.create(Transaction, {
          userId: order.traderId,
          walletId: orderTraderWallet.id,
          type: TransactionType.NFT_PURCHASE,
          status: TransactionStatus.COMPLETED,
          amount: Number(order.gasFee),
          tokenId: wmToken.id,
          reference: order.id,
          note: `Refund gas fee for buy NFT ${order.nftType} in session ${sessionId} (+${order.gasFee}WM)`,
          transactionAt: now,
          createdAt: now,
          updatedAt: now,
        });

        const savedTransactionWM = await queryRunner.manager.save(Transaction, transactionWM);

        await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, orderTraderWallet, wmToken, 'wmBalance', savedTransactionWM, order.gasFee, 'add', now);

      }

      await queryRunner.commitTransaction();
      await queryRunner.release();

      this.logger.log(`[TradingSessionService] Đã hoàn trả đơn hàng ${orderId}`);
      return true;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`[TradingSessionService] Có lỗi xảy ra khi hoàn trả đơn hàng ${orderId}: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Some thing went wrong, please try again later`);
    }
  }
}