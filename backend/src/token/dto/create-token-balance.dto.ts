import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsOptional, IsUUID } from 'class-validator';

export class CreateTokenBalanceDto {
  @ApiProperty({ description: 'User ID' })
  @IsUUID()
  userId: string;

  @ApiProperty({ description: 'Token ID' })
  @IsUUID()
  tokenId: string;

  @ApiProperty({ description: 'Available balance', required: false })
  @IsNumber()
  @IsOptional()
  availableBalance?: number;

  @ApiProperty({ description: 'Locked balance', required: false })
  @IsNumber()
  @IsOptional()
  lockedBalance?: number;
} 