import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsNumber, IsOptional, IsBoolean, IsUrl } from 'class-validator';
import { TokenType } from '../entities/token.entity';

export class CreateTokenDto {
  @ApiProperty({ description: 'Token symbol (e.g., WM, USDT)' })
  @IsString()
  symbol: string;

  @ApiProperty({ description: 'Token name (e.g., WorldMall Token, Tether USD)' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Token type', enum: TokenType })
  @IsEnum(TokenType)
  type: TokenType;

  @ApiProperty({ description: 'Total supply of the token', required: false })
  @IsNumber()
  @IsOptional()
  totalSupply?: number;

  @ApiProperty({ description: 'Circulating supply of the token', required: false })
  @IsNumber()
  @IsOptional()
  circulatingSupply?: number;

  @ApiProperty({ description: 'Token contract address (for blockchain tokens)', required: false })
  @IsString()
  @IsOptional()
  contractAddress?: string;

  @ApiProperty({ description: 'Token decimals', required: false })
  @IsNumber()
  @IsOptional()
  decimals?: number;

  @ApiProperty({ description: 'Whether the token is active', required: false })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({ description: 'Token logo URL', required: false })
  @IsUrl()
  @IsOptional()
  logoUrl?: string;

  @ApiProperty({ description: 'Token description', required: false })
  @IsString()
  @IsOptional()
  description?: string;
} 