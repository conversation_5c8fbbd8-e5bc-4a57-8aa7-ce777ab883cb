import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, QueryRunner } from 'typeorm';
import { Token, TokenType } from './entities/token.entity';
import { TokenBalance } from './entities/token-balance.entity';
import { CreateTokenDto } from './dto/create-token.dto';
import { CreateTokenBalanceDto } from './dto/create-token-balance.dto';
import { ConfigService } from '@nestjs/config';
import { BigNumber } from 'bignumber.js';
@Injectable()
export class TokenService {
  constructor(
    @InjectRepository(Token)
    private readonly tokenRepository: Repository<Token>,
    @InjectRepository(TokenBalance)
    private readonly tokenBalanceRepository: Repository<TokenBalance>,
    private readonly configService: ConfigService,
  ) {
    this.initializeDefaultTokens();
  }

  private async initializeDefaultTokens() {
    const usdtAddress = this.configService.get('USDT_CONTRACT_ADDRESS');
    const defaultTokens = [
      {
        symbol: 'WM',
        name: 'WorldMall Token',
        type: TokenType.NATIVE,
        totalSupply: 100000000,
        circulatingSupply: 0,
        decimals: 8,
        description: 'Native token of the WorldMall platform',
      },
      {
        symbol: 'USDT',
        name: 'Tether USD',
        type: TokenType.STABLECOIN,
        contractAddress: usdtAddress,
        decimals: 6,
        description: 'Stablecoin pegged to the US Dollar',
      },
    ];

    for (const tokenData of defaultTokens) {
      const existingToken = await this.findBySymbol(tokenData.symbol).catch(() => null);
      if (!existingToken) {
        await this.create(tokenData as CreateTokenDto);
      }
    }
  }

  async create(createTokenDto: CreateTokenDto): Promise<Token> {
    const existingToken = await this.tokenRepository.findOne({
      where: { symbol: createTokenDto.symbol },
    });

    if (existingToken) {
      throw new BadRequestException(`Token with symbol ${createTokenDto.symbol} already exists`);
    }

    const token = this.tokenRepository.create(createTokenDto);
    return this.tokenRepository.save(token);
  }

  async findAll(filters: { isActive?: boolean; type?: TokenType } = {}): Promise<Token[]> {
    const where: FindOptionsWhere<Token> = {};
    
    if (filters.isActive !== undefined) {
      where.isActive = filters.isActive;
    }
    
    if (filters.type) {
      where.type = filters.type;
    }
    
    return this.tokenRepository.find({ where });
  }

  async findOne(id: string): Promise<Token> {
    const token = await this.tokenRepository.findOne({ where: { id } });
    
    if (!token) {
      throw new NotFoundException(`Token with ID ${id} not found`);
    }
    
    return token;
  }

  async findBySymbol(symbol: string): Promise<Token> {
    const token = await this.tokenRepository.findOne({ where: { symbol } });
    
    if (!token) {
      throw new NotFoundException(`Token with symbol ${symbol} not found`);
    }
    
    return token;
  }

  async findByAddress(address: string): Promise<Token> {
    const token = await this.tokenRepository.findOne({ where: { contractAddress: address } });
    
    if (!token) {
      throw new NotFoundException(`Token with address ${address} not found`);
    }
    
    return token;
  }

  async findTokenBalanceByUserIdAndTokenId(userId: string, tokenId: string): Promise<TokenBalance> {
    return this.tokenBalanceRepository.findOne({ where: { userId, tokenId } });
  }

  async update(id: string, updateTokenDto: Partial<Token>): Promise<Token> {
    const token = await this.findOne(id);
    
    // Prevent changing symbol if it's already in use
    if (updateTokenDto.symbol && updateTokenDto.symbol !== token.symbol) {
      const existingToken = await this.tokenRepository.findOne({
        where: { symbol: updateTokenDto.symbol },
      });
      
      if (existingToken) {
        throw new BadRequestException(`Token with symbol ${updateTokenDto.symbol} already exists`);
      }
    }
    
    Object.assign(token, updateTokenDto);
    return this.tokenRepository.save(token);
  }

  async remove(id: string): Promise<void> {
    const token = await this.findOne(id);
    
    // Check if token has balances
    const balances = await this.tokenBalanceRepository.find({ where: { tokenId: id } });
    
    if (balances.length > 0) {
      throw new BadRequestException(`Cannot delete token with existing balances`);
    }
    
    await this.tokenRepository.remove(token);
  }

  // Token Balance Methods
  async createTokenBalance(createTokenBalanceDto: CreateTokenBalanceDto): Promise<TokenBalance> {
    // Check if token exists
    await this.findOne(createTokenBalanceDto.tokenId);
    
    // Check if balance already exists
    const existingBalance = await this.tokenBalanceRepository.findOne({
      where: {
        userId: createTokenBalanceDto.userId,
        tokenId: createTokenBalanceDto.tokenId,
      },
    });
    
    if (existingBalance) {
      throw new BadRequestException(`Token balance already exists for this user and token`);
    }
    
    const availableBalance = createTokenBalanceDto.availableBalance || 0;
    const lockedBalance = createTokenBalanceDto.lockedBalance || 0;
    
    const tokenBalance = this.tokenBalanceRepository.create({
      ...createTokenBalanceDto,
      availableBalance,
      lockedBalance,
      totalBalance: availableBalance + lockedBalance,
    });
    
    return this.tokenBalanceRepository.save(tokenBalance);
  }

  async findTokenByUserIdAndId(userId: string, tokenId: string): Promise<TokenBalance> {
    const tokenBalance = await this.tokenBalanceRepository.findOne({
      where: { userId, tokenId },
      relations: ['token'],
    });
    
    if (!tokenBalance) {
      throw new NotFoundException(`Token balance not found for user ${userId} and token ${tokenId}`);
    }
    
    return tokenBalance;
  }

  async findTokenByUserIdAndSymbol(userId: string, symbol: string): Promise<TokenBalance> {
    const token = await this.findBySymbol(symbol);
    return this.findTokenByUserIdAndId(userId, token.id);
  }

  async getUserBalances(userId: string): Promise<TokenBalance[]> {
    return this.tokenBalanceRepository.find({
      where: { userId },
      relations: ['token'],
    });
  }

  async updateTokenBalance(
    userId: string,
    tokenId: string,
    amount: number,
    operation: 'add' | 'subtract',
    balanceType: 'available' | 'locked' = 'available'
  ): Promise<TokenBalance> {
    let tokenBalance: TokenBalance;
    
    try {
      tokenBalance = await this.findTokenByUserIdAndId(userId, tokenId);
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Create new balance if it doesn't exist
        tokenBalance = await this.createTokenBalance({
          userId,
          tokenId,
          availableBalance: 0,
          lockedBalance: 0,
        });
      } else {
        throw error;
      }
    }
    
    if (operation === 'subtract') {
      if (balanceType === 'available' && new BigNumber(tokenBalance.availableBalance).lt(BigNumber(amount))) {
        throw new BadRequestException('Insufficient available balance');
      }
      
      if (balanceType === 'locked' && new BigNumber(tokenBalance.lockedBalance).lt(BigNumber(amount))) {
        throw new BadRequestException('Insufficient locked balance');
      }
    }
    
    if (balanceType === 'available') {
      tokenBalance.availableBalance = operation === 'add'
        ? new BigNumber(tokenBalance.availableBalance).plus(BigNumber(amount)).toNumber()
        : new BigNumber(tokenBalance.availableBalance).minus(BigNumber(amount)).toNumber();
    } else {
      tokenBalance.lockedBalance = operation === 'add'
        ? new BigNumber(tokenBalance.lockedBalance).plus(BigNumber(amount)).toNumber()
        : new BigNumber(tokenBalance.lockedBalance).minus(BigNumber(amount)).toNumber();
    }
    
    tokenBalance.totalBalance = new BigNumber(tokenBalance.availableBalance).plus(new BigNumber(tokenBalance.lockedBalance)).toNumber();
    
    return this.tokenBalanceRepository.save(tokenBalance);
  }

  async updateTokenBalanceWithQueryRunner(
    queryRunner: QueryRunner,
    userId: string,
    tokenId: string,
    amount: number,
    operation: 'add' | 'subtract',
    balanceType: 'available' | 'locked' = 'available'
  ): Promise<TokenBalance> {
    let tokenBalance: TokenBalance;
    
    tokenBalance = await queryRunner.manager.findOne(TokenBalance, {
      where: { userId, tokenId },
    });
    
    if (operation === 'subtract') {
      if (balanceType === 'available' && new BigNumber(tokenBalance.availableBalance).lt(BigNumber(amount))) {
        throw new BadRequestException('Insufficient available balance');
      }
      
      if (balanceType === 'locked' && new BigNumber(tokenBalance.lockedBalance).lt(BigNumber(amount))) {
        throw new BadRequestException('Insufficient locked balance');
      }
    }
    
    if (balanceType === 'available') {
      tokenBalance.availableBalance = operation === 'add'
        ? new BigNumber(tokenBalance.availableBalance).plus(BigNumber(amount)).toNumber()
        : new BigNumber(tokenBalance.availableBalance).minus(BigNumber(amount)).toNumber();
    } else {
      tokenBalance.lockedBalance = operation === 'add'
        ? new BigNumber(tokenBalance.lockedBalance).plus(BigNumber(amount)).toNumber()
        : new BigNumber(tokenBalance.lockedBalance).minus(BigNumber(amount)).toNumber();
    }
    
    tokenBalance.totalBalance = new BigNumber(tokenBalance.availableBalance).plus(new BigNumber(tokenBalance.lockedBalance)).toNumber();
    
    return queryRunner.manager.save(TokenBalance, tokenBalance);
  }
} 