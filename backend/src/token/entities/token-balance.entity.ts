import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Unique } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Token } from './token.entity';
import { User } from '../../users/entities/user.entity';

@Entity('token_balances')
@Unique(['userId', 'tokenId'])
export class TokenBalance {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'Token balance ID' })
  id: string;

  @Column()
  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  @ApiProperty({ description: 'Token ID' })
  tokenId: string;

  @ManyToOne(() => Token)
  @JoinColumn({ name: 'tokenId' })
  token: Token;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Available balance' })
  availableBalance: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Locked balance (e.g., in orders, staking)' })
  lockedBalance: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Total balance (available + locked)' })
  totalBalance: number;

  @CreateDateColumn()
  @ApiProperty({ description: 'Balance creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Balance last update timestamp' })
  updatedAt: Date;
} 