import { Entity, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

export enum TokenType {
  NATIVE = 'NATIVE',  // Native token của platform (WM)
  STABLECOIN = 'STABLECOIN',  // Stablecoin (USDT, USDC...)
  UTILITY = 'UTILITY',  // Utility token
  GOVERNANCE = 'GOVERNANCE',  // Governance token
  REWARD = 'REWARD'  // Reward token
}

@Entity('tokens')
export class Token {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'Token ID' })
  id: string;

  @Column({ unique: true })
  @ApiProperty({ description: 'Token symbol (e.g., WM, USDT)' })
  symbol: string;

  @Column()
  @ApiProperty({ description: 'Token name (e.g., WorldMall Token, Tether USD)' })
  name: string;

  @Column({ type: 'enum', enum: TokenType })
  @ApiProperty({ description: 'Token type', enum: TokenType })
  type: TokenType;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Total supply of the token' })
  totalSupply: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  @ApiProperty({ description: 'Circulating supply of the token' })
  circulatingSupply: number;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Token contract address (for blockchain tokens)', required: false })
  contractAddress?: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Token decimals', required: false })
  decimals?: number;

  @Column({ default: true })
  @ApiProperty({ description: 'Whether the token is active' })
  isActive: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Token logo URL', required: false })
  logoUrl?: string;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({ description: 'Token description', required: false })
  description?: string;

  @CreateDateColumn()
  @ApiProperty({ description: 'Token creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Token last update timestamp' })
  updatedAt: Date;
} 