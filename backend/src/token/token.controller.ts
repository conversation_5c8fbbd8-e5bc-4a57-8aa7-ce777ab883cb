import { Controller, Get, Post, Body, Param, Put, Delete, Query, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { TokenService } from './token.service';
import { Token, TokenType } from './entities/token.entity';
import { TokenBalance } from './entities/token-balance.entity';
import { CreateTokenDto } from './dto/create-token.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiResponseDto } from '../common/dto/api-response.dto';

@ApiTags('tokens')
@Controller('tokens')
export class TokenController {
  constructor(private readonly tokenService: TokenService) {}

  // Admin endpoints
  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new token (Admin only)' })
  @ApiResponse({ status: 201, description: 'Token created successfully', type: ApiResponseDto })
  async create(@Body() createTokenDto: CreateTokenDto): Promise<ApiResponseDto<Token>> {
    const token = await this.tokenService.create(createTokenDto);
    return ApiResponseDto.success(token, 'Token created successfully');
  }

  @Get()
  @ApiOperation({ summary: 'Get all tokens' })
  @ApiResponse({ status: 200, description: 'Return all tokens', type: ApiResponseDto })
  async findAll(
    @Query('isActive') isActive?: boolean,
    @Query('type') type?: TokenType
  ): Promise<ApiResponseDto<Token[]>> {
    const tokens = await this.tokenService.findAll({ isActive, type });
    return ApiResponseDto.success(tokens, 'Tokens retrieved successfully');
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get token by ID' })
  @ApiResponse({ status: 200, description: 'Return token by ID', type: ApiResponseDto })
  async findOne(@Param('id') id: string): Promise<ApiResponseDto<Token>> {
    const token = await this.tokenService.findOne(id);
    return ApiResponseDto.success(token, 'Token retrieved successfully');
  }

  @Get('symbol/:symbol')
  @ApiOperation({ summary: 'Get token by symbol' })
  @ApiResponse({ status: 200, description: 'Return token by symbol', type: ApiResponseDto })
  async findBySymbol(@Param('symbol') symbol: string): Promise<ApiResponseDto<Token>> {
    const token = await this.tokenService.findBySymbol(symbol);
    return ApiResponseDto.success(token, 'Token retrieved successfully');
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update token by ID (Admin only)' })
  @ApiResponse({ status: 200, description: 'Token updated successfully', type: ApiResponseDto })
  async update(
    @Param('id') id: string,
    @Body() updateTokenDto: Partial<Token>
  ): Promise<ApiResponseDto<Token>> {
    const token = await this.tokenService.update(id, updateTokenDto);
    return ApiResponseDto.success(token, 'Token updated successfully');
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete token by ID (Admin only)' })
  @ApiResponse({ status: 200, description: 'Token deleted successfully', type: ApiResponseDto })
  async remove(@Param('id') id: string): Promise<ApiResponseDto<null>> {
    await this.tokenService.remove(id);
    return ApiResponseDto.success(null, 'Token deleted successfully');
  }

  // User balance endpoints
  @Get('balances/me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all token balances for the authenticated user' })
  @ApiResponse({ status: 200, description: 'Return user token balances', type: ApiResponseDto })
  async getUserBalances(@Request() req): Promise<ApiResponseDto<TokenBalance[]>> {
    const userId = req.user.id;
    const balances = await this.tokenService.getUserBalances(userId);
    return ApiResponseDto.success(balances, 'Token balances retrieved successfully');
  }

  @Get('balances/me/:symbol')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get token balance by symbol for the authenticated user' })
  @ApiResponse({ status: 200, description: 'Return user token balance', type: ApiResponseDto })
  async getUserBalanceBySymbol(
    @Request() req,
    @Param('symbol') symbol: string
  ): Promise<ApiResponseDto<TokenBalance>> {
    const userId = req.user.id;
    const balance = await this.tokenService.findTokenByUserIdAndSymbol(userId, symbol);
    return ApiResponseDto.success(balance, 'Token balance retrieved successfully');
  }
} 