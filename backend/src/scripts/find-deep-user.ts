import { createConnection } from 'typeorm';
import { User } from '../users/entities/user.entity';

async function findDeepUser() {
  // Kết nối đến cơ sở dữ liệu
  const connection = await createConnection({
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: 'root',
    password: 'password',
    database: 'worldmall',
    entities: [User],
    synchronize: false
  });

  try {
    // Tìm người dùng có đường dẫn dài nhất (nhiều dấu / nhất)
    const users = await connection
      .getRepository(User)
      .createQueryBuilder('user')
      .select(['user.id', 'user.username', 'user.wallet', 'user.path', 'user.referredBy'])
      .orderBy('LENGTH(user.path)', 'DESC')
      .take(10)
      .getMany();

    // Tính độ sâu của mỗi người dùng
    const usersWithDepth = users.map(user => {
      const depth = user.path ? (user.path.match(/\//g) || []).length : 0;
      return {
        id: user.id,
        username: user.username,
        wallet: user.wallet,
        path: user.path,
        depth: depth
      };
    });

    console.log('Users with deepest paths:');
    console.table(usersWithDepth);

    // Tìm người dùng ở tầng thứ 21 (có 20 người giới thiệu phía trên)
    const level21Users = usersWithDepth.filter(user => user.depth === 20);
    
    if (level21Users.length > 0) {
      console.log('\nUsers at level 21:');
      console.table(level21Users);
    } else {
      console.log('\nNo users found at level 21. Here are the deepest users:');
      
      // Sắp xếp theo độ sâu giảm dần
      usersWithDepth.sort((a, b) => b.depth - a.depth);
      console.table(usersWithDepth.slice(0, 3));
    }
  } catch (error) {
    console.error('Error finding deep users:', error);
  } finally {
    // Đóng kết nối
    await connection.close();
  }
}

findDeepUser().catch(error => console.error('Script error:', error));
