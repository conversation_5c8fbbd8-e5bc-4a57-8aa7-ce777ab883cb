import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddWmBonusColumnsToStakingTransactions1714022500000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Kiểm tra xem bảng staking_transactions có tồn tại không
    const tableExists = await queryRunner.hasTable('staking_transactions');
    if (!tableExists) {
      return;
    }

    // Kiểm tra xem cột wmBonusAmount đã tồn tại chưa
    const wmBonusAmountColumns = await queryRunner.query(
      `SHOW COLUMNS FROM staking_transactions LIKE 'wm_bonus_amount'`
    );

    if (wmBonusAmountColumns.length === 0) {
      // Thêm cột wmBonusAmount nếu chưa tồn tại
      await queryRunner.query(
        `ALTER TABLE staking_transactions ADD COLUMN wm_bonus_amount DECIMAL(18, 8) NOT NULL DEFAULT 0`
      );
    }

    // Kiểm tra xem cột wmUsdRate đã tồn tại chưa
    const wmUsdRateColumns = await queryRunner.query(
      `SHOW COLUMNS FROM staking_transactions LIKE 'wm_usd_rate'`
    );

    if (wmUsdRateColumns.length === 0) {
      // Thêm cột wmUsdRate nếu chưa tồn tại
      await queryRunner.query(
        `ALTER TABLE staking_transactions ADD COLUMN wm_usd_rate DECIMAL(18, 8) NOT NULL DEFAULT 0`
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Kiểm tra xem bảng staking_transactions có tồn tại không
    const tableExists = await queryRunner.hasTable('staking_transactions');
    if (!tableExists) {
      return;
    }

    // Kiểm tra xem cột wmBonusAmount có tồn tại không
    const wmBonusAmountColumns = await queryRunner.query(
      `SHOW COLUMNS FROM staking_transactions LIKE 'wm_bonus_amount'`
    );

    if (wmBonusAmountColumns.length > 0) {
      // Xóa cột wmBonusAmount nếu tồn tại
      await queryRunner.query(
        `ALTER TABLE staking_transactions DROP COLUMN wm_bonus_amount`
      );
    }

    // Kiểm tra xem cột wmUsdRate có tồn tại không
    const wmUsdRateColumns = await queryRunner.query(
      `SHOW COLUMNS FROM staking_transactions LIKE 'wm_usd_rate'`
    );

    if (wmUsdRateColumns.length > 0) {
      // Xóa cột wmUsdRate nếu tồn tại
      await queryRunner.query(
        `ALTER TABLE staking_transactions DROP COLUMN wm_usd_rate`
      );
    }
  }
}
