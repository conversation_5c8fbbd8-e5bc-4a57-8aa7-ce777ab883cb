import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStakingFieldsToUsers1713532800000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm các trường thống kê staking vào bảng users
    await queryRunner.query(`
      ALTER TABLE users 
      ADD COLUMN staking_active BOOLEAN DEFAULT false,
      ADD COLUMN staking_direct_active_count INT DEFAULT 0,
      ADD COLUMN staking_rewarded_milestone INT DEFAULT 0,
      ADD COLUMN staking_total_volume DECIMAL(18, 8) DEFAULT 0,
      ADD COLUMN staking_left_volume DECIMAL(18, 8) DEFAULT 0,
      ADD COLUMN staking_right_volume DECIMAL(18, 8) DEFAULT 0,
      ADD COLUMN staking_total_commission DECIMAL(18, 8) DEFAULT 0,
      ADD COLUMN staking_direct_commission DECIMAL(18, 8) DEFAULT 0,
      ADD COLUMN staking_matching_commission DECIMAL(18, 8) DEFAULT 0,
      ADD COLUMN staking_rank_commission DECIMAL(18, 8) DEFAULT 0,
      ADD COLUMN staking_is_shareholder BOOLEAN DEFAULT false
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa các trường thống kê staking khỏi bảng users
    await queryRunner.query(`
      ALTER TABLE users 
      DROP COLUMN staking_active,
      DROP COLUMN staking_direct_active_count,
      DROP COLUMN staking_rewarded_milestone,
      DROP COLUMN staking_total_volume,
      DROP COLUMN staking_left_volume,
      DROP COLUMN staking_right_volume,
      DROP COLUMN staking_total_commission,
      DROP COLUMN staking_direct_commission,
      DROP COLUMN staking_matching_commission,
      DROP COLUMN staking_rank_commission,
      DROP COLUMN staking_is_shareholder
    `);
  }
}
