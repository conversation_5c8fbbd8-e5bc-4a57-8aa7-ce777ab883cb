import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameTestDayCountToElapsedDays1714022400000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Kiểm tra xem cột test_day_count có tồn tại không
    const tableExists = await queryRunner.hasTable('staking_transactions');
    if (!tableExists) {
      return;
    }

    const columns = await queryRunner.query(
      `SHOW COLUMNS FROM staking_transactions LIKE 'test_day_count'`
    );

    if (columns.length > 0) {
      // Đ<PERSON>i tên cột từ test_day_count thành elapsed_days
      await queryRunner.query(
        `ALTER TABLE staking_transactions CHANGE COLUMN test_day_count elapsed_days INT NOT NULL DEFAULT 0`
      );
    } else {
      // Nếu cột test_day_count không tồn tại, kiểm tra xem cột elapsed_days đã tồn tại chưa
      const elapsedDaysColumns = await queryRunner.query(
        `SHOW COLUMNS FROM staking_transactions LIKE 'elapsed_days'`
      );

      if (elapsedDaysColumns.length === 0) {
        // Nếu cột elapsed_days chưa tồn tại, tạo mới
        await queryRunner.query(
          `ALTER TABLE staking_transactions ADD COLUMN elapsed_days INT NOT NULL DEFAULT 0`
        );
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Kiểm tra xem cột elapsed_days có tồn tại không
    const tableExists = await queryRunner.hasTable('staking_transactions');
    if (!tableExists) {
      return;
    }

    const columns = await queryRunner.query(
      `SHOW COLUMNS FROM staking_transactions LIKE 'elapsed_days'`
    );

    if (columns.length > 0) {
      // Đổi tên cột từ elapsed_days thành test_day_count
      await queryRunner.query(
        `ALTER TABLE staking_transactions CHANGE COLUMN elapsed_days test_day_count INT NOT NULL DEFAULT 0`
      );
    }
  }
}
