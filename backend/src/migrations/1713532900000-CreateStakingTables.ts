import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateStakingTables1713532900000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Tạo bảng staking_transactions
    await queryRunner.query(`
      CREATE TABLE staking_transactions (
        id VARCHAR(36) PRIMARY KEY,
        userId VARCHAR(36) NOT NULL,
        packageCode VARCHAR(50) NOT NULL,
        amount DECIMAL(18, 8) NOT NULL,
        durationDays INT NOT NULL,
        interestRate DECIMAL(5, 2) NOT NULL,
        bonusRate DECIMAL(5, 2) NOT NULL,
        startDate TIMESTAMP NOT NULL,
        endDate TIMESTAMP NOT NULL,
        status ENUM('PENDING', 'ACTIVE', 'COMPLETED', 'CANCELLED') NOT NULL DEFAULT 'PENDING',
        transactionId VARCHAR(36),
        completionTransactionId VARCHAR(36),
        totalInterest DECIMAL(18, 8) DEFAULT 0,
        totalBonus DECIMAL(18, 8) DEFAULT 0,
        lastInterestDate TIMESTAMP,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users(id),
        FOREIGN KEY (transactionId) REFERENCES transactions(id),
        FOREIGN KEY (completionTransactionId) REFERENCES transactions(id)
      )
    `);

    // Tạo bảng staking_interest_history
    await queryRunner.query(`
      CREATE TABLE staking_interest_history (
        id VARCHAR(36) PRIMARY KEY,
        stakingId VARCHAR(36) NOT NULL,
        userId VARCHAR(36) NOT NULL,
        amount DECIMAL(18, 8) NOT NULL,
        transactionId VARCHAR(36),
        calculationDate TIMESTAMP NOT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (stakingId) REFERENCES staking_transactions(id),
        FOREIGN KEY (userId) REFERENCES users(id),
        FOREIGN KEY (transactionId) REFERENCES transactions(id)
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa bảng staking_interest_history
    await queryRunner.query(`DROP TABLE staking_interest_history`);
    
    // Xóa bảng staking_transactions
    await queryRunner.query(`DROP TABLE staking_transactions`);
  }
}
