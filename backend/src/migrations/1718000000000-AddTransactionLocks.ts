import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class AddTransactionLocks1718000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'transaction_locks',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'uuid',
          },
          {
            name: 'transactionId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'processId',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Tạo index cho transactionId để tìm kiếm nhanh
    await queryRunner.createIndex(
      'transaction_locks',
      new TableIndex({
        name: 'IDX_TRANSACTION_LOCKS_TRANSACTION_ID',
        columnNames: ['transactionId'],
      }),
    );

    // Tạo index cho isActive để tìm kiếm nhanh các khóa đang hoạt động
    await queryRunner.createIndex(
      'transaction_locks',
      new TableIndex({
        name: 'IDX_TRANSACTION_LOCKS_IS_ACTIVE',
        columnNames: ['isActive'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('transaction_locks', 'IDX_TRANSACTION_LOCKS_IS_ACTIVE');
    await queryRunner.dropIndex('transaction_locks', 'IDX_TRANSACTION_LOCKS_TRANSACTION_ID');
    await queryRunner.dropTable('transaction_locks');
  }
}
