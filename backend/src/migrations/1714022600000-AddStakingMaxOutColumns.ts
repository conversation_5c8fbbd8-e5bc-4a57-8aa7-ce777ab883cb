import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStakingMaxOutColumns1714022600000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the users table exists
    const tableExists = await queryRunner.hasTable('users');
    if (!tableExists) {
      return;
    }

    // Add stakingTotalInvestment column if it doesn't exist
    const stakingTotalInvestmentExists = await queryRunner.query(
      `SHOW COLUMNS FROM users LIKE 'staking_total_investment'`
    );
    if (stakingTotalInvestmentExists.length === 0) {
      await queryRunner.query(
        `ALTER TABLE users ADD COLUMN staking_total_investment DECIMAL(18, 8) NOT NULL DEFAULT 0`
      );
    }

    // Add stakingTotalEarnings column if it doesn't exist
    const stakingTotalEarningsExists = await queryRunner.query(
      `SHOW COLUMNS FROM users LIKE 'staking_total_earnings'`
    );
    if (stakingTotalEarningsExists.length === 0) {
      await queryRunner.query(
        `ALTER TABLE users ADD COLUMN staking_total_earnings DECIMAL(18, 8) NOT NULL DEFAULT 0`
      );
    }

    // Add stakingMaxedOut column if it doesn't exist
    const stakingMaxedOutExists = await queryRunner.query(
      `SHOW COLUMNS FROM users LIKE 'staking_maxed_out'`
    );
    if (stakingMaxedOutExists.length === 0) {
      await queryRunner.query(
        `ALTER TABLE users ADD COLUMN staking_maxed_out TINYINT NOT NULL DEFAULT 0`
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Check if the users table exists
    const tableExists = await queryRunner.hasTable('users');
    if (!tableExists) {
      return;
    }

    // Remove stakingTotalInvestment column if it exists
    const stakingTotalInvestmentExists = await queryRunner.query(
      `SHOW COLUMNS FROM users LIKE 'staking_total_investment'`
    );
    if (stakingTotalInvestmentExists.length > 0) {
      await queryRunner.query(
        `ALTER TABLE users DROP COLUMN staking_total_investment`
      );
    }

    // Remove stakingTotalEarnings column if it exists
    const stakingTotalEarningsExists = await queryRunner.query(
      `SHOW COLUMNS FROM users LIKE 'staking_total_earnings'`
    );
    if (stakingTotalEarningsExists.length > 0) {
      await queryRunner.query(
        `ALTER TABLE users DROP COLUMN staking_total_earnings`
      );
    }

    // Remove stakingMaxedOut column if it exists
    const stakingMaxedOutExists = await queryRunner.query(
      `SHOW COLUMNS FROM users LIKE 'staking_maxed_out'`
    );
    if (stakingMaxedOutExists.length > 0) {
      await queryRunner.query(
        `ALTER TABLE users DROP COLUMN staking_maxed_out`
      );
    }
  }
}
