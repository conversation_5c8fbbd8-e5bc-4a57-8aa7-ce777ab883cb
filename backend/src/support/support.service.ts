import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Ticket, TicketStatus } from './entities/ticket.entity';
import { CreateTicketDto } from './dto/create-ticket.dto';
import { UpdateTicketDto } from './dto/update-ticket.dto';
import { UsersService } from '../users/users.service';
import { TicketResponseDto } from './dto/ticket-response.dto';
import { SupportTelegramBotService } from '../common/services/support-telegram-bot.service';

@Injectable()
export class SupportService {
  private readonly logger = new Logger(SupportService.name);

  constructor(
    @InjectRepository(Ticket)
    private readonly ticketRepository: Repository<Ticket>,
    private readonly usersService: UsersService,
    private readonly supportTelegramBotService: SupportTelegramBotService,
  ) {}

  /**
   * Create a new support ticket
   * @param userId User ID
   * @param createTicketDto Ticket data
   * @returns Created ticket
   */
  async createTicket(userId: string, createTicketDto: CreateTicketDto): Promise<TicketResponseDto> {
    this.logger.log(`Creating ticket for user ${userId}`);

    // Get user to verify existence
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Generate random ticket code (similar to referralCode)
    let ticketCode: string;
    let isUnique = false;

    // Keep generating until we find a unique code
    while (!isUnique) {
      const randomCode = Math.random().toString(36).substring(2, 8).toUpperCase();
      ticketCode = `TIK#${randomCode}`;

      // Check if code already exists
      const existingTicket = await this.ticketRepository.findOne({ where: { ticketCode } });
      if (!existingTicket) {
        isUnique = true;
      }
    }

    // Create new ticket
    const ticket = this.ticketRepository.create({
      userId,
      ticketCode,
      content: createTicketDto.content,
      txHash: createTicketDto.txHash,
      walletAddress: createTicketDto.walletAddress,
      status: TicketStatus.PENDING,
    });

    // Save ticket
    const savedTicket = await this.ticketRepository.save(ticket);

    // Send Telegram notification (async, don't wait)
    this.sendTelegramNotification(savedTicket, user.referralCode)
      .catch(error => {
        this.logger.error(`Failed to send Telegram notification: ${error.message}`);
      });

    // Return response DTO
    return this.mapToResponseDto(savedTicket, user.referralCode);
  }

  /**
   * Get all tickets for a user
   * @param userId User ID
   * @returns List of tickets
   */
  async getUserTickets(userId: string): Promise<TicketResponseDto[]> {
    this.logger.log(`Getting tickets for user ${userId}`);

    // Get user to verify existence and get referral code
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Get tickets
    const tickets = await this.ticketRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });

    // Map to response DTOs
    return tickets.map(ticket => this.mapToResponseDto(ticket, user.referralCode));
  }

  /**
   * Get all tickets (admin only)
   * @returns List of all tickets
   */
  async getAllTickets(): Promise<TicketResponseDto[]> {
    this.logger.log('Getting all tickets');

    // Get all tickets with user relation
    const tickets = await this.ticketRepository.find({
      relations: ['user'],
      order: { createdAt: 'ASC' },
    });

    // Map to response DTOs
    return tickets.map(ticket => this.mapToResponseDto(
      ticket,
      ticket.user?.referralCode || 'N/A'
    ));
  }

  /**
   * Get a ticket by ID
   * @param id Ticket ID
   * @returns Ticket
   */
  async getTicketById(id: string): Promise<TicketResponseDto> {
    this.logger.log(`Getting ticket with ID ${id}`);

    // Get ticket with user relation
    const ticket = await this.ticketRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!ticket) {
      throw new NotFoundException(`Ticket with ID ${id} not found`);
    }

    // Map to response DTO
    return this.mapToResponseDto(
      ticket,
      ticket.user?.referralCode || 'N/A'
    );
  }

  /**
   * Update a ticket (admin only)
   * @param id Ticket ID
   * @param updateTicketDto Update data
   * @returns Updated ticket
   */
  async updateTicket(id: string, updateTicketDto: UpdateTicketDto): Promise<TicketResponseDto> {
    this.logger.log(`Updating ticket with ID ${id}`);

    // Get ticket with user relation
    const ticket = await this.ticketRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!ticket) {
      throw new NotFoundException(`Ticket with ID ${id} not found`);
    }

    // Update ticket
    if (updateTicketDto.status) {
      ticket.status = updateTicketDto.status;
    }

    if (updateTicketDto.response !== undefined) {
      ticket.response = updateTicketDto.response;
    }

    // Save updated ticket
    const updatedTicket = await this.ticketRepository.save(ticket);

    // Send Telegram notification about update (async, don't wait)
    this.sendTelegramNotification(updatedTicket, ticket.user?.referralCode || 'N/A')
      .catch(error => {
        this.logger.error(`Failed to send Telegram notification: ${error.message}`);
      });

    // Map to response DTO
    return this.mapToResponseDto(
      updatedTicket,
      ticket.user?.referralCode || 'N/A'
    );
  }

  /**
   * Send ticket notification to Telegram
   * @param ticket Ticket to notify about
   * @param userReferralCode User referral code
   */
  private async sendTelegramNotification(ticket: Ticket, userReferralCode: string): Promise<void> {
    try {
      // Format message with emoji and better formatting
      let message = `🎫 *TICKET SUPPORT* 🎫\n\n`;
      message += `📝 *Mã Ticket:* \`${ticket.ticketCode}\`\n`;
      message += `🔑 *ID:* \`${ticket.id}\`\n`;
      message += `🔄 *Trạng thái:* \`${this.getStatusText(ticket.status)}\`\n`;
      message += `👤 *User:* \`${userReferralCode}\`\n`;
      message += `💼 *Ví:* \`${ticket.walletAddress}\`\n`;

      if (ticket.txHash) {
        message += `🧾 *TX Hash:* \`${ticket.txHash}\`\n`;
      }

      message += `\n📋 *Nội dung:*\n${ticket.content}\n\n`;

      if (ticket.response) {
        message += `✅ *Phản hồi:*\n${ticket.response}\n\n`;
      }

      message += `⏰ *Thời gian:* ${new Date().toLocaleString('vi-VN')}`;

      // Send message using support telegram bot
      await this.supportTelegramBotService.sendSupportMessage(message);

      // Generate and send financial report
      try {
        const financialReport = await this.generateFinancialReport(ticket.userId);
        if (financialReport) {
          await this.supportTelegramBotService.sendSupportMessage(financialReport);
        }
      } catch (reportError) {
        this.logger.error(`Failed to generate financial report: ${reportError.message}`, reportError.stack);
        // Don't throw error, just log it
      }
    } catch (error) {
      this.logger.error(`Failed to send Telegram notification: ${error.message}`, error.stack);
      // Don't throw error, just log it
    }
  }

  /**
   * Generate financial report for a user
   * @param userId User ID
   * @returns Formatted financial report message for Telegram
   */
  private async generateFinancialReport(userId: string): Promise<string | null> {
    try {
      // Get financial report from users service
      const report = await this.usersService.getFinancialReport(userId);

      if (!report || !report.fullFinancialReport) {
        return null;
      }

      // Format the report for Telegram
      // Replace \n with actual newlines and format as code block
      const formattedReport = report.fullFinancialReport
        .replace(/\\n/g, '\n')
        .replace(/\*/g, '\\*')  // Escape Markdown characters
        .replace(/\_/g, '\\_')
        .replace(/\`/g, '\\`');

      let message = `📊 *BÁO CÁO TÀI CHÍNH* 📊\n\n`;
      message += `\`\`\`\n${formattedReport}\n\`\`\`\n\n`;
      //message += `💰 *Số dư hệ thống:* \`${report.systemBalance}\`\n`;
      //message += `⚖️ *Chênh lệch:* \`${report.discrepancy}\`\n`;

      return message;
    } catch (error) {
      this.logger.error(`Error generating financial report: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Get human-readable status text
   * @param status Ticket status
   * @returns Status text
   */
  private getStatusText(status: TicketStatus): string {
    switch (status) {
      case TicketStatus.PENDING:
        return '⏳ Chờ xử lý';
      case TicketStatus.PROCESSING:
        return '🔄 Đang xử lý';
      case TicketStatus.RESOLVED:
        return '✅ Đã xử lý';
      default:
        return status;
    }
  }

  /**
   * Map Ticket entity to TicketResponseDto
   * @param ticket Ticket entity
   * @param userReferralCode User referral code
   * @returns TicketResponseDto
   */
  private mapToResponseDto(ticket: Ticket, userReferralCode: string): TicketResponseDto {
    return {
      id: ticket.id,
      ticketCode: ticket.ticketCode,
      userId: ticket.userId,
      userReferralCode,
      content: ticket.content,
      txHash: ticket.txHash,
      status: ticket.status,
      response: ticket.response,
      walletAddress: ticket.walletAddress,
      createdAt: ticket.createdAt,
      updatedAt: ticket.updatedAt,
    };
  }
}
