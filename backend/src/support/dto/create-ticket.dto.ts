import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateTicketDto {
  @ApiProperty({ description: 'Ticket content' })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({ description: 'Transaction hash (if applicable)', required: false })
  @IsString()
  @IsOptional()
  txHash?: string;

  @ApiProperty({ description: 'BNB wallet address' })
  @IsString()
  @IsNotEmpty()
  walletAddress: string;
}
