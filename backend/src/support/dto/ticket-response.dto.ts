import { ApiProperty } from '@nestjs/swagger';
import { TicketStatus } from '../entities/ticket.entity';

export class TicketResponseDto {
  @ApiProperty({ description: 'Ticket ID' })
  id: string;

  @ApiProperty({ description: 'Ticket code (format: TIK#XXXXXX)' })
  ticketCode: string;

  @ApiProperty({ description: 'User ID who created the ticket' })
  userId: string;

  @ApiProperty({ description: 'User referral code' })
  userReferralCode: string;

  @ApiProperty({ description: 'Ticket content' })
  content: string;

  @ApiProperty({ description: 'Transaction hash (if applicable)', required: false })
  txHash?: string;

  @ApiProperty({ description: 'Ticket status', enum: TicketStatus })
  status: TicketStatus;

  @ApiProperty({ description: 'Admin response to the ticket', required: false })
  response?: string;

  @ApiProperty({ description: 'BNB wallet address' })
  walletAddress: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}
