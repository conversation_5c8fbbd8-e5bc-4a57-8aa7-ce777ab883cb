import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '../../common/entities/base.entity';
import { User } from '../../users/entities/user.entity';

export enum TicketStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  RESOLVED = 'resolved',
}

@Entity('tickets')
export class Ticket extends BaseEntity {
  @ApiProperty({ description: 'Ticket ID (auto-generated)' })
  id: string;

  @Column({ unique: true })
  @ApiProperty({ description: 'Ticket code (auto-generated, format: TIK#XXXXXX)' })
  ticketCode: string;

  @Column({ name: 'user_id' })
  @ApiProperty({ description: 'User ID who created the ticket' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  @ApiProperty({ description: 'User who created the ticket', type: () => User })
  user: User;

  @Column({ type: 'text' })
  @ApiProperty({ description: 'Ticket content' })
  content: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Transaction hash (if applicable)', required: false })
  txHash?: string;

  @Column({ type: 'enum', enum: TicketStatus, default: TicketStatus.PENDING })
  @ApiProperty({ description: 'Ticket status', enum: TicketStatus })
  status: TicketStatus;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({ description: 'Admin response to the ticket', required: false })
  response?: string;

  @Column({ name: 'wallet_address' })
  @ApiProperty({ description: 'BNB wallet address' })
  walletAddress: string;
}
