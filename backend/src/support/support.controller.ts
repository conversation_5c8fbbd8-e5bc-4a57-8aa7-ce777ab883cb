import { Controller, Get, Post, Body, Param, Patch, UseGuards, Request, NotFoundException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SupportService } from './support.service';
import { CreateTicketDto } from './dto/create-ticket.dto';
import { UpdateTicketDto } from './dto/update-ticket.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiResponseDto } from '../common/dto/api-response.dto';
import { TicketResponseDto } from './dto/ticket-response.dto';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../common/enums/user.enum';
import { RolesGuard } from '../auth/guards/roles.guard';

@ApiTags('support')
@Controller('support')
export class SupportController {
  constructor(private readonly supportService: SupportService) {}

  /**
   * Create a new support ticket
   * @param req Request object
   * @param createTicketDto Ticket data
   * @returns Created ticket
   */
  @Post('tickets')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new support ticket' })
  @ApiResponse({
    status: 201,
    description: 'Ticket created successfully',
    type: ApiResponseDto
  })
  async createTicket(
    @Request() req,
    @Body() createTicketDto: CreateTicketDto
  ): Promise<ApiResponseDto<TicketResponseDto>> {
    try {
      const userId = req.user.id;
      const ticket = await this.supportService.createTicket(userId, createTicketDto);
      return ApiResponseDto.success(ticket, 'Ticket created successfully', 201);
    } catch (error) {
      console.error('Error creating ticket:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create ticket');
    }
  }

  /**
   * Get all tickets for the current user
   * @param req Request object
   * @returns List of tickets
   */
  @Get('tickets')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all tickets for the current user' })
  @ApiResponse({
    status: 200,
    description: 'Tickets retrieved successfully',
    type: ApiResponseDto
  })
  async getUserTickets(@Request() req): Promise<ApiResponseDto<TicketResponseDto[]>> {
    try {
      const userId = req.user.id;
      const tickets = await this.supportService.getUserTickets(userId);
      return ApiResponseDto.success(tickets, 'Tickets retrieved successfully');
    } catch (error) {
      console.error('Error getting user tickets:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get tickets');
    }
  }

  /**
   * Get all tickets (admin only)
   * @returns List of all tickets
   */
  @Get('admin/tickets')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all tickets (admin only)' })
  @ApiResponse({
    status: 200,
    description: 'All tickets retrieved successfully',
    type: ApiResponseDto
  })
  async getAllTickets(): Promise<ApiResponseDto<TicketResponseDto[]>> {
    try {
      const tickets = await this.supportService.getAllTickets();
      return ApiResponseDto.success(tickets, 'All tickets retrieved successfully');
    } catch (error) {
      console.error('Error getting all tickets:', error);
      throw new InternalServerErrorException('Failed to get all tickets');
    }
  }

  /**
   * Get a ticket by ID
   * @param id Ticket ID
   * @returns Ticket
   */
  @Get('tickets/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a ticket by ID' })
  @ApiResponse({
    status: 200,
    description: 'Ticket retrieved successfully',
    type: ApiResponseDto
  })
  async getTicketById(@Param('id') id: string): Promise<ApiResponseDto<TicketResponseDto>> {
    try {
      const ticket = await this.supportService.getTicketById(id);
      return ApiResponseDto.success(ticket, 'Ticket retrieved successfully');
    } catch (error) {
      console.error('Error getting ticket:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get ticket');
    }
  }

  /**
   * Update a ticket (admin only)
   * @param id Ticket ID
   * @param updateTicketDto Update data
   * @returns Updated ticket
   */
  @Patch('admin/tickets/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a ticket (admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Ticket updated successfully',
    type: ApiResponseDto
  })
  async updateTicket(
    @Param('id') id: string,
    @Body() updateTicketDto: UpdateTicketDto
  ): Promise<ApiResponseDto<TicketResponseDto>> {
    try {
      const ticket = await this.supportService.updateTicket(id, updateTicketDto);
      return ApiResponseDto.success(ticket, 'Ticket updated successfully');
    } catch (error) {
      console.error('Error updating ticket:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update ticket');
    }
  }
}
