# compiled output
/dist
/node_modules

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Environment files
.env
.env.*
!.env.example

# Database
*.sqlite
*.sqlite3

# Temporary files
*.swp
*.swo
*~

# Build files
/build
/tmp
/out-tsc

# Dependencies
package-lock.json
yarn.lock
pnpm-lock.yaml

# Docker
docker-compose.override.yml

# Documentation
/documentation 