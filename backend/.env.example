# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=worldmall
DB_PASSWORD=worldmall_password
DB_DATABASE=worldmall_crypto
DB_SYNC=true

# JWT Configuration
JWT_SECRET=worldmall_jwt_secret_key_for_development
JWT_EXPIRES_IN=30d
JWT_COOKIE_EXPIRES_IN=30

# Web3 Configuration

# Testnet Configuration
WEB3_PROVIDER_URL=https://bsc-testnet.public.blastapi.io
USDT_CONTRACT_ADDRESS=****************************************** # BSC Testnet USDT
WM_CONTRACT_ADDRESS=****************************************** # BSC Testnet WM Token
BSC_CHAIN_ID=97

# Mainnet Configuration
#WEB3_PROVIDER_URL=https://bsc-dataseed.binance.org
#USDT_CONTRACT_ADDRESS=****************************************** # BSC Mainnet USDT
#WM_CONTRACT_ADDRESS=****************************************** # BSC Mainnet WM Token
#BSC_CHAIN_ID=56

# Telegram Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
SUPPORT_TELEGRAM_BOT_TOKEN=your_support_telegram_bot_token
TELEGRAM_SUPPORT_CHAT_ID=-1001234567890