version: '3.8'

services:
  # Nginx service
  nginx:
    image: nginx:alpine
    container_name: worldmall-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - /etc/letsencrypt:/etc/letsencrypt
      - ./nginx/logs:/var/log/nginx
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - worldmall_network

networks:
  worldmall_network:
    name: worldmall_network
    driver: bridge 