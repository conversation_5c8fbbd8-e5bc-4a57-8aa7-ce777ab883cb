version: '3.8'

services:
  # phpMyAdmin service
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: worldmall-phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      - PMA_HOST=mysql
      - PMA_PORT=3306
      - MYSQL_ROOT_PASSWORD=wOrldmAll.92VktL7JNs558.MySQLXroOtp
    volumes:
      - phpmyadmin_data:/var/lib/phpmyadmin
    networks:
      - worldmall_network

volumes:
  phpmyadmin_data:
    name: worldmall_phpmyadmin_data

networks:
  worldmall_network:
    name: worldmall_network
    driver: bridge 