version: '3.8'

services:
  # Backend service
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: worldmall_backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=5000
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN}
      - WEB3_PROVIDER_URL=${WEB3_PROVIDER_URL}
      - BSC_WS_URL=${BSC_WS_URL}
      - USDT_CONTRACT_ADDRESS=${USDT_CONTRACT_ADDRESS}
    ports:
      - "5000:5000"
    volumes:
      - ../backend/logs:/app/logs
    networks:
      - worldmall_network

networks:
  worldmall_network:
    name: worldmall_network
    driver: bridge 