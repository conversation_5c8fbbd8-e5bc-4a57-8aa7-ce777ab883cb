version: '3.8'

services:
  # MySQL service
  mysql:
    image: mysql:8.0
    container_name: worldmall-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: wOrldmAll.92VktL7JNs558.MySQLXroOtp
      MYSQL_DATABASE: worldmall_crypto_production
      MYSQL_USER: worldmall
      MYSQL_PASSWORD: wOrldmAll.92VktL7JNs558.MySQLXworldmallp
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - worldmall_network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  mysql_data:
    name: worldmall_mysql_data

networks:
  worldmall_network:
    name: worldmall_network
    driver: bridge 