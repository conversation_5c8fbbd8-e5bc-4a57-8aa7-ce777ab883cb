version: '3.8'

services:
  # Frontend service
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    container_name: worldmall_frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3000
      - NEXT_PUBLIC_API_URL=http://localhost:5000
    ports:
      - "3000:3000"
    volumes:
      - ../frontend/public:/app/public
      - ../frontend/logs:/app/logs
    networks:
      - worldmall_network

networks:
  worldmall_network:
    name: worldmall_network
    driver: bridge 