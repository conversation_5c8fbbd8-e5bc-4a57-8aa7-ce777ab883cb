version: '3.8'

services:
  # Backend service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: worldmall_backend
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      - ./backend/src:/app/src
      - ./backend/.env:/app/.env
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=worldmall
      - DB_PASSWORD=worldmall_password
      - DB_DATABASE=worldmall_crypto
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - worldmall_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5000/api-docs"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Frontend service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: runner
    container_name: worldmall-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/app:/app/app
      - ./frontend/components:/app/components
      - ./frontend/public:/app/public
      - ./frontend/styles:/app/styles
      - ./frontend/config:/app/config
      - ./frontend/types:/app/types
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      - NEXT_PUBLIC_API_URL=http://localhost:5000/api/v1
    depends_on:
      - backend
    networks:
      - worldmall_network

  # MySQL service
  mysql:
    image: mysql:8.0
    container_name: worldmall_mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: worldmall_crypto
      MYSQL_USER: worldmall
      MYSQL_PASSWORD: worldmall_password
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - worldmall_network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5

  # phpMyAdmin service
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: worldmall-phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      - PMA_HOST=mysql
      - PMA_PORT=3306
      - MYSQL_ROOT_PASSWORD=root_password
    depends_on:
      - mysql
    networks:
      - worldmall_network

volumes:
  mysql_data:
    name: worldmall_mysql_data

networks:
  worldmall_network:
    name: worldmall_network
    driver: bridge 