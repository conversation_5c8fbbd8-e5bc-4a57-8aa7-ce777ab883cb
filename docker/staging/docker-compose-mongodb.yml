services:
  mongodb:
    image: mongo:latest
    container_name: worldmall-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: worldmall
      MONGO_INITDB_ROOT_PASSWORD: wOrldmAll.92VktL7JNs558.MongoDBp
      MONGO_INITDB_DATABASE: worldmall_referral_tree
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    networks:
      - worldmall_network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: ["--auth"]

  mongo-express:
    image: mongo-express:latest
    container_name: worldmall-mongo-express
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: worldmall
      ME_CONFIG_MONGODB_ADMINPASSWORD: wOrldmAll.92VktL7JNs558.MongoDBp
      ME_CONFIG_MONGODB_URL: ******************************************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: wOrldmAll.92VktL7JNs558.MongoExpressp
    networks:
      - worldmall_network
    depends_on:
      - mongodb

volumes:
  mongodb_data:
    name: worldmall_mongodb_data
    external: true
  mongodb_config:
    name: worldmall_mongodb_config
    external: true

networks:
  worldmall_network:
    external: true