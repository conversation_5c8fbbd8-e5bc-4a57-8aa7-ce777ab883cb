#!/bin/bash

# Tạo file start-services.sh
cd docker/staging

# Định nghĩa màu cho output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color
YELLOW='\033[1;33m'

# Function để kiểm tra lỗi
check_error() {
    if [ $? -ne 0 ]; then
        echo -e "${RED}Error: $1 failed${NC}"
        exit 1
    fi
}

# Function để start một service
start_service() {
    local service=$1
    echo -e "${YELLOW}Starting $service...${NC}"
    docker-compose -f docker-compose-$service.yml up -d
    check_error $service
    echo -e "${GREEN}$service started successfully${NC}"
}

# Start từng service
start_service "mysql"
start_service "redis"
start_service "nginx"
start_service "phpmyadmin"
start_service "mongodb"

# Kiểm tra status
echo -e "\n${YELLOW}Checking services status:${NC}"
docker-compose -f docker-compose-mysql.yml ps
docker-compose -f docker-compose-redis.yml ps
docker-compose -f docker-compose-nginx.yml ps
docker-compose -f docker-compose-phpmyadmin.yml ps
docker-compose -f docker-compose-mongodb.yml ps
echo -e "\n${GREEN}All services have been started successfully!${NC}"

# Hiển thị logs nếu cần
echo -e "\n${YELLOW}To view logs, use these commands:${NC}"
echo "docker-compose -f docker-compose-mysql.yml logs -f"
echo "docker-compose -f docker-compose-redis.yml logs -f"
echo "docker-compose -f docker-compose-nginx.yml logs -f"
echo "docker-compose -f docker-compose-phpmyadmin.yml logs -f"
echo "docker-compose -f docker-compose-mongodb.yml logs -f"