#!/bin/bash

# Thông tin container và cơ sở dữ liệu
CONTAINER_NAME="mysql_container"  # Thay bằng tên container MySQL của bạn
DB_USER="root"                    # Username MySQL
DB_PASSWORD="yourpassword"        # Password MySQL
DB_NAME="mydb"                    # Tên cơ sở dữ liệu cần khôi phục
BACKUP_FILE="backup.sql"          # Đường dẫn đến file backup

# Kiểm tra file backup có tồn tại không
if [ ! -f "$BACKUP_FILE" ]; then
  echo "Error: Backup file $BACKUP_FILE does not exist!"
  exit 1
fi

# Kiểm tra container có đang chạy không
if ! docker ps | grep -q "$CONTAINER_NAME"; then
  echo "Error: Container $CONTAINER_NAME is not running!"
  exit 1
fi

# Tạo một bản backup hiện tại trước khi khôi phục (để tr<PERSON>h mất dữ liệu)
TIMESTAMP=$(date +%F_%H-%M-%S)
CURRENT_BACKUP="current_backup_${TIMESTAMP}.sql"
echo "Creating a backup of the current database before restoring..."
docker exec "$CONTAINER_NAME" mysqldump -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$CURRENT_BACKUP"
if [ $? -eq 0 ]; then
  echo "Current database backed up to $CURRENT_BACKUP"
else
  echo "Error: Failed to create a backup of the current database!"
  exit 1
fi

# Khôi phục cơ sở dữ liệu từ file backup
echo "Restoring database $DB_NAME from $BACKUP_FILE..."
docker exec -i "$CONTAINER_NAME" mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$BACKUP_FILE"
if [ $? -eq 0 ]; then
  echo "Database $DB_NAME restored successfully from $BACKUP_FILE!"
else
  echo "Error: Failed to restore database $DB_NAME!"
  exit 1
fi