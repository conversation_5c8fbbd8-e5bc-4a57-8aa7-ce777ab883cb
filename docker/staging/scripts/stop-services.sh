#!/bin/bash

# Tạo file stop-services.sh
cd docker/staging

# Định nghĩa màu
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'
YELLOW='\033[1;33m'

# Function để stop một service
stop_service() {
    local service=$1
    echo -e "${YELLOW}Stopping $service...${NC}"
    docker-compose -f docker-compose-$service.yml down
    if [ $? -ne 0 ]; then
        echo -e "${RED}Warning: Error stopping $service${NC}"
    else
        echo -e "${GREEN}$service stopped successfully${NC}"
    fi
}

# Stop từng service
stop_service "phpmyadmin"
stop_service "nginx"
stop_service "redis"
stop_service "mysql"
stop_service "mongodb"
echo -e "\n${GREEN}All services have been stopped!${NC}"