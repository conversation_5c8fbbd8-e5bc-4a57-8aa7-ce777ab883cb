#!/bin/bash

# <PERSON><PERSON><PERSON> sắc cho output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Định nghĩa tên miền
DOMAIN="tradefounder.space"
EMAIL="<EMAIL>"

echo -e "${GREEN}=== Triển khai SSL cho $DOMAIN ===${NC}"

# Kiểm tra xem Docker đã được cài đặt chưa
if ! command -v docker >/dev/null 2>&1; then
    echo -e "${RED}Lỗi: Docker chưa được cài đặt. Vui lòng cài đặt Docker trước.${NC}"
    exit 1
fi

# Kiểm tra xem Docker-Compose đã được cài đặt chưa
if ! command -v docker-compose >/dev/null 2>&1; then
    echo -e "${RED}Lỗi: Docker-Compose chưa được cài đặt. Vui lòng cài đặt Docker-Compose trước.${NC}"
    exit 1
fi

# Kiểm tra xem Certbot đã được cài đặt chưa
if ! command -v certbot >/dev/null 2>&1; then
    echo -e "\nCertbot chưa được cài đặt. Thực hiện cài đặt Certbot..."
    sudo apt update
    sudo apt install -y certbot python3-certbot-nginx
fi

# Kiểm tra cài đặt Certbot thành công
if ! command -v certbot >/dev/null 2>&1; then
    echo -e "${RED}Lỗi: Không thể cài đặt Certbot. Vui lòng kiểm tra lại.${NC}"
    exit 1
fi

echo -e "\n${GREEN}Phiên bản Certbot:${NC}"
certbot --version

# Kiểm tra xem Nginx đang chạy không (giả sử bạn dùng Nginx)
if ! docker ps | grep -q nginx; then
    echo -e "${RED}Lỗi: Nginx không đang chạy trong Docker. Vui lòng chạy Nginx trước khi triển khai SSL.${NC}"
    exit 1
fi

# Tạo chứng chỉ SSL với Certbot
echo -e "\n${GREEN}Tạo chứng chỉ SSL cho $DOMAIN...${NC}"
sudo certbot --nginx -d "$DOMAIN" -d "www.$DOMAIN" --email "$EMAIL" --agree-tos --non-interactive

# Kiểm tra xem chứng chỉ đã được tạo chưa
if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
    echo -e "\n${GREEN}Chứng chỉ SSL đã được tạo thành công tại /etc/letsencrypt/live/$DOMAIN/${NC}"
else
    echo -e "${RED}Lỗi: Không thể tạo chứng chỉ SSL. Vui lòng kiểm tra log Certbot tại /var/log/letsencrypt/${NC}"
    exit 1
fi

# Cấu hình tự động gia hạn chứng chỉ
echo -e "\n${GREEN}Cấu hình tự động gia hạn chứng chỉ SSL...${NC}"
sudo systemctl enable certbot.timer
sudo systemctl start certbot.timer

echo -e "\n${GREEN}=== Hoàn tất triển khai SSL cho $DOMAIN ===${NC}"
echo "Bạn có thể kiểm tra chứng chỉ bằng cách truy cập https://$DOMAIN"