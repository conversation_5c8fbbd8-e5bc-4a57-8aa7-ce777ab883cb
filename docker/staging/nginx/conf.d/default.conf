# Add this at the top of the file
resolver 127.0.0.11 valid=30s;

# Upstream definitions
upstream frontend {
    server host.docker.internal:3000;
}

upstream backend {
    server host.docker.internal:5000;
}

# Main server configuration
server {
    listen 1194;
    listen [::]:1194;
    server_name tradefounder.space www.tradefounder.space;

    # Thêm location cho certbot
    location /.well-known/acme-challenge/ {
        allow all;
        root /var/www/certbot;
        try_files $uri =404;
    }

    # Frontend location
    location / {
        proxy_pass http://frontend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Backend API location
    location /api/ {
        proxy_pass http://backend/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Basic settings
    client_max_body_size 20M;
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # Error pages
    error_page 502 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
        internal;
    }
}

# Main domain HTTPS
# server {
#     listen 443 ssl;
#     server_name tradefounder.space www.tradefounder.space;

#     ssl_certificate /etc/letsencrypt/live/tradefounder.space/fullchain.pem;
#     ssl_certificate_key /etc/letsencrypt/live/tradefounder.space/privkey.pem;

#     # SSL configuration
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_timeout 1d;
#     ssl_session_cache shared:SSL:50m;
#     ssl_session_tickets off;
#     ssl_stapling on;
#     ssl_stapling_verify on;

#     # Buffer size for POST submissions
#     client_max_body_size 20M;
#     client_body_buffer_size 128k;
    
#     # Buffer size for Headers
#     large_client_header_buffers 4 32k;
    
#     # Timeouts
#     proxy_connect_timeout 300s;
#     proxy_send_timeout 300s;
#     proxy_read_timeout 300s;
#     send_timeout 300s;
#     keepalive_timeout 300s;

#     # Frontend location
#     location / {
#         proxy_pass http://frontend;
#         proxy_http_version 1.1;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#     }

#     # Backend API location
#     location /api/ {
#         proxy_pass http://backend/;
#         proxy_http_version 1.1;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#     }

#     # Error pages
#     error_page 502 504 /50x.html;
#     location = /50x.html {
#         root /usr/share/nginx/html;
#         internal;
#     }

#     # Logging
#     error_log /var/log/nginx/error.log debug;
#     access_log /var/log/nginx/access.log combined buffer=512k flush=1m;
# }