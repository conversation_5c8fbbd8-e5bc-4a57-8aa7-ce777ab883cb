version: '3.8'

services:
  # Redis service
  redis:
    image: redis:7.2-alpine
    container_name: worldmall-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --requirepass wOrldmAll.92VktL7JNs558.RedisXworldmallp --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - worldmall_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Commander service
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: worldmall-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:wOrldmAll.92VktL7JNs558.RedisXworldmallp
      - HTTP_USER=admin
      - HTTP_PASSWORD=wOrldmAll.92VktL7JNs558.RedisCommanderp
      - HTTP_AUTH_USERNAME=admin
      - HTTP_AUTH_PASSWORD=wOrldmAll.92VktL7JNs558.RedisCommanderp
      - READ_ONLY=false
    networks:
      - worldmall_network
    depends_on:
      - redis

volumes:
  redis_data:
    name: worldmall_redis_data

networks:
  worldmall_network:
    name: worldmall_network
    driver: bridge 