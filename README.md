# WorldMall Crypto Platform

Nền tảng thương mại điện tử tích hợp đa ứng dụng, sàn gia<PERSON> dịch, ví điện tử và hệ thống mining token.

## Tổng quan dự án

WorldMall (WM) là một nền tảng thương mại điện tử tích hợp đa ứng dụng, đa dự án đối tác, vừa truyền thông quảng bá cho đối tác và tích hợp ví World và Game để thu hút cộng đồng. Nền tảng xây dựng hệ thống nhiệm vụ cho thành viên tham gia và nhận thưởng bằng WM token và token của các nền tảng đối tác.

## Cài đặt và chạy với Docker

### Yêu cầu

- Docker
- Docker Compose

### Khởi động dự án

1. Clone repository:
   ```
   git clone https://github.com/yourusername/worldmall-crypto-platform.git
   cd worldmall-crypto-platform
   ```

2. Khởi động các dịch vụ với Docker Compose:
   ```
   docker-compose up -d
   ```

3. Truy cập các dịch vụ:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000
   - API Documentation: http://localhost:5000/api-docs
   - phpMyAdmin: http://localhost:8080 (Server: mysql, Username: worldmall, Password: worldmall_password)

### Dừng dự án

```
docker-compose down
```

### Xem logs

```
docker-compose logs -f
```

Để xem logs của một dịch vụ cụ thể:
```
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f mysql
```

### Khởi động lại một dịch vụ

```
docker-compose restart backend
docker-compose restart frontend
docker-compose restart mysql
```

### Xây dựng lại các container

```
docker-compose build
```

Hoặc xây dựng lại một dịch vụ cụ thể:
```
docker-compose build backend
docker-compose build frontend
```

## Cấu trúc dự án

- `backend/`: API backend sử dụng Node.js, Express và Sequelize
- `frontend/`: Giao diện người dùng sử dụng Next.js
- `docker-compose.yml`: Cấu hình Docker Compose
- `backend/Dockerfile`: Cấu hình Docker cho backend
- `frontend/Dockerfile`: Cấu hình Docker cho frontend

## Tài liệu API

Tài liệu API được tạo tự động bằng Swagger và có thể truy cập tại:
http://localhost:5000/api-docs

## Phát triển

### Cấu trúc cơ sở dữ liệu

Dự án sử dụng MySQL làm cơ sở dữ liệu chính với các bảng:
- Users: Quản lý thông tin người dùng
- Mining: Lưu trữ lịch sử mining
- Staking: Quản lý staking
- NFT: Quản lý NFT
- UserNFT: Quản lý NFT của người dùng
- Lightning: Quản lý tia sét
- Task: Quản lý nhiệm vụ
- TaskCompletion: Quản lý hoàn thành nhiệm vụ
- UserCompletedTask: Quản lý nhiệm vụ đã hoàn thành

### Migrations và Seeders

Migrations và seeders được tự động chạy khi khởi động container backend. Bạn cũng có thể chạy thủ công:

```
docker-compose exec backend npx sequelize-cli db:migrate
docker-compose exec backend npx sequelize-cli db:seed:all
```

Để tạo migration mới:
```
docker-compose exec backend npx sequelize-cli migration:generate --name migration-name
```

Để tạo seeder mới:
```
docker-compose exec backend npx sequelize-cli seed:generate --name seeder-name
```

## Giấy phép

Dự án này được cấp phép theo Giấy phép MIT. 