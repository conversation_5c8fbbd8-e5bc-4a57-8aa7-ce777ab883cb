{"$schema": "https://json.schemastore.org/eslintrc.json", "env": {"browser": false, "es2021": true, "node": true}, "extends": "next/core-web-vitals", "plugins": ["react", "unused-imports", "import", "@typescript-eslint", "jsx-a11y", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "settings": {"react": {"version": "detect"}}, "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "react/no-unescaped-entities": "off", "react/display-name": "off", "react/jsx-key": "off", "react-hooks/rules-of-hooks": "off", "react-hooks/exhaustive-deps": "off", "no-unused-vars": "off", "no-console": "off", "prettier/prettier": "off"}}