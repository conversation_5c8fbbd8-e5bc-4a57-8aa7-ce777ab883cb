import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { success: false, message: 'No authorization header' },
        { status: 401 }
      );
    }

    const response = await fetch(`${API_BASE_URL}/api/v1/staking/my-stakings`, {
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
      credentials: 'include'
    });

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: 'Backend API error' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching my stakings:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch my stakings' },
      { status: 500 }
    );
  }
}
