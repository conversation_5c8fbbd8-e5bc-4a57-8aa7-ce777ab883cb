import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionId = params.id;
    const authHeader = request.headers.get('authorization');

    if (!authHeader) {
      return NextResponse.json(
        { success: false, message: 'No authorization header' },
        { status: 401 }
      );
    }

    const response = await fetch(`${API_BASE_URL}/api/v1/trading-sessions/${sessionId}/open`, {
      method: 'POST',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
      credentials: 'include'
    });

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: 'Something went wrong, please try again later' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json({
      success: true,
      data: data.data || null,
      message: 'Session closed successfully'
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, message: 'Something went wrong, please try again later' },
      { status: 500 }
    );
  }
} 