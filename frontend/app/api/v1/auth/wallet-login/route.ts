import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

if (!API_BASE_URL) {
  throw new Error('NEXT_PUBLIC_API_URL environment variable is not set');
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const response = await fetch(`${API_BASE_URL}/api/v1/auth/wallet-login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: 'Backend API error' },
        { status: response.status }
      );
    }

    const data = await response.json();

    const setCookieHeader = response.headers.get('set-cookie');
    
    const nextResponse = NextResponse.json(data);
    
    if (setCookieHeader) {
      nextResponse.headers.set('Set-<PERSON><PERSON>', setCookieHeader);
    }

    return nextResponse;
  } catch (error) {
    return NextResponse.json(
      { success: false, message: '<PERSON><PERSON> failed' },
      { status: 500 }
    );
  }
} 