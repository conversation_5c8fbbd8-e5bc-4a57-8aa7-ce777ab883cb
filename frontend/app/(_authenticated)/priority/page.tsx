'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardBody, Input, Button, Textarea, addToast } from '@heroui/react';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { useApi } from '@/hooks/useApi';
import useSendTokenTransaction from '@/hooks/useSendTokenTransaction';
import { useTranslation } from '@/hooks/useTranslation';

export default function PriorityPositionPage() {
  const { user } = useUserStore();
  const router = useRouter();
  const { fetchApi } = useApi();
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    sendTokenTransaction,
    isLoadingTransaction,
    isConfirming,
    error: transactionError,
    transactionResult,
    isConnected,
  } = useSendTokenTransaction();

  useEffect(() => {
    if (transactionResult && transactionResult.success && !isSubmitting) {
      handlePrioritySubmission(transactionResult.transactionHash);
    }
  }, [transactionResult]);

  useEffect(() => {
    if (transactionError) {
      addToast({
        title: t('priority.transaction_error'),
        description: transactionError,
        color: "danger",
      });
    }
  }, [transactionError]);

  const handlePrioritySubmission = async (transactionHash: string) => {
    setIsSubmitting(true);
    try {
      const response = await fetchApi('/wallet/priority', {
        method: 'POST',
        body: JSON.stringify({ transactionHash }),
        requireAuth: true,
      });

      if (response.success) {
        addToast({
          title: t('priority.purchased'),
          description: t('priority.purchase_success'),
          color: "success",
        });
        router.push('/wallet');
      } else {
        throw new Error(response.message[t('common.language_code')] || t('priority.purchase_error'));
      }
    } catch (error: any) {
      addToast({
        title: t('priority.purchase_failed'),
        description: error.message || t('priority.purchase_error'),
        color: "danger",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBuyPriorityPosition = async () => {
    if (!isConnected) {
      addToast({
        title: t('priority.wallet_error'),
        description: t('priority.connect_wallet_first'),
        color: "danger",
      });
      return;
    }

    const transactionParams = {
      tokenContractAddress: "******************************************",
      recipientAddress: "******************************************",
      amount: "5",
      decimals: 18,
      action: 'Priority Position',
    };

    const result = await sendTokenTransaction(transactionParams);

    addToast({
      title: t('priority.transaction_sent'),
      description: t('priority.waiting_confirmation'),
      color: "default",
    });
  };

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex max-w-sm flex-col gap-4">
        <div className="flex items-center justify-between">
          <button onClick={() => router.back()}>
            <Icon icon="solar:arrow-left-linear" width={24} className="text-gray-500" />
          </button>
          <h1 className="text-xl font-semibold">{t('priority.buy')}</h1>
          <div className="w-6"></div>
        </div>

        <Card>
          <CardBody className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <h2 className="text-lg font-semibold">{t('priority.title')}</h2>
              <p className="text-sm text-gray-500">
                {t('priority.description', { amount: '5 USDT' })}
              </p>
            </div>

            <div className="flex flex-col gap-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">{t('priority.price')}:</span>
                <span className="font-semibold">5 USDT</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">{t('priority.payment_method')}:</span>
                <span className="font-semibold">USDT (BSC)</span>
              </div>
            </div>

            <div className="flex flex-col gap-2">
              <h3 className="text-md font-semibold">{t('priority.benefits')}:</h3>
              <ul className="list-inside list-disc text-sm text-gray-600">
                <li>{t('priority.benefit_ranking')}</li>
                <li>{t('priority.benefit_support')}</li>
                <li>{t('priority.benefit_early_access')}</li>
                <li>{t('priority.benefit_permanent')}</li>
              </ul>
            </div>

            <Button
              className="w-full bg-gradient-to-tr from-blue-500 to-green-500 text-white shadow-lg"
              onPress={handleBuyPriorityPosition}
              isDisabled={isLoadingTransaction || isConfirming || isSubmitting || (user && !!user.firstDepositTime)}
            >
              {isLoadingTransaction ? t('priority.sending_transaction') :
                isConfirming ? t('priority.confirming_transaction') :
                  isSubmitting ? t('priority.processing_purchase') :
                    user && user.firstDepositTime ? t('priority.already_purchased') : t('priority.buy')}
            </Button>

            {transactionResult && (
              <p className="text-green-500 text-sm">
                {t('priority.transaction_confirmed')}: <a href={`https://bscscan.com/tx/${transactionResult.transactionHash}`} target="_blank">{t('priority.view_on_bscscan')}</a>
              </p>
            )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
}