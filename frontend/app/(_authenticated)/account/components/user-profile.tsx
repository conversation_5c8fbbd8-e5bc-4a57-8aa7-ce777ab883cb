'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardBody, Button, User, Link, Spinner } from '@heroui/react';
import { Icon } from '@iconify/react';
import { useUserStore } from '@/store/userStore';
import { useRouter } from 'next/navigation';
import { UserRank, WManagerRank } from '@/types/models';
import { useTranslation } from '@/hooks/useTranslation';

export default function UserProfile() {
    const { user, isAuthenticated } = useUserStore();
    const router = useRouter();
    const { t } = useTranslation();

    /**
     * L<PERSON>y hình ảnh của user dựa vào rank
     */
    const getUserRank = () => {
        if (user?.wManagerRank === WManagerRank.WM1) {
            return '/levels/w-1.png';
        }
        if (user?.wManagerRank === WManagerRank.WM2) {
            return '/levels/w-2.png';
        }
        if (user?.wManagerRank === WManagerRank.WM3) {
            return '/levels/w-3.png';
        }
        if (user?.wManagerRank === WManagerRank.WM4) {
            return '/levels/w-4.png';
        }
        if (user?.wManagerRank === WManagerRank.WM5) {
            return '/levels/w-5.png';
        }
        return '/levels/w-0.png';
    }

    return (
        <Card shadow="sm" className="border-none">
            <CardBody className="p-4">
                <div className="flex flex-col gap-3">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                            <User
                                avatarProps={{
                                    src: isAuthenticated ? getUserRank() : '',
                                }}
                                description={isAuthenticated ? user?.wManagerRank : ''}
                                name={isAuthenticated ? user?.name ? user?.name : user?.username : ''}

                            />
                        </div>
                        <div className="flex items-center gap-1">
                            <Icon icon="solar:heart-linear" width={20} className="text-pink-500" />
                            <span className="text-lg font-semibold text-pink-500">{isAuthenticated ? user?.credits : 0}</span>
                        </div>
                    </div>
                    <Button
                        size="sm"
                        endContent={<Icon icon="solar:arrow-right-linear" width={16} />}
                        onPress={() => router.push('/flash-buy')}
                        className="w-full bg-gradient-to-r from-blue-500 to-green-500 text-white shadow-md"
                    >
                        {t('account.buy_nft')}
                    </Button>
                </div>
            </CardBody>
        </Card>
    );
}