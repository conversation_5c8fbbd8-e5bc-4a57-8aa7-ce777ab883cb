'use client';

import React from 'react';
import CardIconAction from '@/components/card-icon-action';
import { useRouter } from 'next/navigation';
import { useTranslation } from '@/hooks/useTranslation';

export default function ActionsSection() {
  const router = useRouter();
  const { t } = useTranslation();
  return (
    <div className="flex justify-around gap-2">
      <CardIconAction
        color="primary"
        icon="solar:gift-linear"
        title={t('account.my_nfts')}
        className="flex-1"
        onClick={() => router.push('/nfts')}
      />
      <CardIconAction
        color="primary"
        icon="solar:bill-linear"
        title={t('account.orders')}
        className="flex-1"
        onClick={() => router.push('/nfts/orders')}
      />
      <CardIconAction
        color="primary"
        icon="solar:heart-linear"
        title={t('account.credits')}
        className="flex-1"
        onClick={() => router.push('/wallet')}
      />
      <CardIconAction
        color="primary"
        icon="solar:share-circle-linear"
        title={t('account.affiliate')}
        className="flex-1"
        onClick={() => router.push('/affiliate')}
      />
    </div>
  );
}