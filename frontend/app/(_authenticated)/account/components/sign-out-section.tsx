'use client';

import React from 'react';
import { Button } from '@heroui/react';
import { useUserStore } from '@/store/userStore';
import { useRouter } from 'next/navigation';
import { useDisconnect } from 'wagmi';
import { useTranslation } from '@/hooks/useTranslation';

export default function SignOutSection() {
  const { logout } = useUserStore();
  const router = useRouter();
  const { disconnect } = useDisconnect();
  const { t } = useTranslation();

  const handleSignOut = async () => {
    try {
      await disconnect();
      logout();
      router.replace('/');
    } catch (error) {
    }
  };

  return (
    <div className="flex flex-col items-center gap-3 mt-4">
      <Button
        color="danger"
        variant="flat"
        className="w-full max-w-xs"
        onPress={handleSignOut}
      >
        {t('account.sign_out')}
      </Button>
      <p className="text-xs text-gray-500">{t('account.version')}: 1.0.5</p>
    </div>
  );
}