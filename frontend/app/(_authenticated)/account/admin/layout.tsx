'use client';

import React from 'react';
import { <PERSON>, CardBody, Button } from '@heroui/react';
import { Icon } from '@iconify/react';
import { useRouter, usePathname } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { UserRole } from '@/types/models';
import { useTranslation } from '@/hooks/useTranslation';

interface AdminNavItem {
  key: string;
  title: string;
  icon: string;
  href: string;
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { user } = useUserStore();
  const { t } = useTranslation();

  // Kiểm tra quyền admin
  if (!user || user.role !== UserRole.ADMIN) {
    router.replace('/account');
    return null;
  }

  const adminNavItems: AdminNavItem[] = [
    {
      key: 'support',
      title: t('admin.support_management'),
      icon: 'solar:ticket-bold-duotone',
      href: '/account/admin/support'
    },
    // Có thể thêm các menu admin khác sau
  ];

  const handleNavigation = (href: string) => {
    router.push(href);
  };

  const handleBack = () => {
    router.push('/account');
  };

  return (
    <div className="flex w-full flex-col gap-4">
      {/* Header */}
      <div className="flex max-w-sm flex-col gap-4">
        <div className="flex items-center justify-between">
          <button onClick={handleBack}>
            <Icon icon="solar:arrow-left-linear" width={24} className="text-green-500" />
          </button>
          <p className="text-lg font-semibold flex-1 text-center">{t('admin.panel')}</p>
          <div className="w-6" />
        </div>

        {/* Admin Navigation */}
        <Card className="w-full">
          <CardBody className="p-4">
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-3 mb-3">
                <Icon icon="solar:shield-user-bold-duotone" className="text-blue-500" width={24} />
                <h3 className="text-lg font-semibold">{t('admin.panel')}</h3>
              </div>
              
              {adminNavItems.map((item) => (
                <Button
                  key={item.key}
                  variant={pathname.includes(item.href) ? "solid" : "light"}
                  color={pathname.includes(item.href) ? "primary" : "default"}
                  className="justify-start"
                  startContent={<Icon icon={item.icon} width={20} />}
                  onPress={() => handleNavigation(item.href)}
                >
                  {item.title}
                </Button>
              ))}
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Content */}
      <div className="flex-1">
        {children}
      </div>
    </div>
  );
}
