'use client';

import React, { useState } from 'react';
import {
  Card,
  CardBody,
  Input,
  But<PERSON>,
  Spinner
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';
import { useUserStore } from '@/store/userStore';
import UserDetailsCard from './components/user-details-card';
import AdminDepositModal from './components/admin-deposit-modal';
import AdminSetBalanceModal from './components/admin-set-balance-modal';
import AdminUpdateWManagerRankModal from './components/admin-update-wmanager-rank-modal';
import AdminKycModal from './components/admin-kyc-modal';
import AdminTransferModal from './components/admin-transfer-modal';
import AdminWithdrawModal from './components/admin-withdraw-modal';
import AdminTransactionHistoryModal from './components/admin-transaction-history-modal';
import AdminWalletInfoModal from './components/admin-wallet-info-modal';
import AdminAdjustWmModal from './components/admin-adjust-wm-modal';

export default function AdminUserManagementPage() {
  const { t } = useTranslation();
  const { accessToken, user, isAuthenticated } = useUserStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchedUser, setSearchedUser] = useState<User | null>(null);
  const [searchError, setSearchError] = useState<string>('');
  const [isDepositModalOpen, setIsDepositModalOpen] = useState(false);
  const [isSetBalanceModalOpen, setIsSetBalanceModalOpen] = useState(false);
  const [modalType, setModalType] = useState<'USDT' | 'WM'>('USDT');

  // New modal states
  const [isKycModalOpen, setIsKycModalOpen] = useState(false);
  const [isTransferModalOpen, setIsTransferModalOpen] = useState(false);
  const [isWithdrawModalOpen, setIsWithdrawModalOpen] = useState(false);
  const [isTransactionHistoryModalOpen, setIsTransactionHistoryModalOpen] = useState(false);
  const [isWalletInfoModalOpen, setIsWalletInfoModalOpen] = useState(false);
  const [isAdjustWmModalOpen, setIsAdjustWmModalOpen] = useState(false);
  const [isUpdateWManagerRankModalOpen, setIsUpdateWManagerRankModalOpen] = useState(false);

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchError(t('admin.search_query_required'));
      return;
    }

    // Check authentication
    if (!isAuthenticated || !accessToken) {
      setSearchError(t('admin.authentication_required'));
      return;
    }

    // Check if user has admin role
    if (!user || user.role !== 'ADMIN') {
      setSearchError(t('admin.admin_access_required'));
      return;
    }

    console.log('User authenticated:', isAuthenticated);
    console.log('User role:', user?.role);
    console.log('Token exists:', !!accessToken);

    console.log('Token length:', accessToken.length);
    console.log('Token preview:', accessToken.substring(0, 20) + '...');

    setIsSearching(true);
    setSearchError('');
    setSearchedUser(null);

    try {
      const response = await fetch(`/api/v1/users/admin/search?query=${encodeURIComponent(searchQuery.trim())}`, {
        method: 'GET',
        headers: {
          'Authorization': accessToken, // accessToken already includes 'Bearer ' prefix
          'Content-Type': 'application/json',
        },
      });

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (response.ok) {
        const result = await response.json();
        console.log('Search result:', result);
        setSearchedUser(result.data);
      } else if (response.status === 401) {
        const errorData = await response.json();
        console.error('Authentication error:', errorData);
        setSearchError(t('admin.authentication_error'));
      } else if (response.status === 404) {
        setSearchError(t('admin.user_not_found'));
      } else {
        const errorData = await response.json();
        console.error('API error:', errorData);
        setSearchError(t('admin.api_error', { message: errorData.message || 'Unknown error' }));
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchError(t('admin.search_error'));
    } finally {
      setIsSearching(false);
    }
  };

  const handleDeposit = (type: 'USDT' | 'WM') => {
    setModalType(type);
    setIsDepositModalOpen(true);
  };

  const handleSetBalance = (type: 'USDT' | 'WM') => {
    setModalType(type);
    setIsSetBalanceModalOpen(true);
  };

  const handleBalanceUpdated = (updatedUser: User) => {
    setSearchedUser(updatedUser);
  };

  // New handler functions
  const handleUpdateKyc = () => {
    setIsKycModalOpen(true);
  };

  const handleTransfer = (type: 'USDT' | 'WM') => {
    setModalType(type);
    setIsTransferModalOpen(true);
  };

  const handleWithdraw = () => {
    setIsWithdrawModalOpen(true);
  };

  const handleViewTransactions = () => {
    setIsTransactionHistoryModalOpen(true);
  };

  const handleViewWalletInfo = () => {
    setIsWalletInfoModalOpen(true);
  };

  const handleAdjustWm = () => {
    setIsAdjustWmModalOpen(true);
  };

  const handleUpdateWManagerRank = () => {
    setIsUpdateWManagerRankModalOpen(true);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex max-w-sm flex-col gap-4">
        {/* Header */}
        <div className="flex items-center gap-3 mb-2">
          <Icon icon="solar:users-group-two-rounded-bold-duotone" className="text-blue-500" width={24} />
          <h2 className="text-xl font-semibold">{t('admin.user_management')}</h2>
        </div>

        {/* Search Section */}
        <Card className="w-full">
          <CardBody className="p-4">
            <div className="flex flex-col gap-3">
              <div className="text-sm font-medium text-gray-700">
                {t('admin.search_user')}
              </div>
              
              <div className="flex gap-2">
                <Input
                  placeholder={t('admin.search_user_placeholder')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={handleKeyPress}
                  startContent={<Icon icon="solar:magnifer-linear" width={20} />}
                  classNames={{
                    input: "text-sm",
                  }}
                />
                <Button
                  color="primary"
                  onPress={handleSearch}
                  isLoading={isSearching}
                  isDisabled={!searchQuery.trim()}
                >
                  {isSearching ? <Spinner size="sm" /> : t('admin.search')}
                </Button>
              </div>

              {searchError && (
                <div className="text-sm text-red-500 flex items-center gap-2">
                  <Icon icon="solar:danger-circle-linear" width={16} />
                  {searchError}
                </div>
              )}
            </div>
          </CardBody>
        </Card>

        {/* Search Results */}
        {searchedUser && (
          <UserDetailsCard
            user={searchedUser}
            onDeposit={handleDeposit}
            onSetBalance={handleSetBalance}
            onUpdateKyc={handleUpdateKyc}
            onTransfer={handleTransfer}
            onWithdraw={handleWithdraw}
            onViewTransactions={handleViewTransactions}
            onViewWalletInfo={handleViewWalletInfo}
            onAdjustWm={handleAdjustWm}
            onLockAccount={() => console.log('Lock account')}
            onUnlockAccount={() => console.log('Unlock account')}
            onUpdateRole={() => console.log('Update role')}
            onUpdateRank={handleUpdateWManagerRank}
            onResetPassword={() => console.log('Reset password')}
            onViewReferrals={() => console.log('View referrals')}
            onViewStaking={() => console.log('View staking')}
            onSendNotification={() => console.log('Send notification')}
          />
        )}

        {/* Deposit Modal */}
        <AdminDepositModal
          isOpen={isDepositModalOpen}
          onClose={() => setIsDepositModalOpen(false)}
          user={searchedUser}
          type={modalType}
          onBalanceUpdated={handleBalanceUpdated}
        />

        {/* Set Balance Modal */}
        <AdminSetBalanceModal
          isOpen={isSetBalanceModalOpen}
          onClose={() => setIsSetBalanceModalOpen(false)}
          user={searchedUser}
          type={modalType}
          onBalanceUpdated={handleBalanceUpdated}
        />

        {/* KYC Modal */}
        <AdminKycModal
          isOpen={isKycModalOpen}
          onClose={() => setIsKycModalOpen(false)}
          user={searchedUser}
          onKycUpdated={handleBalanceUpdated}
        />

        {/* Transfer Modal */}
        <AdminTransferModal
          isOpen={isTransferModalOpen}
          onClose={() => setIsTransferModalOpen(false)}
          user={searchedUser}
          type={modalType}
          onTransferCompleted={handleBalanceUpdated}
        />

        {/* Withdraw Modal */}
        <AdminWithdrawModal
          isOpen={isWithdrawModalOpen}
          onClose={() => setIsWithdrawModalOpen(false)}
          user={searchedUser}
          onWithdrawCompleted={handleBalanceUpdated}
        />

        {/* Transaction History Modal */}
        <AdminTransactionHistoryModal
          isOpen={isTransactionHistoryModalOpen}
          onClose={() => setIsTransactionHistoryModalOpen(false)}
          user={searchedUser}
        />

        {/* Wallet Info Modal */}
        <AdminWalletInfoModal
          isOpen={isWalletInfoModalOpen}
          onClose={() => setIsWalletInfoModalOpen(false)}
          user={searchedUser}
        />

        {/* Adjust WM Modal */}
        <AdminAdjustWmModal
          isOpen={isAdjustWmModalOpen}
          onClose={() => setIsAdjustWmModalOpen(false)}
          user={searchedUser}
          onAdjustCompleted={handleBalanceUpdated}
        />

        {/* Update WManager Rank Modal */}
        <AdminUpdateWManagerRankModal
          isOpen={isUpdateWManagerRankModalOpen}
          onClose={() => setIsUpdateWManagerRankModalOpen(false)}
          user={searchedUser}
          onRankUpdated={handleBalanceUpdated}
        />
      </div>
    </div>
  );
}
