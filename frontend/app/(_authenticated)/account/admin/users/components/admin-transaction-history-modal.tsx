'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>oot<PERSON>,
  <PERSON>ton,
  Select,
  SelectItem,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Pagination,
  Spinner,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';

interface Transaction {
  id: string;
  type: string;
  amount: number;
  status: string;
  token: {
    symbol: string;
  };
  note?: string;
  transactionAt: string;
  recipientAddress?: string;
  senderAddress?: string;
}

interface TransactionHistoryResponse {
  transactions: Transaction[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface AdminTransactionHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
}

export default function AdminTransactionHistoryModal({ 
  isOpen, 
  onClose, 
  user 
}: AdminTransactionHistoryModalProps) {
  const { t } = useTranslation();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [typeFilter, setTypeFilter] = useState<string>('ALL');
  const limit = 10;

  const fetchTransactions = async (currentPage: number = 1, type: string = 'ALL') => {
    if (!user) return;

    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: limit.toString(),
        ...(type !== 'ALL' && { type })
      });

      const response = await fetch(`/api/v1/wallet/admin/${user.wallet}/transactions/history?${params}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        const data: TransactionHistoryResponse = result.data;
        setTransactions(data.transactions || []);
        setTotalPages(data.totalPages || 1);
      } else {
        const error = await response.json();
        addToast({
          title: t('errors.error'),
          description: error.message?.en || error.message || t('admin.fetch_transactions_failed'),
          color: 'danger',
        });
      }
    } catch (error) {
      console.error('Fetch transactions error:', error);
      addToast({
        title: t('errors.error'),
        description: t('admin.fetch_transactions_failed'),
        color: 'danger',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && user) {
      fetchTransactions(1, typeFilter);
      setPage(1);
    }
  }, [isOpen, user, typeFilter]);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    fetchTransactions(newPage, typeFilter);
  };

  const handleTypeFilterChange = (keys: any) => {
    const newType = Array.from(keys)[0] as string;
    setTypeFilter(newType);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'danger';
      default: return 'default';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'deposit': return 'solar:add-circle-linear';
      case 'withdrawal': return 'solar:download-linear';
      case 'transfer': return 'solar:transfer-horizontal-linear';
      case 'mining': return 'solar:medal-ribbon-linear';
      default: return 'solar:history-linear';
    }
  };

  const getTransactionTypeText = (type: string) => {
    switch (type.toLowerCase()) {
      case 'deposit': return t('admin.deposit');
      case 'withdrawal': return t('admin.withdrawal');
      case 'transfer': return t('admin.transfer');
      case 'mining': return t('admin.mining');
      default: return type;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const handleClose = () => {
    setTransactions([]);
    setPage(1);
    setTotalPages(1);
    setTypeFilter('ALL');
    onClose();
  };

  if (!user) return null;

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose}
      size="5xl"
      classNames={{
        base: "bg-white",
        header: "border-b border-gray-200",
        footer: "border-t border-gray-200",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex items-center gap-2">
          <Icon icon="solar:history-linear" width={20} className="text-default-500" />
          <span>{t('admin.transaction_history')}</span>
        </ModalHeader>
        
        <ModalBody className="py-4">
          <div className="space-y-4">
            {/* User Info */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700 mb-2">
                {t('admin.user_info')}
              </div>
              <div className="space-y-1 text-sm">
                <div>
                  <span className="font-medium">{t('admin.wallet_address')}:</span>
                  <span className="ml-2 font-mono">{user.wallet.substring(0, 10)}...{user.wallet.substring(user.wallet.length - 8)}</span>
                </div>
                <div>
                  <span className="font-medium">{t('admin.referral_code')}:</span>
                  <span className="ml-2 font-mono">{user.referralCode}</span>
                </div>
              </div>
            </div>

            {/* Filter */}
            <div className="flex items-center gap-4">
              <Select
                label={t('admin.transaction_type')}
                selectedKeys={[typeFilter]}
                onSelectionChange={handleTypeFilterChange}
                className="max-w-xs"
              >
                <SelectItem key="ALL" value="ALL">{t('admin.all_types')}</SelectItem>
                <SelectItem key="DEPOSIT" value="DEPOSIT">{t('admin.deposit')}</SelectItem>
                <SelectItem key="WITHDRAWAL" value="WITHDRAWAL">{t('admin.withdrawal')}</SelectItem>
                <SelectItem key="TRANSFER" value="TRANSFER">{t('admin.transfer')}</SelectItem>
                <SelectItem key="MINING" value="MINING">{t('admin.mining')}</SelectItem>
              </Select>
            </div>

            {/* Transactions Table */}
            <div className="min-h-[400px]">
              {isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <Spinner size="lg" />
                </div>
              ) : (
                <Table aria-label={t('admin.transaction_history')}>
                  <TableHeader>
                    <TableColumn>{t('admin.type')}</TableColumn>
                    <TableColumn>{t('admin.token')}</TableColumn>
                    <TableColumn>{t('admin.amount')}</TableColumn>
                    <TableColumn>{t('admin.status')}</TableColumn>
                    <TableColumn>{t('admin.date')}</TableColumn>
                    <TableColumn>{t('admin.note')}</TableColumn>
                  </TableHeader>
                  <TableBody emptyContent={t('admin.no_transactions')}>
                    {transactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Icon icon={getTypeIcon(transaction.type)} width={16} />
                            <span>{getTransactionTypeText(transaction.type)}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Chip size="sm" variant="flat" color="primary">
                            {transaction.token.symbol}
                          </Chip>
                        </TableCell>
                        <TableCell>
                          <span className="font-mono">
                            {transaction.amount.toLocaleString()}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            size="sm" 
                            color={getStatusColor(transaction.status)}
                            variant="flat"
                          >
                            {transaction.status}
                          </Chip>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">
                            {formatDate(transaction.transactionAt)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-gray-600">
                            {transaction.note || '-'}
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center">
                <Pagination
                  total={totalPages}
                  page={page}
                  onChange={handlePageChange}
                  showControls
                  showShadow
                />
              </div>
            )}
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button 
            variant="light" 
            onPress={handleClose}
          >
            {t('common.close')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
