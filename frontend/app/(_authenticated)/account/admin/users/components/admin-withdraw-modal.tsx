'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';

interface AdminWithdrawModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  onWithdrawCompleted?: (updatedUser: User) => void;
}

export default function AdminWithdrawModal({ 
  isOpen, 
  onClose, 
  user, 
  onWithdrawCompleted 
}: AdminWithdrawModalProps) {
  const { t } = useTranslation();
  const [amount, setAmount] = useState('');
  const [recipientAddress, setRecipientAddress] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    if (!user) return;

    if (!amount.trim() || parseFloat(amount) <= 0) {
      addToast({
        title: t('errors.error'),
        description: t('admin.enter_valid_amount'),
        color: 'danger',
      });
      return;
    }

    if (!recipientAddress.trim()) {
      addToast({
        title: t('errors.error'),
        description: t('admin.recipient_address_required'),
        color: 'danger',
      });
      return;
    }

    // Validate wallet address format
    if (!recipientAddress.startsWith('0x') || recipientAddress.length !== 42) {
      addToast({
        title: t('errors.error'),
        description: t('admin.invalid_wallet_address'),
        color: 'danger',
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(`/api/v1/wallet/admin/${user.wallet}/withdraw/usdt`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: parseFloat(amount),
          recipientAddress: recipientAddress.trim()
        }),
      });

      if (response.ok) {
        const result = await response.json();
        addToast({
          title: t('common.success'),
          description: t('admin.withdraw_successful', { amount, address: recipientAddress }),
          color: 'success',
        });
        
        if (onWithdrawCompleted && result.data) {
          onWithdrawCompleted(result.data);
        }
        
        handleClose();
      } else {
        const error = await response.json();
        addToast({
          title: t('errors.error'),
          description: error.message?.en || error.message || t('admin.withdraw_failed'),
          color: 'danger',
        });
      }
    } catch (error) {
      console.error('Withdraw error:', error);
      addToast({
        title: t('errors.error'),
        description: t('admin.withdraw_failed'),
        color: 'danger',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setAmount('');
    setRecipientAddress('');
    setIsLoading(false);
    onClose();
  };

  // Get current USDT balance
  const getCurrentBalance = (): number => {
    if (!user?.tokenBalances) return 0;
    const tokenBalance = user.tokenBalances.find(tb => tb.token.symbol === 'USDT');
    return tokenBalance ? Number(tokenBalance.availableBalance) : 0;
  };

  const currentBalance = getCurrentBalance();

  if (!user) return null;

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose}
      size="sm"
      classNames={{
        base: "bg-white",
        header: "border-b border-gray-200",
        footer: "border-t border-gray-200",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex items-center gap-2">
          <Icon icon="solar:download-linear" width={20} className="text-danger-500" />
          <span>{t('admin.withdraw_usdt')}</span>
        </ModalHeader>
        
        <ModalBody className="py-4">
          <div className="space-y-4">
            {/* User Info */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700 mb-2">
                {t('admin.withdraw_from')}
              </div>
              <div className="space-y-1 text-sm">
                <div>
                  <span className="font-medium">{t('admin.wallet_address')}:</span>
                  <span className="ml-2 font-mono">{user.wallet.substring(0, 10)}...{user.wallet.substring(user.wallet.length - 8)}</span>
                </div>
                <div>
                  <span className="font-medium">{t('admin.current_balance')}:</span>
                  <span className="ml-2 font-bold text-green-600">
                    {currentBalance.toLocaleString()} USDT
                  </span>
                </div>
              </div>
            </div>

            {/* Amount Input */}
            <div>
              <Input
                type="number"
                label={t('admin.withdraw_amount')}
                placeholder={t('admin.enter_amount_placeholder', { type: 'USDT' })}
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                startContent={
                  <div className="flex items-center gap-1">
                    <Icon icon="solar:dollar-minimalistic-linear" width={16} />
                    <span className="text-sm font-medium">USDT</span>
                  </div>
                }
                classNames={{
                  input: "text-right",
                }}
              />
            </div>

            {/* Recipient Address Input */}
            <div>
              <Input
                label={t('admin.recipient_address')}
                placeholder={t('admin.enter_recipient_address')}
                value={recipientAddress}
                onChange={(e) => setRecipientAddress(e.target.value)}
                startContent={<Icon icon="solar:wallet-linear" width={16} />}
                classNames={{
                  input: "font-mono text-sm",
                }}
              />
            </div>

            {/* Warning */}
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start gap-2">
                <Icon icon="solar:danger-triangle-linear" width={16} className="text-red-600 mt-0.5" />
                <div className="text-sm text-red-800">
                  <div className="font-medium mb-1">{t('common.warning')}</div>
                  <div>{t('admin.withdraw_warning')}</div>
                </div>
              </div>
            </div>
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button 
            variant="light" 
            onPress={handleClose}
            isDisabled={isLoading}
          >
            {t('common.cancel')}
          </Button>
          <Button 
            color="danger"
            onPress={handleSubmit}
            isLoading={isLoading}
            startContent={!isLoading && <Icon icon="solar:download-linear" width={16} />}
          >
            {isLoading ? t('common.processing') : t('admin.withdraw_usdt')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
