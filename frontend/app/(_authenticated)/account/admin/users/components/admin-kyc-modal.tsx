'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  Button,
  Input,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';

interface AdminKycModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  onKycUpdated?: (updatedUser: User) => void;
}

export default function AdminKycModal({ isOpen, onClose, user, onKycUpdated }: AdminKycModalProps) {
  const { t } = useTranslation();
  const [transactionHash, setTransactionHash] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    if (!user) return;

    if (!transactionHash.trim()) {
      addToast({
        title: t('errors.error'),
        description: t('admin.transaction_hash_required'),
        color: 'danger',
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(`/api/v1/users/admin/kyc/${user.wallet}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionHash: transactionHash.trim()
        }),
      });

      if (response.ok) {
        const result = await response.json();
        addToast({
          title: t('common.success'),
          description: t('admin.kyc_updated_successfully'),
          color: 'success',
        });
        
        if (onKycUpdated && result.data) {
          onKycUpdated(result.data);
        }
        
        handleClose();
      } else {
        const error = await response.json();
        addToast({
          title: t('errors.error'),
          description: error.message?.en || error.message || t('admin.kyc_update_failed'),
          color: 'danger',
        });
      }
    } catch (error) {
      console.error('KYC update error:', error);
      addToast({
        title: t('errors.error'),
        description: t('admin.kyc_update_failed'),
        color: 'danger',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setTransactionHash('');
    setIsLoading(false);
    onClose();
  };

  if (!user) return null;

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose}
      size="sm"
      classNames={{
        base: "bg-white",
        header: "border-b border-gray-200",
        footer: "border-t border-gray-200",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex items-center gap-2">
          <Icon icon="solar:shield-check-linear" width={20} className="text-warning-500" />
          <span>{t('admin.update_kyc')}</span>
        </ModalHeader>
        
        <ModalBody className="py-4">
          <div className="space-y-4">
            {/* User Info */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700 mb-2">
                {t('admin.user_info')}
              </div>
              <div className="space-y-1 text-sm">
                <div>
                  <span className="font-medium">{t('admin.wallet_address')}:</span>
                  <span className="ml-2 font-mono">{user.wallet.substring(0, 10)}...{user.wallet.substring(user.wallet.length - 8)}</span>
                </div>
                <div>
                  <span className="font-medium">{t('admin.current_kyc_status')}:</span>
                  <span className={`ml-2 ${user.isKycCompleted ? 'text-green-600' : 'text-orange-600'}`}>
                    {user.isKycCompleted ? 'Verified' : 'Not Verified'}
                  </span>
                </div>
              </div>
            </div>

            {/* Transaction Hash Input */}
            <div>
              <Input
                label={t('admin.transaction_hash')}
                placeholder={t('admin.enter_transaction_hash')}
                value={transactionHash}
                onChange={(e) => setTransactionHash(e.target.value)}
                startContent={<Icon icon="solar:link-linear" width={16} />}
                classNames={{
                  input: "font-mono text-sm",
                }}
              />
            </div>

            {/* Warning */}
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start gap-2">
                <Icon icon="solar:danger-triangle-linear" width={16} className="text-yellow-600 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <div className="font-medium mb-1">{t('common.warning')}</div>
                  <div>{t('admin.kyc_update_warning')}</div>
                </div>
              </div>
            </div>
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button 
            variant="light" 
            onPress={handleClose}
            isDisabled={isLoading}
          >
            {t('common.cancel')}
          </Button>
          <Button 
            color="warning"
            onPress={handleSubmit}
            isLoading={isLoading}
            startContent={!isLoading && <Icon icon="solar:shield-check-linear" width={16} />}
          >
            {isLoading ? t('common.processing') : t('admin.update_kyc')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
