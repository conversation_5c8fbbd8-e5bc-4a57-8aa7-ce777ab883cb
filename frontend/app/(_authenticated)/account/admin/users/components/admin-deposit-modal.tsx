'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  Button,
  Input,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';

interface AdminDepositModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  type: 'USDT' | 'WM';
  onBalanceUpdated: (updatedUser: User) => void;
}

export default function AdminDepositModal({
  isOpen,
  onClose,
  user,
  type,
  onBalanceUpdated
}: AdminDepositModalProps) {
  const { t } = useTranslation();
  const [amount, setAmount] = useState('');
  const [transactionHash, setTransactionHash] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setAmount('');
      setTransactionHash('');
    }
  }, [isOpen]);

  // Get current balance
  const getCurrentBalance = (): number => {
    if (!user) return 0;
    const tokenBalance = user.tokenBalances?.find(tb => tb.token.symbol === type);
    return tokenBalance ? Number(tokenBalance.availableBalance) : 0;
  };

  const handleSubmit = async () => {
    if (!user || !amount || parseFloat(amount) <= 0) {
      addToast({
        title: t('errors.error'),
        description: t('admin.enter_valid_amount'),
        color: 'danger',
      });
      return;
    }

    if (!transactionHash || transactionHash.trim().length === 0) {
      addToast({
        title: t('errors.error'),
        description: t('admin.transaction_hash_required'),
        color: 'danger',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/v1/users/admin/${user.id}/deposit`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          amount: parseFloat(amount),
          transactionHash: transactionHash.trim()
        }),
      });

      if (response.ok) {
        const result = await response.json();
        onBalanceUpdated(result.data);

        addToast({
          title: t('common.success'),
          description: t('admin.deposit_successful', { amount, type }),
          color: 'success',
        });
        onClose();
      } else {
        const error = await response.json();
        addToast({
          title: t('errors.error'),
          description: error.message?.en || error.message || t('admin.deposit_failed'),
          color: 'danger',
        });
      }
    } catch (error) {
      console.error('Deposit error:', error);
      addToast({
        title: t('errors.error'),
        description: t('admin.deposit_failed'),
        color: 'danger',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!user) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="sm">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon
              icon={type === 'USDT' ? 'solar:dollar-minimalistic-linear' : 'solar:medal-ribbon-linear'}
              width={24}
              className={type === 'USDT' ? 'text-green-500' : 'text-blue-500'}
            />
            <h3 className="text-lg font-semibold">
              {t('admin.deposit')} {type}
            </h3>
          </div>
          <p className="text-sm text-gray-500">
            {user.name || user.username || `${user.wallet.substring(0, 6)}...${user.wallet.substring(user.wallet.length - 6)}`}
          </p>
        </ModalHeader>

        <ModalBody>
          <div className="flex flex-col gap-4">
            {/* Current Balance Info */}
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">{t('admin.current_balance')}</span>
              <span className={`text-sm font-bold ${type === 'USDT' ? 'text-green-600' : 'text-blue-600'}`}>
                {getCurrentBalance().toLocaleString()} {type}
              </span>
            </div>

            {/* Amount */}
            <Input
              type="number"
              label={`${t('admin.amount')} (${type})`}
              placeholder={t('admin.enter_amount_placeholder', { type })}
              value={amount}
              onValueChange={setAmount}
              startContent={
                <Icon
                  icon={type === 'USDT' ? 'solar:dollar-minimalistic-linear' : 'solar:medal-ribbon-linear'}
                  width={16}
                />
              }
            />

            {/* Transaction Hash */}
            <Input
              type="text"
              label={t('admin.transaction_hash')}
              placeholder={t('admin.enter_transaction_hash')}
              value={transactionHash}
              onValueChange={setTransactionHash}
              startContent={
                <Icon icon="solar:link-linear" width={16} />
              }
            />

            {/* Preview */}
            {amount && parseFloat(amount) > 0 && (
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-sm text-green-600 mb-1">
                  {t('admin.new_balance_preview')}
                </div>
                <div className="text-lg font-bold text-green-700">
                  {(getCurrentBalance() + parseFloat(amount)).toLocaleString()} {type}
                </div>
                <div className="text-xs text-green-500 mt-1">
                  +{parseFloat(amount).toLocaleString()} {type}
                </div>
              </div>
            )}

            {/* Warning */}
            <div className="flex flex-col gap-2 bg-warning-50 p-4 rounded-lg">
              <p className="text-sm font-medium text-warning-700">
                {t('admin.deposit_warning')}
              </p>
            </div>
          </div>
        </ModalBody>

        <ModalFooter className="flex flex-col gap-2">
          <Button
            color="primary"
            className="w-full"
            onPress={handleSubmit}
            isLoading={isSubmitting}
            isDisabled={!amount || parseFloat(amount) <= 0 || !transactionHash.trim()}
          >
            {t('admin.process_deposit')}
          </Button>
          <Button
            color="danger"
            variant="light"
            className="w-full"
            onPress={onClose}
            isDisabled={isSubmitting}
          >
            {t('common.cancel')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
