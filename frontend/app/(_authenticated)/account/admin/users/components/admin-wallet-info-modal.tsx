'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  <PERSON>dal<PERSON>ooter,
  Button,
  Card,
  CardBody,
  Chip,
  Divider,
  Spinner,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';

interface WalletInfo {
  id: string;
  address: string;
  type: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  tokenBalances: Array<{
    id: string;
    availableBalance: string;
    freezeBalance: string;
    token: {
      id: string;
      symbol: string;
      name: string;
      decimals: number;
    };
  }>;
}

interface AdminWalletInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
}

export default function AdminWalletInfoModal({ 
  isOpen, 
  onClose, 
  user 
}: AdminWalletInfoModalProps) {
  const { t } = useTranslation();
  const [walletInfo, setWalletInfo] = useState<WalletInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const fetchWalletInfo = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/v1/wallet/admin/${user.wallet}/me`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        setWalletInfo(result.data || result);
      } else {
        const error = await response.json();
        addToast({
          title: t('errors.error'),
          description: error.message?.en || error.message || t('admin.fetch_wallet_info_failed'),
          color: 'danger',
        });
      }
    } catch (error) {
      console.error('Fetch wallet info error:', error);
      addToast({
        title: t('errors.error'),
        description: t('admin.fetch_wallet_info_failed'),
        color: 'danger',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && user) {
      fetchWalletInfo();
    }
  }, [isOpen, user]);

  const handleClose = () => {
    setWalletInfo(null);
    onClose();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      addToast({
        title: t('common.success'),
        description: t('wallet.address_copied'),
        color: 'success',
      });
    } catch (error) {
      console.error('Failed to copy:', error);
      addToast({
        title: t('errors.error'),
        description: t('wallet.copy_failed'),
        color: 'danger',
      });
    }
  };

  if (!user) return null;

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose}
      size="2xl"
      classNames={{
        base: "bg-white",
        header: "border-b border-gray-200",
        footer: "border-t border-gray-200",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex items-center gap-2">
          <Icon icon="solar:wallet-linear" width={20} className="text-default-500" />
          <span>{t('admin.wallet_info')}</span>
        </ModalHeader>
        
        <ModalBody className="py-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Spinner size="lg" />
            </div>
          ) : walletInfo ? (
            <div className="space-y-4">
              {/* Basic Wallet Info */}
              <Card>
                <CardBody className="p-4">
                  <div className="text-sm font-medium text-gray-700 mb-3">
                    {t('admin.wallet_details')}
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{t('admin.wallet_id')}:</span>
                      <span className="text-sm font-mono">{walletInfo.id}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{t('admin.wallet_address')}:</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                          {walletInfo.address.substring(0, 10)}...{walletInfo.address.substring(walletInfo.address.length - 8)}
                        </span>
                        <Button
                          isIconOnly
                          variant="light"
                          size="sm"
                          onPress={() => copyToClipboard(walletInfo.address)}
                        >
                          <Icon icon="solar:copy-linear" width={16} />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{t('admin.wallet_type')}:</span>
                      <Chip size="sm" color="primary" variant="flat">
                        {walletInfo.type}
                      </Chip>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{t('admin.wallet_status')}:</span>
                      <Chip 
                        size="sm" 
                        color={walletInfo.isActive ? 'success' : 'danger'} 
                        variant="flat"
                      >
                        {walletInfo.isActive ? t('admin.active') : t('admin.inactive')}
                      </Chip>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{t('admin.created_at')}:</span>
                      <span className="text-sm">{formatDate(walletInfo.createdAt)}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{t('admin.updated_at')}:</span>
                      <span className="text-sm">{formatDate(walletInfo.updatedAt)}</span>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Token Balances */}
              <Card>
                <CardBody className="p-4">
                  <div className="text-sm font-medium text-gray-700 mb-3">
                    {t('admin.token_balances')}
                  </div>
                  
                  <div className="space-y-3">
                    {walletInfo.tokenBalances?.map((balance) => (
                      <div key={balance.id} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Icon 
                              icon={balance.token.symbol === 'USDT' ? 'solar:dollar-minimalistic-linear' : 'solar:medal-ribbon-linear'} 
                              width={20} 
                              className={balance.token.symbol === 'USDT' ? 'text-green-600' : 'text-blue-600'}
                            />
                            <span className="font-medium">{balance.token.symbol}</span>
                            <span className="text-sm text-gray-500">({balance.token.name})</span>
                          </div>
                          <Chip size="sm" variant="flat">
                            {balance.token.decimals} {t('admin.decimals')}
                          </Chip>
                        </div>
                        
                        <Divider className="my-2" />
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <div className="text-xs text-gray-500 mb-1">{t('admin.available_balance')}</div>
                            <div className="text-lg font-bold text-green-600">
                              {Number(balance.availableBalance).toLocaleString()} {balance.token.symbol}
                            </div>
                          </div>
                          <div>
                            <div className="text-xs text-gray-500 mb-1">{t('admin.freeze_balance')}</div>
                            <div className="text-lg font-bold text-orange-600">
                              {Number(balance.freezeBalance).toLocaleString()} {balance.token.symbol}
                            </div>
                          </div>
                        </div>
                        
                        <Divider className="my-2" />
                        
                        <div>
                          <div className="text-xs text-gray-500 mb-1">{t('admin.total_balance')}</div>
                          <div className="text-xl font-bold text-blue-600">
                            {(Number(balance.availableBalance) + Number(balance.freezeBalance)).toLocaleString()} {balance.token.symbol}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </div>
          ) : (
            <div className="text-center py-8">
              <Icon icon="solar:wallet-linear" width={48} className="text-gray-400 mx-auto mb-2" />
              <div className="text-gray-500">{t('admin.no_wallet_info')}</div>
            </div>
          )}
        </ModalBody>
        
        <ModalFooter>
          <Button 
            variant="light" 
            onPress={handleClose}
          >
            {t('common.close')}
          </Button>
          {walletInfo && (
            <Button 
              color="primary"
              onPress={fetchWalletInfo}
              startContent={<Icon icon="solar:refresh-linear" width={16} />}
            >
              {t('admin.refresh')}
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
