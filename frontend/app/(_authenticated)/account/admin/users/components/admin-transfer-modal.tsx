'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  <PERSON>dal<PERSON>oot<PERSON>,
  Button,
  Input,
  Select,
  SelectItem,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';

interface AdminTransferModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  type: 'USDT' | 'WM';
  onTransferCompleted?: (updatedUser: User) => void;
}

export default function AdminTransferModal({ 
  isOpen, 
  onClose, 
  user, 
  type, 
  onTransferCompleted 
}: AdminTransferModalProps) {
  const { t } = useTranslation();
  const [amount, setAmount] = useState('');
  const [referralCode, setReferralCode] = useState('');
  const [walletType, setWalletType] = useState<'available' | 'freeze'>('available');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    if (!user) return;

    if (!amount.trim() || parseFloat(amount) <= 0) {
      addToast({
        title: t('errors.error'),
        description: t('admin.enter_valid_amount'),
        color: 'danger',
      });
      return;
    }

    if (!referralCode.trim()) {
      addToast({
        title: t('errors.error'),
        description: t('admin.referral_code_required'),
        color: 'danger',
      });
      return;
    }

    setIsLoading(true);

    try {
      const endpoint = type === 'USDT' 
        ? `/api/v1/wallet/admin/${user.wallet}/transfer/usdt`
        : `/api/v1/wallet/admin/${user.wallet}/transfer/wm`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: parseFloat(amount),
          referralCode: referralCode.trim(),
          walletType
        }),
      });

      if (response.ok) {
        const result = await response.json();
        addToast({
          title: t('common.success'),
          description: t('admin.transfer_successful', { amount, type, referralCode }),
          color: 'success',
        });
        
        if (onTransferCompleted && result.data) {
          onTransferCompleted(result.data);
        }
        
        handleClose();
      } else {
        const error = await response.json();
        addToast({
          title: t('errors.error'),
          description: error.message?.en || error.message || t('admin.transfer_failed'),
          color: 'danger',
        });
      }
    } catch (error) {
      console.error('Transfer error:', error);
      addToast({
        title: t('errors.error'),
        description: t('admin.transfer_failed'),
        color: 'danger',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setAmount('');
    setReferralCode('');
    setWalletType('available');
    setIsLoading(false);
    onClose();
  };

  // Get current balance
  const getCurrentBalance = (): number => {
    if (!user?.tokenBalances) return 0;
    const tokenBalance = user.tokenBalances.find(tb => tb.token.symbol === type);
    return tokenBalance ? Number(tokenBalance.availableBalance) : 0;
  };

  const currentBalance = getCurrentBalance();

  if (!user) return null;

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose}
      size="sm"
      classNames={{
        base: "bg-white",
        header: "border-b border-gray-200",
        footer: "border-t border-gray-200",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex items-center gap-2">
          <Icon icon="solar:transfer-horizontal-linear" width={20} className="text-secondary-500" />
          <span>{t('admin.transfer_token', { type })}</span>
        </ModalHeader>
        
        <ModalBody className="py-4">
          <div className="space-y-4">
            {/* User Info */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700 mb-2">
                {t('admin.transfer_from')}
              </div>
              <div className="space-y-1 text-sm">
                <div>
                  <span className="font-medium">{t('admin.wallet_address')}:</span>
                  <span className="ml-2 font-mono">{user.wallet.substring(0, 10)}...{user.wallet.substring(user.wallet.length - 8)}</span>
                </div>
                <div>
                  <span className="font-medium">{t('admin.current_balance')}:</span>
                  <span className="ml-2 font-bold text-blue-600">
                    {currentBalance.toLocaleString()} {type}
                  </span>
                </div>
              </div>
            </div>

            {/* Amount Input */}
            <div>
              <Input
                type="number"
                label={t('admin.transfer_amount')}
                placeholder={t('admin.enter_amount_placeholder', { type })}
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                startContent={
                  <div className="flex items-center gap-1">
                    <Icon icon={type === 'USDT' ? 'solar:dollar-minimalistic-linear' : 'solar:medal-ribbon-linear'} width={16} />
                    <span className="text-sm font-medium">{type}</span>
                  </div>
                }
                classNames={{
                  input: "text-right",
                }}
              />
            </div>

            {/* Referral Code Input */}
            <div>
              <Input
                label={t('admin.recipient_referral_code')}
                placeholder={t('admin.enter_referral_code')}
                value={referralCode}
                onChange={(e) => setReferralCode(e.target.value)}
                startContent={<Icon icon="solar:user-linear" width={16} />}
                classNames={{
                  input: "font-mono",
                }}
              />
            </div>

            {/* Wallet Type Selection (for WM only) */}
            {type === 'WM' && (
              <div>
                <Select
                  label={t('admin.wallet_type')}
                  selectedKeys={[walletType]}
                  onSelectionChange={(keys) => setWalletType(Array.from(keys)[0] as 'available' | 'freeze')}
                >
                  <SelectItem key="available" value="available">
                    {t('admin.available_wallet')}
                  </SelectItem>
                  <SelectItem key="freeze" value="freeze">
                    {t('admin.freeze_wallet')}
                  </SelectItem>
                </Select>
              </div>
            )}

            {/* Warning */}
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start gap-2">
                <Icon icon="solar:danger-triangle-linear" width={16} className="text-yellow-600 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <div className="font-medium mb-1">{t('common.warning')}</div>
                  <div>{t('admin.transfer_warning')}</div>
                </div>
              </div>
            </div>
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button 
            variant="light" 
            onPress={handleClose}
            isDisabled={isLoading}
          >
            {t('common.cancel')}
          </Button>
          <Button 
            color="secondary"
            onPress={handleSubmit}
            isLoading={isLoading}
            startContent={!isLoading && <Icon icon="solar:transfer-horizontal-linear" width={16} />}
          >
            {isLoading ? t('common.processing') : t('admin.transfer_token', { type })}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
