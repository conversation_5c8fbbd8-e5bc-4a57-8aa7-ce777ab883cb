'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  Button,
  Input,
  Select,
  SelectItem,
  Textarea,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';

interface AdminAdjustWmModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  onAdjustCompleted?: (updatedUser: User) => void;
}

export default function AdminAdjustWmModal({ 
  isOpen, 
  onClose, 
  user, 
  onAdjustCompleted 
}: AdminAdjustWmModalProps) {
  const { t } = useTranslation();
  const [amount, setAmount] = useState('');
  const [adjustType, setAdjustType] = useState<'add' | 'subtract'>('add');
  const [note, setNote] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    if (!user) return;

    if (!amount.trim() || parseFloat(amount) <= 0) {
      addToast({
        title: t('errors.error'),
        description: t('admin.enter_valid_amount'),
        color: 'danger',
      });
      return;
    }

    // Check if subtract amount is not greater than current balance
    if (adjustType === 'subtract') {
      const currentBalance = getCurrentWmBalance();
      if (parseFloat(amount) > currentBalance) {
        addToast({
          title: t('errors.error'),
          description: t('admin.insufficient_balance_error'),
          color: 'danger',
        });
        return;
      }
    }

    setIsLoading(true);

    try {
      const response = await fetch(`/api/v1/wallet/admin/${user.wallet}/adjust/wm`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: parseFloat(amount),
          type: adjustType,
          note: note.trim() || `Admin ${adjustType} ${amount} WM`
        }),
      });

      if (response.ok) {
        const result = await response.json();
        addToast({
          title: t('common.success'),
          description: t('admin.adjust_wm_successful', { 
            type: adjustType === 'add' ? t('admin.added') : t('admin.subtracted'),
            amount 
          }),
          color: 'success',
        });
        
        if (onAdjustCompleted && result.data) {
          onAdjustCompleted(result.data);
        }
        
        handleClose();
      } else {
        const error = await response.json();
        addToast({
          title: t('errors.error'),
          description: error.message?.en || error.message || t('admin.adjust_wm_failed'),
          color: 'danger',
        });
      }
    } catch (error) {
      console.error('Adjust WM error:', error);
      addToast({
        title: t('errors.error'),
        description: t('admin.adjust_wm_failed'),
        color: 'danger',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setAmount('');
    setAdjustType('add');
    setNote('');
    setIsLoading(false);
    onClose();
  };

  // Get current WM balance
  const getCurrentWmBalance = (): number => {
    if (!user?.tokenBalances) return 0;
    const tokenBalance = user.tokenBalances.find(tb => tb.token.symbol === 'WM');
    return tokenBalance ? Number(tokenBalance.availableBalance) : 0;
  };

  // Calculate new balance preview
  const getNewBalancePreview = (): number => {
    const currentBalance = getCurrentWmBalance();
    const adjustAmount = parseFloat(amount) || 0;
    
    if (adjustType === 'add') {
      return currentBalance + adjustAmount;
    } else {
      return Math.max(0, currentBalance - adjustAmount);
    }
  };

  const currentBalance = getCurrentWmBalance();
  const newBalance = getNewBalancePreview();

  if (!user) return null;

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose}
      size="sm"
      classNames={{
        base: "bg-white",
        header: "border-b border-gray-200",
        footer: "border-t border-gray-200",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex items-center gap-2">
          <Icon icon="solar:settings-linear" width={20} className="text-primary-500" />
          <span>{t('admin.adjust_wm')}</span>
        </ModalHeader>
        
        <ModalBody className="py-4">
          <div className="space-y-4">
            {/* User Info */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700 mb-2">
                {t('admin.user_info')}
              </div>
              <div className="space-y-1 text-sm">
                <div>
                  <span className="font-medium">{t('admin.wallet_address')}:</span>
                  <span className="ml-2 font-mono">{user.wallet.substring(0, 10)}...{user.wallet.substring(user.wallet.length - 8)}</span>
                </div>
                <div>
                  <span className="font-medium">{t('admin.current_balance')}:</span>
                  <span className="ml-2 font-bold text-blue-600">
                    {currentBalance.toLocaleString()} WM
                  </span>
                </div>
              </div>
            </div>

            {/* Adjust Type Selection */}
            <div>
              <Select
                label={t('admin.operation_type')}
                selectedKeys={[adjustType]}
                onSelectionChange={(keys) => setAdjustType(Array.from(keys)[0] as 'add' | 'subtract')}
              >
                <SelectItem key="add" value="add">
                  <div className="flex items-center gap-2">
                    <Icon icon="solar:add-circle-linear" width={16} className="text-green-600" />
                    <span>{t('admin.add')}</span>
                  </div>
                </SelectItem>
                <SelectItem key="subtract" value="subtract">
                  <div className="flex items-center gap-2">
                    <Icon icon="solar:minus-circle-linear" width={16} className="text-red-600" />
                    <span>{t('admin.subtract')}</span>
                  </div>
                </SelectItem>
              </Select>
            </div>

            {/* Amount Input */}
            <div>
              <Input
                type="number"
                label={t('admin.adjust_amount')}
                placeholder={t('admin.enter_amount_placeholder', { type: 'WM' })}
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                startContent={
                  <div className="flex items-center gap-1">
                    <Icon 
                      icon={adjustType === 'add' ? 'solar:add-circle-linear' : 'solar:minus-circle-linear'} 
                      width={16} 
                      className={adjustType === 'add' ? 'text-green-600' : 'text-red-600'}
                    />
                    <span className="text-sm font-medium">WM</span>
                  </div>
                }
                classNames={{
                  input: "text-right",
                }}
              />
            </div>

            {/* Balance Preview */}
            {amount && parseFloat(amount) > 0 && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="text-sm font-medium text-blue-800 mb-2">
                  {t('admin.new_balance_preview')}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-blue-700">{t('admin.current_balance')}:</span>
                  <span className="font-mono text-blue-900">{currentBalance.toLocaleString()} WM</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-blue-700">
                    {adjustType === 'add' ? t('admin.add') : t('admin.subtract')}:
                  </span>
                  <span className={`font-mono ${adjustType === 'add' ? 'text-green-600' : 'text-red-600'}`}>
                    {adjustType === 'add' ? '+' : '-'}{parseFloat(amount).toLocaleString()} WM
                  </span>
                </div>
                <div className="border-t border-blue-300 mt-2 pt-2 flex items-center justify-between">
                  <span className="text-sm font-medium text-blue-800">{t('admin.new_balance')}:</span>
                  <span className="font-mono font-bold text-blue-900">{newBalance.toLocaleString()} WM</span>
                </div>
              </div>
            )}

            {/* Note Input */}
            <div>
              <Textarea
                label={t('admin.note')}
                placeholder={t('admin.enter_note_optional')}
                value={note}
                onChange={(e) => setNote(e.target.value)}
                minRows={2}
                maxRows={4}
              />
            </div>

            {/* Warning */}
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start gap-2">
                <Icon icon="solar:danger-triangle-linear" width={16} className="text-yellow-600 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <div className="font-medium mb-1">{t('common.warning')}</div>
                  <div>{t('admin.adjust_wm_warning')}</div>
                </div>
              </div>
            </div>
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button 
            variant="light" 
            onPress={handleClose}
            isDisabled={isLoading}
          >
            {t('common.cancel')}
          </Button>
          <Button 
            color="primary"
            onPress={handleSubmit}
            isLoading={isLoading}
            startContent={!isLoading && <Icon icon="solar:settings-linear" width={16} />}
          >
            {isLoading ? t('common.processing') : t('admin.adjust_wm')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
