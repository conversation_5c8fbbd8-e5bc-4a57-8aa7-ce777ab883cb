'use client';

import React from 'react';
import {
  Card,
  CardBody,
  Button,
  Chip,
  Avatar,
  Divider,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';

interface UserDetailsCardProps {
  user: User;
  onDeposit: (type: 'USDT' | 'WM') => void;
  onSetBalance: (type: 'USDT' | 'WM') => void;
  onUpdateKyc: () => void;
  onTransfer: (type: 'USDT' | 'WM') => void;
  onWithdraw: () => void;
  onViewTransactions: () => void;
  onViewWalletInfo: () => void;
  onAdjustWm: () => void;
  onLockAccount: () => void;
  onUnlockAccount: () => void;
  onUpdateRole: () => void;
  onUpdateRank: () => void;
  onResetPassword: () => void;
  onViewReferrals: () => void;
  onViewStaking: () => void;
  onSendNotification: () => void;
}

export default function UserDetailsCard({
  user,
  onDeposit,
  onSetBalance,
  onUpdateKyc,
  onTransfer,
  onWithdraw,
  onViewTransactions,
  onViewWalletInfo,
  onAdjustWm,
  onLockAccount,
  onUnlockAccount,
  onUpdateRole,
  onUpdateRank,
  onResetPassword,
  onViewReferrals,
  onViewStaking,
  onSendNotification
}: UserDetailsCardProps) {
  const { t } = useTranslation();

  // Shorten wallet address for display
  const shortenAddress = (address: string): string => {
    if (!address || address.length < 10) return address;
    return `${address.substring(0, 6)}...${address.substring(address.length - 6)}`;
  };

  // Handle copy wallet address
  const handleCopyWallet = async (walletAddress: string) => {
    try {
      await navigator.clipboard.writeText(walletAddress);
      addToast({
        title: t('common.success'),
        description: t('wallet.address_copied'),
        color: 'success',
      });
    } catch (error) {
      console.error('Failed to copy wallet address:', error);
      addToast({
        title: t('errors.error'),
        description: t('wallet.copy_failed'),
        color: 'danger',
      });
    }
  };

  // Get user status color
  const getUserStatusColor = () => {
    if (user.isKycCompleted) return 'success';
    if (user.firstDepositTime) return 'warning';
    return 'default';
  };

  // Get user status text
  const getUserStatusText = () => {
    if (user.isKycCompleted) return 'KYC Verified';
    if (user.firstDepositTime) return 'Active';
    return 'New User';
  };

  // Get detailed balance from tokenBalances
  const getTokenBalanceDetails = (symbol: string) => {
    const tokenBalance = user.tokenBalances?.find(tb => tb.token.symbol === symbol);
    if (!tokenBalance) {
      return {
        available: 0,
        locked: 0,
        total: 0
      };
    }
    return {
      available: Number(tokenBalance.availableBalance) || 0,
      locked: Number(tokenBalance.lockedBalance) || 0,
      total: Number(tokenBalance.totalBalance) || 0
    };
  };

  const usdtBalanceDetails = getTokenBalanceDetails('USDT');
  const wmBalanceDetails = getTokenBalanceDetails('WM');

  return (
    <div className="flex flex-col gap-4">
      {/* User Info Card */}
      <Card className="w-full">
        <CardBody className="p-4">
          <div className="flex items-center gap-4 mb-4">
            <Avatar
              size="lg"
              name={user.name || user.username || shortenAddress(user.wallet)}
              className="flex-shrink-0 ring-2 ring-blue-100"
            />
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="text-xl font-bold text-gray-900">
                  {user.name || user.username || t('admin.anonymous_user')}
                </h3>
                <Chip color={getUserStatusColor()} size="sm" variant="flat">
                  {getUserStatusText()}
                </Chip>
              </div>

              {/* Status Indicators */}
              <div className="flex flex-wrap gap-2 mb-2">
                <Chip
                  size="sm"
                  color={user.role === 'ADMIN' ? 'danger' : 'primary'}
                  variant="flat"
                  startContent={<Icon icon={user.role === 'ADMIN' ? 'solar:crown-bold' : 'solar:user-bold'} width={12} />}
                >
                  {t(`admin.role_${user.role.toLowerCase()}`)}
                </Chip>

                <Chip
                  size="sm"
                  color="secondary"
                  variant="flat"
                  startContent={<Icon icon="solar:medal-ribbon-bold" width={12} />}
                >
                  {user.wManagerRank || 'N/A'}
                </Chip>


                {/* Account Status */}
                {user.isLocked && (
                  <Chip
                    size="sm"
                    color="danger"
                    variant="flat"
                    startContent={<Icon icon="solar:lock-bold" width={12} />}
                  >
                    {t('admin.locked')}
                  </Chip>
                )}

                {/* Special Features */}
                {user.hasLightningBolt && (
                  <Chip
                    size="sm"
                    color="warning"
                    variant="flat"
                    startContent={<Icon icon="solar:bolt-bold" width={12} />}
                  >
                    {t('admin.lightning_bolt')}
                  </Chip>
                )}

                {user.isPriorityBuy && (
                  <Chip
                    size="sm"
                    color="success"
                    variant="flat"
                    startContent={<Icon icon="solar:star-bold" width={12} />}
                  >
                    {t('admin.priority_buy')}
                  </Chip>
                )}

                {user.isShareholder && (
                  <Chip
                    size="sm"
                    color="primary"
                    variant="flat"
                    startContent={<Icon icon="solar:chart-square-bold" width={12} />}
                  >
                    {t('admin.shareholder')}
                  </Chip>
                )}

                {user.stakingActive && (
                  <Chip
                    size="sm"
                    color="secondary"
                    variant="flat"
                    startContent={<Icon icon="solar:safe-square-bold" width={12} />}
                  >
                    {t('admin.staking_active')}
                  </Chip>
                )}
              </div>

              <div className="text-sm text-gray-500">
                <span className="font-medium">{t('admin.user_id')}:</span>
                <span className="font-mono ml-1">{user.id.substring(0, 8)}...{user.id.substring(user.id.length - 8)}</span>
              </div>
            </div>
          </div>

          <Divider className="my-3" />

          {/* Basic Information */}
          <div className="space-y-3">
            <div className="text-sm font-medium text-gray-700 mb-2">
              {t('admin.basic_info')}
            </div>

            <div className="grid grid-cols-1 gap-3">
              <div>
                <span className="text-sm font-medium">{t('admin.wallet_address')}:</span>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                    {shortenAddress(user.wallet)}
                  </span>
                  <Button
                    isIconOnly
                    variant="light"
                    size="sm"
                    onPress={() => handleCopyWallet(user.wallet)}
                  >
                    <Icon icon="solar:copy-linear" width={16} />
                  </Button>
                </div>
              </div>

              <div>
                <span className="text-sm font-medium">{t('admin.referral_code')}:</span>
                <span className="text-sm ml-2 font-mono bg-gray-100 px-2 py-1 rounded">
                  {user.referralCode}
                </span>
              </div>

              {user.email && (
                <div>
                  <span className="text-sm font-medium">{t('admin.email')}:</span>
                  <span className="text-sm ml-2">{user.email}</span>
                </div>
              )}

              {user.phone && (
                <div>
                  <span className="text-sm font-medium">{t('admin.phone')}:</span>
                  <span className="text-sm ml-2">{user.phone}</span>
                </div>
              )}

              <div>
                <span className="text-sm font-medium">{t('admin.role')}:</span>
                <Chip color={user.role === 'ADMIN' ? 'danger' : 'primary'} size="sm" variant="flat" className="ml-2">
                  {user.role}
                </Chip>
              </div>

              <div>
                <span className="text-sm font-medium">{t('admin.wmanager_rank')}:</span>
                <Chip color="secondary" size="sm" variant="flat" className="ml-2">
                  {user.wManagerRank || 'N/A'}
                </Chip>
              </div>

              <div>
                <span className="text-sm font-medium">{t('admin.joined_date')}:</span>
                <span className="text-sm ml-2">
                  {new Date(user.createdAt).toLocaleString('vi-VN')}
                </span>
              </div>

              {user.firstDepositTime && (
                <div>
                  <span className="text-sm font-medium">{t('admin.first_deposit')}:</span>
                  <span className="text-sm ml-2">
                    {new Date(user.firstDepositTime).toLocaleString('vi-VN')}
                  </span>
                </div>
              )}
            </div>
          </div>
        </CardBody>
      </Card>

      {/* User Statistics Card */}
      <Card className="w-full">
        <CardBody className="p-4">
          <div className="text-sm font-medium text-gray-700 mb-3">
            {t('admin.user_statistics')}
          </div>

          <div className="grid grid-cols-2 gap-4">
            {/* Account Age */}
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Icon icon="solar:calendar-linear" width={16} className="text-blue-600" />
                <span className="text-sm font-medium text-blue-800">{t('admin.account_age')}</span>
              </div>
              <div className="text-lg font-bold text-blue-600">
                {Math.floor((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24))} {t('admin.days')}
              </div>
            </div>

            {/* Account Status */}
            <div className={`p-3 rounded-lg ${user.isLocked ? 'bg-red-50' : 'bg-green-50'}`}>
              <div className="flex items-center gap-2 mb-1">
                <Icon icon={user.isLocked ? 'solar:lock-linear' : 'solar:check-circle-linear'} width={16} className={user.isLocked ? 'text-red-600' : 'text-green-600'} />
                <span className={`text-sm font-medium ${user.isLocked ? 'text-red-800' : 'text-green-800'}`}>
                  {t('admin.account_status')}
                </span>
              </div>
              <div className={`text-lg font-bold ${user.isLocked ? 'text-red-600' : 'text-green-600'}`}>
                {user.isLocked ? t('admin.locked') : t('admin.active')}
              </div>
            </div>

            {/* KYC Status */}
            <div className={`p-3 rounded-lg ${user.isKycCompleted ? 'bg-green-50' : 'bg-orange-50'}`}>
              <div className="flex items-center gap-2 mb-1">
                <Icon icon="solar:shield-check-linear" width={16} className={user.isKycCompleted ? 'text-green-600' : 'text-orange-600'} />
                <span className={`text-sm font-medium ${user.isKycCompleted ? 'text-green-800' : 'text-orange-800'}`}>
                  {t('admin.kyc_status')}
                </span>
              </div>
              <div className={`text-lg font-bold ${user.isKycCompleted ? 'text-green-600' : 'text-orange-600'}`}>
                {user.isKycCompleted ? t('admin.verified') : t('admin.pending')}
              </div>
            </div>

            {/* Mining Status */}
            <div className="p-3 bg-yellow-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Icon icon="solar:medal-ribbon-linear" width={16} className="text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800">{t('admin.mining_status')}</span>
              </div>
              <div className="text-lg font-bold text-yellow-600">
                {user.totalMined?.toLocaleString() || 0} WM
              </div>
              <div className="text-xs text-yellow-600">
                {t('admin.multiplier')}: {user.miningMultiplier}x
              </div>
            </div>

            {/* Total Balance Value */}
            <div className="p-3 bg-purple-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Icon icon="solar:wallet-money-linear" width={16} className="text-purple-600" />
                <span className="text-sm font-medium text-purple-800">{t('admin.total_value')}</span>
              </div>
              <div className="text-lg font-bold text-purple-600">
                ${(usdtBalanceDetails.total + wmBalanceDetails.total * 0.1).toLocaleString()}
              </div>
            </div>

            {/* Staking Status */}
            {user.stakingActive && (
              <div className="p-3 bg-indigo-50 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <Icon icon="solar:safe-square-linear" width={16} className="text-indigo-600" />
                  <span className="text-sm font-medium text-indigo-800">{t('admin.staking_status')}</span>
                </div>
                <div className="text-lg font-bold text-indigo-600">
                  ${user.stakingTotalInvestment?.toLocaleString() || 0}
                </div>
                <div className="text-xs text-indigo-600">
                  {t('admin.earnings')}: ${user.stakingTotalEarnings?.toLocaleString() || 0}
                </div>
              </div>
            )}

            {/* Referral Code */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Icon icon="solar:users-group-rounded-linear" width={16} className="text-gray-600" />
                <span className="text-sm font-medium text-gray-800">{t('admin.referral_code')}</span>
              </div>
              <div className="text-lg font-bold text-gray-600 font-mono">
                {user.referralCode}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Account Balance Card */}
      <Card className="w-full">
        <CardBody className="p-4">
          <div className="text-sm font-medium text-gray-700 mb-3">
            {t('admin.account_balance')}
          </div>

          <div className="space-y-3">
            {/* USDT Balance */}
            <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <Icon icon="solar:dollar-minimalistic-bold" width={20} className="text-green-600" />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-green-800">USDT {t('admin.balance')}</div>
                    <div className="text-2xl font-bold text-green-600">
                      {usdtBalanceDetails.total.toLocaleString()}
                    </div>
                    <div className="text-xs text-green-600">
                      ≈ ${usdtBalanceDetails.total.toLocaleString()} USD
                    </div>
                  </div>
                </div>
                <div className="text-right space-y-1">
                  <div>
                    <div className="text-xs text-green-600">{t('admin.available')}</div>
                    <div className="text-sm font-bold text-green-700">{usdtBalanceDetails.available.toLocaleString()}</div>
                  </div>
                  {usdtBalanceDetails.locked > 0 && (
                    <div>
                      <div className="text-xs text-orange-600">{t('admin.locked')}</div>
                      <div className="text-sm font-bold text-orange-700">{usdtBalanceDetails.locked.toLocaleString()}</div>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  color="success"
                  variant="flat"
                  onPress={() => onDeposit('USDT')}
                  startContent={<Icon icon="solar:add-circle-linear" width={16} />}
                  className="flex-1"
                >
                  {t('admin.deposit')}
                </Button>
                <Button
                  size="sm"
                  color="primary"
                  variant="flat"
                  onPress={() => onSetBalance('USDT')}
                  startContent={<Icon icon="solar:settings-linear" width={16} />}
                  className="flex-1"
                >
                  {t('admin.set_balance')}
                </Button>
              </div>
            </div>

            {/* WM Balance */}
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Icon icon="solar:medal-ribbon-bold" width={20} className="text-blue-600" />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-blue-800">WM {t('admin.balance')}</div>
                    <div className="text-2xl font-bold text-blue-600">
                      {wmBalanceDetails.total.toLocaleString()}
                    </div>
                    <div className="text-xs text-blue-600">
                      ≈ ${(wmBalanceDetails.total * 0.1).toLocaleString()} USD
                    </div>
                  </div>
                </div>
                <div className="text-right space-y-1">
                  <div>
                    <div className="text-xs text-blue-600">{t('admin.available')}</div>
                    <div className="text-sm font-bold text-blue-700">{wmBalanceDetails.available.toLocaleString()}</div>
                  </div>
                  {wmBalanceDetails.locked > 0 && (
                    <div>
                      <div className="text-xs text-orange-600">{t('admin.locked')}</div>
                      <div className="text-sm font-bold text-orange-700">{wmBalanceDetails.locked.toLocaleString()}</div>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  color="success"
                  variant="flat"
                  onPress={() => onDeposit('WM')}
                  startContent={<Icon icon="solar:add-circle-linear" width={16} />}
                  className="flex-1"
                >
                  {t('admin.deposit')}
                </Button>
                <Button
                  size="sm"
                  color="primary"
                  variant="flat"
                  onPress={() => onSetBalance('WM')}
                  startContent={<Icon icon="solar:settings-linear" width={16} />}
                  className="flex-1"
                >
                  {t('admin.set_balance')}
                </Button>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Extended Information Card */}
      <Card className="w-full">
        <CardBody className="p-4">
          <div className="text-sm font-medium text-gray-700 mb-3">
            {t('admin.extended_info')}
          </div>

          <div className="grid grid-cols-1 gap-3">
            {/* Contact Information */}
            {(user.email || user.phone) && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="text-sm font-medium text-gray-700 mb-2">
                  {t('admin.contact_info')}
                </div>
                <div className="space-y-2">
                  {user.email && (
                    <div className="flex items-center gap-2">
                      <Icon icon="solar:letter-linear" width={16} className="text-gray-500" />
                      <span className="text-sm">{user.email}</span>
                    </div>
                  )}
                  {user.phone && (
                    <div className="flex items-center gap-2">
                      <Icon icon="solar:phone-linear" width={16} className="text-gray-500" />
                      <span className="text-sm">{user.phone}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Account Timeline */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700 mb-2">
                {t('admin.account_timeline')}
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Icon icon="solar:calendar-add-linear" width={16} className="text-blue-500" />
                  <span className="text-sm">
                    <span className="font-medium">{t('admin.joined')}:</span> {new Date(user.createdAt).toLocaleDateString('vi-VN')}
                  </span>
                </div>
                {user.firstDepositTime && (
                  <div className="flex items-center gap-2">
                    <Icon icon="solar:wallet-money-linear" width={16} className="text-green-500" />
                    <span className="text-sm">
                      <span className="font-medium">{t('admin.first_deposit')}:</span> {new Date(user.firstDepositTime).toLocaleDateString('vi-VN')}
                    </span>
                  </div>
                )}
                {user.updatedAt && (
                  <div className="flex items-center gap-2">
                    <Icon icon="solar:refresh-linear" width={16} className="text-orange-500" />
                    <span className="text-sm">
                      <span className="font-medium">{t('admin.last_updated')}:</span> {new Date(user.updatedAt).toLocaleDateString('vi-VN')}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Referral Information */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700 mb-2">
                {t('admin.referral_info')}
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Icon icon="solar:link-linear" width={16} className="text-purple-500" />
                  <span className="text-sm">
                    <span className="font-medium">{t('admin.referral_code')}:</span>
                    <span className="font-mono ml-1 bg-white px-2 py-1 rounded border">{user.referralCode}</span>
                  </span>
                </div>
                {user.referredBy && (
                  <div className="flex items-center gap-2">
                    <Icon icon="solar:user-plus-linear" width={16} className="text-indigo-500" />
                    <span className="text-sm">
                      <span className="font-medium">{t('admin.referred_by')}:</span> {user.referredBy}
                    </span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <Icon icon="solar:users-group-two-rounded-linear" width={16} className="text-green-500" />
                  <span className="text-sm">
                    <span className="font-medium">{t('admin.total_referrals')}:</span> {user.referrals?.length || 0}
                  </span>
                </div>
              </div>
            </div>

            {/* Special Features */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700 mb-2">
                {t('admin.special_features')}
              </div>
              <div className="space-y-2">
                {user.hasLightningBolt && (
                  <div className="flex items-center gap-2">
                    <Icon icon="solar:bolt-linear" width={16} className="text-yellow-500" />
                    <span className="text-sm">
                      <span className="font-medium">{t('admin.lightning_bolt')}:</span>
                      <span className="ml-1">{t('admin.purchased_on')} {user.lightningBoltPurchaseDate ? new Date(user.lightningBoltPurchaseDate).toLocaleDateString('vi-VN') : 'N/A'}</span>
                    </span>
                  </div>
                )}
                {user.isPriorityBuy && (
                  <div className="flex items-center gap-2">
                    <Icon icon="solar:star-linear" width={16} className="text-orange-500" />
                    <span className="text-sm">
                      <span className="font-medium">{t('admin.priority_buy')}:</span> {t('admin.enabled')}
                    </span>
                  </div>
                )}
                {user.isShareholder && (
                  <div className="flex items-center gap-2">
                    <Icon icon="solar:chart-square-linear" width={16} className="text-blue-500" />
                    <span className="text-sm">
                      <span className="font-medium">{t('admin.shareholder')}:</span> {t('admin.active')}
                    </span>
                  </div>
                )}
                {user.hasMinDeposit && (
                  <div className="flex items-center gap-2">
                    <Icon icon="solar:wallet-check-linear" width={16} className="text-green-500" />
                    <span className="text-sm">
                      <span className="font-medium">{t('admin.min_deposit')}:</span> {t('admin.completed')}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Completion Status */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700 mb-2">
                {t('admin.completion_status')}
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center gap-2">
                  <Icon
                    icon={user.isFirstProfileCompleted ? "solar:check-circle-bold" : "solar:close-circle-bold"}
                    width={16}
                    className={user.isFirstProfileCompleted ? "text-green-500" : "text-red-500"}
                  />
                  <span className="text-sm">{t('admin.first_profile')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Icon
                    icon={user.isKycCompleted ? "solar:check-circle-bold" : "solar:close-circle-bold"}
                    width={16}
                    className={user.isKycCompleted ? "text-green-500" : "text-red-500"}
                  />
                  <span className="text-sm">{t('admin.kyc_completed')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Icon
                    icon={user.isInviteCompleted ? "solar:check-circle-bold" : "solar:close-circle-bold"}
                    width={16}
                    className={user.isInviteCompleted ? "text-green-500" : "text-red-500"}
                  />
                  <span className="text-sm">{t('admin.invite_completed')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Icon
                    icon={user.isNftCompleted ? "solar:check-circle-bold" : "solar:close-circle-bold"}
                    width={16}
                    className={user.isNftCompleted ? "text-green-500" : "text-red-500"}
                  />
                  <span className="text-sm">{t('admin.nft_completed')}</span>
                </div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Admin Action Buttons Section */}
      <Card className="w-full">
        <CardBody className="p-4">
          <div className="text-sm font-medium text-gray-700 mb-3">
            {t('admin.admin_actions')}
          </div>

          <div className="space-y-4">
            {/* Account Management */}
            <div>
              <div className="text-xs font-medium text-gray-500 mb-2 uppercase tracking-wide">
                {t('admin.account_management')}
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  color="warning"
                  variant="flat"
                  onPress={onUpdateKyc}
                  startContent={<Icon icon="solar:shield-check-linear" width={16} />}
                  className="justify-start h-12"
                >
                  {t('admin.update_kyc')}
                </Button>
                <Button
                  color="default"
                  variant="flat"
                  onPress={onViewWalletInfo}
                  startContent={<Icon icon="solar:wallet-linear" width={16} />}
                  className="justify-start h-12"
                >
                  {t('admin.wallet_info')}
                </Button>

                {/* Account Lock/Unlock */}
                {user.isLocked ? (
                  <Button
                    color="success"
                    variant="flat"
                    onPress={onUnlockAccount}
                    startContent={<Icon icon="solar:lock-unlocked-linear" width={16} />}
                    className="justify-start h-12"
                  >
                    {t('admin.unlock_account')}
                  </Button>
                ) : (
                  <Button
                    color="danger"
                    variant="flat"
                    onPress={onLockAccount}
                    startContent={<Icon icon="solar:lock-linear" width={16} />}
                    className="justify-start h-12"
                  >
                    {t('admin.lock_account')}
                  </Button>
                )}

                <Button
                  color="primary"
                  variant="flat"
                  onPress={onUpdateRole}
                  startContent={<Icon icon="solar:user-id-linear" width={16} />}
                  className="justify-start h-12"
                >
                  {t('admin.update_role')}
                </Button>

                <Button
                  color="secondary"
                  variant="flat"
                  onPress={onUpdateRank}
                  startContent={<Icon icon="solar:medal-ribbon-linear" width={16} />}
                  className="justify-start h-12"
                >
                  {t('admin.update_wmanager_rank')}
                </Button>

                <Button
                  color="warning"
                  variant="flat"
                  onPress={onResetPassword}
                  startContent={<Icon icon="solar:key-linear" width={16} />}
                  className="justify-start h-12"
                >
                  {t('admin.reset_password')}
                </Button>
              </div>
            </div>

            {/* Financial Operations */}
            <div>
              <div className="text-xs font-medium text-gray-500 mb-2 uppercase tracking-wide">
                {t('admin.financial_operations')}
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  color="secondary"
                  variant="flat"
                  onPress={() => onTransfer('USDT')}
                  startContent={<Icon icon="solar:transfer-horizontal-linear" width={16} />}
                  className="justify-start h-12"
                >
                  {t('admin.transfer_usdt')}
                </Button>
                <Button
                  color="secondary"
                  variant="flat"
                  onPress={() => onTransfer('WM')}
                  startContent={<Icon icon="solar:transfer-horizontal-linear" width={16} />}
                  className="justify-start h-12"
                >
                  {t('admin.transfer_wm')}
                </Button>
                <Button
                  color="danger"
                  variant="flat"
                  onPress={onWithdraw}
                  startContent={<Icon icon="solar:download-linear" width={16} />}
                  className="justify-start h-12"
                >
                  {t('admin.withdraw_usdt')}
                </Button>
                <Button
                  color="primary"
                  variant="flat"
                  onPress={onAdjustWm}
                  startContent={<Icon icon="solar:settings-linear" width={16} />}
                  className="justify-start h-12"
                >
                  {t('admin.adjust_wm')}
                </Button>
              </div>
            </div>

            {/* User Analytics & Management */}
            <div>
              <div className="text-xs font-medium text-gray-500 mb-2 uppercase tracking-wide">
                {t('admin.user_analytics')}
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  color="default"
                  variant="flat"
                  onPress={onViewTransactions}
                  startContent={<Icon icon="solar:history-linear" width={16} />}
                  className="justify-start h-12"
                >
                  {t('admin.transaction_history')}
                </Button>

                <Button
                  color="secondary"
                  variant="flat"
                  onPress={onViewReferrals}
                  startContent={<Icon icon="solar:users-group-rounded-linear" width={16} />}
                  className="justify-start h-12"
                >
                  {t('admin.view_referrals')}
                </Button>

                <Button
                  color="primary"
                  variant="flat"
                  onPress={onViewStaking}
                  startContent={<Icon icon="solar:safe-square-linear" width={16} />}
                  className="justify-start h-12"
                >
                  {t('admin.view_staking')}
                </Button>

                <Button
                  color="warning"
                  variant="flat"
                  onPress={onSendNotification}
                  startContent={<Icon icon="solar:bell-linear" width={16} />}
                  className="justify-start h-12"
                >
                  {t('admin.send_notification')}
                </Button>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
