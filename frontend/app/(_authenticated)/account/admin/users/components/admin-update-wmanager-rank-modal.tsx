'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON>dalHeader,
  ModalBody,
  ModalFooter,
  Button,
  Select,
  SelectItem,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User, WManagerRank } from '@/types/models';

interface AdminUpdateWManagerRankModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  onRankUpdated: (updatedUser: User) => void;
}

export default function AdminUpdateWManagerRankModal({
  isOpen,
  onClose,
  user,
  onRankUpdated
}: AdminUpdateWManagerRankModalProps) {
  const { t } = useTranslation();
  const [selectedRank, setSelectedRank] = useState<WManagerRank | ''>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (isOpen && user) {
      setSelectedRank(user.wManagerRank || '');
    }
  }, [isOpen, user]);

  const handleSubmit = async () => {
    if (!user || !selectedRank) return;

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/v1/users/admin/${user.id}/wmanager-rank`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          wManagerRank: selectedRank
        }),
      });

      if (response.ok) {
        const result = await response.json();
        onRankUpdated(result.data);
        addToast({
          title: t('common.success'),
          description: t('admin.wmanager_rank_updated_successfully', { rank: selectedRank }),
          color: 'success',
        });
        onClose();
      } else {
        const error = await response.json();
        addToast({
          title: t('errors.error'),
          description: error.message || t('admin.wmanager_rank_update_failed'),
          color: 'danger',
        });
      }
    } catch (error) {
      console.error('Update wManagerRank error:', error);
      addToast({
        title: t('errors.error'),
        description: t('admin.wmanager_rank_update_failed'),
        color: 'danger',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setSelectedRank('');
    onClose();
  };

  if (!user) return null;

  const wManagerRankOptions = [
    { key: '', label: t('admin.no_rank') },
    { key: WManagerRank.WM1, label: 'WM1' },
    { key: WManagerRank.WM2, label: 'WM2' },
    { key: WManagerRank.WM3, label: 'WM3' },
    { key: WManagerRank.WM4, label: 'WM4' },
    { key: WManagerRank.WM5, label: 'WM5' },
  ];

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="sm">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon
              icon="solar:medal-ribbon-linear"
              width={24}
              className="text-purple-500"
            />
            <h3 className="text-lg font-semibold">
              {t('admin.update_wmanager_rank')}
            </h3>
          </div>
          <p className="text-sm text-gray-500">
            {user.name || user.username || `${user.wallet.substring(0, 6)}...${user.wallet.substring(user.wallet.length - 6)}`}
          </p>
        </ModalHeader>

        <ModalBody>
          <div className="flex flex-col gap-4">
            {/* Current Rank Info */}
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">{t('admin.current_wmanager_rank')}</span>
              <span className="text-sm font-bold text-purple-600">
                {user.wManagerRank || t('admin.no_rank')}
              </span>
            </div>

            {/* New Rank Selection */}
            <Select
              label={t('admin.new_wmanager_rank')}
              placeholder={t('admin.select_wmanager_rank')}
              selectedKeys={selectedRank ? [selectedRank] : []}
              onSelectionChange={(keys) => {
                const selected = Array.from(keys)[0] as WManagerRank | '';
                setSelectedRank(selected);
              }}
            >
              {wManagerRankOptions.map((option) => (
                <SelectItem key={option.key} value={option.key}>
                  {option.label}
                </SelectItem>
              ))}
            </Select>

            {/* Warning */}
            <div className="flex flex-col gap-2 bg-warning-50 p-4 rounded-lg">
              <p className="text-sm font-medium text-warning-700">
                {t('admin.wmanager_rank_update_warning')}
              </p>
            </div>
          </div>
        </ModalBody>

        <ModalFooter className="flex flex-col gap-2">
          <Button
            color="primary"
            className="w-full"
            onPress={handleSubmit}
            isLoading={isSubmitting}
            isDisabled={selectedRank === user.wManagerRank}
          >
            {t('admin.update_wmanager_rank')}
          </Button>
          <Button
            color="danger"
            variant="light"
            className="w-full"
            onPress={handleClose}
            isDisabled={isSubmitting}
          >
            {t('common.cancel')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
