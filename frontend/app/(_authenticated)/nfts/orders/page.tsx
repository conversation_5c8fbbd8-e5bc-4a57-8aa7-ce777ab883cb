'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { useApi } from '@/hooks/useApi';
import { Icon } from '@iconify/react';
import { addToast, Spinner } from '@heroui/react';
import { NFTOrder } from '@/types/models';
import NFTOrderCard from '../components/nft-order-card';

export default function NFTOrdersPage() {
  const router = useRouter();
  const { user, accessToken, hydrate, isHydrated } = useUserStore();
  const { fetchApi } = useApi();
  const [isLoading, setIsLoading] = useState(true);
  const [orders, setOrders] = useState<NFTOrder[]>([]);

  useEffect(() => {
    hydrate();
  }, [hydrate]);

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        if (!isHydrated || !accessToken) {
          router.push('/');
          return;
        }

        setIsLoading(true);
        const response = await fetchApi('/nfts/orders');
        if (response.success && response.data) {
          setOrders(response.data);
        }
      } catch (error: any) {
      } finally {
        setIsLoading(false);
      }
    };

    if (isHydrated) {
      fetchOrders();
    }
  }, [isHydrated, accessToken]);

  const handleAction = async (action: 'cancel', order: NFTOrder) => {
    try {
      setIsLoading(true);
      const response = await fetchApi(`/nfts/orders/${order.id}/cancel`, {
        method: 'POST',
      });
      if (response.success) {
        addToast({
          title: 'Success',
          description: 'Order cancelled successfully',
          color: 'success',
        });
        const updatedOrders = await fetchApi('/nfts/orders');
        if (updatedOrders.success) {
          setOrders(updatedOrders.data);
        }
      }
    } catch (error: any) {
      addToast({
        title: 'Error',
        description: error.message || 'Failed to cancel order',
        color: 'danger',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isHydrated || isLoading) {
    return (
      <div className="fixed inset-0 flex justify-center items-center z-50">
        <Spinner color="success" />
      </div>
    );
  }

  return (
    <div className="flex w-full flex-col max-w-sm mx-auto gap-4">
      <div className="flex items-center justify-between">
        <button onClick={() => router.back()}>
          <Icon icon="solar:arrow-left-linear" width={24} className="text-gray-500" />
        </button>
        <p className="text-lg font-semibold flex-1 text-center">My NFTs</p>
      </div>

      <div className="flex flex-col gap-4">
        {orders.length > 0 ? (
          orders.map((order) => (
            <NFTOrderCard
              key={order.id}
              order={order}
            />
          ))
        ) : (
          <div className="flex flex-col items-center justify-center py-[100px]">
            <Icon icon="solar:box-bold" width={48} height={48} className="text-green-400 mb-2" />
            <p className="text-center text-gray-500 text-sm">No orders found</p>
          </div>
        )}
      </div>
    </div>
  );
}