'use client';

import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { useApi } from '@/hooks/useApi';
import { addToast, Spinner, useDisclosure, Tabs, Tab, Card, CardBody } from '@heroui/react';
import { NFT, NFTOrder } from '@/types/models';
import NFTCard from './components/nft-card';
import NFTModal from './components/nft-modal';
import NFTOrderCard from './components/nft-order-card';
import NFTListConfirmationModal from './components/nft-list-confirmation-modal';
import { useTranslation } from '@/hooks/useTranslation';

export default function NFTsPage() {
  const router = useRouter();
  const { user, accessToken, hydrate, isHydrated } = useUserStore();
  const { fetchApi } = useApi();
  const { t } = useTranslation();
  const [myNFTs, setMyNFTs] = useState<NFT[]>([]);
  const [listedNFTs, setListedNFTs] = useState<NFT[]>([]);
  const [selectedNFT, setSelectedNFT] = useState<NFT | null>(null);
  const [ordersNFTs, setOrdersNFTs] = useState<NFTOrder[]>([]);
  const [loadingStates, setLoadingStates] = useState({ 'my-nfts': false, 'listed-nfts': false, 'order-nfts': false });
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isListModalOpen, onOpen: onListModalOpen, onClose: onListModalClose } = useDisclosure();
  const [nftToList, setNftToList] = useState<NFT | null>(null);
  const [isListing, setIsListing] = useState(false);

  React.useEffect(() => {
    hydrate();
  }, [hydrate]);

  React.useEffect(() => {
    if (isHydrated && !accessToken) {
      router.push('/');
    }
  }, [isHydrated, accessToken, router]);

  const fetchNFTs = async (tabKey: string) => {
    if (!isHydrated || !accessToken) {
      router.push('/');
      return;
    }

    setLoadingStates((prev) => ({ ...prev, [tabKey]: true }));
    try {
      let response;
      if (tabKey === 'my-nfts') {
        response = await fetchApi('/nfts/me', { method: 'GET', requireAuth: true });
        if (response.success && response.data) {
          setMyNFTs(response.data);
        }
      } else if (tabKey === 'listed-nfts') {
        response = await fetchApi('/nfts/listed', { method: 'GET', requireAuth: true });
        if (response.success && response.data) {
          setListedNFTs(response.data);
        }
      } else if (tabKey === 'order-nfts') {
        response = await fetchApi('/nfts/orders', { method: 'GET', requireAuth: true });
        if (response.success && response.data) {
          setOrdersNFTs(response.data);
        }
      }
    } catch (error: any) {
      addToast({
        title: t('nfts.notification'),
        description: error.message || t('nfts.fetch_error', { type: tabKey === 'my-nfts' ? t('nfts.my') : tabKey === 'listed-nfts' ? t('nfts.listed') : t('nfts.orders') }),
        color: 'warning',
      });
      if (error.message.includes('Authentication required')) {
        router.push('/');
      }
    } finally {
      setLoadingStates((prev) => ({ ...prev, [tabKey]: false }));
    }
  };

  const handleNFTClick = (nft: NFT) => {
    setSelectedNFT(nft);
    onOpen();
  };

  const handleAction = async (action: 'list' | 'withdraw' | 'buy', nft: NFT) => {
    try {
      let response;
      switch (action) {
        case 'list':
          setNftToList(nft);
          onListModalOpen();
          break;
        case 'withdraw':
          response = await fetchApi(`/nfts/${nft.id}/withdraw`, { method: 'POST' });
          if (response.success) {
            addToast({ title: t('common.success'), description: t('nfts.profits_withdrawn'), color: 'success' });
          }
          break;
        case 'buy':
          router.push(`/flash-buy`);
          break;
      }
    } catch (error: any) {
      addToast({
        title: t('errors.error'),
        description: error.message || t('nfts.action_failed'),
        color: 'danger',
      });
    }
  };

  const handleListConfirm = async (quantity: string) => {
    if (!nftToList) return;

    setIsListing(true);
    try {
      const response = await fetchApi(`/nfts/${nftToList.id}/listing`, {
        method: 'POST',
        requireAuth: true,
        body: JSON.stringify({ quantity }),
      });

      if (response.success) {
        addToast({ title: t('common.success'), description: t('nfts.listed_success'), color: 'success' });
        fetchNFTs('my-nfts');
        onListModalClose();
      }
    } catch (error: any) {
      addToast({
        title: t('errors.error'),
        description: error.message || t('nfts.list_error'),
        color: 'danger',
      });
    } finally {
      setIsListing(false);
    }
  };

  if (!isHydrated) {
    return (
      <div className="fixed inset-0 flex justify-center items-center bg-gray-100 bg-opacity-50 z-50">
        <Spinner color="success" />
      </div>
    );
  }

  const tabs = [
    {
      id: 'listed-nfts',
      label: t('nfts.marketplace'),
      content: (
        <div className="flex flex-col gap-4">
          {loadingStates['listed-nfts'] ? (
            <div className="flex justify-center items-center py-10">
              <Spinner color="success" />
            </div>
          ) : listedNFTs.length > 0 ? (
            listedNFTs.map((nft) => (
              <NFTCard
                key={nft.id}
                nft={nft}
                onClick={handleNFTClick}
                onAction={handleAction}
                userId={user?.id}
              />
            ))
          ) : (
            <div className="flex flex-col items-center justify-center py-[100px]">
              <Icon icon="solar:box-bold" width={48} height={48} className="text-green-400 mb-2" />
              <p className="text-center text-gray-500 text-sm">{t('nfts.no_listed_nfts')}</p>
            </div>
          )}
        </div>
      ),
    },
    {
      id: 'my-nfts',
      label: t('nfts.my_nfts'),
      content: (
        <div className="flex flex-col gap-4">
          {loadingStates['my-nfts'] ? (
            <div className="flex justify-center items-center py-10">
              <Spinner color="success" />
            </div>
          ) : myNFTs.length > 0 ? (
            myNFTs.map((nft) => (
              <NFTCard
                key={nft.id}
                nft={nft}
                onClick={handleNFTClick}
                onAction={handleAction}
                userId={user?.id}
              />
            ))
          ) : (
            <div className="flex flex-col items-center justify-center py-[100px]">
              <Icon icon="solar:box-bold" width={48} height={48} className="text-green-400 mb-2" />
              <p className="text-center text-gray-500 text-sm">{t('nfts.no_nfts')}</p>
            </div>
          )}
        </div>
      ),
    },
    {
      id: 'order-nfts',
      label: t('nfts.buy_sell'),
      content: (
        <div className="flex flex-col gap-4">
          {loadingStates['order-nfts'] ? (
            <div className="flex justify-center items-center py-10">
              <Spinner color="success" />
            </div>
          ) : ordersNFTs.length > 0 ? (
            ordersNFTs.map((order) => (
              <NFTOrderCard key={order.id} order={order} />
            ))
          ) : (
            <div className="flex flex-col items-center justify-center py-[100px]">
              <Icon icon="solar:box-bold" width={48} height={48} className="text-green-400 mb-2" />
              <p className="text-center text-gray-500 text-sm">{t('nfts.no_orders')}</p>
            </div>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="flex w-full flex-col gap-4 max-w-sm mx-auto">
      <div className="flex items-center justify-between">
        <button onClick={() => router.push('/account')}>
          <Icon icon="solar:arrow-left-linear" width={24} className="text-gray-500" />
        </button>
        <p className="text-lg font-semibold flex-1 text-center">{t('nfts.title')}</p>
      </div>

      <Tabs
        aria-label="NFT Tabs"
        fullWidth
        items={tabs}
        onSelectionChange={(key) => fetchNFTs(key as string)}
      >
        {(item) => (
          <Tab key={item.id} title={item.label}>
            {item.content}
          </Tab>
        )}
      </Tabs>

      <NFTModal
        nft={selectedNFT}
        isOpen={isOpen}
        onClose={onClose}
        onAction={handleAction}
        userId={user?.id}
      />

      <NFTListConfirmationModal
        nft={nftToList}
        isOpen={isListModalOpen}
        onClose={onListModalClose}
        onConfirm={handleListConfirm}
        isLoading={isListing}
      />
    </div>
  );
}