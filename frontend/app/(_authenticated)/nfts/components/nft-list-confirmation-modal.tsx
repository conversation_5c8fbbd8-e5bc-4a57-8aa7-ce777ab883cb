import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Input } from '@heroui/react';
import { NFT } from '@/types/models';
import { useTranslation } from '@/hooks/useTranslation';

interface NFTListConfirmationModalProps {
  nft: NFT | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (quantity: number) => void;
  isLoading?: boolean;
}

const NFTListConfirmationModal: React.FC<NFTListConfirmationModalProps> = ({
  nft,
  isOpen,
  onClose,
  onConfirm,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const [quantity, setQuantity] = React.useState<number>(0);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (nft && isOpen) {
      setQuantity(Number(nft.quantity));
      setErro<PERSON>('');
    }
  }, [nft, isOpen]);

  if (!nft) return null;

  const handleConfirm = () => {
    if (quantity < 1) {
      setError(t('nfts.quantity_min_error'));
      return;
    }
    if (quantity > Number(nft.quantity)) {
      setError(t('nfts.quantity_max_error'));
      return;
    }
    onConfirm(quantity);
  };

  const isQuantityValid = quantity >= 1 && quantity <= Number(nft.quantity);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="sm">
      <ModalContent>
        <ModalHeader>
          <h3 className="text-lg font-semibold">{t('nfts.list_for_sale')}</h3>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-4">
            <p className="text-gray-600">
              {t('nfts.confirm_list', { type: nft.type })}
            </p>
            <Input
              label={t('nfts.quantity_to_list')}
              type="number"
              placeholder="0"
              min={1}
              max={Number(nft.quantity)}
              step={1}
              value={Number(quantity || 0).toString()}
              onChange={(e) => {
                const value = parseInt(e.target.value);
                setQuantity(value);
                setError('');
                if (!value || value < 1) {
                  setError(t('nfts.quantity_min_error'));
                } else if (value > Number(nft.quantity)) {
                  setError(t('nfts.quantity_max_error'));
                }
              }}
              onKeyDown={(e) => {
                if (e.key === '.' || e.key === ',') {
                  e.preventDefault();
                }
              }}
              endContent={<span className="text-gray-500 text-sm">{t('nfts.units')}</span>}
              disabled={isLoading}
              isInvalid={!!error}
              errorMessage={error}
              description={t('nfts.available_units', { quantity: nft.quantity })}
            />
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-sm text-blue-700">
                {t('nfts.current_quantity')}: <span className="font-semibold">{Number(nft.quantity)}</span>
              </p>
              <p className="text-sm text-blue-700 mt-2">
                {t('nfts.listing_quantity')}: <span className="font-semibold">{Number(quantity || 0)}</span>
              </p>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="flex justify-end gap-3">
            <Button variant="flat" onClick={onClose} disabled={isLoading}>
              {t('common.cancel')}
            </Button>
            <Button
              color="primary"
              onClick={handleConfirm}
              isLoading={isLoading}
              disabled={!isQuantityValid || isLoading}
              className="bg-gradient-to-r from-blue-500 to-green-500 text-white"
            >
              {t('nfts.confirm_listing')}
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default NFTListConfirmationModal;