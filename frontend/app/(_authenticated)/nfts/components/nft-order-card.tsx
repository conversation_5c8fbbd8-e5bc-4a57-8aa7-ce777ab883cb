// components/NFTOrderBill.tsx
'use client';

import React from 'react';
import { Card, CardBody, Chip } from '@heroui/react';
import { NFTOrder, OrderType, OrderStatus } from '@/types/models';
import moment from 'moment-timezone';
import { useTranslation } from '@/hooks/useTranslation';
interface NFTOrderCardProps {
  order: NFTOrder;
}

const NFTOrderCard: React.FC<NFTOrderCardProps> = ({ order }) => {
  const { t } = useTranslation();
  const formatDate = (date?: Date | string) => {
    if (!date) return 'N/A';
    return moment(date).tz('Asia/Dubai').format('DD/MM/YYYY HH:mm:ss');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'warning';
      case 'MATCHED': return 'success';
      case 'CANCELLED': return 'danger';
      default: return 'default';
    }
  };

  const isBuy = order.orderType === OrderType.BUY;
  const isSell = order.orderType === OrderType.SELL;
  const isScheduled = order.orderType === OrderType.BUY && order.status === OrderStatus.PENDING && order.isScheduled;

  return (
    <Card className="w-full max-w-sm shadow-md">
      <CardBody className="p-4 flex flex-col gap-2">
        <div className="flex justify-between items-center">
          <h3 className="text-sm font-semibold text-gray-800">{isBuy ? isScheduled ? <Chip color="warning" radius="sm" size='sm'>{t('nfts.scheduled')}</Chip> : <Chip color="success" radius="sm" size='sm'>{t('nfts.buy')}</Chip> : <Chip color="danger" radius="sm" size='sm'>{t('nfts.sell')}</Chip>} {t('nfts.order')} #{order.id.slice(0, 8)}</h3>
          <Chip color={getStatusColor(order.status)} variant="flat" size="sm">
            {order.status}
          </Chip>
        </div>

        <div className="flex flex-col gap-1 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-500">{order.orderType === OrderType.BUY ? t('nfts.buyer') : t('nfts.seller')}:</span>
            <span className="font-normal">{order.traderId.slice(0, 8)}...</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">{t('nfts.nft_type')}:</span>
            <span className="font-bold">{order.nftType}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">{t('nfts.nft_id')}:</span>
            <span className="font-normal">{order.nftId.slice(0, 8)}...</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">{t('nfts.price')}:</span>
            <span className="font-bold">{order.nftPrice} USDT</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">{t('nfts.quantity')}:</span>
            <span className="font-bold">{order.orderType === OrderType.BUY ? Number(order.quantity) : `${Number(order.soldQuantity)}/${Number(order.quantity)}`}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">{t('nfts.gas_fee')}:</span>
            <span className="font-bold">{order.gasFee} WM ({order.gasFeePercentage}%)</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">{t('nfts.session')}:</span>
            <span className="font-bold">{order.sessionType ? order.sessionType : 'N/A'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">{t('nfts.session_id')}:</span>
            <span className="font-normal">{order.sessionId ? order.sessionId.slice(0, 8) : 'N/A'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">{t('nfts.session_time')}:</span>
            <span className="font-normal">{formatDate(order.sessionTime)}</span>
          </div>
          {order.matchedAt && (
            <div className="flex justify-between">
              <span className="text-gray-500">{t('nfts.matched_at')}:</span>
              <span className="font-normal">{formatDate(order.matchedAt)}</span>
            </div>
          )}
          {order.completedAt && (
            <div className="flex justify-between">
              <span className="text-gray-500">{t('nfts.completed_at')}:</span>
              <span className="font-normal">{formatDate(order.completedAt)}</span>
            </div>
          )}
          <div className="flex justify-between">
            <span className="text-gray-500">{t('nfts.created_at')}:</span>
            <span className="font-normal">{formatDate(order.createdAt)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">{t('nfts.updated_at')}:</span>
            <span className="font-normal">{formatDate(order.updatedAt)}</span>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default NFTOrderCard;