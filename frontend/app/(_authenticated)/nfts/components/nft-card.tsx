'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@heroui/react';
import { Icon } from '@iconify/react';
import { NFT } from '@/types/models';
import { formatNumber } from '@/utils/format';
import BigNumber from 'bignumber.js';
import { useTranslation } from '@/hooks/useTranslation';

interface NFTCardProps {
    nft: NFT;
    onClick: (nft: NFT) => void;
    onAction: (action: 'list' | 'withdraw' | 'buy', nft: NFT) => void;
    userId?: string;
    isFlashBuy?: boolean;
}

const NFTCard: React.FC<NFTCardProps> = ({ nft, onClick, onAction, userId, isFlashBuy = false }) => {
    const { t } = useTranslation();
    const getTypeImage = (type: string) => {
        switch (type) {
            case 'PHOENIX':
                return '/nfts/phoenix-1.jpeg';
            case 'SPIRIT_TURTLE':
                return '/nfts/spirit-turtle-1.jpeg';
            case 'UNICORN':
                return '/nfts/unicorn-1.jpeg';
            case 'DRAGON':
                return '/nfts/dragon-1.jpeg';
            default:
                return '/nfts/default.jpeg';
        }
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'PHOENIX':
                return 'game-icons:phoenix-head';
            case 'SPIRIT_TURTLE':
                return 'game-icons:turtle';
            case 'UNICORN':
                return 'game-icons:unicorn';
            case 'DRAGON':
                return 'game-icons:dragon-head';
            default:
                return 'game-icons:perspective-dice-six';
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'AVAILABLE':
                return 'primary';
            case 'LISTED':
                return 'success';
            case 'SOLD':
                return 'warning';
            default:
                return 'default';
        }
    };

    return (
        <Card
            key={nft.id}
            className="w-full max-w-sm hover:shadow-lg transition-shadow cursor-pointer"
        >
            <CardBody className="p-4">
                <div className="flex flex-col gap-3">
                    {isFlashBuy && <div className="flex justify-between items-start">
                        <div className="flex items-center gap-2">
                            <h3 className="font-semibold text-sm">{nft.type}</h3>
                        </div>
                        <Chip size="sm" color={getStatusColor(nft.status)} variant="flat">
                            {nft.status}
                        </Chip>
                    </div>}

                    <img
                        src={getTypeImage(nft.type)}
                        alt={nft.type}
                        className="w-full h-auto object-cover rounded-lg"
                    />

                    <div className="flex flex-col gap-1">
                        <div className="flex justify-between">
                            <span className="text-xs text-gray-500">{t('nfts.price')}</span>
                            <span className="font-semibold text-sm">{formatNumber(nft.currentPrice)} USDT</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-xs text-gray-500">{t('nfts.gas_fee')}</span>
                            <span className="font-semibold text-sm">{formatNumber(nft.gasFee, 1)} USDT</span>
                        </div>
                        {nft.status === 'AVAILABLE' && nft.ownerId === userId && new BigNumber(nft.quantity).gt(0) && (<div className="flex justify-between">
                            <span className="text-xs text-gray-500">{t('nfts.quantity')}</span>
                            <span className="font-semibold text-sm">{nft.quantity || 0}</span>
                        </div>)}
                    </div>


                    {nft.status === 'AVAILABLE' && nft.ownerId === userId && new BigNumber(nft.quantity).gt(0) && (
                        <div className="flex gap-2 mt-2">
                            <Button
                                color="success"
                                variant="flat"
                                className="flex-1 text-xs bg-gradient-to-r from-blue-500 to-green-500 text-white"
                                onPress={() => onAction('list', nft)}
                            >
                                {t('nfts.list_now')}
                            </Button>
                        </div>
                    )}
                    {nft.status === 'LISTED' && nft.ownerId !== userId && new BigNumber(nft.quantity).gt(0) && (
                        <div className="flex gap-2 mt-2">
                            <Button
                                color="primary"
                                variant="flat"
                                className="flex-1 text-xs bg-gradient-to-r from-blue-500 to-green-500 text-white"
                                onPress={() => onAction('buy', nft)}
                            >
                                {t('nfts.buy_with_flash_buy')}
                            </Button>
                        </div>
                    )}
                </div>
            </CardBody>
        </Card>
    );
};

export default NFTCard;