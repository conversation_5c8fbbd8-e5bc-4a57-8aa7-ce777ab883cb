'use client';

import React from 'react';
import { <PERSON>, CardBody, Button, Chip, addToast } from '@heroui/react';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { useTranslation } from '@/hooks/useTranslation';

export default function FlashBuyPage() {
  const router = useRouter();
  const { user } = useUserStore();
  const { t } = useTranslation();

  const handleNotifyMe = () => {
    addToast({
      title: t('common.notification_set'),
      description: t('coming_soon.flash_buy_notify'),
      color: "success",
    });
  };

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex w-full flex-col gap-4">
        <div className="flex items-center justify-between">
          <button onClick={() => router.back()}>
            <Icon icon="solar:arrow-left-linear" width={24} className="text-green-500" />
          </button>
          <p className="text-lg font-semibold flex-1 text-center">{t('flash_buy.title')}</p>
          <div className="w-6" />
        </div>

        <Card className="w-full">
          <CardBody className="flex flex-col items-center justify-center py-8 px-4 text-center">
            <div className="mb-6">
              <Icon
                icon="solar:rocket-bold-duotone"
                width={80}
                height={80}
                className="text-green-500 opacity-80"
              />
            </div>
            <h2 className="text-2xl font-bold mb-2">{t('common.coming_soon')}</h2>
            <p className="text-default-500 mb-8 max-w-md">
              {t('coming_soon.flash_buy_description')}
            </p>
            <div className="flex flex-col gap-3 w-full max-w-xs">
              <Button
                variant="flat"
                className="w-full bg-gradient-to-tr from-blue-500 to-green-500 text-white shadow-lg"
                startContent={<Icon icon="solar:bell-bold" width={20} />}
                onPress={handleNotifyMe}
              >
                {t('common.notify_when_available')}
              </Button>
              <Button
                color="default"
                variant="light"
                className="w-full"
                onPress={() => router.push('/wallet')}
              >
                {t('common.back_to_wallet')}
              </Button>
            </div>
          </CardBody>
        </Card>

        <Card className="w-full mt-4">
          <CardBody>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">{t('flash_buy.benefits')}</h3>
              <Chip color="warning" variant="flat" size="sm">{t('common.coming_soon')}</Chip>
            </div>
            <ul className="space-y-3">
              <li className="flex items-start gap-2">
                <Icon icon="solar:check-circle-bold" className="text-success mt-0.5" width={20} />
                <span>{t('flash_buy.benefit_instant')}</span>
              </li>
              <li className="flex items-start gap-2">
                <Icon icon="solar:check-circle-bold" className="text-success mt-0.5" width={20} />
                <span>{t('flash_buy.benefit_rates')}</span>
              </li>
              <li className="flex items-start gap-2">
                <Icon icon="solar:check-circle-bold" className="text-success mt-0.5" width={20} />
                <span>{t('flash_buy.benefit_fast')}</span>
              </li>
              <li className="flex items-start gap-2">
                <Icon icon="solar:check-circle-bold" className="text-success mt-0.5" width={20} />
                <span>{t('flash_buy.benefit_secure')}</span>
              </li>
            </ul>
          </CardBody>
        </Card>

        <Card className="w-full mt-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10">
          <CardBody>
            <div className="flex items-center gap-3 mb-3">
              <Icon icon="solar:lightbulb-bold-duotone" className="text-warning" width={24} />
              <h3 className="text-lg font-semibold">{t('common.did_you_know')}</h3>
            </div>
            <p className="text-default-600">
              {t('flash_buy.did_you_know_content')}
            </p>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}