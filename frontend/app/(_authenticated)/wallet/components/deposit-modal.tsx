'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalBody,
  <PERSON>dal<PERSON>ooter,
  Button,
  Card,
  CardBody,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { QRCodeSVG } from 'qrcode.react';
import { useApi } from '@/hooks/useApi';
import { useSocketContext } from '@/providers/socket-provider';
import { useUserStore } from '@/store/userStore';
import { useTranslation } from '@/hooks/useTranslation';

interface DepositModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function DepositModal({ isOpen, onClose }: DepositModalProps) {
  const { fetchApi } = useApi();
  const { socket } = useSocketContext();
  const { user, updateUser } = useUserStore();
  const { t } = useTranslation();
  const [walletAddress, setWalletAddress] = useState<string>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const getWalletInfo = async () => {
      try {
        setIsLoading(true);
        const response = await fetchApi('/wallet/default');
        if (response.success && response.data) {
          setWalletAddress(response.data);
        }
      } catch (error) {
        addToast({
          title: t('errors.error'),
          description: t('wallet.deposit_load_error'),
          color: 'danger'
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      getWalletInfo();
    }
  }, [isOpen]);

  useEffect(() => {
    if (!socket) return;

    // Listen for deposit completed event
    socket.on('wallet.deposit.completed', (data: any) => {
      if (data.user) {
        updateUser(data.user);
      }
      addToast({
        title: t('wallet.deposit_successful'),
        description: t('wallet.deposit_credited', { amount: data.amount, token: data.tokenSymbol }),
        color: 'success'
      });

      onClose();
    });

    return () => {
      socket.off('wallet.deposit.completed');
    };
  }, [socket, onClose, updateUser]);

  const handleCopyAddress = async () => {
    if (walletAddress) {
      try {
        await navigator.clipboard.writeText(walletAddress);
        addToast({
          title: t('common.success'),
          description: t('wallet.address_copied'),
          color: 'success'
        });
      } catch (error) {
        addToast({
          title: t('errors.error'),
          description: t('wallet.copy_failed'),
          color: 'danger'
        });
      }
    }
  };

  const networkInfo = {
    name: t('wallet.bnb_smart_chain'),
    chainId: '56',
    rpcUrl: 'https://bsc-dataseed.binance.org',
    explorer: 'https://bscscan.com'
  };

  if (isLoading) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="sm">
        <ModalContent>
          <ModalBody>
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="sm">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h3 className="text-lg font-semibold">{t('wallet.deposit_crypto')}</h3>
          <p className="text-sm text-gray-500">
            {t('wallet.send_crypto_to_address')}
          </p>
        </ModalHeader>
        <ModalBody>
          <div className="flex flex-col gap-3 pb-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">{t('wallet.network')}</span>
              <span className="text-sm font-bold">{networkInfo.name}</span>
            </div>

            {/* QR Code */}
            <div className="flex justify-center">
              <QRCodeSVG
                value={walletAddress || ''}
                size={220}
                level="H"
                includeMargin
              />
            </div>

            {walletAddress && (
              <div className="flex flex-col gap-2">
                <span className="text-sm text-gray-500">{t('wallet.your_deposit_address')}</span>
                <div className="flex items-center gap-2">
                  <div className="flex-1 p-2 bg-gray-50 rounded-lg font-mono text-sm break-all">
                    {walletAddress}
                  </div>
                  <Button
                    isIconOnly
                    variant="light"
                    onPress={handleCopyAddress}
                  >
                    <Icon icon="solar:copy-linear" />
                  </Button>
                </div>
              </div>
            )}

            <div className="flex flex-col gap-2 bg-warning-50 p-4 rounded-lg">
              <p className="text-sm font-medium text-warning-700">
                {t('wallet.important_notes')}:
              </p>
              <ul className="text-sm text-warning-600 list-disc list-inside">
                <li>{t('wallet.send_only_to_address')}</li>
                <li>{t('wallet.ensure_network', { network: networkInfo.name })}</li>
                <li>{t('wallet.minimum_deposit')}: 0.10</li>
                <li>{t('wallet.deposit_credited_after')}</li>
              </ul>
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}