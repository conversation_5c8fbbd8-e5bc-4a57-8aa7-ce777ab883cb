'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>oot<PERSON>,
  Button,
  Input,
  Card,
  CardBody,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useApi } from '@/hooks/useApi';
import { useUserStore } from '@/store/userStore';
import { useTranslation } from '@/hooks/useTranslation';

interface WithdrawModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function WithdrawModal({ isOpen, onClose }: WithdrawModalProps) {
  const { fetchApi } = useApi();
  const { user, updateUser } = useUserStore();
  const { t } = useTranslation();
  const [amount, setAmount] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const handleWithdraw = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      addToast({
        title: t('errors.error'),
        description: t('wallet.enter_valid_amount'),
        color: 'danger'
      });
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetchApi('/wallet/withdraw/usdt', {
        method: 'POST',
        body: JSON.stringify({ amount: parseFloat(amount) })
      });

      if (response.success) {
        if (response.data?.user) {
          updateUser(response.data.user);
        }

        addToast({
          title: t('common.success'),
          description: t('wallet.withdrawal_processed', { amount: amount, token: 'USDT' }),
          color: 'success'
        });
        setAmount('');
        onClose();
      } else {
        addToast({
          title: t('errors.error'),
          description: response.message[t('common.language_code')] || t('wallet.withdrawal_error'),
          color: 'danger'
        });
      }
    } catch (error) {
      addToast({
        title: t('errors.error'),
        description: error.message || t('wallet.withdrawal_error'),
        color: 'danger'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const networkInfo = {
    name: t('wallet.bnb_smart_chain'),
    chainId: '56',
    explorer: 'https://bscscan.com'
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="sm">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h3 className="text-lg font-semibold">{t('wallet.withdraw_usdt')}</h3>
          <p className="text-sm text-gray-500">
            {t('wallet.enter_amount_withdraw')}
          </p>
        </ModalHeader>
        <ModalBody>
          <div className="flex flex-col gap-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">{t('wallet.network')}</span>
              <span className="text-sm font-bold">{networkInfo.name}</span>
            </div>

            <Input
              type="number"
              label={t('wallet.amount_usdt')}
              placeholder="0.00"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              min={0}
              step="0.01"
              endContent={
                <div className="flex items-center">
                  <span className="text-gray-500 text-sm">USDT</span>
                </div>
              }
              disabled={isLoading}
            />

            {user?.web3Wallet?.usdtBalance && (
              <div className="text-sm text-gray-500">
                {t('wallet.available_balance')}: {user?.web3Wallet?.usdtBalance} USDT
              </div>
            )}

            <div className="flex flex-col gap-2 bg-warning-50 p-4 rounded-lg">
              <p className="text-sm font-medium text-warning-700">
                {t('wallet.important_notes')}:
              </p>
              <ul className="text-sm text-warning-600 list-disc list-inside">
                <li>{t('wallet.minimum_withdrawal')}: 10 USDT</li>
                <li>{t('wallet.ensure_sufficient_balance')}</li>
                <li>{t('wallet.network_fees')}: 3%->WM</li>
                <li>{t('wallet.processing_time')}</li>
              </ul>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            color="primary"
            className="w-full bg-gradient-to-r from-blue-500 to-green-500"
            onPress={handleWithdraw}
            isLoading={isLoading}
            disabled={!amount || parseFloat(amount) <= 0}
          >
            {t('wallet.withdraw_now')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}