'use client';

import React, { useState } from 'react';
import CardIconAction from '@/components/card-icon-action';
import { useRouter } from 'next/navigation';
import DepositModal from './deposit-modal';
import WithdrawModal from './withdraw-modal';
import TransferWmModal from './transfer-wm-modal';
import DepositWeb3Modal from './deposit-web3-modal';
import { useTranslation } from '@/hooks/useTranslation';

export default function WalletSection() {
  const router = useRouter();
  const { t } = useTranslation();
  const [isDepositModalOpen, setIsDepositModalOpen] = useState(false);
  const [isWithdrawModalOpen, setIsWithdrawModalOpen] = useState(false);
  const [isTransferWmModalOpen, setIsTransferWmModalOpen] = useState(false);

  return (
    <>
      <div className="flex justify-around gap-2">
        <CardIconAction
          color="primary"
          icon="solar:upload-linear"
          title={t('wallet.deposit')}
          className="flex-1"
          onClick={() => setIsDepositModalOpen(true)}
        />
        <CardIconAction
          color="primary"
          icon="solar:download-linear"
          title={t('wallet.withdraw')}
          className="flex-1"
          onClick={() => setIsWithdrawModalOpen(true)}
        />
        <CardIconAction
          color="primary"
          icon="solar:download-linear"
          title={t('wallet.transfer')}
          className="flex-1"
          onClick={() => setIsTransferWmModalOpen(true)}
        />
        <CardIconAction
          color="primary"
          icon="solar:bill-list-linear"
          title={t('wallet.history')}
          className="flex-1"
          onClick={() => router.push('/transactions')}
        />
      </div>

      <DepositWeb3Modal
        isOpen={isDepositModalOpen}
        onClose={() => setIsDepositModalOpen(false)}
      />
      <WithdrawModal
        isOpen={isWithdrawModalOpen}
        onClose={() => setIsWithdrawModalOpen(false)}
      />
      <TransferWmModal
        isOpen={isTransferWmModalOpen}
        onClose={() => setIsTransferWmModalOpen(false)}
      />
    </>
  );
}