'use client';

import React, { useEffect } from 'react';
import { Card, CardBody, Button, User, Link } from '@heroui/react';
import { Icon } from '@iconify/react';
import { useUserStore } from '@/store/userStore';
import { useRouter } from 'next/navigation';
import { UserRank, WManagerRank } from '@/types/models';
import { useTranslation } from '@/hooks/useTranslation';

export default function UserGreeting() {
    const { user, isAuthenticated } = useUserStore();
    const router = useRouter();
    const { t } = useTranslation();

    /**
     * L<PERSON>y hình ảnh của user dựa vào rank
     */
    const getUserRank = () => {
        if (user?.wManagerRank === WManagerRank.WM1) {
            return '/levels/w-1.png';
        }
        if (user?.wManagerRank === WManagerRank.WM2) {
            return '/levels/w-2.png';
        }
        if (user?.wManagerRank === WManagerRank.WM3) {
            return '/levels/w-3.png';
        }
        if (user?.wManagerRank === WManagerRank.WM4) {
            return '/levels/w-4.png';
        }
        if (user?.wManagerRank === WManagerRank.WM5) {
            return '/levels/w-5.png';
        }
        return '/levels/w-0.png';
    }

    return (
        <Card shadow="sm" className="border-none">
            <CardBody className="p-4">
                <div className="flex flex-col gap-3">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                            <User
                                avatarProps={{
                                    src: isAuthenticated ? getUserRank() : '',
                                }}
                                description={isAuthenticated ? user?.wManagerRank : ''}
                                name={isAuthenticated ? user?.name ? user?.name : user?.username : ''}

                            />
                        </div>
                        <div className="flex items-center gap-1">
                            <Icon icon="solar:heart-linear" width={20} className="text-pink-500" />
                            <span className="text-lg font-semibold text-pink-500">{isAuthenticated ? user?.credits : 0}</span>
                        </div>
                    </div>
                    {(!user?.hasLightningBolt || !user?.firstDepositTime) && (
                        <div className="flex justify-between items-center gap-2">
                            {!user?.hasLightningBolt && (
                                <Button
                                    className="w-full bg-gradient-to-tr from-purple-500 to-pink-500 text-white shadow-lg"
                                    radius="full"
                                    size="sm"
                                    onPress={() => router.push('/lightning')}
                                    startContent={<Icon icon="solar:flame-broken" />}
                                >
                                    {t('wallet.lightning_boost')}
                                </Button>
                            )}
                            {!user?.firstDepositTime && (
                                <Button
                                    className="w-full bg-gradient-to-tr from-blue-500 to-green-500 text-white shadow-lg"
                                    radius="full"
                                    size="sm"
                                    onPress={() => router.push('/priority')}
                                    startContent={<Icon icon="solar:cup-first-outline" />}
                                >
                                    {t('wallet.priority_position')}
                                </Button>
                            )}
                        </div>
                    )}
                </div>
            </CardBody>
        </Card>
    );
}