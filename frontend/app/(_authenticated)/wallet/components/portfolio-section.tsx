'use client';

import React from 'react';
import CardAction from '@/components/card-action';
import { useTokenBalances } from '@/hooks/useTokenBalances';
import { Spinner } from '@heroui/react';
import { formatNumber } from '@/utils/format';
import CardActionToken from '@/components/card-action-token';
import { useTranslation } from '@/hooks/useTranslation';

const TOKEN_ICONS = {
  USDT: 'solar:shield-check-linear',
  WM: 'solar:card-linear',
  DEFAULT: 'solar:document-medicine-linear'
};

export default function PortfolioSection() {
  const { tokens, isLoading, getTotalUsdtValue, wmUsdRate } = useTokenBalances();
  const { t } = useTranslation();

  if (isLoading) {
    return (
      <div className="flex flex-col gap-3">
        <p className="font-semibold">{t('wallet.portfolio')}</p>
        <div className="flex justify-center p-4">
          <Spinner size="sm" />
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-3">
      <div className="flex flex-col gap-1">
        <div className="flex justify-between items-center">
          <p className="font-semibold">{t('wallet.portfolio')}</p>
          <p className="text-sm text-gray-500">
            ≈ {formatNumber(getTotalUsdtValue(), 2)} USDT
          </p>
        </div>
        <p className="text-xs text-gray-400 text-right">
          {t('wallet.exchange_rate')}: 1 WM = {wmUsdRate} USDT
        </p>
      </div>

      {tokens.map((token) => {
        const availableBalance = token.balance?.availableBalance || 0;
        const usdtValue = token.symbol === 'USDT' ? availableBalance || 0 : (availableBalance || 0) * wmUsdRate;

        return (
          <CardActionToken
            key={token.id}
            description={`${formatNumber(availableBalance)} ${token.symbol}${token.symbol !== 'USDT' ? ` ≈ ${formatNumber(usdtValue)} USDT` : ''
              }`}
            logo={token.logoUrl}
            title={token.name}
            onPress={() => {
            }}
            color={token.symbol === 'USDT' ? 'primary' : undefined}
          />
        );
      })}
      {tokens.map((token) => {
        if (token.symbol === 'WM') {
          const lockedBalance = token.balance?.lockedBalance || 0;
          const usdtValue = (lockedBalance || 0) * wmUsdRate;

          return (
            <CardActionToken
              key={token.id}
              description={`${formatNumber(lockedBalance)} ${token.symbol}${` ≈ ${formatNumber(usdtValue)} USDT`}`}
              logo={token.logoUrl}
              title={token.name}
              balanceType='locked'
              onPress={() => {
              }}
            />
          );
        }
      })}
    </div>
  );
}