'use client';

import React from 'react';
import { Card, CardBody, Button, Progress } from '@heroui/react';
import CardCheckable from '@/components/card-checkable';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { useTranslation } from '@/hooks/useTranslation';

export default function GetStartedSection() {
  const router = useRouter();
  const { user } = useUserStore();
  const { t } = useTranslation();
  const isFirstProfileCompleted = user?.isFirstProfileCompleted;
  const isKycCompleted = user?.isKycCompleted;
  const isInviteCompleted = user?.isInviteCompleted;
  const isNftCompleted = user?.isNftCompleted;
  let step = 0;
  if (isFirstProfileCompleted) step++;
  if (isKycCompleted) step++;
  if (isInviteCompleted) step++;
  if (isNftCompleted) step++;

  return (
    <Card>
      <CardBody>
        <div className="flex justify-between items-center">
          <p className="font-semibold">{t('account.get_started')}</p>
          <span className="text-sm text-gray-500">{step}/4</span>
        </div>
        <Progress value={step/4*100} className="my-2" classNames={{indicator: 'bg-gradient-to-r from-blue-500 to-green-500',}}/>
        <div className="flex flex-col gap-2">
          <CardCheckable
            icon="solar:user-linear"
            title={t('account.complete_profile')}
            color="primary"
            isCompleted={isFirstProfileCompleted}
            onPress={() => (!isFirstProfileCompleted ? router.push('/edit-profile') : null)}
          />
          <CardCheckable
            icon="solar:shield-check-linear"
            title={t('account.kyc_account')}
            color="primary"
            isCompleted={isKycCompleted}
            onPress={() => (!isKycCompleted ? router.push('/kyc') : null )}
          />
          <CardCheckable
            icon="solar:user-plus-linear"
            title={t('account.invite_friends')}
            color="primary"
            isCompleted={isInviteCompleted}
            onPress={() => (!isInviteCompleted ? router.push('/affiliate') : null)}
          />
          <CardCheckable
            icon="solar:cart-linear"
            title={t('account.buy_first_nft')}
            color="primary"
            isCompleted={isNftCompleted}
            onPress={() => (!isNftCompleted ? router.push('/flash-buy') : null)}
          />
          <Button
            className="w-full bg-gradient-to-r from-blue-500 to-green-500 text-white shadow-md mt-2"
            startContent={<span className="text-xl">🌍</span>}
            onPress={() => router.push('/global-mining-co-share')}
          >
            {t('account.global_mining')}
          </Button>
        </div>
      </CardBody>
    </Card>
  );
}