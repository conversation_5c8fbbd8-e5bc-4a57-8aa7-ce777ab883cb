import { useState } from 'react';
import { Card, CardBody, Button, addToast } from '@heroui/react';
import { useApi } from '@/hooks/useApi';
import { useUserStore } from '@/store/userStore';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '@/hooks/useTranslation';

export function MiningSection() {
  const router = useRouter();
  const [isMining, setIsMining] = useState(false);
  const { user } = useUserStore();
  const { fetchApi } = useApi();
  const { t } = useTranslation();

  const handleMining = async () => {
    try {
      setIsMining(true);
      const response = await fetchApi('/wallet/mining', {
        method: 'POST',
        requireAuth: true
      });

      if (response.success) {
        useUserStore.setState((state) => ({
          ...state,
          user: state.user ? {
            ...state.user,
            totalMined: Number(response.data.totalMined),
            rank: response.data.rank,
            lastMiningTime: response.data.lastMiningTime ? new Date(response.data.lastMiningTime) : state.user.lastMiningTime,
            dailyMiningRate: Number(response.data.amount),
            miningMultiplier: Number(response.data.miningMultiplier || state.user.miningMultiplier),
            referralMiningBonus: Number(response.data.referralBonus || state.user.referralMiningBonus)
          } : null
        }));

        addToast({
          title: t('wallet.mining'),
          description: t('wallet.mining_success', { amount: response.data.amount, balance: response.data.newBalance }),
          color: "success",
        });
      } else {
        addToast({
          title: "Mining",
          description: response.message.en || "Mining failed. Please try again later.",
          color: "danger",
        });
      }
    } catch (error: any) {
      addToast({
        title: "Mining",
        description: error.message.en || "Mining failed. Please try again later.",
        color: "danger",
      });
    } finally {
      setIsMining(false);
    }
  };

  const getBaseRate = (rank: string) => {
    switch (rank) {
      case 'BRONZE': return '1.00';
      case 'SILVER': return '1.20';
      case 'GOLD': return '1.50';
      case 'PLATINUM': return '2.00';
      case 'DIAMOND': return '3.00';
      default: return '1.00';
    }
  };

  return (
    <>
      <Card shadow="sm" className="border-none">
        <CardBody className="p-4">
          <div className="flex flex-col gap-4">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-semibold">{t('wallet.daily_mining')}</h3>
                <p className="text-sm text-gray-500">
                  {t('wallet.base_rate')}: 0.5 WM/day
                </p>
              </div>

              {user?.hasLightningBolt && (
                <Button
                  isIconOnly
                  variant="light"
                  size="lg"
                  startContent={<Icon icon="solar:flame-broken" className="text-red-500" />}
                >
                  x2
                </Button>
              )}
            </div>

            <div className="text-sm text-gray-500">
              <p>{t('wallet.total_mined')}: {user?.totalMined ? Number(user.totalMined).toFixed(2) : '0.00'} WM</p>
              <p>{t('wallet.mining_multiplier')}: x{user?.miningMultiplier ? Number(user.miningMultiplier).toFixed(2) : '1.00'}</p>
              {user?.referralMiningBonus && (
                <p>{t('wallet.referral_bonus')}: +{Number(user.referralMiningBonus).toFixed(2)} WM/day</p>
              )}
            </div>

            <div className="flex gap-2">
              <Button
                className="w-full bg-gradient-to-tr from-blue-500 to-green-500 text-white shadow-lg"
                radius="full"
                isLoading={isMining}
                onPress={handleMining}
                startContent={<Icon icon="solar:key-broken" />}
              >
                {t('wallet.mining')}
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>
    </>
  );
}