'use client';

import React, { useState, useEffect } from 'react';
import { Button, Skeleton } from '@heroui/react';
import { useTokenBalances } from '@/hooks/useTokenBalances';
import { formatNumber } from '@/utils/format';
import { Icon } from '@iconify/react';
import { useUserStore } from '@/store/userStore';
import { useTranslation } from '@/hooks/useTranslation';


export default function BalanceSection() {
  const { tokens, isLoading, getTotalUsdtValue, wmUsdRate } = useTokenBalances();
  const { user } = useUserStore();
  const { t } = useTranslation();
  const [isBalanceHidden, setIsBalanceHidden] = useState(true);

  const usdtBalance = Number(user?.web3Wallet?.usdtBalance || 0);
  const wmBalance = Number(user?.web3Wallet?.wmBalance || 0) * wmUsdRate;
  const wmLockedBalance = Number(user?.web3Wallet?.wmLockedBalance || 0) * wmUsdRate;
  const totalBalance = usdtBalance + wmBalance + wmLockedBalance;

  useEffect(() => {
    const savedState = localStorage.getItem('hideBalance');
    setIsBalanceHidden(savedState === 'true');
  }, []);

  const toggleBalance = () => {
    const newState = !isBalanceHidden;
    setIsBalanceHidden(newState);
    localStorage.setItem('hideBalance', String(newState));
  };

  const hideNumber = (value: string) => {
    return '••••••';
  };

  return (
    <div className="flex flex-col items-center gap-2 relative">
      <div className="absolute top-0 w-24 h-24 rounded-full bg-primary/10 -z-10" />

      <div className="flex items-center gap-2">
        <p className="text-sm font-semibold text-gray-500">{t('wallet.balance')}</p>
        <Button
          isIconOnly
          variant="light"
          size="sm"
          onPress={toggleBalance}
          className="min-w-0 w-6 h-6"
        >
          <Icon
            icon={isBalanceHidden ? "solar:eye-closed-linear" : "solar:eye-linear"}
            className="text-gray-500"
            width={16}
          />
        </Button>
      </div>

      {isLoading ? (
        <Skeleton className="w-32 h-8 rounded-lg">
          <div className="h-8 w-32 rounded-lg bg-default-200"></div>
        </Skeleton>
      ) : (
        <p className="text-3xl font-bold">
          {isBalanceHidden ? hideNumber(formatNumber(totalBalance)) : formatNumber(totalBalance)} USDT
        </p>
      )}

      <div className="flex items-center gap-2 text-sm text-gray-500">
        {isLoading ? (
          <Skeleton className="w-24 h-4 rounded-lg">
            <div className="h-4 w-24 rounded-lg bg-default-100"></div>
          </Skeleton>
        ) : (
          <p>≈ {isBalanceHidden ? hideNumber(formatNumber(totalBalance)) : `$${formatNumber(totalBalance)}`}</p>
        )}
      </div>
    </div>
  );
}