'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  <PERSON>dal<PERSON>ody,
  Modal<PERSON>ooter,
  Button,
  Input,
  Card,
  CardBody,
  addToast,
  Radio,
  RadioGroup
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useApi } from '@/hooks/useApi';
import { useUserStore } from '@/store/userStore';
import { useRouter } from 'next/navigation';
import { useTranslation } from '@/hooks/useTranslation';

interface TransferWmModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function TransferWmModal({ isOpen, onClose }: TransferWmModalProps) {
  const { fetchApi } = useApi();
  const { user, updateUser } = useUserStore();
  const { t } = useTranslation();
  const [amount, setAmount] = useState<string>('');
  const [referralCode, setReferralCode] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [walletType, setWalletType] = useState<'available' | 'freeze'>('available');
  const router = useRouter();

  const handleTransfer = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      addToast({
        title: t('wallet.transfer_wm'),
        description: t('wallet.enter_valid_amount'),
        color: 'danger'
      });
      return;
    }
    if (walletType === 'available' && !referralCode) {
      addToast({
        title: t('wallet.transfer_wm'),
        description: t('wallet.enter_referral_code'),
        color: 'danger'
      });
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetchApi('/wallet/transfer/wm', {
        method: 'POST',
        body: JSON.stringify({
          amount: parseFloat(amount),
          referralCode,
          walletType
        })
      });

      if (response.success) {
        console.log(response.data);

        if (response.data?.user) {
          updateUser(response.data.user);
        }

        addToast({
          title: t('wallet.transfer_wm'),
          description: t('wallet.transfer_processed', { amount: amount, token: 'WM', referral: referralCode }),
          color: 'success'
        });
        setAmount('');
        setReferralCode('');
        onClose();
      } else {
        addToast({
          title: t('wallet.transfer_wm'),
          description: response.message[t('common.language_code')] || t('wallet.transfer_error'),
          color: 'danger'
        });
      }
    } catch (error) {
      addToast({
        title: t('wallet.transfer_wm'),
        description: error.message || t('wallet.transfer_error'),
        color: 'danger'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const networkInfo = {
    name: t('wallet.bnb_smart_chain'),
    chainId: '56',
    explorer: 'https://bscscan.com'
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="sm">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h3 className="text-lg font-semibold">{t('wallet.transfer_wm')}</h3>
          <p className="text-sm text-gray-500">
            {t('wallet.enter_amount_and_referral')}
          </p>
        </ModalHeader>
        <ModalBody>
          <div className="flex flex-col gap-4">
            {/* <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">Network</span>
              <span className="text-sm font-bold">{networkInfo.name}</span>
            </div> */}

            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">{t('wallet.select_wallet_type')}</label>
              <RadioGroup
                value={walletType}
                onValueChange={(value) => setWalletType(value as 'available' | 'freeze')}
                orientation="horizontal"
                className="flex gap-4"
              >
                <Radio value="available" size="sm">
                  {t('wallet.available_balance')}
                </Radio>
                <Radio value="freeze" size="sm">
                  {t('wallet.freeze_balance')}
                </Radio>
              </RadioGroup>
            </div>

            <Input
              type="number"
              label={t('wallet.amount_wm')}
              placeholder="0.00"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              min={0}
              step="0.01"
              endContent={
                <div className="flex items-center">
                  <span className="text-gray-500 text-sm">WM</span>
                </div>
              }
              disabled={isLoading}
            />

            {walletType === 'available' && (
              <Input
                type="text"
                label={t('wallet.referral_code')}
                placeholder={t('wallet.enter_referral_code_placeholder')}
                value={referralCode}
                onChange={(e) => setReferralCode(e.target.value)}
                disabled={isLoading}
              />
            )}

            {walletType === 'available' && user?.web3Wallet?.wmBalance && (
              <div className="text-sm text-gray-500">
                {t('wallet.available_balance')}: {user?.web3Wallet?.wmBalance} WM
              </div>
            )}

            {walletType === 'freeze' && user?.web3Wallet?.wmLockedBalance && (
              <div className="text-sm text-gray-500">
                {t('wallet.freeze_balance')}: {user?.web3Wallet?.wmLockedBalance} WM
              </div>
            )}

            <div className="flex flex-col gap-2 bg-warning-50 p-4 rounded-lg">
              <p className="text-sm font-medium text-warning-700">
                {t('wallet.important_notes')}:
              </p>
              <ul className="text-sm text-warning-600 list-disc list-inside">
                <li>{t('wallet.minimum_transfer')}: 10 WM</li>
                <li>{t('wallet.ensure_sufficient_balance')}</li>
                <li>{t('wallet.network_fees')}: 1%WM</li>
                {/* <li>{t('wallet.processing_time')}</li> */}
              </ul>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            color="primary"
            className="w-full bg-gradient-to-r from-blue-500 to-green-500"
            onPress={handleTransfer}
            isLoading={isLoading}
            disabled={!amount || parseFloat(amount) <= 0 || !referralCode}
          >
            {t('wallet.transfer_now')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}