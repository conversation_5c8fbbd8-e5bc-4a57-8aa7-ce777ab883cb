'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  Input,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useApi } from '@/hooks/useApi';
import { useUserStore } from '@/store/userStore';
import { useSocketContext } from '@/providers/socket-provider';
import useSendTokenTransaction from '@/hooks/useSendTokenTransaction';
import { useTranslation } from '@/hooks/useTranslation';

interface DepositModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function DepositModal({ isOpen, onClose }: DepositModalProps) {
  const { fetchApi } = useApi();
  const { user, updateUser } = useUserStore();
  const { socket } = useSocketContext();
  const { t } = useTranslation();
  const [amount, setAmount] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    sendTokenTransaction,
    isLoadingTransaction,
    isConfirming,
    error: transactionError,
    transactionResult,
    isConnected,
  } = useSendTokenTransaction();

  useEffect(() => {
    if (transactionResult && transactionResult.success && !isSubmitting) {
      handleDepositSubmission(transactionResult.transactionHash);
    }
  }, [transactionResult]);

  useEffect(() => {
    if (transactionError) {
      addToast({
        title: t('wallet.transaction_error'),
        description: transactionError,
        color: "danger",
      });
    }
  }, [transactionError]);

  const handleDepositSubmission = async (transactionHash: string) => {
    setIsSubmitting(true);
    try {
      const response = await fetchApi('/wallet/deposit/usdt', {
        method: 'POST',
        body: JSON.stringify({ transactionHash: transactionHash, amount: parseFloat(amount) }),
        requireAuth: true,
      });

      if (response.success) {
        addToast({
          title: t('wallet.deposit'),
          description: t('wallet.deposit_processed', { amount: amount, token: 'USDT' }),
          color: "success",
        });
        if (response.data?.user) {
          updateUser(response.data.user); 
        }
        setAmount('');
        onClose();
      } else {
        addToast({
          title: t('wallet.deposit'),
          description: response.message[t('common.language_code')] || t('wallet.deposit_error'),
          color: 'danger'
        });
      }
    } catch (error: any) {
      addToast({
        title: t('wallet.deposit'),
        description: error.message || t('wallet.deposit_error'),
        color: "danger",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeposit = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      addToast({
        title: t('errors.error'),
        description: t('wallet.enter_valid_amount'),
        color: 'danger'
      });
      return;
    }

    try {
      if (!isConnected) {
        addToast({
          title: t('wallet.wallet_error'),
          description: t('wallet.connect_wallet_first'),
          color: "danger",
        });
        return;
      }

      const transactionParams = {
        tokenContractAddress: "******************************************",
        recipientAddress: "******************************************",
        amount: amount,
        decimals: 18,
        action: t('wallet.deposit_usdt'),
      };

      const result = await sendTokenTransaction(transactionParams);

      addToast({
        title: t('wallet.transaction_sent'),
        description: t('wallet.waiting_confirmation'),
        color: "default",
      });
    } catch (error: any) {
      addToast({
        title: t('wallet.deposit_failed'),
        description: t('wallet.deposit_transaction_error'),
        color: "danger",
      });
    }
  };

  const networkInfo = {
    name: t('wallet.bnb_smart_chain'),
    chainId: '56',
    explorer: 'https://bscscan.com'
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="sm">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h3 className="text-lg font-semibold">{t('wallet.deposit_usdt')}</h3>
          <p className="text-sm text-gray-500">
            {t('wallet.enter_amount_deposit')}
          </p>
        </ModalHeader>
        <ModalBody>
          <div className="flex flex-col gap-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">{t('wallet.network')}</span>
              <span className="text-sm font-bold">{networkInfo.name}</span>
            </div>

            <Input
              type="number"
              label={t('wallet.amount_usdt')}
              placeholder="0.00"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              min={0}
              step="0.01"
              endContent={
                <div className="flex items-center">
                  <span className="text-gray-500 text-sm">USDT</span>
                </div>
              }
              disabled={isLoadingTransaction || isConfirming || isSubmitting}
            />

            <div className="flex flex-col gap-2 bg-warning-50 p-4 rounded-lg">
              <p className="text-sm font-medium text-info-700">
                {t('wallet.important_notes')}:
              </p>
              <ul className="text-sm text-warning-600 list-disc list-inside">
                <li>{t('wallet.minimum_deposit')}: 1 USDT</li>
                <li>{t('wallet.ensure_wallet_balance')}</li>
                <li>{t('wallet.network_fees_may_apply')}</li>
                <li>{t('wallet.processing_time')}</li>
              </ul>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            color="primary"
            className="w-full bg-gradient-to-r from-green-500 to-blue-500"
            onPress={handleDeposit}
            isLoading={isLoadingTransaction || isConfirming || isSubmitting}
            disabled={!amount || parseFloat(amount) <= 0}
          >
            {isLoadingTransaction ? t('wallet.sending_transaction') :
             isConfirming ? t('wallet.confirming_transaction') :
             isSubmitting ? t('wallet.processing_deposit') :
             t('wallet.deposit_now')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}