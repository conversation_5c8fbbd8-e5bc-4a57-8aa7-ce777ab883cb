'use client';

import React, { useEffect, useState, useRef } from 'react';
import UserGreeting from './components/user-greeting';
import BalanceSection from './components/balance-section';
import GetStartedSection from './components/get-started-section';
import PortfolioSection from './components/portfolio-section';
import WalletSection from './components/wallet-section';
import { MiningSection } from './components/mining-section';
import { useUserStore } from '@/store/userStore';
import { useApi } from '@/hooks/useApi';
import { addToast, Spinner } from '@heroui/react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '@/hooks/useTranslation';

export default function WalletPage() {
  const { user, accessToken, updateUser, hydrate, isHydrated } = useUserStore();
  const { fetchApi } = useApi();
  const router = useRouter();
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const hasFetched = useRef(false);

  useEffect(() => {
    hydrate();
  }, [hydrate]);

  useEffect(() => {
    if (isHydrated && !isLoading && (!accessToken || !user)) {
      router.replace('/');
    }
  }, [isHydrated, isLoading, accessToken, user, router]);

  useEffect(() => {
    let isMounted = true;

    const fetchUserData = async () => {
      if (!accessToken || hasFetched.current) {
        setIsLoading(false);
        return;
      }

      try {
        const response = await fetchApi('/users/me');

        if (!isMounted) return;

        if (response.success && response.data) {
          updateUser(response.data);

          hasFetched.current = true;
        } else {
          addToast({
            title: t('errors.error'),
            description: t('errors.something_went_wrong'),
            color: 'danger'
          });
        }
      } catch (error: any) {
        if (!isMounted) return;
        addToast({
          title: t('errors.error'),
          description: t('errors.please_try_again'),
          color: 'danger'
        });
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    if (isHydrated && !hasFetched.current) {
      fetchUserData();
    } else {
      setIsLoading(false);
    }

    return () => {
      isMounted = false;
    };
  }, [isHydrated, accessToken, updateUser, fetchApi]);

  if (!isHydrated || isLoading) {
    return (
      <div className="fixed inset-0 flex justify-center items-center bg-gray-100 bg-opacity-50 z-50">
        <Spinner color="success" />
      </div>
    );
  }

  if (!accessToken || !user) {
    return null;
  }

  return (
    <div className="flex w-full flex-col gap-3">
      <div className="flex max-w-sm flex-col gap-3">
        <UserGreeting />
        <BalanceSection />
        <WalletSection />
        <MiningSection />
        <GetStartedSection />
        <PortfolioSection />
      </div>
    </div>
  );
}