'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardBody, Input, Button, Textarea, addToast } from '@heroui/react';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { useApi } from '@/hooks/useApi';
import { useSocketContext } from '@/providers/socket-provider';
import useSendTokenTransaction from '@/hooks/useSendTokenTransaction';
import { useTranslation } from '@/hooks/useTranslation';

export default function LightningPage() {
  const { user } = useUserStore();
  const router = useRouter();
  const { fetchApi } = useApi();
  const { socket } = useSocketContext();
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    sendTokenTransaction,
    isLoadingTransaction,
    isConfirming,
    error: transactionError,
    transactionResult,
    isConnected,
  } = useSendTokenTransaction();

  useEffect(() => {
    if (transactionResult && transactionResult.success && !isSubmitting) {
      handleLightningSubmission(transactionResult.transactionHash);
    }
  }, [transactionResult]);

  useEffect(() => {
    if (transactionError) {
      addToast({
        title: t('lightning.transaction_error'),
        description: transactionError,
        color: "danger",
      });
    }
  }, [transactionError]);

  const handleLightningSubmission = async (transactionHash: string) => {
    setIsSubmitting(true);
    try {
      const response = await fetchApi('/wallet/lightning', {
        method: 'POST',
        body: JSON.stringify({ transactionHash }),
        requireAuth: true,
      });

      if (response.success) {
        addToast({
          title: t('lightning.purchased'),
          description: t('lightning.purchase_success'),
          color: "success",
        });
        router.push('/wallet');
      } else {
        addToast({
          title: t('lightning.purchase'),
          description: response.message[t('common.language_code')] || t('lightning.process_error'),
          color: 'danger'
        });
      }
    } catch (error: any) {
      addToast({
        title: t('lightning.purchase_failed'),
        description: error.message || t('lightning.purchase_error'),
        color: "danger",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBuyLightning = async () => {
    try {
      if (!isConnected) {
        addToast({
          title: t('lightning.wallet_error'),
          description: t('lightning.connect_wallet_first'),
          color: "danger",
        });
        return;
      }

      const transactionParams = {
        tokenContractAddress: "******************************************",
        recipientAddress: "******************************************",
        amount: "15",
        decimals: 18,
        action: 'Lightning Boost',
      };

      const result = await sendTokenTransaction(transactionParams);

      addToast({
        title: t('lightning.transaction_sent'),
        description: t('lightning.waiting_confirmation'),
        color: "default",
      });
    } catch (error: any) {
      addToast({
        title: t('lightning.purchase_failed'),
        description: t('lightning.transaction_error'),
        color: "danger",
      });
    }
  };

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex max-w-sm flex-col gap-4">
        <div className="flex items-center justify-between">
          <button onClick={() => router.back()}>
            <Icon icon="solar:arrow-left-linear" width={24} className="text-gray-500" />
          </button>
          <h1 className="text-xl font-semibold">{t('lightning.buy')}</h1>
          <div className="w-6"></div>
        </div>

        <Card>
          <CardBody className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <h2 className="text-lg font-semibold">{t('lightning.title')}</h2>
              <p className="text-sm text-gray-500">
                {t('lightning.description')}
              </p>
            </div>

            <div className="flex flex-col gap-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">{t('lightning.price')}:</span>
                <span className="font-semibold">15 USDT</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">{t('lightning.payment_method')}:</span>
                <span className="font-semibold">USDT (BSC)</span>
              </div>
            </div>

            <div className="flex flex-col gap-2">
              <h3 className="text-md font-semibold">{t('lightning.price')}:</h3>
              <ul className="list-inside list-disc text-sm text-gray-600">
                <li>{t('lightning.cost', { amount: '15 USDT' })}</li>
              </ul>
            </div>

            <div className="flex flex-col gap-2">
              <h3 className="text-md font-semibold">{t('lightning.benefits')}:</h3>
              <ul className="list-inside list-disc text-sm text-gray-600">
                <li>{t('lightning.benefit_mining_rate')}</li>
                <li>{t('lightning.benefit_permanent')}</li>
                <li>{t('lightning.benefit_stackable')}</li>
                <li>{t('lightning.benefit_early_access')}</li>
              </ul>
            </div>

            <Button
              className="w-full bg-gradient-to-tr from-blue-500 to-green-500 text-white shadow-lg"
              onPress={handleBuyLightning}
              isDisabled={isLoadingTransaction || isConfirming || isSubmitting || (user && user.hasLightningBolt)}
            >
              {isLoadingTransaction ? t('lightning.sending_transaction') :
               isConfirming ? t('lightning.confirming_transaction') :
               isSubmitting ? t('lightning.processing_purchase') :
               user && user.hasLightningBolt ? t('lightning.already_purchased') : t('lightning.buy')}
            </Button>

            {transactionResult && (
              <p className="text-green-500 text-sm">
                {t('lightning.transaction_confirmed')}: <a href={`https://bscscan.com/tx/${transactionResult.transactionHash}`} target="_blank">{t('lightning.view_on_bscscan')}</a>
              </p>
            )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
}