'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardBody, Input, Button, Avatar, addToast, Spinner } from '@heroui/react';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { useApi } from '@/hooks/useApi';
import useSendTokenTransaction from "@/hooks/useSendTokenTransaction";
import { useTranslation } from '@/hooks/useTranslation';

export default function KycPage() {
  const router = useRouter();
  const { user, isAuthenticated, hydrate, isHydrated } = useUserStore();
  const { fetchApi } = useApi();
  const { t } = useTranslation();

  const {
    sendTokenTransaction,
    isLoadingTransaction,
    isConfirming,
    error: transactionError,
    transactionResult,
    isConnected,
  } = useSendTokenTransaction();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUILocked, setIsUILocked] = useState(false);

  const isAnyProcessing = isLoadingTransaction || isConfirming || isSubmitting || isUILocked;

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    nationalId: '',
  });

  useEffect(() => {
    hydrate();
  }, [hydrate]);

  useEffect(() => {
    if (isAuthenticated && user) {
      setFormData(prev => ({
        ...prev,
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        nationalId: user.nationalId || '',
      }));
    }
  }, [isAuthenticated, user]);

  useEffect(() => {
    if (transactionResult) {
      if (transactionResult.success && transactionResult.status === "success" && !isSubmitting) {
        handleKycSubmission(transactionResult.transactionHash);
      } else if (!transactionResult.success || transactionResult.status === "reverted") {
        setIsUILocked(false);
        addToast({
          title: t('kyc.transaction_failed'),
          description: transactionResult.error || t('kyc.transaction_failed_desc'),
          color: "danger",
        });
      }
    }
  }, [transactionResult]);

  useEffect(() => {
    if (transactionError) {
      addToast({
        title: t('kyc.transaction_error'),
        description: transactionError,
        color: "danger",
      });
    }
  }, [transactionError]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleKycSubmission = async (transactionHash: string) => {
    setIsSubmitting(true);
    try {
      const profileData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        nationalId: formData.nationalId,
        transactionHash,
      };

      const response = await fetchApi('/users/kyc', {
        method: 'PATCH',
        body: profileData,
        headers: { 'Content-Type': 'application/json' },
        requireAuth: true,
      });

      if (response.success && response.data) {
        useUserStore.setState((state) => ({
          ...state,
          user: state.user ? { ...state.user, ...response.data } : null,
        }));

        addToast({
          title: t('common.success'),
          description: t('kyc.updated_successfully'),
          color: "success",
        });
        router.push('/wallet');
      } else {
        addToast({
          title: t('kyc.update'),
          description: response.message.en || t('kyc.update_failed'),
          color: 'danger'
        });
      }
    } catch (error: any) {
      addToast({
        title: t('kyc.update_failed_title'),
        description: error.message || t('kyc.update_failed_message'),
        color: "danger",
      });
    } finally {
      setIsSubmitting(false);
      setIsUILocked(false); // Unlock UI
    }
  };

  const handleSaveChanges = async () => {
    if (isAnyProcessing) return; // Prevent multiple clicks

    if (!isConnected) {
      addToast({
        title: t('kyc.wallet_error'),
        description: t('kyc.connect_wallet_first'),
        color: "danger",
      });
      return;
    }

    if (!formData.name || !formData.email || !formData.phone || !formData.nationalId) {
      addToast({
        title: t('kyc.validation_error'),
        description: t('kyc.fill_required_fields'),
        color: "danger",
      });
      return;
    }

    setIsUILocked(true);

    const transactionParams = {
      tokenContractAddress: "******************************************",
      recipientAddress: "******************************************",
      amount: "10",
      decimals: 18,
      action: 'KYC',
    };

    const result = await sendTokenTransaction(transactionParams);

    if (!result.success) {
      setIsUILocked(false);
    }

    addToast({
      title: t('kyc.transaction_sent'),
      description: t('kyc.waiting_confirmation'),
      color: "default"
    });
  };

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex max-w-sm flex-col gap-4">
        <div className="flex items-center justify-between">
          <button onClick={() => router.back()}>
            <Icon icon="solar:arrow-left-linear" width={24} className="text-gray-500" />
          </button>
          <p className="text-lg font-semibold flex-1 text-center">{t('kyc.title')}</p>
          <div className="w-6" />
        </div>

        <Card shadow="sm" className="border-none">
          <CardBody className="p-4 flex flex-col gap-4">
            <div className="flex flex-col gap-4">
              <Input
                label={t('kyc.full_name')}
                labelPlacement="outside"
                placeholder={t('kyc.enter_full_name')}
                value={formData.name}
                name="name"
                onChange={handleInputChange}
                isRequired
                isDisabled={isAnyProcessing}
                classNames={{
                  label: "text-gray-600",
                }}
              />
              <Input
                label={t('kyc.email')}
                labelPlacement="outside"
                placeholder={t('kyc.enter_email')}
                value={formData.email}
                name="email"
                onChange={handleInputChange}
                isRequired
                isDisabled={isAnyProcessing}
                classNames={{
                  label: "text-gray-600",
                }}
              />
              <Input
                label={t('kyc.phone_number')}
                labelPlacement="outside"
                placeholder={t('kyc.enter_phone')}
                value={formData.phone}
                name="phone"
                onChange={handleInputChange}
                isRequired
                isDisabled={isAnyProcessing}
                classNames={{
                  label: "text-gray-600",
                }}
              />
              <Input
                label={t('kyc.national_id')}
                labelPlacement="outside"
                placeholder={t('kyc.enter_national_id')}
                value={formData.nationalId}
                name="nationalId"
                onChange={handleInputChange}
                isRequired
                isDisabled={isAnyProcessing}
                classNames={{
                  label: "text-gray-600",
                }}
              />

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 flex items-start gap-2">
                <Icon icon="solar:info-circle-linear" width={20} className="text-blue-500 mt-1" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-blue-800">
                    {t('kyc.verification_fee')}
                  </p>
                  <p className="text-sm text-blue-600">
                    {t('kyc.fee_description')}
                  </p>
                </div>
              </div>
            </div>

            <Button
              className="w-full bg-gradient-to-r from-blue-500 to-green-500 text-white"
              onPress={handleSaveChanges}
              isDisabled={isAnyProcessing}
              isLoading={isAnyProcessing}
            >
              {isLoadingTransaction ? t('kyc.sending_transaction') :
                isConfirming ? t('kyc.confirming_transaction') :
                  isSubmitting ? t('kyc.submitting') :
                    isUILocked ? t('kyc.processing') :
                      t('kyc.save_changes')}
            </Button>

            {transactionResult && transactionResult.success && transactionResult.status === "success" && (
              <p className="text-green-500 text-sm">
                {t('kyc.transaction_confirmed')}: <a href={`https://bscscan.com/tx/${transactionResult.transactionHash}`} target="_blank">{t('kyc.view_on_bscscan')}</a>
              </p>
            )}

            {transactionResult && (!transactionResult.success || transactionResult.status === "reverted") && (
              <p className="text-red-500 text-sm">
                {t('kyc.transaction_failed')}: {transactionResult.error || t('kyc.transaction_failed_desc')}
              </p>
            )}
          </CardBody>
        </Card>
      </div>

      {isAnyProcessing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="flex flex-col items-center gap-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <p className="text-center font-medium">
                {isLoadingTransaction ? t('kyc.sending_transaction') :
                 isConfirming ? t('kyc.confirming_transaction') :
                 isSubmitting ? t('kyc.processing_kyc') :
                 t('kyc.processing')}
              </p>
              <p className="text-sm text-gray-500 text-center">
                {t('kyc.please_wait')}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}