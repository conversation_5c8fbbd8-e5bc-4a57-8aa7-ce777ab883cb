'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardBody, Input, Button, Avatar, addToast, Spinner } from '@heroui/react';
import MobileSafeInput from '@/components/mobile-safe-input';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { useApi } from '@/hooks/useApi';
import { useTranslation } from '@/hooks/useTranslation';

export default function EditProfilePage() {
  const router = useRouter();
  const { user, isAuthenticated } = useUserStore();
  const { fetchApi } = useApi();
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    avatar: '',
    name: '',
    email: '',
    phone: '',
  });

  useEffect(() => {
    if (isAuthenticated && user) {
      setFormData(prev => ({
        ...prev,
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
      }));
    }
  }, [isAuthenticated, user]);

  const [avatarSrc, setAvatarSrc] = useState<string | null>(formData.avatar);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSaveChanges = async () => {
    try {
      if (!formData.name || !formData.email || !formData.phone) {
        addToast({
          title: t('edit_profile.update_profile'),
          description: t('edit_profile.fill_required_fields'),
          color: "danger",
        });
        return;
      }

      const profileData: {
        name: string;
        email: string;
        phone?: string;
        avatar?: string;
      } = {
        name: formData.name,
        email: formData.email,
      };

      if (formData.phone) {
        profileData.phone = formData.phone;
      }

      if (formData.avatar) {
        profileData.avatar = formData.avatar;
      }

      const response = await fetchApi('/users/profile', {
        method: 'PATCH',
        body: JSON.stringify(profileData),
        headers: {
          'Content-Type': 'application/json'
        },
        requireAuth: true
      });

      if (response.success && response.data) {
        useUserStore.setState((state) => ({
          ...state,
          user: state.user ? { ...state.user, ...response.data } : null
        }));

        addToast({
          title: t('edit_profile.update_profile'),
          description: t('edit_profile.profile_updated'),
          color: "success",
        });

        router.push('/account');
      } else {
        addToast({
          title: t('edit_profile.update_profile'),
          description: response.message.en || t('edit_profile.update_failed'),
          color: 'danger'
        });
      }
    } catch (error: any) {
      addToast({
        title: t('edit_profile.update_profile'),
        description: error.message || t('edit_profile.update_error'),
        color: "danger",
      });
    }
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const url = URL.createObjectURL(file);
      setAvatarSrc(url);
      setFormData((prev) => ({ ...prev, avatar: url }));
    }
  };

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex max-w-sm flex-col gap-4">
        <div className="flex items-center justify-between">
          <button onClick={() => router.back()}>
            <Icon icon="solar:arrow-left-linear" width={24} className="text-gray-500" />
          </button>
          <p className="text-lg font-semibold flex-1 text-center">{t('edit_profile.title')}</p>
          <div className="w-6" />
        </div>

        <Card shadow="sm" className="border-none">
          <CardBody className="p-4 flex flex-col gap-4">
            <div className="flex flex-col items-center gap-2">
              <div className="relative">
                <Avatar
                  src={avatarSrc ?? undefined}
                  size="lg"
                  className="w-20 h-20"
                  fallback={<Icon icon="solar:user-linear" width={40} />}
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="absolute bottom-[0px] right-[0px] bg-success text-white rounded-full p-1.5"
                >
                  <Icon icon="solar:pen-linear" width={12} />
                </button>
                <input
                  type="file"
                  accept="image/*"
                  ref={fileInputRef}
                  onChange={handleAvatarChange}
                  className="hidden"
                />
              </div>
            </div>

            <div className="flex flex-col gap-4 mobile-input-fix">
              <Input
                label={t('edit_profile.full_name')}
                labelPlacement="outside"
                placeholder={t('edit_profile.enter_full_name')}
                value={formData.name}
                name="name"
                onChange={handleInputChange}
                isRequired
                classNames={{
                  label: "text-gray-600",
                  inputWrapper: "mobile-input-wrapper",
                }}
              />
              <Input
                label={t('edit_profile.email')}
                labelPlacement="outside"
                placeholder={t('edit_profile.enter_email')}
                value={formData.email}
                name="email"
                onChange={handleInputChange}
                isRequired
                classNames={{
                  label: "text-gray-600",
                  inputWrapper: "mobile-input-wrapper",
                }}
              />
              <Input
                label={t('edit_profile.phone_number')}
                labelPlacement="outside"
                placeholder={t('edit_profile.enter_phone')}
                value={formData.phone}
                name="phone"
                onChange={handleInputChange}
                isRequired
                classNames={{
                  label: "text-gray-600",
                  inputWrapper: "mobile-input-wrapper",
                }}
              />
            </div>

            <Button
              className="w-full bg-gradient-to-r from-blue-500 to-green-500 text-white"
              onPress={handleSaveChanges}
            >
              {t('edit_profile.save_changes')}
            </Button>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}