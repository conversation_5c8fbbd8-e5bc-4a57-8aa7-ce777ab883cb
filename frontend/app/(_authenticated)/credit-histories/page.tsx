'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { DatePicker } from '@heroui/react';
import { Icon } from '@iconify/react';
import { parseDate, DateValue, CalendarDate, CalendarDateTime, ZonedDateTime } from '@internationalized/date';
import CardTransaction from './components/card-transaction';
import { useTranslation } from '@/hooks/useTranslation';

type CreditHistory = {
  id: number;
  title: string;
  description: string;
  credits: number;
  date: string;
  icon: string;
};

type DateRange = {
  startDate: DateValue;
  endDate: DateValue;
};

export default function CreditHistoriesPage() {
  const router = useRouter();
  const { t } = useTranslation();

  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: parseDate('2025-03-01'),
    endDate: parseDate('2025-03-31'),
  });

  const creditHistories: CreditHistory[] = [
    {
      id: 1,
      title: t('credit_histories.bonus'),
      description: t('credit_histories.bonus_invite_friend'),
      credits: 7,
      date: '05/03/2025',
      icon: 'solar:heart-linear',
    },
    {
      id: 2,
      title: t('credit_histories.bonus'),
      description: t('credit_histories.bonus_invite_friend'),
      credits: 7,
      date: '04/03/2025',
      icon: 'solar:heart-linear',
    },
    {
      id: 3,
      title: t('credit_histories.bonus'),
      description: t('credit_histories.bonus_invite_friend'),
      credits: 7,
      date: '04/03/2025',
      icon: 'solar:heart-linear',
    },
    {
      id: 4,
      title: t('credit_histories.bonus'),
      description: t('credit_histories.bonus_invite_friend'),
      credits: 7,
      date: '03/03/2025',
      icon: 'solar:heart-linear',
    },
    {
      id: 5,
      title: t('credit_histories.bonus'),
      description: t('credit_histories.bonus_invite_friend'),
      credits: 7,
      date: '03/03/2025',
      icon: 'solar:heart-linear',
    },
    {
      id: 6,
      title: t('credit_histories.bonus'),
      description: t('credit_histories.bonus_invite_friend'),
      credits: 7,
      date: '02/03/2025',
      icon: 'solar:heart-linear',
    },
    {
      id: 7,
      title: t('credit_histories.bonus'),
      description: t('credit_histories.bonus_invite_friend'),
      credits: 7,
      date: '02/03/2025',
      icon: 'solar:heart-linear',
    },
    {
      id: 8,
      title: t('credit_histories.bonus'),
      description: t('credit_histories.bonus_new_account'),
      credits: 14,
      date: '01/03/2025',
      icon: 'solar:heart-linear',
    },
  ];

  const handleStartDateChange = (value: CalendarDate | CalendarDateTime | ZonedDateTime | null) => {
    if (value) {
      setDateRange((prev) => ({ ...prev, startDate: value as DateValue }));
    }
  };

  const handleEndDateChange = (value: CalendarDate | CalendarDateTime | ZonedDateTime | null) => {
    if (value) {
      setDateRange((prev) => ({ ...prev, endDate: value as DateValue }));
    }
  };

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex max-w-sm flex-col gap-4">
        <div className="flex items-center justify-between">
          <button onClick={() => router.back()}>
            <Icon icon="solar:arrow-left-linear" width={24} className="text-gray-500" />
          </button>
          <p className="text-lg font-semibold flex-1 text-center">{t('credit_histories.title')}</p>
          <div className="w-6" />
        </div>

        <div className="flex items-center gap-2">
          <DatePicker
            value={dateRange.startDate}
            onChange={handleStartDateChange}
            className="flex-1"
            granularity="day"
            classNames={{
              inputWrapper: "h-10",
            }}
          />
          <span className="text-gray-500">-</span>
          <DatePicker
            value={dateRange.endDate}
            onChange={handleEndDateChange}
            className="flex-1"
            granularity="day"
            classNames={{
              inputWrapper: "h-10",
            }}
          />
        </div>

        <div className="flex flex-col gap-3">
          {creditHistories.map((history) => (
            <CardTransaction
              key={history.id}
              title={history.title}
              description={history.description}
              credits={history.credits}
              date={history.date}
              icon={history.icon}
            />
          ))}
        </div>
      </div>
    </div>
  );
}