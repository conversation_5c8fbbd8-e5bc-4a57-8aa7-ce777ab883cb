'use client';

import React from 'react';
import { Card, CardBody } from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';

type CardTransactionProps = {
  title: string;
  description: string;
  credits: number;
  date: string;
  icon: string;
};

export default function CardTransaction({ title, description, credits, date, icon }: CardTransactionProps) {
  const { t } = useTranslation();
  return (
    <Card shadow="sm" className="border-none">
      <CardBody className="p-4 flex flex-row items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-pink-100">
          <Icon icon={icon} width={20} className="text-pink-500" />
        </div>
        <div className="flex-1">
          <p className="text-sm">{title}</p>
          <p className="text-sm text-gray-500">{description}</p>
        </div>
        <div className="flex flex-col items-end">
          <p className={`text-sm ${credits > 0 ? 'text-success' : 'text-danger'}`}>
            {credits > 0 ? '+' : ''}{credits} {t('credit_histories.credits')}
          </p>
          <p className="text-sm text-gray-500">{date}</p>
        </div>
      </CardBody>
    </Card>
  );
}