'use client';

import React from 'react';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import CardTransfer from '@/components/card-tranfer';
import { useTranslation } from '@/hooks/useTranslation';

export default function TransferPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useUserStore();
  const { t } = useTranslation();

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex max-w-sm flex-col gap-4">
        <div className="flex items-center justify-between">
          <button onClick={() => router.back()}>
            <Icon icon="solar:arrow-left-linear" width={24} className="text-gray-500" />
          </button>
          <p className="text-lg font-semibold flex-1 text-center">{t('wallet.transfer')}</p>
          <div className="w-6" />
        </div>


        <CardTransfer apiEndpoint={''} usdtContractAddress={'******************************************'} />
      </div>
    </div>
  );
}