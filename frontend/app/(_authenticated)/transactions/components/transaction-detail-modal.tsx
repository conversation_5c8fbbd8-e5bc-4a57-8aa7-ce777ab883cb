'use client';

import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { Transaction } from '@/types/models';
import { formatNumber } from '@/utils/format';
import moment from "moment-timezone";
import { useTranslation } from '@/hooks/useTranslation';

interface TransactionDetailModalProps {
  transaction: Transaction | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function TransactionDetailModal({
  transaction,
  isOpen,
  onClose
}: TransactionDetailModalProps) {
  const { t } = useTranslation();
  if (!transaction) return null;

  const formatDate = (date?: Date | string) => {
    if (!date) return 'N/A';
    return moment(date).tz('Asia/Dubai').format('DD/MM/YYYY HH:mm:ss');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'PENDING':
        return 'warning';
      case 'FAILED':
        return 'danger';
      case 'EXPIRED':
        return 'default';
      default:
        return 'default';
    }
  };

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case 'DEPOSIT':
        return 'solar:download-square-linear';
      case 'WITHDRAWAL':
        return 'solar:upload-square-linear';
      case 'TRANSFER':
        return 'solar:repeat-linear';
      case 'MINING':
        return 'solar:mining-linear';
      case 'REFERRAL_BONUS':
        return 'solar:user-plus-rounded-linear';
      case 'TASK_REWARD':
        return 'solar:medal-ribbon-linear';
      case 'LIGHTNING_BOLT_PURCHASE':
        return 'solar:flash-linear';
      case 'PRIORITY_POSITION_PURCHASE':
        return 'solar:crown-linear';
      default:
        return 'solar:document-linear';
    }
  };

  const formatTransactionType = (type: string) => {
    return type.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  };

  const shortenAddress = (address: string) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}

      size="sm"
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon
              icon={getTransactionTypeIcon(transaction.type)}
              className="text-primary"
              width={24}
            />
            <h3 className="text-lg font-semibold">
              {formatTransactionType(transaction.type)}
            </h3>
          </div>
        </ModalHeader>
        <ModalBody>
          <div className="flex flex-col gap-4">
            {/* Amount and Status */}
            <div className="flex justify-between items-center">
              <div className="flex flex-col gap-1">
                <span className="text-sm text-gray-500">{t('transactions.amount')}</span>
                <span className="text-xl font-bold">
                  {formatNumber(transaction.amount)} {transaction.token?.symbol}
                </span>
              </div>
              <Chip
                color={getStatusColor(transaction.status)}
                variant="flat"
              >
                {transaction.status}
              </Chip>
            </div>

            {/* Date and Time */}
            <div className="flex flex-col gap-1">
              <span className="text-sm text-gray-500">{t('transactions.date_time')}</span>
              <span className="text-medium">{formatDate(transaction.createdAt)}</span>
            </div>

            {/* Transaction Hash if exists */}
            {transaction.txHash && (
              <div className="flex flex-col gap-1">
                <span className="text-sm text-gray-500">{t('transactions.transaction_hash')}</span>
                <div className="flex items-center gap-2">
                  <code className="text-xs bg-gray-100 p-2 rounded flex-1 break-all">
                    {transaction.txHash}
                  </code>
                  <Button
                    isIconOnly
                    variant="light"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(transaction.txHash || '');
                    }}
                  >
                    <Icon icon="solar:copy-linear" width={16} />
                  </Button>
                </div>
              </div>
            )}

            {/* From/To Addresses */}
            {(transaction.senderAddress || transaction.recipientAddress) && (
              <div className="grid grid-cols-2 gap-4">
                {transaction.senderAddress && (
                  <div className="flex flex-col gap-1">
                    <span className="text-sm text-gray-500">{t('transactions.from')}</span>
                    <div className="flex items-center gap-2">
                      <code className="text-xs bg-gray-100 p-2 rounded">
                        {shortenAddress(transaction.senderAddress)}
                      </code>
                      <Button
                        isIconOnly
                        variant="light"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(transaction.senderAddress || '');
                        }}
                      >
                        <Icon icon="solar:copy-linear" width={16} />
                      </Button>
                    </div>
                  </div>
                )}
                {transaction.recipientAddress && (
                  <div className="flex flex-col gap-1">
                    <span className="text-sm text-gray-500">{t('transactions.to')}</span>
                    <div className="flex items-center gap-2">
                      <code className="text-xs bg-gray-100 p-2 rounded">
                        {shortenAddress(transaction.recipientAddress)}
                      </code>
                      <Button
                        isIconOnly
                        variant="light"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(transaction.recipientAddress || '');
                        }}
                      >
                        <Icon icon="solar:copy-linear" width={16} />
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Note if exists */}
            {transaction.note && (
              <div className="flex flex-col gap-1">
                <span className="text-sm text-gray-500">{t('transactions.note')}</span>
                <p className="text-medium bg-gray-50 p-3 rounded-lg">
                  {transaction.note}
                </p>
              </div>
            )}
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="light"
            onPress={onClose}
          >
            {t('common.close')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}