'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardBody, Button, addToast, useDisclosure, Chip, Tabs, Tab } from '@heroui/react';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { useApi } from '@/hooks/useApi';
import { Transaction } from '@/types/models';
import { formatNumber } from '@/utils/format';
import TransactionDetailModal from './components/transaction-detail-modal';
import moment from 'moment-timezone';
import { useTranslation } from '@/hooks/useTranslation';

interface TransactionHistoryResponse {
  transactions: Transaction[];
  total: number;
  page: number;
  limit: number;
}

export default function TransactionsPage() {
  const router = useRouter();
  const { user, accessToken, isAuthenticated } = useUserStore();
  const { fetchApi } = useApi();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { t } = useTranslation();

  const [isLoading, setIsLoading] = useState(false);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [limit] = useState(10);
  const [selectedType, setSelectedType] = useState<string>('all');

  useEffect(() => {
    if (!isAuthenticated || !accessToken) {
      router.push('/');
      return;
    }

    fetchTransactions(currentPage);
  }, [currentPage, isAuthenticated, accessToken, selectedType]);

  const fetchTransactions = async (page: number) => {
    if (!isAuthenticated || !accessToken) return;

    try {
      setIsLoading(true);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });

      if (selectedType !== 'all') {
        params.append('type', selectedType);
      }

      const response = await fetchApi(`/wallet/transactions/history?${params.toString()}`, {
        method: 'GET',
        requireAuth: true
      });

      if (response.success) {
        const data: TransactionHistoryResponse = response.data;
        setTransactions(data.transactions);
        setTotalPages(Math.ceil(data.total / limit));
      }
    } catch (error) {
      addToast({
        title: t('errors.error'),
        description: t('transactions.load_error'),
        color: "danger",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTransactionClick = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    onOpen();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'PENDING':
        return 'warning';
      case 'FAILED':
        return 'danger';
      case 'EXPIRED':
        return 'default';
      default:
        return 'default';
    }
  };

  const formatDate = (date?: Date | string) => {
    if (!date) return 'N/A';
    return moment(date).tz('Asia/Dubai').format('DD/MM/YYYY HH:mm:ss');
  };

  const formatTransactionType = (type: string) => {
    return type.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  };

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex max-w-2xl flex-col gap-4">
        <div className="flex items-center justify-between">
          <button onClick={() => router.back()}>
            <Icon icon="solar:arrow-left-linear" width={24} className="text-gray-500" />
          </button>
          <h1 className="text-xl font-semibold">{t('transactions.history')}</h1>
          <div className="w-6"></div>
        </div>

        <Tabs
          aria-label="Transaction types"
          selectedKey={selectedType}
          onSelectionChange={(key) => {
            setSelectedType(key as string);
            setCurrentPage(1);
          }}
          className="p-0"
          variant="light"
        >
          <Tab key="ALL" title={t('transactions.all')} />
          <Tab key="DEPOSIT" title={t('transactions.deposits')} />
          <Tab key="WITHDRAWAL" title={t('transactions.withdrawals')} />
          <Tab key="TRANSFER" title={t('transactions.transfers')} />
          <Tab key="MINING" title={t('transactions.mining')} />
          <Tab key="REFERRAL_BONUS" title={t('transactions.referral_bonus')} />
          <Tab key="REFERRAL_BONUS_MINING" title={t('transactions.mining_referral')} />
          <Tab key="TASK_REWARD" title={t('transactions.task_rewards')} />
          <Tab key="UPDATE_PROFILE_BONUS" title={t('transactions.profile_bonus')} />
          <Tab key="KYC_VERIFY_BONUS" title={t('transactions.kyc_bonus')} />
          <Tab key="KYC_VERIFY_COMMISSION" title={t('transactions.kyc_commission')} />
          <Tab key="KYC_PURCHASE" title={t('transactions.kyc_purchase')} />
          <Tab key="LIGHTNING_BOLT_PURCHASE" title={t('transactions.lightning_bolt')} />
          <Tab key="PRIORITY_POSITION_PURCHASE" title={t('transactions.priority_position')} />
          <Tab key="NFT_PURCHASE" title={t('transactions.nft_purchase')} />
          <Tab key="NFT_WITHDRAWAL" title={t('transactions.nft_withdrawal')} />
          <Tab key="NFT_SALE" title={t('transactions.nft_sale')} />
          <Tab key="SALE_BONUS" title={t('transactions.sale_bonus')} />
          <Tab key="DIRECT_COMMISSION" title={t('transactions.direct_commission')} />
          <Tab key="RANKING_COMMISSION" title={t('transactions.ranking_commission')} />
          <Tab key="CO_SHAREHOLDER_BONUS" title={t('transactions.co_shareholder_bonus')} />
          <Tab key="GAS_FEE" title={t('transactions.gas_fee')} />
          <Tab key="SCHEDULING_FEE" title={t('transactions.scheduling_fee')} />
          <Tab key="SYSTEM_COMMISSION" title={t('transactions.system_commission')} />

          <Tab key="GLOBAL_MINING_COSHARE" title={t('transactions.global_mining_coshare')} />
          <Tab key="GLOBAL_MINING_COSHARE_INTEREST" title={t('transactions.coshare_interest')} />
          <Tab key="GLOBAL_MINING_COSHARE_BONUS" title={t('transactions.coshare_bonus')} />
          <Tab key="GLOBAL_MINING_COSHARE_COMMISSION_DIRECT" title={t('transactions.coshare_direct_comm')} />
          <Tab key="GLOBAL_MINING_COSHARE_COMMISSION_MATCHING" title={t('transactions.coshare_matching_comm')} />
          <Tab key="GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING" title={t('transactions.coshare_profit_comm')} />
          <Tab key="GLOBAL_MINING_COSHARE_COMMISSION_RANK" title={t('transactions.coshare_rank_comm')} />
          <Tab key="GLOBAL_MINING_COSHARE_COMMISSION_SAME_LEVEL" title={t('transactions.coshare_same_level')} />
          <Tab key="GLOBAL_MINING_COSHARE_REFERRAL_MILESTONE" title={t('transactions.coshare_milestone')} />
          <Tab key="GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER" title={t('transactions.coshare_shareholder')} />
        </Tabs>

        {isLoading ? (
          <div className="flex justify-center p-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </div>
        ) : transactions.length === 0 ? (
          <div className="flex flex-col items-center justify-center gap-2 p-8">
            <Icon icon="solar:document-linear" className="text-gray-400" width={48} />
            <p className="text-gray-500">{t('transactions.no_transactions')}</p>
          </div>
        ) : (
          <>
            <div className="flex flex-col divide-y">
              {transactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="flex flex-col p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => handleTransactionClick(transaction)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="font-small">{formatTransactionType(transaction.type)}</span>
                    </div>
                    <Chip
                      size="sm"
                      color={getStatusColor(transaction.status)}
                      variant="flat"
                    >
                      {transaction.status}
                    </Chip>
                  </div>

                  <div className="flex flex-col gap-1 text-sm">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-500">{t('transactions.amount')}</span>
                      <span className={`font-medium ${transaction.amount < 0 ? 'text-red-500' : 'text-green-500'}`}>
                        {formatNumber(transaction.amount)} {transaction.token?.symbol}
                      </span>
                    </div>

                    {/* Date Row */}
                    <div className="flex justify-between items-center">
                      <span className="text-gray-500">{t('transactions.date')}</span>
                      <span className="text-gray-600">{formatDate(transaction.createdAt)}</span>
                    </div>

                    {/* Note Row - Only show if exists */}
                    {transaction.note && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">{t('transactions.note')}</span>
                        <span className="text-gray-600 truncate max-w-[200px]">{transaction.note}</span>
                      </div>
                    )}

                    {/* From/To Address - Only show if exists */}
                    {transaction.senderAddress && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">{t('transactions.from')}</span>
                        <span className="text-gray-600 font-mono text-xs">
                          {transaction.senderAddress.substring(0, 6)}...{transaction.senderAddress.substring(transaction.senderAddress.length - 4)}
                        </span>
                      </div>
                    )}
                    {transaction.recipientAddress && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">{t('transactions.to')}</span>
                        <span className="text-gray-600 font-mono text-xs">
                          {transaction.recipientAddress.substring(0, 6)}...{transaction.recipientAddress.substring(transaction.recipientAddress.length - 4)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 p-4">
                <Button
                  isIconOnly
                  variant="light"
                  onPress={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  <Icon icon="solar:arrow-left-linear" />
                </Button>
                <span className="text-sm text-gray-500">
                  {t('transactions.page')} {currentPage} {t('transactions.of')} {totalPages}
                </span>
                <Button
                  isIconOnly
                  variant="light"
                  onPress={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  <Icon icon="solar:arrow-right-linear" />
                </Button>
              </div>
            )}
          </>
        )}
      </div>

      <TransactionDetailModal
        isOpen={isOpen}
        onClose={onClose}
        transaction={selectedTransaction}
      />
    </div>
  );
}
