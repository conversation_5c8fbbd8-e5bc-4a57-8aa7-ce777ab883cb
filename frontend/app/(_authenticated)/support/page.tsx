'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardBody, Input, Button, Textarea, addToast, Accordion, AccordionItem, Chip } from '@heroui/react';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { useTranslation } from '@/hooks/useTranslation';
import { useSupportService, Ticket } from '@/services/support.service';

export default function SupportPage() {
  const router = useRouter();
  const { user } = useUserStore();
  const { t } = useTranslation();
  const supportService = useSupportService();

  const userId = user?.referralCode || 'N/A';
  const walletAddress = user?.wallet || 'N/A';

  const [formData, setFormData] = useState({
    content: '',
    txHash: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchTickets = async () => {
    setIsLoading(true);
    try {
      const response = await supportService.getUserTickets();

      if (response.success && response.data) {
        setTickets(response.data);
      } else {
        addToast({
          title: t('errors.error'),
          description: typeof response.message === 'string' ? response.message : (response.message?.en || t('support.send_failed')),
          color: "danger",
        });
      }
    } catch (error) {
      console.error('Error fetching tickets:', error);
      addToast({
        title: t('errors.error'),
        description: t('support.send_failed'),
        color: "danger",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTickets();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async () => {
    if (!formData.content.trim()) {
      addToast({
        title: t('support.validation_error'),
        description: t('support.fill_required_fields'),
        color: "danger",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await supportService.createTicket({
        content: formData.content,
        txHash: formData.txHash || undefined,
        walletAddress: walletAddress,
      });

      if (response.success && response.data) {
        setTickets([response.data, ...tickets]);

        addToast({
          title: t('support.ticket_sent'),
          description: t('support.ticket_submitted'),
          color: "success",
        });

        // Reset form
        setFormData({
          content: '',
          txHash: '',
        });
      } else {
        addToast({
          title: t('errors.error'),
          description: typeof response.message === 'string' ? response.message : (response.message?.en || t('support.send_failed')),
          color: "danger",
        });
      }
    } catch (error) {
      addToast({
        title: t('errors.error'),
        description: t('support.send_failed'),
        color: "danger",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStatusChip = (status: 'pending' | 'processing' | 'resolved') => {
    let color: "primary" | "warning" | "success" = "primary";
    let text = t('support.status_pending');

    if (status === 'processing') {
      color = "warning";
      text = t('support.status_processing');
    } else if (status === 'resolved') {
      color = "success";
      text = t('support.status_resolved');
    }

    return <Chip color={color} size="sm">{text}</Chip>;
  };

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex max-w-sm flex-col gap-4">
        <div className="flex items-center justify-between">
          <button onClick={() => router.back()}>
            <Icon icon="solar:arrow-left-linear" width={24} className="text-green-500" />
          </button>
          <p className="text-lg font-semibold flex-1 text-center">{t('support.title')}</p>
          <div className="w-6" />
        </div>

        <Card className="w-full">
          <CardBody className="p-4">
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-3 mb-2">
                <Icon icon="solar:ticket-bold-duotone" className="text-green-500" width={24} />
                <h3 className="text-lg font-semibold">{t('support.contact_support')}</h3>
              </div>

              <Input
                label={t('support.ticket_id')}
                labelPlacement="outside"
                value={userId}
                isDisabled
                classNames={{
                  label: "text-gray-600",
                }}
              />

              <Input
                label={t('support.wallet_address')}
                labelPlacement="outside"
                value={walletAddress}
                isDisabled
                classNames={{
                  label: "text-gray-600",
                }}
              />

              <Textarea
                label={t('support.content')}
                labelPlacement="outside"
                placeholder={t('support.content_placeholder')}
                value={formData.content}
                name="content"
                onChange={handleInputChange}
                isRequired
                minRows={4}
                classNames={{
                  label: "text-gray-600",
                }}
              />

              <Input
                label={t('support.tx_hash')}
                labelPlacement="outside"
                placeholder={t('support.tx_hash_placeholder')}
                value={formData.txHash}
                name="txHash"
                onChange={handleInputChange}
                classNames={{
                  label: "text-gray-600",
                }}
              />

              <Button
                className="w-full bg-gradient-to-tr from-blue-500 to-green-500 text-white shadow-lg"
                onPress={handleSubmit}
                isLoading={isSubmitting}
              >
                {t('support.send_message')}
              </Button>
            </div>
          </CardBody>
        </Card>

        {tickets.length > 0 && (
          <Card className="w-full mt-2">
            <CardBody>
              <div className="flex items-center gap-3 mb-4">
                <Icon icon="solar:ticket-list-bold-duotone" className="text-green-500" width={24} />
                <h3 className="text-lg font-semibold">Ticket History</h3>
              </div>

              <div className="flex flex-col gap-4">
                {tickets.map((ticket, index) => (
                  <Card key={index} className="w-full">
                    <CardBody className="p-3">
                      <div className="flex justify-between items-start mb-2">
                        <div className="font-medium">{ticket.ticketCode}</div>
                        {renderStatusChip(ticket.status)}
                      </div>
                      <p className="text-sm mb-2">{ticket.content}</p>
                      {ticket.txHash && (
                        <div className="text-xs text-gray-500 mb-2">
                          <span className="font-medium">TX Hash: </span>
                          <span className="font-mono">{ticket.txHash}</span>
                        </div>
                      )}
                      <div className="text-xs text-gray-500 mb-2">
                        {new Date(ticket.createdAt).toLocaleString()}
                      </div>

                      {ticket.status !== 'pending' && (
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <div className="text-sm font-medium mb-1">{t('support.response')}:</div>
                          <p className="text-sm">
                            {ticket.response || t('support.no_response')}
                          </p>
                        </div>
                      )}
                    </CardBody>
                  </Card>
                ))}
              </div>
            </CardBody>
          </Card>
        )}

        <Card className="w-full mt-2">
          <CardBody>
            <div className="flex items-center gap-3 mb-4">
              <Icon icon="solar:document-text-bold-duotone" className="text-green-500" width={24} />
              <h3 className="text-lg font-semibold">{t('support.faq')}</h3>
            </div>

            <Accordion>
              <AccordionItem
                key="1"
                aria-label={t('support.faq_wallet_title')}
                title={t('support.faq_wallet_title')}
                classNames={{
                  title: "text-medium font-medium",
                  content: "text-sm text-default-500"
                }}
              >
                {t('support.faq_wallet_content')}
              </AccordionItem>

              <AccordionItem
                key="2"
                aria-label={t('support.faq_referrals_title')}
                title={t('support.faq_referrals_title')}
                classNames={{
                  title: "text-medium font-medium",
                  content: "text-sm text-default-500"
                }}
              >
                {t('support.faq_referrals_content')}
              </AccordionItem>

              <AccordionItem
                key="3"
                aria-label={t('support.faq_nft_title')}
                title={t('support.faq_nft_title')}
                classNames={{
                  title: "text-medium font-medium",
                  content: "text-sm text-default-500"
                }}
              >
                {t('support.faq_nft_content')}
              </AccordionItem>

              <AccordionItem
                key="4"
                aria-label={t('support.faq_security_title')}
                title={t('support.faq_security_title')}
                classNames={{
                  title: "text-medium font-medium",
                  content: "text-sm text-default-500"
                }}
              >
                {t('support.faq_security_content')}
              </AccordionItem>

              <AccordionItem
                key="5"
                aria-label={t('support.faq_withdraw_title')}
                title={t('support.faq_withdraw_title')}
                classNames={{
                  title: "text-medium font-medium",
                  content: "text-sm text-default-500"
                }}
              >
                {t('support.faq_withdraw_content')}
              </AccordionItem>
            </Accordion>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}