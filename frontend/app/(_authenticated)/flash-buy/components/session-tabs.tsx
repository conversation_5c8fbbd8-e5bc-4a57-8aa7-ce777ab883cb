'use client';

import React, { useState } from 'react';
import { Tabs, Tab } from '@heroui/react';
import { GroupedSessions, NFTTradingSession } from '@/types/models';
import moment from 'moment-timezone';
import SessionCard from './session-card';
import { useTranslation } from '@/hooks/useTranslation';

interface SessionTabsProps {
  groupedSessions: GroupedSessions;
  onSessionsUpdate?: (updatedSessions: GroupedSessions) => void;
}

export default function SessionTabs({ groupedSessions, onSessionsUpdate }: SessionTabsProps) {
  const { t } = useTranslation();
  const [sessions, setSessions] = useState<GroupedSessions>(groupedSessions);

  const dates = Object.keys(sessions).sort((a, b) =>
    moment(a).diff(moment(b))
  );
  const handleSessionUpdate = (updatedSession: NFTTradingSession) => {
    const date = moment(updatedSession.date).format('YYYY-MM-DD');
    const updatedSessions = { ...sessions };

    if (updatedSessions[date]) {
      updatedSessions[date] = updatedSessions[date].map(session =>
        session.id === updatedSession.id ? updatedSession : session
      );
      setSessions(updatedSessions);
      onSessionsUpdate?.(updatedSessions);
    }
  };

  return (
    <Tabs
      className="w-full"
      radius='full'
      defaultSelectedKey={dates[0]}
    >
      {dates.map((date) => (
        <Tab
          key={date}
          title={moment(date).format('DD/MM')}
          className="w-full"
        >
          <div className='flex flex-col gap-4'>
            {sessions[date].map((session) => (
              <SessionCard
                key={session.id}
                session={session}
                onSessionUpdate={handleSessionUpdate}
                groupedSessions={sessions}
                onSessionsUpdate={setSessions}
              />
            ))}
          </div>
        </Tab>
      ))}
    </Tabs>
  );
}