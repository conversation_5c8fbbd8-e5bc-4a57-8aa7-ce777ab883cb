import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Input } from '@heroui/react';
import { NFTTradingSession } from '@/types/models';
import moment from 'moment-timezone';
import { useTranslation } from '@/hooks/useTranslation';

interface ScheduleConfirmationModalProps {
  session: NFTTradingSession | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
  amountUSDT: string;
  setAmountUSDT: (amount: string) => void;
}

const ScheduleConfirmationModal: React.FC<ScheduleConfirmationModalProps> = ({
  session,
  isOpen,
  onClose,
  onConfirm,
  amountUSDT,
  setAmountUSDT,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  if (!session) return null;

  const sessionTime = moment(session.openTime).format('HH:mm DD/MM/YYYY');

  return (
    <Modal
      isOpen={isO<PERSON>}
      onClose={onClose}
      size="sm"
    >
      <ModalContent>
        <ModalHeader>
          <h3 className="text-lg font-semibold">{t('flash_buy.schedule_confirmation')}</h3>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-4">
            <p className="text-gray-600">
              {t('flash_buy.confirm_schedule', { time: sessionTime })}
            </p>
            <Input
              type="number"
              label={t('flash_buy.amount_usdt')}
              placeholder="0.00"
              value={amountUSDT}
              onChange={(e) => setAmountUSDT(e.target.value)}
              min={100}
              step="100"
              endContent={
                <div className="flex items-center">
                  <span className="text-gray-500 text-sm">{t('common.usdt')}</span>
                </div>
              }
              disabled={isLoading}
            />
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">

              <p className="text-sm text-yellow-700">
                {t('flash_buy.scheduling_fee')}: <span className="font-semibold">{amountUSDT ? parseFloat(amountUSDT)/100 : 0} WM</span>
              </p>
              <p className="text-sm text-yellow-700 mt-2">
                * {t('flash_buy.fee_refund_note')}
              </p>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="flex justify-end gap-3">
            <Button
              variant="flat"
              onClick={onClose}
              disabled={isLoading}
            >
              {t('common.cancel')}
            </Button>
            <Button
              color="primary"
              onClick={onConfirm}
              isLoading={isLoading}
              className='bg-gradient-to-r from-blue-500 to-green-500 text-white'
            >
              {t('flash_buy.confirm_schedule_button')}
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ScheduleConfirmationModal;