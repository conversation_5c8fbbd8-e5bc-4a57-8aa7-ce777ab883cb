'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardBody, Button, Chip, addToast, cn } from '@heroui/react';
import { useRouter } from 'next/navigation';
import moment from 'moment-timezone';
import { useSocketContext } from '@/providers/socket-provider';
import { useApi } from '@/hooks/useApi';
import { NFTTradingSession, NFTSessionStatus, NFTSessionType, GroupedSessions } from '@/types/models';
import ScheduleConfirmationModal from './schedule-confirmation-modal';
import { Icon } from '@iconify/react/dist/iconify.js';
import { useUserStore } from '@/store/userStore';
import { useTranslation } from '@/hooks/useTranslation';

interface SessionCardProps {
  session: NFTTradingSession;
  onSessionUpdate?: (updatedSession: NFTTradingSession) => void;
  groupedSessions: GroupedSessions;
  onSessionsUpdate: (updatedSessions: GroupedSessions) => void;
}

const SessionCard: React.FC<SessionCardProps> = ({ session, onSessionUpdate, groupedSessions, onSessionsUpdate }) => {
  const { user, isAuthenticated } = useUserStore();
  const { fetchApi } = useApi();
  const router = useRouter();
  const { socket, isConnected } = useSocketContext();
  const { t } = useTranslation();
  const [sessionState, setSessionState] = useState<NFTTradingSession>(session);
  const [countdownData, setCountdownData] = useState<{
    timeLeft: string;
    timeLeftInSeconds: number;
    timeLeftInMinutes: number;
    timeLeftInHours: number;
    status: NFTSessionStatus;
    shouldEmit: boolean;
  } | null>(null);
  const [isScheduleModalOpen, setIsScheduleModalOpen] = useState(false);
  const [isScheduling, setIsScheduling] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [isOpening, setIsOpening] = useState(false);
  const [selectedSession, setSelectedSession] = useState<NFTTradingSession | null>(null);
  const [isStatisticsVisible, setIsStatisticsVisible] = useState(false);
  const [amountUSDT, setAmountUSDT] = useState<string>('100');

  useEffect(() => {
    if (!socket || !isConnected) return;

    socket.on('session:countdown', (data: any) => {
      if (data.sessionId === session.id) {
        setCountdownData({
          timeLeft: data.timeLeft,
          timeLeftInSeconds: data.timeLeftInSeconds,
          timeLeftInMinutes: data.timeLeftInMinutes,
          timeLeftInHours: data.timeLeftInHours,
          status: data.status,
          shouldEmit: data.shouldEmit,
        });

        if (data.status !== sessionState.status) {
          const updatedSession = { ...sessionState, status: data.status };
          setSessionState(updatedSession);
          onSessionUpdate?.(updatedSession);
        }
      }
    });

    socket.on('session:opened', (data: any) => {
      if (data.sessionId === session.id) {
        const updatedSession = { ...sessionState, status: NFTSessionStatus.OPEN };
        setSessionState(updatedSession);
        onSessionUpdate?.(updatedSession);
      }
    });

    socket.on('session:closed', (data: any) => {
      if (data.sessionId === session.id) {
        const updatedSession = {
          ...sessionState,
          status: NFTSessionStatus.CLOSED,
        };
        setSessionState(updatedSession);
        onSessionUpdate?.(updatedSession);
        setCountdownData(null);
      }
    });

    socket.on('session:statistics', (data: any) => {
      if (data.sessionId === session.id) {
        const updatedSession = {
          ...sessionState,
          totalOrders: data.totalOrders,
          matchedOrders: data.matchedOrders,
          totalVolume: data.totalVolume,
        };
        setSessionState(updatedSession);
        onSessionUpdate?.(updatedSession);
      }
    });

    return () => {
      if (socket) {
        socket.off('session:countdown');
        socket.off('session:opened');
        socket.off('session:closed');
        socket.off('session:statistics');
      }
    };
  }, [socket, isConnected, session.id, sessionState, onSessionUpdate]);

  const getStatusColor = (status: NFTSessionStatus) => {
    switch (status) {
      case NFTSessionStatus.OPEN: return 'success';
      case NFTSessionStatus.PENDING: return 'warning';
      case NFTSessionStatus.CLOSED: return 'danger';
      default: return 'default';
    }
  };

  const formatVolume = (volume: number | string) => {
    const numericVolume = typeof volume === 'string' ? parseFloat(volume) : volume;
    return isNaN(numericVolume) ? '0.00' : numericVolume.toFixed(2);
  };

  const renderCountdown = () => {
    if (!countdownData || sessionState.status === NFTSessionStatus.CLOSED) return null;

    const { timeLeftInHours, timeLeftInMinutes, timeLeftInSeconds } = countdownData;
    return (
      <div className="flex justify-center">
        <div className="flex gap-2">
          <div className="flex flex-col items-center">
            <div className="bg-gray-100 rounded-md p-2 w-12 h-12 flex items-center justify-center">
              <span className="text-lg font-semibold text-red-500">{timeLeftInHours.toString().padStart(2, '0')}</span>
            </div>
            <span className="text-xs text-gray-500 mt-1">{t('flash_buy.hours')}</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="bg-gray-100 rounded-md p-2 w-12 h-12 flex items-center justify-center">
              <span className="text-lg font-semibold text-red-500">{timeLeftInMinutes.toString().padStart(2, '0')}</span>
            </div>
            <span className="text-xs text-gray-500 mt-1">{t('flash_buy.minutes')}</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="bg-gray-100 rounded-md p-2 w-12 h-12 flex items-center justify-center">
              <span className="text-lg font-semibold text-red-500">{timeLeftInSeconds.toString().padStart(2, '0')}</span>
            </div>
            <span className="text-xs text-gray-500 mt-1">{t('flash_buy.seconds')}</span>
          </div>
        </div>
      </div>
    );
  };

  const handleSessionAction = async (session: NFTTradingSession, action: 'join' | 'schedule' | 'close' | 'open') => {
    if (session.status === NFTSessionStatus.OPEN) {
      if (action === 'join') {
        router.push(`/flash-buy/${session.id}`);
      }
    } else if (session.status === NFTSessionStatus.PENDING) {
      if (action === 'schedule') {
        // setSelectedSession(session);
        // setIsScheduleModalOpen(true);

        router.push(`/flash-buy/${session.id}/schedule`);
      } else if (action === 'join') {
        router.push(`/flash-buy/${session.id}`);
      }
    }

    if (action === 'close') {
      try {
        setIsClosing(true);
        const response = await fetchApi(`/trading-sessions/${session.id}/close`, {
          method: 'POST',
        });

        if (response.success) {
          addToast({
            title: t('common.success'),
            description: t('flash_buy.session_closed'),
            color: 'success',
          });

          const updatedSession = {
            ...sessionState,
            status: NFTSessionStatus.CLOSED,
            totalOrders: response.data?.totalOrders ?? sessionState.totalOrders,
            matchedOrders: response.data?.matchedOrders ?? sessionState.matchedOrders,
            totalVolume: response.data?.totalVolume ?? sessionState.totalVolume,
          };
          setSessionState(updatedSession);
          onSessionUpdate?.(updatedSession);

          const updatedSessions = { ...groupedSessions };
          const date = moment(session.date).format('YYYY-MM-DD');
          updatedSessions[date] = updatedSessions[date].map((s) =>
            s.id === session.id ? updatedSession : s
          );
          onSessionsUpdate(updatedSessions);

          setCountdownData(null);
        }
      } catch (error: any) {
        addToast({
          title: t('errors.error'),
          description: error.message || t('flash_buy.close_session_error'),
          color: 'danger',
        });
      } finally {
        setIsClosing(false);
      }
    }

    if (action === 'open') {
      try {
        setIsOpening(true);
        const response = await fetchApi(`/trading-sessions/${session.id}/open`, {
          method: 'POST',
        });

        if (response.success) {
          addToast({
            title: t('common.success'),
            description: t('flash_buy.session_opened'),
            color: 'success',
          });

          const updatedSession = {
            ...sessionState,
            status: NFTSessionStatus.OPEN,
            totalOrders: 0,
            matchedOrders: 0,
            totalVolume: 0,
          };
          setSessionState(updatedSession);
          onSessionUpdate?.(updatedSession);

          const updatedSessions = { ...groupedSessions };
          const date = moment(session.date).format('YYYY-MM-DD');
          updatedSessions[date] = updatedSessions[date].map((s) =>
            s.id === session.id ? updatedSession : s
          );
          onSessionsUpdate(updatedSessions);
        }
      } catch (error: any) {
        addToast({
          title: t('errors.error'),
          description: error.message || t('flash_buy.open_session_error'),
          color: 'danger',
        });
      } finally {
        setIsOpening(false);
      }
    }
  };

  const handleSchedule = async () => {
    if (!selectedSession) return;

    try {
      setIsScheduling(true);
      const response = await fetchApi(`/trading-sessions/${selectedSession.id}/schedule`, {
        method: 'POST',
        body: JSON.stringify({ amount: parseFloat(amountUSDT) }),
      });

      if (response.success) {
        addToast({
          title: t('common.success'),
          description: t('flash_buy.session_scheduled'),
          color: 'success',
        });

        const updatedSessions = { ...groupedSessions };
        const date = moment(selectedSession.date).format('YYYY-MM-DD');
        updatedSessions[date] = updatedSessions[date].map((s) =>
          s.id === selectedSession.id ? { ...s, totalSchedules: s.totalSchedules + 1 } : s
        );
        onSessionsUpdate(updatedSessions);
      }
    } catch (error: any) {
      addToast({
        title: t('errors.error'),
        description: error.message || t('flash_buy.schedule_session_error'),
        color: 'danger',
      });
    } finally {
      setIsScheduling(false);
      setIsScheduleModalOpen(false);
    }
  };

  return (
    <>
      <Card key={session.id} className="w-full max-w-sm hover:shadow-lg transition-shadow">
        <CardBody className="p-4">
          <div className="flex flex-col gap-3">
            <div className="flex justify-between items-start">
              <div className="flex flex-col">
                <h3 className="text-sm font-semibold">
                  {sessionState.type === NFTSessionType.MORNING ? t('flash_buy.morning_session') : sessionState.type === NFTSessionType.MIDDLE ? t('flash_buy.afternoon_session') : t('flash_buy.evening_session')}
                </h3>
                <p className="text-xs text-gray-500">{t('flash_buy.high_liquidity')}</p>
              </div>
              <Chip color={getStatusColor(sessionState.status)} variant="flat" size="sm">
                {sessionState.status}
              </Chip>
            </div>

            {renderCountdown()}

            {isStatisticsVisible && (
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <p className="text-gray-500">{t('flash_buy.schedules')}</p>
                  <p className="font-semibold">{sessionState.totalSchedules}</p>
                </div>
                <div>
                  <p className="text-gray-500">{t('flash_buy.orders')}</p>
                  <p className="font-semibold">{sessionState.totalOrders}</p>
                </div>
                <div>
                  <p className="text-gray-500">{t('flash_buy.matched')}</p>
                  <p className="font-semibold">{sessionState.matchedOrders}</p>
                </div>
                <div>
                  <p className="text-gray-500">{t('flash_buy.volume')}</p>
                  <p className="font-semibold">{formatVolume(sessionState.totalVolume)} USDT</p>
                </div>
              </div>
            )}

            {sessionState.status === NFTSessionStatus.PENDING && (
              <Button
                color="primary"
                className={cn(
                  'w-full text-xs mt-2 text-white',
                  'bg-gradient-to-r from-blue-500 to-green-500'
                )}
                onClick={() => handleSessionAction(sessionState, 'schedule')}
              >
                {t('flash_buy.schedule')}
              </Button>
            )}
            {sessionState.status === NFTSessionStatus.OPEN && (
              <Button
                color="primary"
                className={cn(
                  'w-full text-xs mt-2 text-white',
                  'bg-gradient-to-r from-pink-500 to-purple-500'
                )}
                onClick={() => handleSessionAction(sessionState, 'join')}
              >
                {t('flash_buy.join_now')}
              </Button>
            )}
            {false && sessionState.status === NFTSessionStatus.PENDING && (
              <Button
                color="danger"
                className="w-full text-xs mt-2 text-white bg-gradient-to-r from-red-500 to-orange-500"
                onClick={() => handleSessionAction(sessionState, 'open')}
                isLoading={isOpening}
              >
                {t('flash_buy.open_session')}
              </Button>
            )}
            {false && sessionState.status === NFTSessionStatus.OPEN && (
              <Button
                color="danger"
                className="w-full text-xs mt-2 text-white bg-gradient-to-r from-red-500 to-orange-500"
                onClick={() => handleSessionAction(sessionState, 'close')}
                isLoading={isClosing}
              >
                {t('flash_buy.close_session')}
              </Button>
            )}
          </div>
        </CardBody>
      </Card>

      <ScheduleConfirmationModal
        session={selectedSession}
        isOpen={isScheduleModalOpen}
        onClose={() => setIsScheduleModalOpen(false)}
        onConfirm={handleSchedule}
        isLoading={isScheduling}
        amountUSDT={amountUSDT}
        setAmountUSDT={setAmountUSDT}
      />
    </>
  );
};

export default SessionCard;