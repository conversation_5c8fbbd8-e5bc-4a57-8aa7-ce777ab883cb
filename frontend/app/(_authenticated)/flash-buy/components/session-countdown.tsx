'use client';

import React, { useState, useEffect } from 'react';
import moment from 'moment-timezone';
import { NFTSessionStatus } from '@/types/models';
import { useSocketContext } from '@/providers/socket-provider';
import { useTranslation } from '@/hooks/useTranslation';

interface SessionCountdownProps {
  openTime: Date;
  status: NFTSessionStatus;
}

export default function SessionCountdown({ openTime, status }: SessionCountdownProps) {
  const [timeLeft, setTimeLeft] = useState<string>('');
  const [timeLeftInSeconds, setTimeLeftInSeconds] = useState<number>(0);
  const [timeLeftInMinutes, setTimeLeftInMinutes] = useState<number>(0);
  const [timeLeftInHours, setTimeLeftInHours] = useState<number>(0);
  const [shouldShow, setShouldShow] = useState(false);
  const { socket } = useSocketContext();
  const { t } = useTranslation();

  useEffect(() => {
    if (!socket) return;

    socket.emit('session:requestTime', {
      sessionTime: openTime,
      status: status
    });

    socket.on('session:timeUpdate', (data: {
      timeLeft: string;
      timeLeftInSeconds: number;
      timeLeftInMinutes: number;
      timeLeftInHours: number;
      isEnded: boolean;
      shouldShowCountdown: boolean;
    }) => {
      setTimeLeft(data.timeLeft);
      setTimeLeftInSeconds(data.timeLeftInSeconds);
      setTimeLeftInMinutes(data.timeLeftInMinutes);
      setTimeLeftInHours(data.timeLeftInHours);
      setShouldShow(data.shouldShowCountdown);
    });

    const timer = setInterval(() => {
      socket.emit('session:requestTime', {
        sessionTime: openTime,
        status: status
      });
    }, 1000);

    return () => {
      socket.off('session:timeUpdate');
      clearInterval(timer);
    };
  }, [socket, openTime, status]);

  const getMessage = () => {
    if (!shouldShow) return null;

    if (status === NFTSessionStatus.PENDING) {
      return {
        label: t('flash_buy.session_starts_in'),
        value: timeLeft || t('common.calculating')
      };
    } else if (status === NFTSessionStatus.OPEN) {
      return {
        label: t('flash_buy.session_ends_in'),
        value: timeLeft || t('common.calculating')
      };
    }
    return {
      label: t('flash_buy.session_status'),
      value: t('flash_buy.session_closed')
    };
  };

  const message = getMessage();
  if (!message) return null;

  return (
    <div className="bg-gradient-to-r from-blue-50 to-green-50 p-4 rounded-lg my-3">
      <p className="text-gray-600 mb-1">{message.label}</p>
      <p className="text-xl font-semibold">{message.value}</p>
    </div>
  );
}