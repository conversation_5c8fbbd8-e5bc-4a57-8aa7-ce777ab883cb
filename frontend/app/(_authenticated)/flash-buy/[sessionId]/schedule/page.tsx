'use client';

import React, { useEffect, useState } from 'react';
import { Icon } from '@iconify/react';
import { useParams, useRouter } from 'next/navigation';
import { useUserStore } from '@/store/userStore';
import { useApi, getErrorMessageFromCode } from '@/hooks/useApi';
import { addToast, Button, Spinner, useDisclosure } from '@heroui/react';
import { NFT, NFTTradingSession } from '@/types/models';
import NFTCard from '../components/nft-card';
import NFTModal from '../components/nft-modal';
import { useSocketContext } from '@/providers/socket-provider';
import { useTranslation } from '@/hooks/useTranslation';

export default function FlashBuySessionPage() {
  const router = useRouter();
  const { user, accessToken, hydrate, isHydrated } = useUserStore();
  const { fetchApi } = useApi();
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [loadingNFTs, setLoadingNFTs] = useState<Record<string, boolean>>({});
  const [nfts, setNfts] = useState<NFT[]>([]);
  const [selectedNFT, setSelectedNFT] = useState<NFT | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { sessionId } = useParams();
  const { socket, isConnected } = useSocketContext();

  useEffect(() => {
    hydrate();
  }, [hydrate]);

  useEffect(() => {
    const fetchNFTs = async () => {
      try {
        if (!isHydrated || !accessToken) {
          router.push('/');
          return;
        }

        setIsLoading(true);
        const response = await fetchApi(`/nfts/sessions/${sessionId}/listed`, {
          method: 'GET',
          requireAuth: true
        });

        if (response.success && response.data) {
          setNfts(response.data);
        }
      } catch (error: any) {
        addToast({
          title: t('common.error'),
          description: error.message || t('flash_buy.fetch_nfts_failed'),
          color: 'danger'
        });

        if (error.message.includes('Authentication required')) {
          router.push('/');
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (isHydrated) {
      fetchNFTs();
    }
  }, [isHydrated, accessToken]);

  useEffect(() => {
    if (!socket || !isConnected) return;

    socket.on('session:closed', (data: any) => {
      if (data.sessionId === sessionId) {
        router.push('/flash-buy');
      }
    });

    return () => {
      if (socket) {
        socket.off('session:closed');
      }
    };
  }, [socket, isConnected]);

  if (!isHydrated || isLoading) {
    return (
      <div className="fixed inset-0 flex justify-center items-center bg-gray-100 bg-opacity-50 z-50">
        <Spinner color="success" />
      </div>
    );
  }

  const handleAction = async (action: 'buy', nft: NFT) => {
    try {
      setLoadingNFTs(prev => ({ ...prev, [nft.id]: true }));
      let response;
      switch (action) {
        case 'buy':
          response = await fetchApi(`/nfts/${nft.id}/session/${sessionId}/schedule`, {
            method: 'POST',
            requireAuth: true
          });
          if (response.success) {
            addToast({ title: t('common.success'), description: t('flash_buy.nft_scheduled'), color: 'success' });
          } else {
            const errorMessage = response.errorCode
              ? getErrorMessageFromCode(response.errorCode, t)
              : response.message[t('language')] || t('flash_buy.schedule_failed');
            addToast({ title: t('common.error'), description: errorMessage, color: 'danger' });
          }
          break;
      }
    } catch (error: any) {
      addToast({
        title: t('common.error'),
        description: error.message || t('flash_buy.action_failed'),
        color: 'danger'
      });
    } finally {
      setLoadingNFTs(prev => ({ ...prev, [nft.id]: false }));
    }
  };

  return (
    <div className="flex w-full flex-col gap-4 max-w-sm mx-auto">
      <div className="flex items-center justify-between">
        <button onClick={() => router.back()}>
          <Icon icon="solar:arrow-left-linear" width={24} className="text-gray-500" />
        </button>
        <p className="text-lg font-semibold flex-1 text-center">{t('flash_buy.schedule_nft')}</p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        {nfts.length > 0 ? (
          nfts.map((nft, index) => (
            <NFTCard
              key={nft.id}
              nft={nft}
              onAction={handleAction}
              index={index}
              loadingNFTs={loadingNFTs}
              isLoading={loadingNFTs[nft.id] || false}
              mode="schedule"
            />
          ))
        ) : (
          <div className="flex flex-col items-center justify-center py-[100px]">
            <Icon icon="solar:box-bold" width={48} height={48} className="text-green-400 mb-2" />
            <p className="text-center text-gray-500 text-sm">{t('flash_buy.no_nfts')}</p>
          </div>
        )}
      </div>

      <NFTModal
        nft={selectedNFT}
        isOpen={isOpen}
        onClose={onClose}
        onAction={handleAction}
        userId={user?.id}
        isLoading={selectedNFT ? loadingNFTs[selectedNFT.id] || false : false}
      />
    </div>
  );
}