'use client';

import React from 'react';
import { Card, CardBody, Chip } from '@heroui/react';
import { NFTOrder, OrderStatus, OrderSession, NFTType, NFTSessionType } from '@/types/models';
import moment from 'moment-timezone';

interface NFTOrderCardProps {
    order: NFTOrder;
    onClick?: (order: NFTOrder) => void;
}

const NFTOrderCard: React.FC<NFTOrderCardProps> = ({ order, onClick }) => {
    const getStatusColor = (status: OrderStatus) => {
        switch (status) {
            case OrderStatus.PENDING:
                return 'warning';
            case OrderStatus.MATCHED:
                return 'success';
            case OrderStatus.CANCELLED:
                return 'danger';
            default:
                return 'default';
        }
    };

    const getTypeIcon = (type: NFTType) => {
        switch (type) {
            case NFTType.PHOENIX:
                return 'game-icons:phoenix-head';
            case NFTType.SPIRIT_TURTLE:
                return 'game-icons:turtle';
            case NFTType.UNICORN:
                return 'game-icons:unicorn';
            case NFTType.DRAGON:
                return 'game-icons:dragon-head';
            default:
                return 'game-icons:perspective-dice-six';
        }
    };

    const formatDate = (date?: Date) => {
        return date ? moment(date).format('HH:mm DD/MM/YYYY') : 'N/A';
    };

    return (
        <Card
            className="w-full max-w-sm hover:shadow-lg transition-shadow cursor-pointer"
            isPressable={!!onClick}
            onPress={() => onClick?.(order)}
        >
            <CardBody className="p-4">
                <div className="flex flex-col gap-3">
                    <div className="flex justify-between items-start">
                        <h3 className="text-sm font-semibold">Order #{order.id.slice(0, 8)}</h3>
                        <Chip size="sm" color={getStatusColor(order.status)} variant="flat">
                            {order.status}
                        </Chip>
                    </div>

                    <div className="flex flex-col gap-2 text-xs">
                        <div className="flex justify-between">
                            <span className="text-gray-500">NFT Type</span>
                            <span className="font-semibold">{order.nftType}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-500">Session</span>
                            <span className="font-semibold">{order.session.type === NFTSessionType.MORNING ? 'Morning' : 'Evening'}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-500">Session Time</span>
                            <span className="font-semibold">{formatDate(order.sessionTime)}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-500">Gas Fee</span>
                            <span className="font-semibold">{order.gasFee.toFixed(2)} ({order.gasFeePercentage}%)</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-500">Quantity</span>
                            <span className="font-semibold">{order.quantity}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-500">Matched At</span>
                            <span className="font-normal">{formatDate(order.matchedAt)}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-500">Completed At</span>
                            <span className="font-normal">{formatDate(order.completedAt)}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-500">Seller Paid</span>
                            <span className="font-normal">{order.sellerPaid ? 'Yes' : 'No'}</span>
                        </div>
                    </div>
                </div>
            </CardBody>
        </Card>
    );
};

export default NFTOrderCard;