import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@heroui/react';
import { NFTOrder, OrderStatus } from '@/types/models';
import moment from 'moment-timezone';

interface NFTOrderModalProps {
  order: NFTOrder | null;
  isOpen: boolean;
  onClose: () => void;
  onAction?: (action: 'cancel', order: NFTOrder) => void;
  userId?: string;
  isLoading?: boolean;
}

const NFTOrderModal: React.FC<NFTOrderModalProps> = ({
  order,
  isOpen,
  onClose,
  onAction,
  userId,
  isLoading = false,
}) => {
  if (!order) return null;

  const formatDate = (date?: Date) => {
    return date ? moment(date).format('HH:mm DD/MM/YYYY') : 'N/A';
  };

  const isOwner = userId === order.traderId;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="sm"
      className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
    >
      <ModalContent>
        <ModalHeader>
          <h3 className="text-lg font-semibold">Order Details #{order.id.slice(0, 8)}</h3>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-500">NFT Type</span>
              <span className="font-semibold">{order.nftType}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Session</span>
              <span className="font-semibold">{order.session.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Status</span>
              <span className="font-semibold">{order.status}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Session Time</span>
              <span className="font-semibold">{formatDate(order.sessionTime)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Gas Fee</span>
              <span className="font-semibold">${order.gasFee.toFixed(2)} ({order.gasFeePercentage}%)</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Matched At</span>
              <span className="font-semibold">{formatDate(order.matchedAt)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Completed At</span>
              <span className="font-semibold">{formatDate(order.completedAt)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Seller Paid</span>
              <span className="font-semibold">{order.sellerPaid ? 'Yes' : 'No'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Buyer</span>
              <span className="font-semibold">{order.trader?.username || 'N/A'}</span>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="flex justify-end gap-3">
            <Button variant="flat" onClick={onClose} disabled={isLoading}>
              Close
            </Button>
            {isOwner && order.status === OrderStatus.PENDING && (
              <Button
                color="danger"
                onClick={() => onAction?.('cancel', order)}
                isLoading={isLoading}
              >
                Cancel Order
              </Button>
            )}
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default NFTOrderModal;