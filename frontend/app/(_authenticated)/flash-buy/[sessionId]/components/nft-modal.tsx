import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@heroui/react';
import { Icon } from '@iconify/react';
import { NFT } from '@/types/models';
import { formatNumber } from '@/utils/format';
import { useTranslation } from '@/hooks/useTranslation';

interface NFTModalProps {
  nft: NFT | null;
  isOpen: boolean;
  onClose: () => void;
  onAction: (action: 'list' | 'withdraw' | 'buy', nft: NFT) => Promise<void>;
  userId?: string;
  isLoading?: boolean;
}

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'PHOENIX':
      return 'game-icons:phoenix-head';
    case 'SPIRIT_TURTLE':
      return 'game-icons:turtle';
    case 'UNICORN':
      return 'game-icons:unicorn';
    case 'DRAGON':
      return 'game-icons:dragon-head';
    default:
      return 'game-icons:perspective-dice-six';
  }
};

export default function NFTModal({ nft, isO<PERSON>, onClose, onAction, userId, isLoading }: NFTModalProps) {
  const { t } = useTranslation();
  if (!nft) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon={getTypeIcon(nft.type)} width={28} className="text-green-500" />
            <h2>{nft.name}</h2>
          </div>
        </ModalHeader>
        <ModalBody>
          <img
            src={nft.imageUrl}
            alt={nft.name}
            className="w-full h-64 object-cover rounded-lg mb-4"
          />

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <p className="text-sm text-gray-500">{t('flash_buy.current_price')}</p>
              <p className="font-semibold">${formatNumber(nft.currentPrice)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('flash_buy.initial_price')}</p>
              <p className="font-semibold">${formatNumber(nft.initialPrice)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('flash_buy.gas_fee_usdt')}</p>
              <p className="font-semibold">{formatNumber(nft.gasFee)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('flash_buy.rarity_score')}</p>
              <p className="font-semibold">{typeof nft.rarityScore === 'number' ? nft.rarityScore.toFixed(2) : '0.00'}</p>
            </div>
          </div>

          <div className="mb-4">
            <h3 className="font-semibold mb-2">{t('flash_buy.description')}</h3>
            <p className="text-gray-600">{nft.description}</p>
          </div>

          <div>
            <h3 className="font-semibold mb-2">{t('flash_buy.attributes')}</h3>
            <div className="grid grid-cols-2 gap-2">
              {nft.attributes.map((attr, index) => (
                <div key={index} className="bg-gray-50 p-2 rounded">
                  <p className="text-sm text-gray-500">{attr.trait_type}</p>
                  <p className="font-semibold">{attr.value}</p>
                </div>
              ))}
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="flex gap-2 w-full">
            {nft.status === 'AVAILABLE' && nft.owner.id === userId && (
              <Button
                color="success"
                variant="flat"
                className="flex-1"
                onPress={() => {
                  onAction('list', nft);
                  onClose();
                }}
              >
                {t('flash_buy.list_nft')}
              </Button>
            )}
            {nft.status === 'LISTED' && nft.owner.id !== userId && (
              <Button
                color="primary"
                variant="flat"
                className="flex-1"
                onPress={() => {
                  onAction('buy', nft);
                  onClose();
                }}
              >
                {t('flash_buy.buy_now')}
              </Button>
            )}
            {nft.status === 'SOLD' && nft.owner.id === userId && (
              <Button
                color="warning"
                variant="flat"
                className="flex-1"
                onPress={() => {
                  onAction('withdraw', nft);
                  onClose();
                }}
              >
                {t('flash_buy.withdraw_profits')}
              </Button>
            )}
            <Button
              color="default"
              variant="light"
              className="flex-1"
              onPress={onClose}
            >
              {t('common.close')}
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}