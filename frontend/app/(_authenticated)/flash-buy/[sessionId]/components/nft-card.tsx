'use client';

import React from 'react';
import { <PERSON>, CardBody, Button, Chip } from '@heroui/react';
import { Icon } from '@iconify/react';
import { NFT } from '@/types/models';
import { formatNumber } from '@/utils/format';
import { round } from 'lodash';
import { useTranslation } from '@/hooks/useTranslation';

interface NFTCardProps {
    nft: NFT;
    index: number;
    onAction: (action: 'list' | 'withdraw' | 'buy', nft: NFT) => void;
    isLoading?: boolean;
    loadingNFTs?: Record<string, boolean>;
    mode?: 'schedule' | 'flash-buy';
}

const NFTCard: React.FC<NFTCardProps> = ({ nft, onAction, isLoading = false, index, loadingNFTs, mode }) => {
    const { t } = useTranslation();

    const getTypeImage = (type: string) => {
        switch (type) {
            case 'PHOENIX':
                return '/nfts/phoenix-1.jpeg';
            case 'SPIRIT_TURTLE':
                return '/nfts/spirit-turtle-1.jpeg';
            case 'UNICORN':
                return '/nfts/unicorn-1.jpeg';
            case 'DRAGON':
                return '/nfts/dragon-1.jpeg';
            default:
                return '/nfts/default.jpeg';
        }
    };

    return (
        <Card
            key={nft.id}
            className="w-full max-w-sm hover:shadow-lg transition-shadow cursor-pointer"
        >
            <CardBody className="p-4">
                <div className="flex flex-col gap-3">
                    <img
                        src={getTypeImage(nft.type)}
                        alt={nft.type}
                        className="w-full h-auto object-cover rounded-lg"
                    />

                    <div className="flex flex-col gap-1">
                        <div className="flex justify-between">
                            <span className="text-xs text-gray-500">NFT #{index + 1}</span>
                            <span className="font-semibold text-tiny">{nft.type}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-xs text-gray-500">{t('flash_buy.price')}</span>
                            <span className="font-semibold text-tiny">{formatNumber(nft.currentPrice)} USDT</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-xs text-gray-500">{t('flash_buy.gas_fee')}</span>
                            <span className="font-semibold text-tiny">{formatNumber(nft.gasFee, 1)} USDT</span>
                        </div>
                        {/* <div className="flex justify-between">
                            <span className="text-xs text-gray-500">Quantity</span>
                            <span className="font-semibold text-tiny">{nft.quantity || 0}</span>
                        </div> */}
                    </div>

                    <div className="flex gap-2 mt-2">
                        <Button
                            color="primary"
                            variant="flat"
                            size="sm"
                            className="flex-1 text-xs bg-gradient-to-r from-blue-500 to-green-500 text-white"
                            onPress={() => onAction('buy', nft)}
                            isLoading={isLoading}
                            isDisabled={loadingNFTs?.[nft.id]}
                        >
                            {mode === 'schedule' ? t('flash_buy.schedule_now') : t('flash_buy.buy_now')}
                        </Button>
                    </div>
                </div>
            </CardBody>
        </Card>
    );
};

export default NFTCard;