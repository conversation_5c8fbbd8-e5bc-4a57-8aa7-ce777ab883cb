'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useApi } from '@/hooks/useApi';
import { useUserStore } from '@/store/userStore';
import { NFTTradingSession, GroupedSessions } from '@/types/models';
import SessionTabs from './components/session-tabs';
import moment from 'moment-timezone';
import { addToast, Spinner } from '@heroui/react';
import { useTranslation } from '@/hooks/useTranslation';

export default function FlashBuyPage() {
  const router = useRouter();
  const { fetchApi } = useApi();
  const { accessToken, hydrate, isHydrated } = useUserStore();
  const { t } = useTranslation();
  const [groupedSessions, setGroupedSessions] = useState<GroupedSessions>({});
  const [isLoading, setIsLoading] = useState(true);
  const [hasFetched, setHasFetched] = useState(false);

  useEffect(() => {
  }, [isHydrated, accessToken, hasFetched]);

  useEffect(() => {
    if (!isHydrated) {
      hydrate();
    }
  }, [hydrate, isHydrated]);

  const fetchSessions = useCallback(async () => {
    if (!accessToken) {
      router.push('/');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetchApi('/trading-sessions', {
        method: 'GET',
        requireAuth: true
      });

      if (!response.success || !response.data) {
        addToast({
          title: t('flash_buy.title'),
          description: response.message.en || t('flash_buy.fetch_error'),
          color: 'danger'
        });
      }

      const grouped = response.data.reduce((acc: GroupedSessions, session: NFTTradingSession) => {
        const date = moment(session.date).format('YYYY-MM-DD');
        acc[date] = acc[date] || [];
        acc[date].push(session);
        return acc;
      }, {});

      setGroupedSessions(grouped);
      setHasFetched(true);
    } catch (error: any) {
      addToast({
        title: t('errors.error'),
        description: error.message || t('flash_buy.fetch_sessions_error'),
        color: 'danger',
      });

      setHasFetched(true);
      if (error.message.includes('Authentication required')) {
        router.push('/');
      }
    } finally {
      setIsLoading(false);
    }
  }, [accessToken, fetchApi, router]);

  useEffect(() => {
    if (isHydrated && !hasFetched) {
      fetchSessions();
    }
  }, [isHydrated, hasFetched, fetchSessions]);

  const handleSessionsUpdate = useCallback((updatedSessions: GroupedSessions) => {
    setGroupedSessions(updatedSessions);
  }, []);

  if (!isHydrated || isLoading) {
    return (
      <div className="fixed inset-0 flex justify-center items-center bg-gray-100 bg-opacity-50 z-50">
        <Spinner color="success" />
      </div>
    );
  }

  return (
    <div className="flex w-full flex-col gap-4 max-w-sm mx-auto">
      <div className="flex items-center justify-between">
        <button onClick={() => router.back()}>
          <Icon icon="solar:arrow-left-linear" width={24} className="text-gray-500" />
        </button>
        <p className="text-lg font-semibold flex-1 text-center">{t('flash_buy.title')}</p>
      </div>

      <SessionTabs
        groupedSessions={groupedSessions}
        onSessionsUpdate={handleSessionsUpdate}
      />
    </div>
  );
}