'use client';

import React from 'react';
import { <PERSON><PERSON>, Card, CardBody, Progress } from '@heroui/react';
import { Icon } from '@iconify/react';
import { Staking } from '@/types/models';
import { formatNumber } from '@/utils/format';
import { useTranslation } from '@/hooks/useTranslation';

interface StakingCardProps {
  staking: Staking;
  onClick: (staking: Staking) => void;
}

const StakingCard: React.FC<StakingCardProps> = ({ staking, onClick }) => {
  const { t } = useTranslation();
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'text-green-500';
      case 'COMPLETED':
        return 'text-blue-500';
      case 'CANCELLED':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'solar:check-circle-bold';
      case 'COMPLETED':
        return 'solar:medal-ribbon-bold';
      case 'CANCELLED':
        return 'solar:close-circle-bold';
      default:
        return 'solar:question-circle-bold';
    }
  };

  const getPackageImage = (code: string) => {
    switch (code) {
      case 'PHOENIX':
        return '/co-share/phoenix.jpg';
      case 'SPIRIT_TURTLE':
        return '/co-share/spirit-turtle.jpg';
      case 'UNICORN':
        return '/co-share/unicorn.jpg';
      case 'DRAGON':
        return '/co-share/dragon.jpg';
      case 'DRAGON_LORD':
        return '/co-share/dragon-lord.jpg';
      case 'ETERNAL_DRAGON':
        return '/co-share/eternal-dragon.jpg';
      case 'SOVEREIGN_DRAGON':
        return '/co-share/sovereign-dragon.jpg';
      default:
        return '/co-share/phoenix.jpg';
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDisplayName = (name: string) => {
    return name.replace(' Package', '').toUpperCase();
  };

  const getTotalBonus = () => {
    return staking.wmBonusAmount || 0;
  };

  const handleClick = () => {
    console.log('StakingCard clicked, calling onClick with staking:', staking);
    onClick(staking);
  };

  return (
    <Card
      className="w-full hover:shadow-lg transition-shadow cursor-pointer"
      onPress={handleClick}
    >
      <CardBody className="p-4">
        <div className="flex flex-col gap-3">
          <div className="relative cursor-pointer" onClick={handleClick}>
            <img
              src={getPackageImage(staking.packageCode)}
              alt={staking.packageName}
              className="w-full h-auto object-cover rounded-lg"
            />
            <div className="absolute top-2 right-2 bg-black bg-opacity-50 rounded-full px-2 py-1">
              <div className={`flex items-center gap-1 ${getStatusColor(staking.status)}`}>
                <Icon icon={getStatusIcon(staking.status)} width={16} height={16} />
                <span className="text-xs font-medium text-white">{staking.status}</span>
              </div>
            </div>
            {/* <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 px-2 py-1">
              <h3 className="text-sm font-semibold text-white">{getDisplayName(staking.packageName)}</h3>
            </div> */}
          </div>

          <div className="flex justify-between text-sm text-gray-600">
            <span>{t('global_mining.price')}:</span>
            <span className="font-medium">{formatNumber(staking.amount)} USDT</span>
          </div>

          <div className="flex justify-between text-sm text-gray-600">
            <span>{t('global_mining.duration')}:</span>
            <span className="font-medium">{staking.durationDays} {t('global_mining.days')}</span>
          </div>

          <div className="flex justify-between text-sm text-gray-600">
            <span>{t('global_mining.interest_rate_month')}:</span>
            <span className="font-medium">{staking.interestRate}% USDT</span>
          </div>

          <div className="flex justify-between text-sm text-gray-600">
            <span>{t('global_mining.bonus_rate')}:</span>
            <span className="font-medium">{staking.bonusRate}% WM</span>
          </div>

          <div className="flex justify-between text-sm text-gray-600">
            <span>{t('global_mining.start_date')}:</span>
            <span className="font-medium">{formatDate(staking.startDate)}</span>
          </div>

          <div className="flex justify-between text-sm text-gray-600">
            <span>{t('global_mining.end_date')}:</span>
            <span className="font-medium">{formatDate(staking.endDate)}</span>
          </div>

          <div className="flex justify-between text-sm text-gray-600">
            <span>{t('global_mining.total_interest')}:</span>
            <span className="font-medium text-green-600">{formatNumber(staking.totalInterest)} USDT</span>
          </div>

          <div className="flex flex-col">
            <div className="flex justify-between text-sm text-gray-600">
              <span>{t('global_mining.total_bonus')}:</span>
              <span className="font-medium text-blue-600">{formatNumber(getTotalBonus())} WM</span>
            </div>
            <div className="flex justify-end text-xs text-gray-500">
              <span>(1 WM = {formatNumber(staking.wmUsdRate || 10)} USD)</span>
            </div>
          </div>

          {staking.status === 'ACTIVE' && (
            <div className="mt-2">
              <div className="flex justify-between text-sm mb-1">
                <span>{t('global_mining.progress')}:</span>
                <span>{(staking.progressPercentage || 0).toFixed(2)}%</span>
              </div>
              <Progress
                value={staking.progressPercentage || 0}
                color="success"
                className="h-2"
              />
              <div className="flex justify-between text-xs mt-1 text-gray-500">
                <span>{t('global_mining.day')} {staking.elapsedDays || 0}</span>
                <span>{staking.daysRemaining || 0} {t('global_mining.days_left')}</span>
              </div>
            </div>
          )}

          <div className="mt-3">
            <Button
              size="sm"
              className="w-full bg-gradient-to-r from-blue-500 to-green-500 text-white"
              onClick={handleClick}
            >
              {t('global_mining.view_details')}
            </Button>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default StakingCard;
