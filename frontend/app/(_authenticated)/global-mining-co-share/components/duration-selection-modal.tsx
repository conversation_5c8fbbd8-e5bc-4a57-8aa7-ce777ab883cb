'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card, CardBody } from '@heroui/react';
import { StakingPackage } from '@/types/models';
import { formatNumber } from '@/utils/format';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';

interface DurationSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  package: StakingPackage | null;
  onConfirm: (packageCode: string, durationDays: number) => Promise<void>;
  isLoading?: boolean;
  wmUsdRate?: number;
  error?: string | null;
}

const DurationSelectionModal: React.FC<DurationSelectionModalProps> = ({
  isOpen,
  onClose,
  package: pkg,
  onConfirm,
  isLoading = false,
  wmUsdRate = 1.2, 
  error = null,
}) => {
  const { t } = useTranslation();
  const [selectedDuration, setSelectedDuration] = useState<number | null>(null);

  useEffect(() => {
    if (isOpen && pkg && pkg.durations.length > 0) {
      console.log('Modal opened with package:', pkg);
      setSelectedDuration(pkg.durations[0].days);
    }
  }, [isOpen, pkg]);

  if (!pkg) return null;

  const handleConfirm = () => {
    if (selectedDuration === null) return;
    onConfirm(pkg.code, selectedDuration);
  };

  const getDurationDetails = (days: number) => {
    const duration = pkg.durations.find(d => d.days === days);
    if (!duration) return null;
    return duration;
  };

  const calculateExpectedReturn = (days: number) => {
    const duration = getDurationDetails(days);
    if (!duration) return 0;

    const principal = pkg.amount;
    const interestAmount = (principal * duration.interestRate / 100) * (duration.days / 30);
    return principal + interestAmount;
  };

  const calculateWMReturn = (days: number) => {
    const duration = getDurationDetails(days);
    if (!duration) return 0;

    const principal = pkg.amount;
    const bonusAmountUSDT = principal * duration.bonusRate / 100;

    return bonusAmountUSDT / wmUsdRate;
  };

  const getDisplayName = (name: string) => {
    return name.replace(' Package', '').toUpperCase();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="sm" scrollBehavior="inside">
      <ModalContent className="max-h-[90vh] sm:max-h-[85vh]">
        <ModalHeader>
          <h3 className="text-lg font-semibold">{t('global_mining.select_duration')}</h3>
        </ModalHeader>
        <ModalBody className="overflow-y-auto">
          <div className="space-y-3">
            <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-none shadow-sm">
              <CardBody className="p-2">
                <div className="flex items-center gap-3">
                  <div className="bg-blue-500 rounded-full p-2">
                    <Icon icon="solar:box-bold" className="text-white text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-blue-700 font-semibold">{getDisplayName(pkg.name)}</p>
                    <p className="text-lg text-blue-800 font-bold">{formatNumber(pkg.amount)} {pkg.tokenSymbol}</p>
                  </div>
                </div>
              </CardBody>
            </Card>

            <div className="pb-1">
              <h4 className="text-sm font-medium mb-3">{t('global_mining.select_duration')}</h4>
              <div className="grid grid-cols-1 gap-3 max-h-[30vh] sm:max-h-none overflow-y-auto pr-1">
                {pkg.durations.map((duration) => {
                  const isSelected = selectedDuration === duration.days;

                  return (
                    <div
                      key={duration.days}
                      className={`border rounded-lg p-2 cursor-pointer transition-all ${
                        isSelected
                          ? 'border-green-500 bg-green-50 shadow-md'
                          : 'border-gray-200 hover:border-green-300 hover:bg-green-50/50'
                      }`}
                      onClick={() => setSelectedDuration(duration.days)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                            isSelected ? 'border-green-500 bg-green-500' : 'border-gray-300'
                          }`}>
                            {isSelected && <Icon icon="solar:check-bold" className="text-white text-xs" />}
                          </div>
                          <span className="font-semibold text-base">{duration.days} {t('global_mining.days')}</span>
                        </div>
                      </div>

                      <div className="mt-2 pl-8 grid grid-cols-2 gap-x-4 gap-y-1 text-xs text-gray-600">
                        <div className="flex justify-between">
                          <span>{t('global_mining.interest_rate')}:</span>
                          <span className="font-medium">{duration.interestRate}%USDT</span>
                        </div>
                        <div className="flex justify-between">
                          <span>{t('global_mining.bonus_rate')}:</span>
                          <span className="font-medium">{duration.bonusRate}%WM</span>
                        </div>
                      </div>
                      <div className="mt-2 pl-8 grid grid-cols-1 gap-x-4 gap-y-1 text-xs text-gray-600">
                        <div className="flex justify-between">
                          <span>{t('global_mining.daily_interest')}:</span>
                          <span className="font-medium">{duration.dailyInterestRate?.toFixed(8) || '0.00'}%USDT</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {selectedDuration && (
              <Card className="bg-gradient-to-r from-green-50 to-green-100 border-none shadow-sm">
                <CardBody className="p-4">
                  <h4 className="text-sm font-semibold text-green-700 flex items-center gap-2 mb-2">
                    <Icon icon="solar:document-text-bold" className="text-green-600" />
                    {t('global_mining.investment_summary')}
                  </h4>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">{t('global_mining.package')}:</span>
                      <span className="font-medium">{getDisplayName(pkg.name)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">{t('global_mining.price')}:</span>
                      <span className="font-medium">{formatNumber(pkg.amount)} {pkg.tokenSymbol}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">{t('global_mining.duration')}:</span>
                      <span className="font-medium">{selectedDuration} {t('global_mining.days')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">{t('global_mining.interest_rate')}:</span>
                      <span className="font-medium">{getDurationDetails(selectedDuration)?.interestRate || 0}%USDT</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">{t('global_mining.bonus_rate')}:</span>
                      <span className="font-medium">{getDurationDetails(selectedDuration)?.bonusRate || 0}%WM</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">{t('global_mining.daily_interest')}:</span>
                      <span className="font-medium">{getDurationDetails(selectedDuration)?.dailyInterestRate?.toFixed(2) || '0.00'}%USDT</span>
                    </div>
                    <div className="col-span-2 mt-2 pt-2 border-t border-green-200">
                      <div className="flex justify-between text-green-700 font-semibold mb-1">
                        <span>{t('global_mining.expected_return_usdt')}:</span>
                        <span className="text-semibold">{formatNumber(calculateExpectedReturn(selectedDuration))} USDT</span>
                      </div>
                      <div className="flex justify-between text-blue-700 font-semibold mb-1">
                        <span>{t('global_mining.expected_return_wm')}:</span>
                        <span className="text-semibold">{formatNumber(calculateWMReturn(selectedDuration))} WM</span>
                      </div>
                      <div className="flex justify-between text-gray-500 text-xs">
                        <span>{t('global_mining.exchange_rate')}:</span>
                        <span>1 WM = {wmUsdRate} USDT</span>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            )}

            {error && (
              <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <Icon icon="solar:danger-triangle-bold" className="text-red-500 mt-0.5" width={18} height={18} />
                  <div>
                    <p className="text-sm font-medium text-red-700">{t('errors.error')}</p>
                    <p className="text-xs text-red-600">{error}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </ModalBody>
        <ModalFooter className="border-t border-gray-100">
          <div className="flex justify-end gap-3 w-full">
            <Button
              variant="flat"
              onPress={onClose}
              disabled={isLoading}
              className="flex-1"
            >
              {t('common.cancel')}
            </Button>
            <Button
              color="primary"
              onPress={handleConfirm}
              isLoading={isLoading}
              disabled={selectedDuration === null || isLoading}
              className="bg-gradient-to-r from-blue-500 to-green-500 text-white flex-1"
            >
              {t('global_mining.confirm_investment')}
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default DurationSelectionModal;
