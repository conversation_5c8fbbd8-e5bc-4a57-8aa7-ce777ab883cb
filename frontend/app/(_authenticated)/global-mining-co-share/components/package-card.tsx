'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, Button } from '@heroui/react';
import { Icon } from '@iconify/react';
import { StakingPackage } from '@/types/models';
import { formatNumber } from '@/utils/format';
import { useTranslation } from '@/hooks/useTranslation';

interface PackageCardProps {
  package: StakingPackage;
  onClick: (pkg: StakingPackage) => void;
  isLoading?: boolean;
}

const PackageCard: React.FC<PackageCardProps> = ({ package: pkg, onClick, isLoading = false }) => {
  const { t } = useTranslation();
  const getPackageImage = (code: string) => {
    switch (code) {
      case 'PHOENIX':
        return '/co-share/phoenix.jpg';
      case 'SPIRIT_TURTLE':
        return '/co-share/spirit-turtle.jpg';
      case 'UNICORN':
        return '/co-share/unicorn.jpg';
      case 'DRAGON':
        return '/co-share/dragon.jpg';
      case 'DRAGON_LORD':
        return '/co-share/dragon-lord.jpg';
      case 'ETERNAL_DRAGON':
        return '/co-share/eternal-dragon.jpg';
      case 'SOVEREIGN_DRAGON':
        return '/co-share/sovereign-dragon.jpg';
      default:
        return '/co-share/phoenix.jpg';
    }
  };

  const getPackageIcon = (code: string) => {
    switch (code) {
      case 'PHOENIX':
        return 'game-icons:phoenix-head';
      case 'SPIRIT_TURTLE':
        return 'game-icons:turtle';
      case 'UNICORN':
        return 'game-icons:unicorn';
      case 'DRAGON':
      case 'DRAGON_LORD':
      case 'ETERNAL_DRAGON':
      case 'SOVEREIGN_DRAGON':
        return 'game-icons:dragon-head';
      default:
        return 'game-icons:perspective-dice-six';
    }
  };

  const getDisplayName = (name: string) => {
    return name.replace(' Package', '');
  };

  return (
    <Card
      className="w-full max-w-sm hover:shadow-lg transition-shadow cursor-pointer"
      onPress={() => onClick(pkg)}
    >
      <CardBody className="p-2">
        <div className="flex flex-col gap-3">
          <img
            src={getPackageImage(pkg.code)}
            alt={pkg.name}
            className="w-full h-auto object-cover rounded-lg"
          />
          <div className="flex justify-between items-center">
            <Button
              color="primary"
              size="sm"
              fullWidth
              onPress={() => onClick(pkg)}
              isLoading={isLoading}
              className="bg-gradient-to-r from-blue-500 to-green-500 text-white"
            >
              {t('global_mining.invest')}
            </Button>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default PackageCard;
