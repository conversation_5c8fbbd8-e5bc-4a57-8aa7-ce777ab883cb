'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>dal<PERSON>ontent, ModalHeader, ModalBody, Progress, Spinner } from '@heroui/react';
import { Icon } from '@iconify/react';
import { Staking } from '@/types/models';
import { formatNumber } from '@/utils/format';
import { useApi } from '@/hooks/useApi';
import { useTranslation } from '@/hooks/useTranslation';

interface StakingDetailModalProps {
  staking: Staking | null;
  isOpen: boolean;
  onClose: () => void;
}

interface InterestHistory {
  id: string;
  amount: number;
  calculationDate: Date;
  createdAt: Date;
}

const StakingDetailModal: React.FC<StakingDetailModalProps> = ({ staking, isOpen, onClose }) => {
  const [interestHistory, setInterestHistory] = useState<InterestHistory[]>([]);
  const [stakingDetails, setStakingDetails] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const { fetchApi } = useApi();
  const { t } = useTranslation();

  useEffect(() => {
    if (isOpen && staking) {
      console.log('Modal opened with staking:', staking);
      fetchStakingDetails();
    }
  }, [isOpen, staking]);

  useEffect(() => {
    console.log('StakingDetailModal rendered with props:', { isOpen, staking: !!staking });
  }, []);

  const fetchStakingDetails = async () => {
    if (!staking) return;

    setLoading(true);
    try {
      console.log('Fetching staking details for ID:', staking.id);
      const response = await fetchApi(`/staking/stakings/${staking.id}`);
      console.log('Staking details response:', response);
      if (response.success && response.data) {
        const history = response.data.interestHistory || [];
        setInterestHistory(history);
        setStakingDetails(response.data);
        console.log('API response data:', response.data);
      } else {
        console.error('Failed to fetch staking details:', response);
      }
    } catch (error) {
      console.error('Error fetching staking details:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!staking || !isOpen) {
    console.log('Not rendering modal because:', { staking: !!staking, isOpen });
    return null;
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'text-green-500';
      case 'COMPLETED':
        return 'text-blue-500';
      case 'CANCELLED':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const getDisplayName = (name: string) => {
    return name.replace(' Package', '').toUpperCase();
  };

  console.log('Rendering StakingDetailModal with:', { isOpen, staking });

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="sm" scrollBehavior="inside">
      <ModalContent>
        <ModalHeader>
          <h3 className="text-lg font-semibold">{t('global_mining.investment_details')}</h3>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h4 className="text-xl font-semibold">{getDisplayName(staking.packageName)}</h4>
              <div className={`flex items-center gap-1 ${getStatusColor(staking.status)}`}>
                <Icon icon="solar:check-circle-bold" width={20} height={20} />
                <span className="font-medium">{staking.status}</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col">
                <span className="text-sm text-gray-600">{t('global_mining.price')}</span>
                <span className="text-base font-semibold">{formatNumber(staking.amount)} USDT</span>
              </div>

              <div className="flex flex-col">
                <span className="text-sm text-gray-600">{t('global_mining.duration')}</span>
                <span className="text-base font-semibold">{staking.durationDays} {t('global_mining.days')}</span>
              </div>

              <div className="flex flex-col">
                <span className="text-sm text-gray-600">{t('global_mining.interest_rate')}</span>
                <span className="text-base font-semibold">{staking.interestRate}%</span>
              </div>

              <div className="flex flex-col">
                <span className="text-sm text-gray-600">{t('global_mining.bonus_rate')}</span>
                <span className="text-base font-semibold">{staking.bonusRate}%</span>
              </div>

              <div className="flex flex-col">
                <span className="text-sm text-gray-600">{t('global_mining.start_date')}</span>
                <span className="text-base font-semibold">{formatDate(staking.startDate)}</span>
              </div>

              <div className="flex flex-col">
                <span className="text-sm text-gray-600">{t('global_mining.end_date')}</span>
                <span className="text-base font-semibold">{formatDate(staking.endDate)}</span>
              </div>

              <div className="flex flex-col">
                <span className="text-sm text-gray-600">{t('global_mining.total_interest')}</span>
                <span className="text-base font-semibold text-green-600">{formatNumber(staking.totalInterest)} USDT</span>
              </div>

              <div className="flex flex-col">
                <span className="text-sm text-gray-600">{t('global_mining.total_bonus')}</span>
                <span className="text-base font-semibold text-blue-600">{formatNumber(staking.wmBonusAmount)} WM</span>
                <span className="text-xs text-gray-500">(1 WM = {formatNumber(staking.wmUsdRate || 1.2)} USDT)</span>
              </div>

              <div className="flex flex-col">
                <span className="text-sm text-gray-600">{t('global_mining.daily_interest_rate')}</span>
                <span className="text-base font-semibold">{staking.dailyInterestRate.toFixed(8)}%</span>
              </div>
            </div>

            {staking.status === 'ACTIVE' && (
              <div className="mt-2">
                <div className="flex justify-between text-sm mb-1">
                  <span>{t('global_mining.progress')}:</span>
                  <span>{(staking.progressPercentage || 0).toFixed(2)}%</span>
                </div>
                <Progress
                  value={staking.progressPercentage || 0}
                  color="success"
                  className="h-2"
                />
                <div className="flex justify-between text-xs mt-1 text-gray-500">
                  <span>{t('global_mining.day')} {staking.elapsedDays || 0}</span>
                  <span>{staking.daysRemaining || 0} {t('global_mining.days_left')}</span>
                </div>
              </div>
            )}
            
            {stakingDetails?.isMaxedOut && (
              <div className="mt-4 bg-red-50 p-3 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Icon icon="solar:danger-triangle-bold" className="text-red-500" width={20} height={20} />
                    <span className="text-sm font-medium text-red-700">{t('global_mining.max_out_status')}</span>
                  </div>
                  <span className="text-xs font-medium text-red-500 bg-red-100 px-2 py-0.5 rounded-full">
                    MAX OUT
                  </span>
                </div>
                <p className="text-xs text-red-600 mt-1">
                  {t('global_mining.max_out_message')}
                </p>
                {stakingDetails?.earningsPercentage && (
                  <div className="mt-2">
                    <div className="flex justify-between text-xs mb-1">
                      <span className="text-red-700">{t('global_mining.earnings_progress')}:</span>
                      <span className="text-red-700">{stakingDetails.earningsPercentage.toFixed(2)}%</span>
                    </div>
                    <Progress
                      value={Math.min(100, stakingDetails.earningsPercentage / 3)}
                      color="danger"
                      className="h-1.5"
                    />
                  </div>
                )}
              </div>
            )}

            <div className="mt-4">
              <h4 className="text-lg font-semibold mb-2">{t('global_mining.interest_history')}</h4>
              {loading ? (
                <div className="flex justify-center py-4">
                  <Spinner color="success" />
                </div>
              ) : interestHistory.length > 0 ? (
                <div className="border rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('global_mining.date')}</th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{t('global_mining.amount')}</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {interestHistory.map((history) => (
                        <tr key={history.id}>
                          <td className="px-4 py-3 text-sm text-gray-900">{formatDate(history.calculationDate)}</td>
                          <td className="px-4 py-3 text-sm text-right text-green-600 font-medium">{formatNumber(history.amount)} USDT</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  {t('global_mining.no_interest_history')}
                </div>
              )}
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default StakingDetailModal;
