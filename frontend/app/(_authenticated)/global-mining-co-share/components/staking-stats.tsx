'use client';

import React from 'react';
import { Card, CardBody, Spinner, Progress } from '@heroui/react';
import { Icon } from '@iconify/react';
import { StakingStats } from '@/types/models';
import { formatNumber } from '@/utils/format';
import { useTranslation } from '@/hooks/useTranslation';

interface StakingStatsProps {
  stats: StakingStats | null;
  loading: boolean;
}

const StakingStatsComponent: React.FC<StakingStatsProps> = ({ stats, loading }) => {
  const { t } = useTranslation();
  if (loading) {
    return (
      <Card className="w-full mb-4">
        <CardBody className="p-4 flex justify-center items-center h-32">
          <Spinner color="success" />
        </CardBody>
      </Card>
    );
  }

  if (!stats) {
    return (
      <Card className="w-full mb-4">
        <CardBody className="p-4 flex justify-center items-center h-32">
          <p className="text-gray-500">{t('global_mining.no_stats')}</p>
        </CardBody>
      </Card>
    );
  }

  const earningsPercentage = stats.earningsPercentage || 0;
  const isMaxedOut = stats.isMaxedOut || false;

  return (
    <Card className="w-full mb-4">
      <CardBody className="p-4">
        <h3 className="text-lg font-semibold mb-4">{t('global_mining.investment_statistics')}</h3>

        <div className="grid grid-cols-2 gap-2">
          <div className="flex flex-col">
            <div className="flex items-center gap-2 text-gray-600">
              <Icon icon="solar:box-bold" className="text-green-500" width={16} height={16} />
              <span className="text-sm">{t('global_mining.total')}</span>
            </div>
            <span className="text-base font-semibold">{stats.totalStakings || 0}</span>
          </div>

          <div className="flex flex-col">
            <div className="flex items-center gap-2 text-gray-600">
              <Icon icon="solar:check-circle-bold" className="text-green-500" width={16} height={16} />
              <span className="text-sm">{t('global_mining.active')}</span>
            </div>
            <span className="text-base font-semibold">{stats.activeStakings || 0}</span>
          </div>

          <div className="flex flex-col">
            <div className="flex items-center gap-2 text-gray-600">
              <Icon icon="solar:dollar-bold" className="text-green-500" width={16} height={16} />
              <span className="text-sm">{t('global_mining.investment')}</span>
            </div>
            <span className="text-base font-semibold">{formatNumber(stats.totalInvestment || 0)} USDT</span>
          </div>

          <div className="flex flex-col">
            <div className="flex items-center gap-2 text-gray-600">
              <Icon icon="solar:money-bag-bold" className="text-green-500" width={16} height={16} />
              <span className="text-sm">{t('global_mining.interest')}</span>
            </div>
            <span className="text-base font-semibold">{formatNumber(stats.totalInterest || 0)} USDT</span>
          </div>

          <div className="flex flex-col">
            <div className="flex items-center gap-2 text-gray-600">
              <Icon icon="solar:medal-ribbon-bold" className="text-green-500" width={16} height={16} />
              <span className="text-sm">{t('global_mining.commission')}</span>
            </div>
            <span className="text-base font-semibold">{formatNumber(stats.totalCommission || 0)} USDT</span>
          </div>

          <div className="flex flex-col">
            <div className="flex items-center gap-2 text-gray-600">
              <Icon icon="solar:chart-2-bold" className="text-green-500" width={16} height={16} />
              <span className="text-sm">{t('global_mining.total_volume')}</span>
            </div>
            <span className="text-base font-semibold">{formatNumber(stats.totalVolume || 0)} USDT</span>
          </div>

          {stats.rank && (
            <div className="flex flex-col">
              <div className="flex items-center gap-2 text-gray-600">
                <Icon icon="solar:ranking-bold" className="text-green-500" width={16} height={16} />
                <span className="text-sm">{t('global_mining.rank')}</span>
              </div>
              <span className="text-base font-semibold">{stats.rank}</span>
            </div>
          )}
        </div>

        {stats.totalInvestment > 0 && stats.totalEarnings !== undefined && stats.maxEarnings !== undefined && (
          <div className="mt-4">
            <div className="flex justify-between items-center mb-1">
              <div className="flex items-center gap-2">
                <Icon icon="solar:chart-bold" className={isMaxedOut ? "text-red-500" : "text-blue-500"} width={16} height={16} />
                <span className="text-sm font-medium">{t('global_mining.max_out_progress')}</span>
              </div>
              <div className="flex items-center">
                {isMaxedOut && (
                  <span className="text-xs font-medium text-red-500 bg-red-100 px-2 py-0.5 rounded-full mr-2">
                    MAX OUT
                  </span>
                )}
                <span className="text-sm">{earningsPercentage.toFixed(2)}%</span>
              </div>
            </div>
            <Progress
              value={Math.min(100, earningsPercentage / 3)}
              color={isMaxedOut ? "danger" : "primary"}
              className="h-2"
            />
            <div className="flex justify-between text-xs mt-1 text-gray-500">
              <span>{formatNumber(stats.totalEarnings)} / {formatNumber(stats.maxEarnings)} USDT (300%)</span>
            </div>
          </div>
        )}

        <div className="mt-4 bg-green-50 p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-green-700">
              <Icon icon="solar:calendar-bold" width={16} height={16} />
              <span className="text-sm font-medium">{t('global_mining.todays_interest')}</span>
            </div>
            <span className="text-sm font-semibold text-green-700">
              {formatNumber(stats.todayInterest || 0)} USDT
            </span>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default StakingStatsComponent;
