'use client';

import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useApi } from '@/hooks/useApi';
import { addToast, Spinner, Tabs, Tab } from '@heroui/react';
import { StakingPackage, Staking, StakingStats } from '@/types/models';
import PackageCard from './components/package-card';
import DurationSelectionModal from './components/duration-selection-modal';
import StakingCard from './components/staking-card';
import StakingDetailModal from './components/staking-detail-modal';
import StakingStatsComponent from './components/staking-stats';
import { useTranslation } from '@/hooks/useTranslation';
import { handleApiError } from '@/utils/error-handler';

const GlobalMiningCoShare: React.FC = () => {
  const router = useRouter();
  const { fetchApi } = useApi();
  const { t } = useTranslation();

  const [packages, setPackages] = useState<StakingPackage[]>([]);
  const [loadingPackages, setLoadingPackages] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<StakingPackage | null>(null);
  const [isPackageModalOpen, setIsPackageModalOpen] = useState(false);
  const [isCreatingStaking, setIsCreatingStaking] = useState(false);
  const [stakingError, setStakingError] = useState<string | null>(null);

  const [myStakings, setMyStakings] = useState<Staking[]>([]);
  const [loadingMyStakings, setLoadingMyStakings] = useState(false);
  const [selectedStaking, setSelectedStaking] = useState<Staking | null>(null);
  const [isStakingModalOpen, setIsStakingModalOpen] = useState(false);
  const [stakingStats, setStakingStats] = useState<StakingStats | null>(null);
  const [loadingStats, setLoadingStats] = useState(false);

  const [activeTab, setActiveTab] = useState('packages');

  useEffect(() => {
    fetchPackages();
    fetchStakingStats();
  }, []);

  useEffect(() => {
    if (activeTab === 'my-stakings') {
      fetchMyStakings();
      fetchStakingStats();
    }
  }, [activeTab]);

  const fetchPackages = async () => {
    setLoadingPackages(true);
    try {
      const response = await fetchApi('/staking/packages');
      if (response.success && response.data) {
        const processedPackages = response.data.map((pkg: any) => {
          const durations = pkg.durations.map((duration: any) => {
            const dailyInterestRate = duration.interestRate / 30;

            const principal = pkg.amount;
            const interestAmount = (principal * duration.interestRate / 100) * (duration.days / 30);
            const bonusAmount = principal * duration.bonusRate / 100;
            const totalReturn = principal + interestAmount + bonusAmount;

            return {
              ...duration,
              dailyInterestRate,
              totalReturn
            };
          });

          return {
            ...pkg,
            durations
          };
        });

        setPackages(processedPackages);
      }
    } catch (error: any) {
      addToast({
        title: t('errors.error'),
        description: error.message || t('global_mining.fetch_packages_error'),
        color: 'danger',
      });
    } finally {
      setLoadingPackages(false);
    }
  };

  const fetchMyStakings = async () => {
    setLoadingMyStakings(true);
    try {
      const response = await fetchApi('/staking/my-stakings');

      if (response.success && response.data) {
        const processedStakings = response.data.map((staking: any) => {
          const startDate = new Date(staking.startDate);
          const endDate = new Date(staking.endDate);
          const now = new Date();

          const totalDays = staking.durationDays;

          let elapsedDays = staking.elapsedDays;

          if (!elapsedDays || elapsedDays === 0) {
            elapsedDays = Math.floor((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
          }

          const daysRemaining = Math.max(0, totalDays - elapsedDays);

          let progressPercentage = staking.progressPercentage;

          if (!progressPercentage || progressPercentage === 0) {
            progressPercentage = Math.min(100, (elapsedDays / totalDays) * 100);
          }

          const totalInterest = typeof staking.totalInterest === 'string'
            ? parseFloat(staking.totalInterest)
            : (typeof staking.totalInterest === 'number' ? staking.totalInterest : 0);

          let calculatedInterest = totalInterest;
          if (totalInterest === 0 && staking.status === 'ACTIVE' && elapsedDays > 0) {
            const dailyInterestRate = staking.interestRate / 30;
            const dailyInterest = staking.amount * (dailyInterestRate / 100);
            calculatedInterest = dailyInterest * elapsedDays;
          }

          const totalBonus = typeof staking.totalBonus === 'string'
            ? parseFloat(staking.totalBonus)
            : (typeof staking.totalBonus === 'number' ? staking.totalBonus : 0);

          const wmBonusAmount = typeof staking.wmBonusAmount === 'string'
            ? parseFloat(staking.wmBonusAmount)
            : (typeof staking.wmBonusAmount === 'number' ? staking.wmBonusAmount : 0);

          return {
            ...staking,
            startDate,
            endDate,
            elapsedDays,
            daysRemaining,
            progressPercentage,
            totalInterest: calculatedInterest,
            totalBonus: totalBonus,
            wmBonusAmount: wmBonusAmount
          };
        });

        setMyStakings(processedStakings);
      }
    } catch (error: any) {
      addToast({
        title: t('errors.error'),
        description: error.message || t('global_mining.fetch_investments_error'),
        color: 'danger',
      });
    } finally {
      setLoadingMyStakings(false);
    }
  };

  const fetchStakingStats = async () => {
    setLoadingStats(true);
    try {
      const response = await fetchApi('/staking/stats');
      if (response.success && response.data) {
        const processedStats = {
          totalStakings: (response.data.activeStakingsCount || 0) + (response.data.completedStakingsCount || 0),
          activeStakings: response.data.activeStakingsCount || 0,
          totalInvestment: typeof response.data.totalStakingAmount === 'string'
            ? parseFloat(response.data.totalStakingAmount)
            : (response.data.totalStakingAmount || 0),
          totalInterest: typeof response.data.totalInterestEarned === 'string'
            ? parseFloat(response.data.totalInterestEarned)
            : (response.data.totalInterestEarned || 0),
          todayInterest: typeof response.data.todayInterest === 'string'
            ? parseFloat(response.data.todayInterest)
            : (response.data.todayInterest || 0),
          totalEarnings: typeof response.data.totalEarnings === 'string'
            ? parseFloat(response.data.totalEarnings)
            : (response.data.totalEarnings || 0),
          maxEarnings: response.data.maxEarnings || 0,
          earningsPercentage: response.data.earningsPercentage || 0,
          isMaxedOut: response.data.isMaxedOut || false,
          totalCommission: typeof response.data.totalCommission === 'string'
            ? parseFloat(response.data.totalCommission)
            : (response.data.totalCommission || 0),
          directCommission: typeof response.data.directCommission === 'string'
            ? parseFloat(response.data.directCommission)
            : (response.data.directCommission || 0),
          matchingCommission: typeof response.data.matchingCommission === 'string'
            ? parseFloat(response.data.matchingCommission)
            : (response.data.matchingCommission || 0),
          rankCommission: typeof response.data.rankCommission === 'string'
            ? parseFloat(response.data.rankCommission)
            : (response.data.rankCommission || 0),
          rank: response.data.rank || null,
          wmUsdRate: typeof response.data.wmUsdRate === 'string'
            ? parseFloat(response.data.wmUsdRate)
            : (response.data.wmUsdRate || 1.2),
          totalVolume: typeof response.data.totalVolume === 'string'
            ? parseFloat(response.data.totalVolume)
            : (response.data.totalVolume || 0)
        };

        setStakingStats(processedStats);
      }
    } catch (error: any) {
      addToast({
        title: t('errors.error'),
        description: error.message || t('global_mining.fetch_stats_error'),
        color: 'danger',
      });
    } finally {
      setLoadingStats(false);
    }
  };

  const handlePackageClick = (pkg: StakingPackage) => {
    setSelectedPackage(pkg);
    setIsPackageModalOpen(true);
  };

  const handleStakingClick = (staking: Staking) => {
    setSelectedStaking(staking);
    setIsStakingModalOpen(true);
  };

  const handleCreateStaking = async (packageCode: string, durationDays: number) => {
    setIsCreatingStaking(true);
    setStakingError(null);

    try {
      const response = await fetchApi('/staking/create', {
        method: 'POST',
        body: JSON.stringify({
          packageCode,
          durationDays
        })
      });

      if (response.success) {
        addToast({
          title: t('common.success'),
          description: t('global_mining.investment_created'),
          color: 'success',
        });
        setIsPackageModalOpen(false);
        setActiveTab('my-stakings');
        fetchMyStakings();
        fetchStakingStats();
      } else {
        const errorMessage = handleApiError(response, t, 'global_mining.create_investment_error');

        setStakingError(errorMessage);
      }
    } catch (error: any) {
      const errorMessage = handleApiError(error, t, 'global_mining.create_investment_error');

      setStakingError(errorMessage);
    } finally {
      setIsCreatingStaking(false);
    }
  };

  const renderPackagesTab = () => {
    if (loadingPackages) {
      return (
        <div className="flex justify-center items-center py-10">
          <Spinner color="success" />
        </div>
      );
    }

    if (packages.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-[100px]">
          <Icon icon="solar:box-bold" width={48} height={48} className="text-green-400 mb-2" />
          <p className="text-center text-gray-500 text-sm">{t('global_mining.no_packages')}</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-1 gap-2">
        {packages.map((pkg) => (
          <PackageCard
            key={pkg.code}
            package={pkg}
            onClick={handlePackageClick}
            isLoading={isCreatingStaking}
          />
        ))}
      </div>
    );
  };

  const renderMyStakingsTab = () => {
    return (
      <div className="space-y-2">
        <StakingStatsComponent stats={stakingStats} loading={loadingStats} />

        {loadingMyStakings ? (
          <div className="flex justify-center items-center py-6">
            <Spinner color="success" />
          </div>
        ) : myStakings.length > 0 ? (
          <div className="space-y-2">
            {myStakings.map((staking) => (
              <StakingCard
                key={staking.id}
                staking={staking}
                onClick={handleStakingClick}
              />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-[50px]">
            <Icon icon="solar:box-bold" width={36} height={36} className="text-green-400 mb-2" />
            <p className="text-center text-gray-500 text-xs">{t('global_mining.no_investments')}</p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex w-full flex-col gap-1 max-w-sm mx-auto">
      <div className="flex items-center justify-between mb-1">
        <button onClick={() => router.push('/wallet')}>
          <Icon icon="solar:arrow-left-linear" width={24} className="text-gray-500" />
        </button>
        <p className="text-lg font-semibold flex-1 text-center">{t('global_mining.title')}</p>
      </div>

      <Tabs
        aria-label="Global Mining Co-Share Tabs"
        fullWidth
        selectedKey={activeTab}
        onSelectionChange={(key) => setActiveTab(key as string)}
        classNames={{
          tabList: "gap-1",
          panel: "pt-1"
        }}
      >
        <Tab key="packages" title={t('global_mining.investment_packages')}>
          {renderPackagesTab()}
        </Tab>
        <Tab key="my-stakings" title={t('global_mining.my_investments')}>
          {renderMyStakingsTab()}
        </Tab>
      </Tabs>

      {selectedPackage && (
        <DurationSelectionModal
          isOpen={isPackageModalOpen}
          onClose={() => {
            setIsPackageModalOpen(false);
            setStakingError(null);
          }}
          package={selectedPackage}
          onConfirm={handleCreateStaking}
          isLoading={isCreatingStaking}
          wmUsdRate={stakingStats?.wmUsdRate || 1.2}
          error={stakingError}
        />
      )}

      <StakingDetailModal
        isOpen={isStakingModalOpen}
        onClose={() => setIsStakingModalOpen(false)}
        staking={selectedStaking}
      />
    </div>
  );
};

export default GlobalMiningCoShare;
