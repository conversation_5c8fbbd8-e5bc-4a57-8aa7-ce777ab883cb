import { User } from "@/types/models";

export interface TreeStats {
    totalReferrals: number;
    totalReferralsKyc: number;
    totalDirectReferrals: number;
    totalDirectReferralsKyc: number;
    totalVolume: number;
    totalEarnings: number;
    totalVolumePerDay: number;
    totalEarningsPerDay: number;
    totalVolumeSession: number;
    totalEarningsSession: number;
    totalVolumeDirectReferrals: number;
    totalVolumePerDayDirectReferrals: number;
    totalEarningsDirectReferrals: number;
    totalEarningsPerDayDirectReferrals: number;
    totalVolumeSessionDirectReferrals: number;
    totalEarningsSessionDirectReferrals: number;
    totalSellVolume: number;
    totalSellVolumePerDay: number;
    totalSellVolumeSession: number;
    totalSellVolumeDirectReferrals: number;
    totalSellVolumePerDayDirectReferrals: number;
    totalSellVolumeSessionDirectReferrals: number;
    hasF1BuySession: boolean;
    hasF1SellSession: boolean;
}

export interface TreeNodeData {
    id: string;
    name: string;
    username: string;
    walletAddress: string;
    rank: string;
    wManagerRank: string;
    totalMined: number;
    totalEarnings: number;
    totalVolume: number;
    totalVolumePerDay: number;
    totalEarningsPerDay: number;
    totalVolumeSession: number;
    totalEarningsSession: number;
    totalVolumeDirectReferrals: number;
    totalVolumePerDayDirectReferrals: number;
    totalEarningsDirectReferrals: number;
    totalEarningsPerDayDirectReferrals: number;
    totalVolumeSessionDirectReferrals: number;
    totalEarningsSessionDirectReferrals: number;
    level: string;
    totalReferrals: number;
    directReferrals: number;
    firstDepositTime: string | null;
    joinedAt: string;
    hasLightningBolt: boolean;
    children: TreeNodeData[];
    avatarUrl: string;
}

export interface TreeResponseDto {
    stats: TreeStats;
    tree: TreeNodeData;
    directReferrals: TreeNodeData[];
}