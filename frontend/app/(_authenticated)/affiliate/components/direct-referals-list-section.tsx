'use client';

import React, { useState } from 'react';
import { Card, CardBody, CardHeader, Avatar, useDisclosure } from '@heroui/react';
import { User, UserRank, WManagerRank } from '@/types/models';
import { formatDate } from '@/utils/date';
import UserModal from '@/components/user-modal';
import { UserShowModal } from '@/types/models';
import { TreeNodeData } from '../types/type';
import { useTranslation } from '@/hooks/useTranslation';

interface DirectReferalsListProps {
  directReferrals?: TreeNodeData[];
}

export default function DirectReferalsListSection({ directReferrals = [] }: DirectReferalsListProps) {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [selectedUser, setSelectedUser] = useState<TreeNodeData | null>(null);
  const { t } = useTranslation();

  /**
     * <PERSON><PERSON>y hình ảnh của user dựa vào rank
     */
  const getUserRank = (member: TreeNodeData) => {
    if (member.wManagerRank === WManagerRank.WM1) {
        return '/levels/w-1.png';
    }
    if (member.wManagerRank === WManagerRank.WM2) {
        return '/levels/w-2.png';
    }
    if (member.wManagerRank === WManagerRank.WM3) {
        return '/levels/w-3.png';
    }
    if (member.wManagerRank === WManagerRank.WM4) {
        return '/levels/w-4.png';
    }
    if (member.wManagerRank === WManagerRank.WM5) {
        return '/levels/w-5.png';
    }
    return '/levels/w-0.png';
}

  const handleUserClick = (member: TreeNodeData) => {
    setSelectedUser({
      id: member.id,
      name: member.name,
      username: member.username,
      walletAddress: member.walletAddress,
      level: '1',
      totalReferrals: member.totalReferrals,
      totalVolume: member.totalVolume,
      totalEarnings: member.totalEarnings,
      directReferrals: member.directReferrals,
      totalVolumePerDay: member.totalVolumePerDay,
      totalEarningsPerDay: member.totalEarningsPerDay,
      totalVolumeSession: member.totalVolumeSession,
      totalEarningsSession: member.totalEarningsSession,
      rank: member.rank,
      totalMined: member.totalMined,
      firstDepositTime: member.firstDepositTime,
      hasLightningBolt: member.hasLightningBolt,
      children: member.children,
      joinedAt: member.joinedAt,
      avatarUrl: member.avatarUrl || null,
      wManagerRank: member.wManagerRank
    });
    onOpen();
  };

  return (
    <>
      <Card>
        <CardHeader className="flex gap-3">
          <div className="flex flex-col gap-2">
            <p className="text-lg">{t('affiliate.direct_referrals')} ({directReferrals.length})</p>
          </div>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            {directReferrals.map((member) => (
              <div
                key={member.id}
                className="flex justify-between items-center cursor-pointer hover:bg-default-100 p-1 rounded-lg transition-colors"
                onClick={() => handleUserClick(member)}
              >
                <div className="flex gap-3 items-center">
                  <Avatar
                    src={getUserRank(member)}
                    size="sm"
                  />
                  <div className="flex flex-col">
                    <p className="text-small font-semibold">{member.username}</p>
                    <p className="text-tiny text-default-500">
                      {t('affiliate.joined')} {formatDate(member.joinedAt)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {member.firstDepositTime && (
                    <span className="text-tiny bg-success-100 text-success-600 px-2 py-1 rounded">
                      {t('affiliate.active')}
                    </span>
                  )}
                  {!member.firstDepositTime && (
                    <span className="text-tiny bg-default-100 text-default-600 px-2 py-1 rounded">
                      {t('affiliate.pending')}
                    </span>
                  )}
                </div>
              </div>
            ))}

            {directReferrals.length === 0 && (
              <div className="text-center text-default-500 py-4">
                {t('affiliate.no_team_members')}
              </div>
            )}
          </div>
        </CardBody>
      </Card>

      {selectedUser && (
        <UserModal
          isOpen={isOpen}
          onOpenChange={onOpenChange}
          userData={selectedUser}
        />
      )}
    </>
  );
}