'use client';

import React from 'react';
import { Card, CardBody, Button, addToast, ToastProvider } from '@heroui/react';
import { Icon } from '@iconify/react';
import { useUserStore } from '@/store/userStore';
import { useTranslation } from '@/hooks/useTranslation';

type ToastPlacement =
    | "top-left"
    | "top-center"
    | "top-right"
    | "bottom-left"
    | "bottom-center"
    | "bottom-right";

export default function InviteLinkSection() {
    const { user } = useUserStore();
    const { t } = useTranslation();

    const currentDomain = window.location.origin;
    const inviteLink = `${currentDomain}/?ref=${user?.referralCode}`;

    const [placement, setPlacement] = React.useState<ToastPlacement>("bottom-center");

    const handleCopy = () => {
        navigator.clipboard.writeText(inviteLink);
        addToast({
            title: t('common.copied'),
            description: t('affiliate.link_copied'),
            color: 'success',
        });
    };

    return (
        <>
            <ToastProvider placement={placement} toastOffset={placement.includes("top") ? 3 : 0} />

            <Card shadow="sm" className="border-none">
                <CardBody className="p-4 flex flex-col gap-2">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-green-500">
                                <Icon icon="solar:link-linear" width={16} className="text-white" />
                            </div>
                            <p className="text-medium font-semibold">{t('affiliate.your_invite_link')}:</p>
                        </div>
                        <Button
                            isIconOnly
                            variant="light"
                            onPress={handleCopy}
                        >
                            <Icon icon="solar:copy-linear" width={20} className="text-gray-500" />
                        </Button>
                    </div>
                    <p className="text-sm text-gray-500 break-all">{inviteLink}</p>
                </CardBody>
            </Card>
        </>
    );
}