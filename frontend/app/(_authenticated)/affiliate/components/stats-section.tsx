'use client';

import React from 'react';
import { Card, CardBody, CircularProgress, Skeleton } from '@heroui/react';
import { Icon } from '@iconify/react';
import { TreeStats } from '../types/type';
import { useTranslation } from '@/hooks/useTranslation';

export default function StatsSection({ stats }: { stats: TreeStats | null }) {
  const { t } = useTranslation();
  const referralsKycPercentage = stats?.totalReferrals > 0 ? (stats?.totalReferralsKyc || 0) / stats?.totalReferrals * 100 : 0;
  const directReferralsKycPercentage = stats?.totalDirectReferrals > 0 ? (stats?.totalDirectReferralsKyc || 0) / stats?.totalDirectReferrals * 100 : 0;
  const statItems = [
    {
      icon: "solar:users-group-rounded-bold-duotone",
      label: t('affiliate.total_referrals'),
      value: stats?.totalReferrals || 0,
      color: "text-green-500",
      name: "totalReferrals",
    },
    {
      icon: "solar:user-plus-rounded-bold-duotone",
      label: t('affiliate.direct_referrals'),
      value: stats?.totalDirectReferrals || 0,
      color: "text-green-500",
      name: "totalDirectReferrals",
    },
    {
      icon: "solar:user-plus-rounded-bold-duotone",
      label: t('affiliate.total_referrals'),
      value: stats?.totalReferralsKyc || 0,
      color: "text-green-500",
      name: "totalReferralsKyc",
    },
    {
      icon: "solar:user-plus-rounded-bold-duotone",
      label: t('affiliate.direct_referrals'),
      value: stats?.totalDirectReferralsKyc || 0,
      color: "text-green-500",
      name: "totalDirectReferralsKyc",
    },
    {
      icon: "solar:chart-bold-duotone",
      label: t('affiliate.total_buy_volume'),
      value: stats?.totalVolume || "0",
      color: "text-green-500",
      name: "totalVolume",
    },
    {
      icon: "solar:chart-bold-duotone",
      label: t('affiliate.direct_referrals_total_buy_volume'),
      value: stats?.totalVolumeDirectReferrals || "0",
      color: "text-green-500",
      name: "totalVolumeDirectReferrals",
    },
    {
      icon: "solar:chart-bold-duotone",
      label: t('affiliate.buy_volume_day'),
      value: stats?.totalVolumePerDay || "0",
      color: "text-green-500",
      name: "totalVolumePerDay",
    },
    {
      icon: "solar:chart-bold-duotone",
      label: t('affiliate.direct_referrals_buy_volume_day'),
      value: stats?.totalVolumePerDayDirectReferrals || "0",
      color: "text-green-500",
      name: "totalVolumePerDayDirectReferrals",
    },
    {
      icon: "solar:chart-bold-duotone",
      label: t('affiliate.buy_volume_session'),
      value: stats?.totalVolumeSession || "0",
      color: "text-green-500",
      name: "totalVolumeSession",
    },
    {
      icon: "solar:chart-bold-duotone",
      label: t('affiliate.direct_referrals_buy_volume_session'),
      value: stats?.totalVolumeSessionDirectReferrals || "0",
      color: "text-green-500",
      name: "totalVolumeSessionDirectReferrals",
    },

    {
      icon: "solar:chart-bold-duotone",
      label: t('affiliate.total_sell_volume'),
      value: stats?.totalSellVolume || "0",
      color: "text-red-500",
      name: "totalSellVolume",
    },
    {
      icon: "solar:chart-bold-duotone",
      label: t('affiliate.direct_referrals_total_sell_volume'),
      value: stats?.totalSellVolumeDirectReferrals || "0",
      color: "text-red-500",
      name: "totalSellVolumeDirectReferrals",
    },
    {
      icon: "solar:chart-bold-duotone",
      label: t('affiliate.sell_volume_day'),
      value: stats?.totalSellVolumePerDay || "0",
      color: "text-red-500",
      name: "totalSellVolumePerDay",
    },
    {
      icon: "solar:chart-bold-duotone",
      label: t('affiliate.direct_referrals_sell_volume_day'),
      value: stats?.totalSellVolumePerDayDirectReferrals || "0",
      color: "text-red-500",
      name: "totalSellVolumePerDayDirectReferrals",
    },
    {
      icon: "solar:chart-bold-duotone",
      label: t('affiliate.sell_volume_session'),
      value: stats?.totalSellVolumeSession || "0",
      color: "text-red-500",
      name: "totalSellVolumeSession",
    },
    {
      icon: "solar:chart-bold-duotone",
      label: t('affiliate.direct_referrals_sell_volume_session'),
      value: stats?.totalSellVolumeSessionDirectReferrals || "0",
      color: "text-red-500",
      name: "totalSellVolumeSessionDirectReferrals",
    },

    {
      icon: "solar:dollar-outline",
      label: t('affiliate.total_earnings'),
      value: stats?.totalEarnings || "0",
      color: "text-green-500",
      name: "totalEarnings",
    },
    {
      icon: "solar:dollar-outline",
      label: t('affiliate.direct_referrals_total_earnings'),
      value: stats?.totalEarningsDirectReferrals || "0",
      color: "text-green-500",
      name: "totalEarningsDirectReferrals",
    },
    {
      icon: "solar:dollar-outline",
      label: t('affiliate.earnings_day'),
      value: stats?.totalEarningsPerDay || "0",
      color: "text-green-500",
      name: "totalEarningsPerDay",
    },
    {
      icon: "solar:dollar-outline",
      label: t('affiliate.direct_referrals_earnings_day'),
      value: stats?.totalEarningsPerDayDirectReferrals || "0",
      color: "text-green-500",
      name: "totalEarningsPerDayDirectReferrals",
    },
    {
      icon: "solar:dollar-outline",
      label: t('affiliate.earnings_session'),
      value: stats?.totalEarningsSession || "0",
      color: "text-green-500",
      name: "totalEarningsSession",
    },
    {
      icon: "solar:dollar-outline",
      label: t('affiliate.direct_referrals_earnings_session'),
      value: stats?.totalEarningsSessionDirectReferrals || "0",
      color: "text-green-500",
      name: "totalEarningsSessionDirectReferrals",
    },
  ];

  return (
    <div className="grid grid-cols-2 gap-4">
      {statItems.map((item, index) => (
        <Card key={index} className="w-full">
          <CardBody className="p-3">
            {!stats ? (
              <div className="space-y-4">
                <Skeleton className="rounded-lg">
                  <div className="h-6 rounded-lg bg-default-300"></div>
                </Skeleton>
                <Skeleton className="rounded-lg">
                  <div className="h-4 w-3/5 rounded-lg bg-default-200"></div>
                </Skeleton>
              </div>
            ) : (
              <>
                <div className="flex items-center gap-2 mb-2">
                  <Icon
                    icon={item.icon}
                    className={`${item.color}`}
                    width={16}
                  />
                  <p className="text-tiny text-default-500">{item.label}</p>
                </div>
                <div className="flex gap-2 items-center">
                  {item?.name === "totalReferralsKyc" && (
                    <>
                      <CircularProgress
                        aria-label="Loading..."
                        color="success"
                        showValueLabel={true}
                        size="md"
                        value={referralsKycPercentage}
                      />
                    </>
                  )}
                  {item?.name === "totalDirectReferralsKyc" && (
                    <>
                      <CircularProgress
                        aria-label="Loading..."
                        color="success"
                        showValueLabel={true}
                        size="md"
                        value={directReferralsKycPercentage}
                      />
                    </>
                  )}
                  <p className="text-lg font-semibold">{item.value} {(item?.name === "totalReferralsKyc" || item?.name === "totalDirectReferralsKyc") && t('affiliate.kyc')}</p>
                </div>
              </>
            )}
          </CardBody>
        </Card>
      ))}
    </div>
  );
}