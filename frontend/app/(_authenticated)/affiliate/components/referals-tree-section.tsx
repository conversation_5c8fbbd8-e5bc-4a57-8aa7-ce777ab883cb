import React, { useEffect, useState, useCallback, useRef } from 'react';
import Tree from 'react-d3-tree';
import { Avatar, Button, useDisclosure, user } from '@heroui/react';
import { TreeNodeData } from '../types/type';
import UserModal from '@/components/user-modal';
import { UserRank, WManagerRank } from '@/types/models';
import { useTranslation } from '@/hooks/useTranslation';

const shortenAddress = (address: string): string => {
  if (!address || address.length < 10) return address;
  return `${address.substring(0, 6)}...${address.substring(address.length - 6)}`;
};

const getUserRank = (nodeDatum: TreeNodeData) => {
  if (nodeDatum.wManagerRank === WManagerRank.WM1) {
      return '/levels/w-1.png';
  }
  if (nodeDatum.wManagerRank === WManagerRank.WM2) {
      return '/levels/w-2.png';
  }
  if (nodeDatum.wManagerRank === WManagerRank.WM3) {
      return '/levels/w-3.png';
  }
  if (nodeDatum.wManagerRank === WManagerRank.WM4) {
      return '/levels/w-4.png';
  }
  if (nodeDatum.wManagerRank === WManagerRank.WM5) {
      return '/levels/w-5.png';
  }
  return '/levels/w-0.png';
}

const ReferalsTreeSection = ({ treeData }: { treeData?: TreeNodeData }) => {
  const { t } = useTranslation();
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [isReady, setIsReady] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [selectedUser, setSelectedUser] = useState<TreeNodeData | null>(null);

  const CustomNodeComponent = ({ nodeDatum, onNodeSelect }: { nodeDatum: TreeNodeData, onNodeSelect: (node: TreeNodeData) => void }) => {
    return (
      <g>
        <foreignObject
          width="120"
          height="140"
          x="-60"
          y="-70"
          style={{ cursor: 'pointer' }}
          onClick={(e) => {
            e.stopPropagation();
            onNodeSelect(nodeDatum);
          }}
        >
          <div style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: '8px',
          }}>
            <Avatar
              size="lg"
              src={getUserRank(nodeDatum)}
              alt={shortenAddress(nodeDatum.walletAddress)}
              color={nodeDatum.firstDepositTime ? 'success' : 'default'}
            />
            <div style={{
              marginTop: '5px',
              textAlign: 'center',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>
              {nodeDatum.name ? nodeDatum.name : nodeDatum.username ? nodeDatum.username : shortenAddress(nodeDatum.walletAddress)}
            </div>
            <div style={{
              fontSize: '10px',
              color: '#666'
            }}>
              {nodeDatum.directReferrals} {t('affiliate.referrals')}
            </div>
            <div style={{
              fontSize: '9px',
              color: nodeDatum.firstDepositTime ? '#4CAF50' : '#999'
            }}>
              {nodeDatum.firstDepositTime ? t('affiliate.active') : t('affiliate.pending')}
            </div>
          </div>
        </foreignObject>
      </g>
    );
  };

  const updateDimensions = useCallback(() => {
    if (containerRef.current) {
      const { width, height } = containerRef.current.getBoundingClientRect();
      setDimensions({ width, height });
      if (width > 0) {
        setIsReady(true);
      }
    }
  }, []);

  useEffect(() => {
    const handleResize = () => {
      updateDimensions();
    };

    const timer = setTimeout(() => {
      updateDimensions();
    }, 100);

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(timer);
    };
  }, [updateDimensions]);

  useEffect(() => {
    if (treeData) {
      updateDimensions();
    }
  }, [treeData, updateDimensions]);

  if (!treeData) {
    return (
      <div className="w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">{t('affiliate.referral_network')}</h2>
        </div>
        <div className="flex justify-center items-center h-60 border border-gray-200 rounded-lg">
          <p className="text-gray-500">{t('affiliate.no_referral_data')}</p>
        </div>
      </div>
    );
  }

  const getTranslatePosition = () => {
    const width = dimensions.width || window.innerWidth;
    return {
      x: width / 2,
      y: 100
    };
  };

  const handleNodeSelect = (node: TreeNodeData) => {
    setSelectedUser({
      id: node.id,
      name: node.name,
      username: node.username,
      walletAddress: node.walletAddress,
      rank: node.rank,
      totalMined: node.totalMined,
      totalEarnings: node.totalEarnings,
      totalVolume: node.totalVolume,
      totalVolumePerDay: node.totalVolumePerDay,
      totalEarningsPerDay: node.totalEarningsPerDay,
      totalVolumeSession: node.totalVolumeSession,
      totalEarningsSession: node.totalEarningsSession,
      level: node.level,
      totalReferrals: node.totalReferrals,
      directReferrals: node.directReferrals,
      firstDepositTime: node.firstDepositTime ,
      joinedAt: node.joinedAt,
      avatarUrl: node.avatarUrl,
      hasLightningBolt: node.hasLightningBolt,
      children: node.children,
      wManagerRank: node.wManagerRank,
      totalVolumeDirectReferrals: node.totalVolumeDirectReferrals,
      totalVolumePerDayDirectReferrals: node.totalVolumePerDayDirectReferrals,
      totalEarningsDirectReferrals: node.totalEarningsDirectReferrals,
      totalEarningsPerDayDirectReferrals: node.totalEarningsPerDayDirectReferrals,
      totalVolumeSessionDirectReferrals: node.totalVolumeSessionDirectReferrals,
      totalEarningsSessionDirectReferrals: node.totalEarningsSessionDirectReferrals,
    });

    onOpen();
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">{t('affiliate.referral_network')}</h2>
      </div>

      <div
        ref={containerRef}
        style={{ width: '100%', height: '480px' }}
        className="border border-gray-200 rounded-lg overflow-hidden"
      >
        {isReady && dimensions.width > 0 && (
          <Tree
            data={treeData}
            orientation="vertical"
            pathFunc="step"
            translate={getTranslatePosition()}
            collapsible={false}
            nodeSize={{ x: 140, y: 200 }}
            separation={{ siblings: 1.2, nonSiblings: 1.5 }}
            renderCustomNodeElement={(rd) =>
              CustomNodeComponent({
                nodeDatum: rd.nodeDatum as unknown as TreeNodeData,
                onNodeSelect: handleNodeSelect
              })
            }
            zoom={0.6}
            centeringTransitionDuration={800}
          />
        )}
      </div>

      {selectedUser && (
        <UserModal
          isOpen={isOpen}
          onOpenChange={onOpenChange}
          userData={selectedUser}
        />
      )}
    </div>
  );
};



ReferalsTreeSection.displayName = 'ReferalsTreeSection';

export default ReferalsTreeSection;