import {Tabs, Tab, <PERSON>, CardBody} from "@heroui/react";
import InviteCodeSection from "./invite-code-section";
import InviteLinkSection from "./invite-link-section";
import { useTranslation } from '@/hooks/useTranslation';

export default function InviteSection() {
  const { t } = useTranslation();
  return (
    <div className="flex w-full flex-col">
      <Tabs aria-label="Invite options">
        <Tab key="invite-link" title={t('affiliate.invite_link')}>
          <InviteLinkSection />
        </Tab>
        <Tab key="invite-code" title={t('affiliate.invite_code')}>
          <InviteCodeSection />
        </Tab>
      </Tabs>
    </div>
  );
}
