'use client';

import React from 'react';
import { Card, CardBody, Button, ToastProvider, addToast } from '@heroui/react';
import { Icon } from '@iconify/react';
import { useUserStore } from '@/store/userStore';
import { useTranslation } from '@/hooks/useTranslation';

type ToastPlacement =
  | "top-left"
  | "top-center"
  | "top-right"
  | "bottom-left"
  | "bottom-center"
  | "bottom-right";

export default function InviteCodeSection() {
  const { user } = useUserStore();
  const { t } = useTranslation();
  const inviteCode = user?.referralCode;

  const [placement, setPlacement] = React.useState<ToastPlacement>("bottom-center");

  const handleCopy = () => {
    if (inviteCode) {
      navigator.clipboard.writeText(inviteCode);

      addToast({
        title: t('common.copied'),
        description: t('affiliate.code_copied'),
        color: 'success',
      });
    }
  };

  return (
    <>
      <ToastProvider placement={placement} toastOffset={placement.includes("top") ? 3 : 0} />

        <Card shadow="sm" className="border-none">
          <CardBody className="p-4 flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-green-500">
                  <Icon icon="solar:user-id-linear" width={16} className="text-white" />
                </div>
                <p className="text-medium font-semibold">{t('affiliate.your_invite_code')}</p>
              </div>
              <Button
                isIconOnly
                variant="light"
                onPress={handleCopy}
              >
                <Icon icon="solar:copy-linear" width={20} className="text-gray-500" />
              </Button>
            </div>
            <p className="text-sm text-gray-500 break-all">{inviteCode}</p>
          </CardBody>
        </Card>
      </>
    );
  }