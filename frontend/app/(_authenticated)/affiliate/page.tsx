'use client';

import React, { useEffect } from 'react';
import { useUserStore } from '@/store/userStore';
import StatsSection from './components/stats-section';
import DirectReferalsListSection from './components/direct-referals-list-section';
import InviteSection from './components/invite-section';
import { TreeResponseDto } from './types/type';
import ReferalsTreeSection from '@/app/(_authenticated)/affiliate/components/referals-tree-section';
import { Spinner } from '@heroui/react';
import { useTranslation } from '@/hooks/useTranslation';

export default function AffiliatePage() {
    const { hydrate, fetchTreeData, treeData, isHydrated, isTreeLoading } = useUserStore();
    const { t } = useTranslation();

    useEffect(() => {
        const initialize = async () => {
            await hydrate();
            await fetchTreeData();
        };
        initialize();
    }, [hydrate, fetchTreeData]);

    if (!isHydrated || isTreeLoading) {
        return (
            <div className="fixed inset-0 flex justify-center items-center bg-gray-100 bg-opacity-50 z-50">
                <Spinner color="success" />
            </div>
        );
    }

    return (
        <div className="space-y-8">
            <InviteSection />
            <StatsSection stats={treeData?.stats || null} />
            <ReferalsTreeSection treeData={treeData?.tree || undefined} />
            <DirectReferalsListSection directReferrals={treeData?.directReferrals || []} />
        </div>
    );
}