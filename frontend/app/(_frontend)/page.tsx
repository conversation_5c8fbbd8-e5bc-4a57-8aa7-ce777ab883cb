'use client';

import { Suspense } from 'react';
import WalletConnectFrontend from './components/wallet-connect-frontend';
import ExecutiveTeam from './team/components/executive-team';
import NewsSection from './news/components/news-section';
import CardSocial from '@/components/card-social';
import Carousel from '@/components/carousel';
import Image from 'next/image';

export default function Home() {
  const items = [
    {
      title: 'WorldMall 1',
      icon: 'mdi:wallet',
      description: 'WorldMall 1',
      image: '/banners/banner-1.jpeg',
    },
    {
      title: 'WorldMall 2',
      icon: 'mdi:wallet',
      description: 'WorldMall 2',
      image: '/banners/banner-2.jpeg',
    },

  ];

  const renderItem = (item: any, index: number) => {
    return (
      <div className="w-full">
        <div className="relative w-full h-40 mb-4">
          <div key={index}>
            <Image src={item.image} alt={item.title} fill style={{ objectFit: 'cover' }} className="rounded-lg" />
          </div>
        </div>
      </div>
    );
  };

  return (
    <section className="flex flex-col items-center justify-center gap-4 py-4">
      <div className="w-full text-center">
        <div className="flex w-full flex-col gap-3">
          <div className="flex max-w-sm flex-col gap-3">
            {process.env.NEXT_PUBLIC_MAINTENANCE_MODE === 'true' && (
              <div className="flex flex-col gap-2 bg-warning-50 p-4 rounded-lg">
                <p className="text-sm font-medium text-info-700">
                  Important Notes:
                </p>
                <ul className="text-sm text-warning-600 list-disc list-inside">
                  <li>System is under maintenance, please check back later.</li>
                </ul>
              </div>
            )}
            <Carousel items={items} renderItem={renderItem} hideButton={true} />
            
            <Suspense fallback={<div>Loading...</div>}>
              <WalletConnectFrontend title="Connect Wallet" icon="mdi:wallet" description="Connect your wallet to start using" />
            </Suspense>

            <ExecutiveTeam />

            <NewsSection />

            <CardSocial />
          </div>
        </div>
      </div>
    </section>
  );
}