"use client";

import React, { useEffect, useState } from "react";
import { Card, CardBody, Input, Button, addToast } from "@heroui/react";
import { Icon } from "@iconify/react";
import { cn } from "@heroui/react";
import { useAccount, useDisconnect, useSignMessage, useSwitchChain } from "wagmi";
import { useAccountModal, useConnectModal } from "@rainbow-me/rainbowkit";
import { useRouter } from "next/navigation";
import { useUserStore } from "@/store/userStore";
import { SiweMessage } from "siwe";

export type WalletConnectFrontendProps = {
  icon: string;
  title: string;
  color?: "primary" | "secondary" | "warning" | "danger";
  description: string;
  className?: string;
};

const WalletConnectFrontend = React.forwardRef<HTMLDivElement, WalletConnectFrontendProps>(
  ({ color, title, icon, description, className, ...props }, ref) => {
    const { setAuth } = useUserStore();
    const router = useRouter();
    const { address, isConnected } = useAccount();
    const { openConnectModal } = useConnectModal();
    const { openAccountModal } = useAccountModal();
    const { switchChain } = useSwitchChain();
    const { disconnect } = useDisconnect();
    const { signMessageAsync } = useSignMessage();
    const [refCode, setRefCode] = useState("");
    const [isLoggingIn, setIsLoggingIn] = useState(false);

    const colors = React.useMemo(() => {
      switch (color) {
        case "primary":
          return {
            card: "border-default-200",
            iconWrapper: "bg-primary-50 border-primary-100",
            icon: "text-primary",
          };
        case "secondary":
          return {
            card: "border-secondary-100",
            iconWrapper: "bg-secondary-50 border-secondary-100",
            icon: "text-secondary",
          };
        case "warning":
          return {
            card: "border-warning-500",
            iconWrapper: "bg-warning-50 border-warning-100",
            icon: "text-warning-600",
          };
        case "danger":
          return {
            card: "border-danger-300",
            iconWrapper: "bg-danger-50 border-danger-100",
            icon: "text-danger",
          };
        default:
          return {
            card: "border-default-200",
            iconWrapper: "bg-default-50 border-default-100",
            icon: "text-default-500",
          };
      }
    }, [color]);

    const handleConnectClick = () => {
      if (openConnectModal) {
        openConnectModal();
      }
    };

    const walletLogin = async (address) => {
      try {
        setIsLoggingIn(true);

        const siweMessage = new SiweMessage({
          domain: window.location.host,
          address: address,
          statement: "Login to WorldMall",
          uri: window.location.origin,
          version: "1",
          chainId: 57,
          nonce: Math.random().toString(36).slice(2),
          issuedAt: new Date().toISOString(),
        });

        const message = siweMessage.prepareMessage();

        const signature = await signMessageAsync({ message: message, account: address });
        const response = await fetch("/api/v1/auth/wallet-login", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({
            wallet: address,
            message,
            signature,
            ref: refCode || null,
          }),
        });

        const data = await response.json();

        if (!response.ok || (response.ok && !data.success)) {
          addToast({
            title: "Error",
            description: data.message || "Something went wrong",
            color: "danger",
          });
          await disconnect();
        } else {
          addToast({
            title: "Success",
            description: data.message || "Wallet connected",
            color: "success",
          });

          setAuth(data.data.accessToken, data.data.user);

          router.push("/wallet");
        }
      } catch (error) {
        addToast({
          title: "Error",
          description: error.message || "Something went wrong",
          color: "danger",
        });
        await disconnect();
      } finally {
        setIsLoggingIn(false);
      }
    };

    useEffect(() => {
      const searchParams = new URLSearchParams(window.location.search);
      const ref = searchParams.get("ref");
      setRefCode(ref || '');
    }, []);

    useEffect(() => {
      if (isConnected && address && !isLoggingIn) {
        walletLogin(address);
      }
    }, [isConnected, address]);

    return (
      <Card
        ref={ref}
        className={cn("border-small", colors?.card, className)}
        shadow="sm"
        {...props}
      >
        <CardBody className="flex flex-col items-start gap-3 p-4">
          <div className="flex items-center gap-3 w-full">
            <div className={cn("flex items-center rounded-medium border p-2", colors?.iconWrapper)}>
              <Icon className={colors?.icon} icon={icon} width={24} />
            </div>
            <div className="flex flex-col flex-1">
              <p className="text-medium font-semibold">{title}</p>
              <p className="text-small text-default-400">{description}</p>
            </div>
          </div>

          <Input
            placeholder="Enter referral code (optional)"
            label="Referral Code"
            labelPlacement="outside"
            className="w-full"
            value={refCode}
            onChange={(e) => setRefCode(e.target.value)}
          />

          <Button
            onClick={handleConnectClick}
            className="w-full bg-gradient-to-r from-blue-500 to-green-500 text-white"
            disabled={isConnected || isLoggingIn}
            isLoading={isLoggingIn}
            startContent={<Icon icon="mdi:wallet" />}
          >
            {isLoggingIn
              ? "Logging In..."
              : isConnected
                ? `Wallet connected: ${address.slice(0, 6)}...${address.slice(-4)}`
                : "Connect Wallet"}
          </Button>
        </CardBody>
      </Card>
    );
  },
);

WalletConnectFrontend.displayName = "WalletConnectFrontend";

export default WalletConnectFrontend;
