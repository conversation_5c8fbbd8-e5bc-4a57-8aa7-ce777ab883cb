'use client';

import TopBar from "@/components/top-bar";
import { TabItem } from "@/components/tab-bar";
import TabBar from "@/components/tab-bar";

export default function FrontendLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const tabItems: TabItem[] = [
    { key: "/", title: "Home", icon: "solar:home-outline" },
    { key: "/team", title: "Team", icon: "solar:user-outline" },
    { key: "/news", title: "News", icon: "solar:book-outline" },
    { key: "/contact", title: "Contact", icon: "solar:phone-outline" },
  ];
  return (
    <>
      <div className="bg-background max-w-sm mx-auto min-h-screen flex flex-col">
        <header className="fixed bg-background top-0 z-10 w-full max-w-sm">
          <TopBar logoText="World Mall" buttonText="0xc18e...bd0ef" />
        </header>

        <main
          className="flex-1 overflow-y-auto p-4"
          style={{
            height: 'calc(100vh - 106px)',
            marginTop: '50px',
            marginBottom: '56px',
          }}
        >
          {children}
        </main>

        <footer className="fixed bottom-0 z-10 w-full max-w-sm">
          <div className="py-0">
            <TabBar items={tabItems} />
          </div>
        </footer>
      </div>
    </>
  );
}