'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardBody, Input, Button, Textarea, tabs, Tab, Tabs } from '@heroui/react';
import { useRouter } from 'next/navigation';
import CardNewsItem from '@/components/card-news-item';
import { NewsItemProps } from '@/components/card-news-item';

export default function NewsSection() {

  const [newsItems] = useState<NewsItemProps[]>([
    {
      imageSrc: "/news/news1.jpeg",
      title: "WORLDMALL Talk Titans Contest",
      description: "Talk, Engage & Win Big!<br>Rewards Pool: 100$ in USDT for top 10 dynamic Chatter <br>Duration: 7 March - 21 March, 2025",
      date: "05/03/2025",
      category: "News",
      type: "image",
      videoSrc: "",
    },
    {
      imageSrc: "/news/news6.jpeg",
      title: "Boost Your Earnings, Power the Web3 E-Commerce",
      description: "Discover WORLD MALL’s High-Yield Returns on BSC Platform",
      date: "06/03/2025",
      category: "News",
      type: "image",
      videoSrc: "",
    },
    {
      imageSrc: "/news/news4.jpeg",
      title: "The Pros of top-tier Web3 E-commerce - WORLDMALL",
      description: "Download Worldmall Social Network App Right now!",
      date: "07/03/2025",
      category: "News",
      type: "image",
      videoSrc: "",
    },
    {
      imageSrc: "/news/news3.jpeg",
      title: "World Mall Longterm Interest Cycle Introduction",
      description: "Innovative E-commerce Platform <br> Huge users data <br>Easy to use",
      date: "08/03/2025",
      category: "News", 
      type: "image",
      videoSrc: "",
    },
    {
      imageSrc: "/news/news2.jpeg",
      title: "World Mall Model: The Smart, Sustainable E-commerce Platform",
      description: "World Mall Model: The Smart, Sustainable E-commerce Platform",
      date: "09/03/2025",
      category: "News",
      type: "image",
      videoSrc: "",
    },
    {
      imageSrc: "/news/news5.jpeg",
      title: "Claim $WM Instantly on Airdrop",
      description: "Load Up Your Wallet Today!",
      date: "10/03/2025",
      category: "News",
      type: "image",
      videoSrc: "",
    },
  ]);

  const tabs = [
    { id: "news", label: "News" },
    { id: "announcement", label: "Announcement" },
    { id: "events", label: "Events" },
  ];

  return (
    <div className="flex max-w-sm flex-col gap-4">
        <h2 className="text-xl font-semibold">News</h2>

        {newsItems
          .filter((item) => item.category === "News")
          .map((item, index) => (
            <CardNewsItem
              key={index}
              imageSrc={item.imageSrc}
              title={item.title}
              description={item.description}
                    date={item.date}
                    category={item.category}
                    type={item.type}
                    videoSrc={item.videoSrc}
                  />
                ))}

      </div>
  );
}