'use client';

import React, { useState, useEffect } from 'react';
import { Accordion, AccordionItem, Avatar } from '@heroui/react';
import TeamMembers from './components/executive-team';
import ExecutiveTeam from './components/executive-team';

export type Job = {
  period: string; 
  role: string; 
};

export type ExecutiveMemberProps = {
  imageSrc: string; 
  name: string; 
  title: string; 
  jobs: Job[]; 
};

export default function TeamPage() {

  return (
    <div className="flex w-full flex-col gap-4">
      <ExecutiveTeam />
    </div>
  );
}