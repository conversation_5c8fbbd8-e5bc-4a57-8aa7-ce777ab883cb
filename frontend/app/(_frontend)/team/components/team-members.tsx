'use client';

import React, { useState, useEffect } from 'react';
import { Accordion, AccordionItem, Avatar } from '@heroui/react';

export type Job = {
    period: string; 
    role: string; 
};

export type ExecutiveMemberProps = {
    imageSrc: string; 
    name: string; 
    title: string; 
    jobs: Job[]; 
};

export default function TeamMembers({ members }: { members: ExecutiveMemberProps[] }) {
    return (
        <Accordion variant="shadow">
            {members.map((member, index) => (
                <AccordionItem
                    key={index}
                    aria-label={member.name}
                    startContent={
                        <Avatar
                            isBordered
                            radius="full"
                            size="lg"
                            color="success"
                            src={member.imageSrc}
                        />
                    }
                    subtitle={member.title}
                    title={member.name}
                >
                    <ul className="list-none space-y-2 text-small text-default-400">
                        {member.jobs.map((job: Job, index: number) => (
                            <li key={index} className="break-words text-left gap-2">
                                <p className="font-semibold leading-none text-default-600">{job.period}:</p>
                                <p className="tracking-tight text-default-400">{job.role}</p>
                            </li>
                        ))}
                    </ul>
                </AccordionItem>
            ))}
        </Accordion>
    );
}