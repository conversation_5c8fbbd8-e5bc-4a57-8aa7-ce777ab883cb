'use client';

import React, { useState, useEffect } from 'react';
import { Accordion, AccordionItem, Avatar } from '@heroui/react';
import TeamMembers, { ExecutiveMemberProps } from './team-members';

export default function ExecutiveTeam() {
    const [executiveMembers] = useState<ExecutiveMemberProps[]>([
        {
            imageSrc: "/team/ceo.jpg",
            name: "<PERSON>",
            title: "CEO World Mall",
            jobs: [
                { period: "2010 - 2015", role: "Technical Advisor Startupjob.com at Austin, Texas" },
                { period: "2015 - 2018", role: "CTO Transform JP Ventures at San Juan, Puerto Rico" },
                { period: "2018 - 2022", role: "CTO Sterling Faster Capital at Dubai, United Arab Emirates" },
                { period: "2022 - Now", role: "CEO at World Mall" },
            ],
        },
        {
            imageSrc: "/team/cfo.jpg",
            name: "<PERSON>",
            title: "CFO World Mall",
            jobs: [
                { period: "2008 - 2012", role: "Finance Manager, Investor Relations IDNEAUS Asset Trading at Switzerland Zug" },
                { period: "2012 - 2015", role: "Senior Finance Advisor HANUSH at United Kingdom, England" },
                { period: "2015 - 2022", role: "CFO LP Trading Trademakers at British Virgin Islands" },
                { period: "2022 - Now", role: "CFO at World Mall" },
            ],
        },
        {
            imageSrc: "/team/cmo.jpg",
            name: "Aaron Loeb",
            title: "CMO World Mall",
            jobs: [
                { period: "1999 - 2005", role: "Marketing Manager S&B Capital LLC at San Francisco Bay Area" },
                { period: "2006 - 2015", role: "CMO TechMul Inc at Los Angeles, California, United States" },
                { period: "2015 - 2022", role: "CMO Segur Signature at San Francisco, California, United States" },
                { period: "2022 - Now", role: "CMO at World Mall" },
            ],
        },
        {
            imageSrc: "/team/cio.jpg",
            name: "Katy Atkinson",
            title: "CIO World Mall",
            jobs: [
                { period: "2009 - 2012", role: "Senior QA Big Data & Analytics Blur Inc, San Francisco, California, United States" },
                { period: "2012 - 2015", role: "Technical Advisor Fidelseo Ponte Vedra, Florida, United States" },
                { period: "2015 - 2022", role: "CTO EthAnum Venture Partners at Singapore" },
                { period: "2022 - Now", role: "CIO at World Mall" },
            ],
        },
        {
            imageSrc: "/team/cto.jpg",
            name: "Noah Schumacher",
            title: "Tech Manager World Mall",
            jobs: [
                { period: "2013 - 2015", role: "Technical Manager Citroen Region at de Paris, France" },
                { period: "2015 - 2018", role: "IT Business Consultant OPAN Region de Paris, France" },
                { period: "2018 - 2022", role: "Blockchain Advisor CRED Blockchain Platform Vilnius" },
                { period: "2022 - Now", role: "Tech Manager at World Mall" },
            ],
        },
        {
            imageSrc: "/team/mm.jpg",
            name: "Mary Loeb",
            title: "Marketing Manager World Mall",
            jobs: [
                { period: "2009 - 2011", role: "Marketing Manager PTA Region de Paris, France" },
                { period: "2011 - 2015", role: "Senior Marketing Advisor Fidelseo Ponte Vedra, Florida, United States" },
                { period: "2015 - 2022", role: "CMO EthAnum Venture Partners at Singapore" },
                { period: "2022 - Now", role: "Marketing Manager at World Mall" },
            ],
        },
    ]);

    return (
        <div className="flex w-full flex-col gap-4">
            <h2 className="text-xl font-semibold">Executive Team</h2>
            <TeamMembers members={executiveMembers} />
        </div>
    );
}