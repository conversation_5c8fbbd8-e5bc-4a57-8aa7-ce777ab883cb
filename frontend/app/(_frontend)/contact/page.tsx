'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardBody, Input, Button, Textarea } from '@heroui/react';
import { useRouter } from 'next/navigation';

export default function ContactPage() {
  const router = useRouter();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async () => {
  };

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex max-w-sm flex-col gap-4">
        <h2 className="text-lg font-semibold">Contact</h2>

        <Card shadow="sm" className="border-none">
          <CardBody className="p-4 flex flex-col gap-4">
            <div className="flex flex-col gap-4">
              <Input
                label="Name"
                labelPlacement="outside"
                placeholder="Enter your name"
                value={formData.name}
                name="name"
                onChange={handleInputChange}
                isRequired
                classNames={{
                  label: "text-gray-600",
                }}
              />
              <Input
                label="Phone"
                labelPlacement="outside"
                placeholder="Enter your phone number"
                value={formData.phone}
                name="phone"
                onChange={handleInputChange}
                isRequired
                classNames={{
                  label: "text-gray-600",
                }}
              />
              <Input
                label="Email"
                labelPlacement="outside"
                placeholder="Enter your email"
                value={formData.email}
                name="email"
                onChange={handleInputChange}
                isRequired
                classNames={{
                  label: "text-gray-600",
                }}
              />
              <Input
                label="Subject"
                labelPlacement="outside"
                placeholder="Enter message subject"
                value={formData.subject}
                name="subject"
                onChange={handleInputChange}
                isRequired
                classNames={{
                  label: "text-gray-600",
                }}
              />
              <Textarea
                label="Your message"
                labelPlacement="outside"
                placeholder="Type your message here..."
                value={formData.message}
                name="message"
                onChange={handleInputChange}
                isRequired
                minRows={4}
                classNames={{
                  label: "text-gray-600",
                }}
              />
            </div>

            <Button
              className="w-full bg-gradient-to-r from-blue-500 to-green-500 text-white"
              onPress={handleSubmit}
            >
              Send Message
            </Button>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}