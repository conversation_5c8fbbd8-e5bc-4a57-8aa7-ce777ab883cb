'use client';

import React, { useEffect } from 'react';
import { cn } from '@heroui/react';
import { Button } from '@heroui/react';
import { useAccountModal } from '@rainbow-me/rainbowkit';
import { Icon } from '@iconify/react/dist/iconify.js';
import { useDisconnect, useAccount } from 'wagmi';
import { useUserStore } from '@/store/userStore';
import { useRouter } from 'next/navigation';

export type TopBarProps = React.HTMLAttributes<HTMLDivElement> & {
  logoText?: string; 
  buttonText?: string;
  onButtonClick?: () => void;
};

const TopBar = React.forwardRef<HTMLDivElement, TopBarProps>(
  ({ logoText = "WORLD MALL", buttonText = "Connect Wallet", onButtonClick, className, ...props }, ref) => {
    const { accessToken } = useUserStore();
    const { openAccountModal } = useAccountModal();
    const { isConnected } = useAccount();
    const { logout } = useUserStore();
    const router = useRouter();

    useEffect(() => {
      if (!isConnected) {
        logout();
        router.replace('/');
      }
    }, [isConnected, logout, router]);

    return (
      <div
        ref={ref}
        className={cn(
          "flex items-center justify-between w-full shadow-sm py-3 px-4",
          className
        )}
        {...props}
      >
        <div className="text-lg font-bold text-primary flex items-center">
          <img src="/logo.png" alt="world-mall" style={{ height: '30px' }} />
        </div>
        {openAccountModal && (
          <div className="flex items-center gap-2">
            <Button onClick={openAccountModal} isIconOnly>
              <Icon icon="mdi:wallet" />
            </Button>
          </div>
        )}
      </div>
    );
  }
);

TopBar.displayName = "TopBar";

export default TopBar;