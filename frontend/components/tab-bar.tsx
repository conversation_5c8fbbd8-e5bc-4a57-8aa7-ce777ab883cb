"use client";

import React from "react";
import { cn } from "@heroui/react";
import { Icon } from "@iconify/react";
import { usePathname, useRouter } from "next/navigation";

export type TabItem = {
  key: string;
  title: string;
  icon: string;
  badge?: number;
};

export type TabBarProps = React.HTMLAttributes<HTMLDivElement> & {
  items: TabItem[];
  activeKey?: string;
  onChange?: (key: string) => void;
  fixed?: boolean;
};

const TabBarItem = ({
  item,
  active,
  onClick,
}: {
  item: TabItem;
  active: boolean;
  onClick: () => void;
}) => {
  return (
    <div
      className={cn(
        "flex flex-1 flex-col items-center justify-center py-2 cursor-pointer",
        active ? "text-primary" : "text-default-500"
      )}
      onClick={onClick}
    >
      <div className="relative">
        <Icon
          icon={item.icon}
          width={24}
          className={cn(
            "transition-all duration-200",
            active
              ? "bg-gradient-to-r from-blue-500 to-green-500 text-white rounded-full p-1"
              : "text-default-500"
          )}
        />
        {item.badge && item.badge > 0 && (
          <div className="absolute -top-1 -right-1 flex h-4 min-w-4 items-center justify-center rounded-full bg-danger px-1 text-tiny text-white">
            {item.badge > 99 ? "99+" : item.badge}
          </div>
        )}
      </div>
      <span className="mt-1 text-tiny">{item.title}</span>
    </div>
  );
};

const TabBar = React.forwardRef<HTMLDivElement, TabBarProps>(
  ({ items, activeKey, onChange, fixed = true, className, ...props }, ref) => {
    const router = useRouter();
    const pathname = usePathname();
    
    const currentKey = activeKey || pathname;
    
    const handleChange = (key: string) => {
      if (onChange) {
        onChange(key);
      } else {
        router.push(key);
      }
    };

    return (
      <div
        ref={ref}
        className={cn(
          "flex w-full bg-background text-center shadow-sm border-t",
          className
        )}
        {...props}
      >
        {items.map((item) => (
          <TabBarItem
            key={item.key}
            item={item}
            active={currentKey === item.key}
            onClick={() => handleChange(item.key)}
          />
        ))}
      </div>
    );
  }
);

TabBar.displayName = "TabBar";

export default TabBar;
const ExampleUsage = () => {
  const tabItems: TabItem[] = [
    { key: "/wallet", title: "Wallet", icon: "mdi:wallet" },
    { key: "/comming-soon/nfts", title: "My NFT", icon: "mdi:cube-outline" },
    { key: "/flash-buy", title: "Flash Buy", icon: "mdi:flash" },
    { key: "/affiliate", title: "Affiliate", icon: "mdi:account-group" },
    { key: "/account", title: "Account", icon: "mdi:account" },
  ];

  return <TabBar items={tabItems} />;
};

export { ExampleUsage };