"use client";

import type { CardProps } from "@heroui/react";
import React, { useState, useEffect } from "react";
import { Card, CardBody, Button, Input, Select, SelectItem } from "@heroui/react";
import { Icon } from "@iconify/react";
import { cn } from "@heroui/react";
import { ethers } from "ethers";
import { useTranslation } from "@/hooks/useTranslation";

export type TransferCardProps = CardProps & {
  icon?: string;
  title?: string;
  apiEndpoint: string;
  usdtContractAddress: string;
  provider?: ethers.BrowserProvider;
  onTransferSuccess?: (txHash: string) => void;
  color?: "primary" | "success" | "warning" | "danger";
};

const ERC20_ABI = [
  "function transfer(address to, uint256 amount) public returns (bool)",
  "function decimals() public view returns (uint8)",
  "function balanceOf(address account) public view returns (uint256)",
];

const SUPPORTED_NETWORKS = [
  { id: 1, name: "Ethereum Mainnet" },
  { id: 56, name: "Binance Smart Chain" },
  { id: 137, name: "Polygon" },
  { id: 43114, name: "Avalanche C-Chain" },
];

const TransferCard = React.forwardRef<HTMLDivElement, TransferCardProps>(
  (
    {
      icon = "mdi:wallet",
      title = "Transfer USDT",
      apiEndpoint,
      usdtContractAddress,
      provider: externalProvider,
      onTransferSuccess,
      color,
      className,
      ...props
    },
    ref
  ) => {
    const { t } = useTranslation();

    const [amount, setAmount] = useState<string>("");
    const [toAddress, setToAddress] = useState<string>("");
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [walletConnected, setWalletConnected] = useState<boolean>(false);
    const [internalProvider, setInternalProvider] = useState<ethers.BrowserProvider | null>(null);
    const [currentNetworkId, setCurrentNetworkId] = useState<number | null>(null);
    const [selectedNetworkId, setSelectedNetworkId] = useState<string>("");

    const provider = externalProvider || internalProvider;

    useEffect(() => {
      const connectWallet = async () => {
        let web3Provider: ethers.BrowserProvider;

        if (externalProvider) {
          web3Provider = externalProvider;
        } else if (window.ethereum) {
          web3Provider = new ethers.BrowserProvider(window.ethereum);
          try {
            await web3Provider.send("eth_requestAccounts", []);
            setInternalProvider(web3Provider);
          } catch (err) {
            setError(t('transfer.connect_wallet_failed'));
            return;
          }
        } else {
          setError(t('transfer.install_wallet'));
          return;
        }

        try {
          const network = await web3Provider.getNetwork();
          const chainId = Number(network.chainId);
          setCurrentNetworkId(chainId);
          setWalletConnected(true);

          if (SUPPORTED_NETWORKS.some((net) => net.id === chainId)) {
            setSelectedNetworkId(chainId.toString());
          }
        } catch (err) {
          setError(t('transfer.check_connection_error'));
        }
      };

      connectWallet();
    }, [externalProvider]);

    useEffect(() => {
      const fetchWalletAddress = async () => {
        setLoading(true);
        try {
          const response = await fetch(apiEndpoint);
          const data = await response.json();
          if (data.address) {
            setToAddress(data.address);
          } else {
            setError(t('transfer.fetch_address_failed'));
          }
        } catch (err) {
          setError(t('transfer.fetch_address_error'));
        } finally {
          setLoading(false);
        }
      };

      fetchWalletAddress();
    }, [apiEndpoint]);

    const handleNetworkChange = async (value: string) => {
      const newNetworkId = Number(value);
      setSelectedNetworkId(value);

      if (provider && currentNetworkId !== newNetworkId) {
        if (typeof window !== "undefined" && window.ethereum) { 
          try {
            await window.ethereum.request({
              method: "wallet_switchEthereumChain",
              params: [{ chainId: `0x${newNetworkId.toString(16)}` }],
            });
            setCurrentNetworkId(newNetworkId);
            setError(null);
          } catch (switchError: any) {
            if (switchError.code === 4902) {
              setError(t('transfer.network_not_added'));
            } else {
              setError(t('transfer.switch_network_failed'));
            }
          }
        } else {
          setError(t('transfer.wallet_not_available'));
        }
      }
    };

    const colors = React.useMemo(() => {
      switch (color) {
        case "primary":
          return {
            card: "border-primary-200",
            iconWrapper: "bg-primary-50 border-primary-100",
            icon: "text-primary",
            button: "bg-primary text-white",
          };
        case "success":
          return {
            card: "border-success-200",
            iconWrapper: "bg-success-50 border-success-100",
            icon: "text-success",
            button: "bg-success text-white",
          };
        case "warning":
          return {
            card: "border-warning-200",
            iconWrapper: "bg-warning-50 border-warning-100",
            icon: "text-warning-600",
            button: "bg-warning-600 text-white",
          };
        case "danger":
          return {
            card: "border-danger-200",
            iconWrapper: "bg-danger-50 border-danger-100",
            icon: "text-danger",
            button: "bg-danger text-white",
          };
        default:
          return {
            card: "border-blue-200",
            iconWrapper: "bg-gradient-to-r from-blue-500 to-green-500 border-blue-200",
            icon: "text-white",
            button: "bg-gradient-to-r from-blue-500 to-green-500 text-white",
          };
      }
    }, [color]);

    const handleTransfer = async () => {
      if (!amount || !toAddress || !provider || !selectedNetworkId || currentNetworkId !== Number(selectedNetworkId)) {
        setError(t('transfer.validation_error'));
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const signer = await provider.getSigner();
        const usdtContract = new ethers.Contract(usdtContractAddress, ERC20_ABI, signer);

        const decimals = await usdtContract.decimals();
        const amountInWei = ethers.parseUnits(amount, decimals);

        const tx = await usdtContract.transfer(toAddress, amountInWei);
        await tx.wait();

        setAmount("");
        if (onTransferSuccess) {
          onTransferSuccess(tx.hash);
        }
      } catch (err: any) {
        setError(t('transfer.transfer_failed', { error: err.message }));
      } finally {
        setLoading(false);
      }
    };

    return (
      <Card
        ref={ref}
        isPressable
        className={cn("border-small", colors?.card, className)}
        shadow="sm"
        {...props}
      >
        <CardBody className="flex h-full flex-col gap-4 p-4">
          <div className="flex flex-row items-center gap-3">
            <div
              className={cn(
                "item-center flex rounded-medium border p-2",
                colors?.iconWrapper
              )}
            >
              <Icon className={colors?.icon} icon={icon} width={24} />
            </div>
            <p className="text-medium font-semibold">{t('transfer.title')}</p>
          </div>

          <div className="flex flex-col gap-1">
            <p className="text-small text-default-400">{t('transfer.wallet_status')}:</p>
            <p className="text-small">
              {walletConnected ? t('transfer.connected') : t('transfer.not_connected')}
            </p>
          </div>

          <Select
            label={t('transfer.network')}
            placeholder={t('transfer.select_network')}
            value={selectedNetworkId}
            onChange={(e) => handleNetworkChange(e.target.value)}
            className="w-full"
            isDisabled={!walletConnected}
          >
            {SUPPORTED_NETWORKS.map((network) => (
              <SelectItem key={network.id}>
                {network.name}
              </SelectItem>
            ))}
          </Select>

          <div className="flex flex-col gap-1">
            <p className="text-small text-default-400">{t('transfer.to_address')}:</p>
            <p className="text-small font-mono break-all">
              {loading ? t('common.loading') : toAddress || t('transfer.no_address')}
            </p>
            {error && <p className="text-small text-danger">{error}</p>}
          </div>

          <Input
            label={t('transfer.amount_usdt')}
            placeholder={t('transfer.enter_amount')}
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            type="number"
            min="0"
            step="0.01"
            className="w-full"
            isDisabled={!walletConnected || !selectedNetworkId}
          />

          <Button
            className={cn("w-full", colors?.button)}
            onClick={handleTransfer}
            isDisabled={
              loading ||
              !toAddress ||
              !walletConnected ||
              !selectedNetworkId ||
              currentNetworkId !== Number(selectedNetworkId)
            }
            isLoading={loading}
          >
            {t('transfer.transfer_usdt')}
          </Button>
        </CardBody>
      </Card>
    );
  }
);

TransferCard.displayName = "TransferCard";

export default TransferCard;