import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalBody,
  ModalFooter,
  Button,
  Link,
  Tooltip,
  Avatar,
  useToast,
  addToast,
  user,
  Chip,
} from "@heroui/react";
import { TreeNodeData } from '@/app/(_authenticated)/affiliate/types/type';
import { UserRank, WManagerRank } from '@/types/models';
import moment from "moment-timezone";
import { useTranslation } from '@/hooks/useTranslation';

interface UserModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  userData: TreeNodeData;
}

const UserModal: React.FC<UserModalProps> = ({ isOpen, onOpenChange, userData }) => {
  const { t } = useTranslation();

  const getUserRank = (userData: TreeNodeData) => {
    if (userData.wManagerRank === WManagerRank.WM1) {
      return '/levels/w-1.png';
    }
    if (userData.wManagerRank === WManagerRank.WM2) {
      return '/levels/w-2.png';
    }
    if (userData.wManagerRank === WManagerRank.WM3) {
      return '/levels/w-3.png';
    }
    if (userData.wManagerRank === WManagerRank.WM4) {
      return '/levels/w-4.png';
    }
    if (userData.wManagerRank === WManagerRank.WM5) {
      return '/levels/w-5.png';
    }
    return '/levels/w-0.png';
  }

  const shortenAddress = (address: string): string => {
    if (!address || address.length < 10) return address;
    return `${address.substring(0, 6)}...${address.substring(address.length - 6)}`;
  };

  const formatDate = (date?: Date | string) => {
    if (!date) return 'N/A';
    return moment(date).tz('Asia/Dubai').format('DD/MM/YYYY HH:mm:ss');
  };

  const handleCopyAddress = () => {
    if (userData.walletAddress) {
      navigator.clipboard.writeText(userData.walletAddress)
        .then(() => {
          addToast({
            title: t('affiliate.address_copied'),
            description: t('affiliate.address_copied_description', { address: shortenAddress(userData.walletAddress) }),
            color: "success",
          });
        })
        .catch((error) => {
          addToast({
            title: t('affiliate.copy_failed'),
            description: t('affiliate.copy_failed_description'),
            color: "danger",
          });
        });
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      backdrop="transparent"
      placement="center"
      classNames={{
        base: "max-w-[360px] mx-auto transform -translate-x-2",
      }}
      motionProps={{
        variants: {
          enter: {
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.3,
              ease: "easeOut",
            },
          },
          exit: {
            y: 20,
            opacity: 0,
            transition: {
              duration: 0.2,
              ease: "easeIn",
            },
          },
        }
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-row items-center gap-2 border-b border-default-200/50">
              <Avatar
                isBordered
                color={userData.firstDepositTime ? "success" : "default"}
                className="w-10 h-10"
                src={userData.avatarUrl || getUserRank(userData)}
                name={userData.name || userData.username || shortenAddress(userData.walletAddress)}
              />
              <div className="flex flex-col">
                <h3 className="text-medium font-semibold">
                  {userData.name || userData.username || shortenAddress(userData.walletAddress)}
                </h3>
                <p className="text-small text-default-500">
                  {shortenAddress(userData.walletAddress)}
                </p>
              </div>
            </ModalHeader>
            <ModalBody>
              <div className="flex flex-col gap-2 py-2">
                {/* <div className="flex justify-between items-center border-b border-default-200/50 pb-2">
                  <span className="text-sm text-default-500">Level</span>
                  <span className="font-medium">{userData.level}</span>
                </div> */}
                <div className="flex justify-between items-center border-b border-default-200/50 pb-2">
                  <span className="text-sm text-default-500">{t('affiliate.status')}</span>
                  <span className="font-medium" style={{ color: userData.firstDepositTime ? '#4CAF50' : '#999' }}>
                    {userData.firstDepositTime ? <Chip color="success" variant="flat" size="sm">{t('affiliate.active')}</Chip> : <Chip color="danger" variant="flat" size="sm">{t('affiliate.pending')}</Chip>}
                  </span>
                </div>
                <div className="flex justify-between items-center border-b border-default-200/50 pb-2">
                  <span className="text-sm text-default-500">{t('affiliate.joined')}</span>
                  <span className="font-medium text-sm">{formatDate(userData.joinedAt)}</span>
                </div>

                <div className="flex justify-between items-center border-b border-default-200/50 pb-2">
                  <span className="text-sm text-default-500">{t('affiliate.direct_referrals')}</span>
                  <span className="font-medium">{userData.directReferrals || 0}</span>
                </div>

                <div className="flex justify-between items-center border-b border-default-200/50 pb-2">
                  <span className="text-sm text-default-500">{t('affiliate.total_referrals')}</span>
                  <span className="font-medium">{userData.totalReferrals || 0}</span>
                </div>


                <div className="flex justify-between items-center border-b border-default-200/50 pb-2">
                  <span className="text-sm text-default-500">{t('affiliate.total_volume')}</span>
                  <span className="font-medium">{userData.totalVolume || 0}</span>
                </div>


                <div className="flex justify-between items-center border-b border-default-200/50 pb-2">
                  <span className="text-sm text-default-500">{t('affiliate.total_earnings')}</span>
                  <span className="font-medium">{userData.totalEarnings || 0}</span>
                </div>

                <div className="flex justify-between items-center border-b border-default-200/50 pb-2">
                  <span className="text-sm text-default-500">{t('affiliate.volume_day')}</span>
                  <span className="font-medium">{userData.totalVolumePerDay || 0}</span>
                </div>

                <div className="flex justify-between items-center border-b border-default-200/50 pb-2">
                  <span className="text-sm text-default-500">{t('affiliate.earnings_day')}</span>
                  <span className="font-medium">{userData.totalEarningsPerDay || 0}</span>
                </div>

                <div className="flex justify-between items-center border-b border-default-200/50 pb-2">
                  <span className="text-sm text-default-500">{t('affiliate.volume_session')}</span>
                  <span className="font-medium">{userData.totalVolumeSession || 0}</span>
                </div>

                <div className="flex justify-between items-center border-b border-default-200/50 pb-2">
                  <span className="text-sm text-default-500">{t('affiliate.earnings_session')}</span>
                  <span className="font-medium">{userData.totalEarningsSession || 0}</span>
                </div>
              </div>
            </ModalBody>
            <ModalFooter className="flex justify-between">
              <Button
                className="w-full bg-gradient-to-r from-blue-500 to-green-500 text-white"
                onPress={handleCopyAddress}
                startContent={
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 5H6C4.89543 5 4 5.89543 4 7V19C4 20.1046 4.89543 21 6 21H16C17.1046 21 18 20.1046 18 19V17M8 5C8 6.10457 8.89543 7 10 7H12C13.1046 7 14 6.10457 14 5M8 5C8 3.89543 8.89543 3 10 3H12C13.1046 3 14 3.89543 14 5M14 5H16C17.1046 5 18 5.89543 18 7V10M20 14H10M10 14L13 11M10 14L13 17"
                      stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                }
              >
                {t('affiliate.copy_address')}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

export default UserModal;