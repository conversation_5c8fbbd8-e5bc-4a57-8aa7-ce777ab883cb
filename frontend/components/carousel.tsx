'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardBody, Button } from '@heroui/react';
import { Icon } from '@iconify/react';

interface CarouselProps {
  items: any[];
  renderItem: (item: any, index: number) => React.ReactNode;
  hideButton?: boolean;
}

const Carousel: React.FC<CarouselProps> = ({ items, renderItem, hideButton = false }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % items.length);
  };

  const handlePrev = () => {
    setCurrentIndex((prev) => (prev - 1 + items.length) % items.length);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      handleNext();
    }, 3000);
    return () => clearInterval(interval);
  }, [items.length]);

  return (
    <div className="relative w-full max-w-sm mx-auto">
      <div className="overflow-hidden">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {items.map((item, index) => (
            <div key={index} className="w-full flex-shrink-0">
              {renderItem(item, index)}
            </div>
          ))}
        </div>
      </div>
      {!hideButton && <div className="flex justify-between mt-4">
        <Button
          variant="flat"
          onClick={handlePrev}
          disabled={items.length <= 1}
          className="text-gray-500 hover:text-gray-700"
        >
          <Icon icon="solar:arrow-left-linear" width={20} />
        </Button>
        <Button
          variant="flat"
          onClick={handleNext}
          disabled={items.length <= 1}
          className="text-gray-500 hover:text-gray-700"
        >
          <Icon icon="solar:arrow-right-linear" width={20} />
          </Button>
        </div>}
    </div>
  );
};

export default Carousel;