'use client';

import React from 'react';
import { Card, CardBody, Progress } from '@heroui/react';
import { Icon } from '@iconify/react';
import { cn } from '@heroui/react';

export type CardCheckableProps = {
  icon: string;
  title: string;
  color?: 'primary' | 'secondary' | 'warning' | 'danger';
  isCompleted?: boolean;
  onPress?: () => void;
};

const CardCheckable = React.forwardRef<HTMLDivElement, CardCheckableProps>(
  ({ icon, title, color = 'warning', isCompleted = false, onPress }, ref) => {
    const colors = React.useMemo(() => {
      switch (color) {
        case 'primary':
          return {
            card: 'border-default-200',
            iconWrapper: 'bg-gradient-to-r from-blue-500 to-green-500',
            icon: 'text-white',
            check: 'text-primary',
            iconCheckWrapper: 'bg-primary/10',
          };
        case 'secondary':
          return {
            card: 'border-secondary-100',
            iconWrapper: 'bg-gradient-to-r from-purple-500 to-pink-500',
            icon: 'text-white',
            check: 'text-secondary',
            iconCheckWrapper: 'bg-secondary/10',
          };
        case 'warning':
          return {
            card: 'border-warning-200',
            iconWrapper: 'bg-gradient-to-r from-yellow-500 to-orange-500',
            icon: 'text-white',
            check: 'text-warning',
            iconCheckWrapper: 'bg-warning/10',
          };
        case 'danger':
          return {
            card: 'border-danger-200',
            iconWrapper: 'bg-gradient-to-r from-red-500 to-pink-500',
            icon: 'text-white',
            check: 'text-danger',
            iconCheckWrapper: 'bg-danger/10',
          };
        default:
          return {
            card: 'border-default-200',
            iconWrapper: 'bg-gradient-to-r from-gray-500 to-gray-700',
            icon: 'text-white',
            check: 'text-default-500',
            iconCheckWrapper: 'bg-default/10',
          };
      }
    }, [color]);

    return (
      <Card
        ref={ref}
        isPressable
        className={cn('border-small', colors?.card)}
        shadow="sm"
        onPress={onPress}
      >
        <CardBody className="flex h-full flex-row items-center gap-3 p-2">
          <div className={cn('flex items-center rounded-medium border p-2', colors?.iconWrapper)}>
            <Icon className={colors?.icon} icon={icon} width={14} />
          </div>
          <div className="flex-1">
            <p className="text-sm">{title}</p>
          </div>
          {isCompleted && (
            <div className={cn('flex items-center justify-center rounded-full p-1', colors?.iconCheckWrapper)}>
              <Icon className={colors?.check} icon="solar:check-circle-linear" width={14} />
            </div>
          )}
        </CardBody>
      </Card>
    );
  },
);

CardCheckable.displayName = 'CardCheckable';

export default CardCheckable;
