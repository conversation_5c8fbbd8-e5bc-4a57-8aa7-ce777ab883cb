"use client";

import type { CardProps } from "@heroui/react";

import React from "react";
import { <PERSON>ton, Card, CardBody, Image, } from "@heroui/react";
import { Icon } from "@iconify/react";

export type NewsItemProps = CardProps & {
  imageSrc: string;
  title: string;
  description: string;
  date: string;
  category: string;
  type: string;
  videoSrc: string;
};

export default function CardNewsItem({ imageSrc, title, description, date, category, type, videoSrc, ...props }: NewsItemProps) {
  return (
    <Card className="w-full max-w-sm overflow-hidden rounded-lg" {...props}>
      <CardBody className="flex flex-col flex-wrap p-0 sm:flex-nowrap">
        {type === "image" && (
          <Image
            removeWrapper
            alt={title}
            className="h-auto w-full flex-none object-cover object-top px-2 py-2"
            src={imageSrc}
          />
        )}
        {type === "video" && (
          <video
            src={videoSrc}
            className="h-auto w-full flex-none object-cover object-top px-2 py-2"
            autoPlay
            muted
            loop
          />
        )}

        <div className="px-4 py-4 flex-1">
          <h3 className="text-sm font-semibold line-clamp-2">{title}</h3>
          <div className="flex flex-col gap-2 pt-2 text-small text-default-400">
            <p className="text-small line-clamp-2" dangerouslySetInnerHTML={{ __html: description }} />
            <p className="text-tiny opacity-80">{date}</p>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}