'use client';

import type { ButtonProps } from '@heroui/react';

import React from 'react';
import { Button } from '@heroui/react';
import { Icon } from '@iconify/react';
import { cn } from '@heroui/react';

export type ButtonActionProps = ButtonProps & {
  icon: string;
  title: string;
  color?: 'primary' | 'secondary' | 'warning' | 'danger';
};

const ButtonAction = React.forwardRef<HTMLButtonElement, ButtonActionProps>(
  ({ color, title, icon, className, ...props }, ref) => {
    const colors = React.useMemo(() => {
      switch (color) {
        case 'primary':
          return {
            button: 'bg-primary text-primary-foreground',
            icon: 'text-primary-foreground',
          };
        case 'secondary':
          return {
            button: 'bg-secondary text-secondary-foreground',
            icon: 'text-secondary-foreground',
          };
        case 'warning':
          return {
            button: 'bg-warning text-warning-foreground',
            icon: 'text-warning-foreground',
          };
        case 'danger':
          return {
            button: 'bg-danger text-danger-foreground',
            icon: 'text-danger-foreground',
          };
        default:
          return {
            button: 'bg-default text-default-foreground',
            icon: 'text-default-foreground',
          };
      }
    }, [color]);

    return (
      <Button
        ref={ref}
        className={cn(colors?.button, className)}
        startContent={<Icon className={colors?.icon} icon={icon} width={24} />}
        {...props}
      >
        {title}
      </Button>
    );
  },
);

ButtonAction.displayName = 'ButtonAction';

export default ButtonAction;