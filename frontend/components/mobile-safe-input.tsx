'use client';

import React, { useRef, useEffect } from 'react';
import { Input, InputProps } from '@heroui/react';
import { cn } from '@heroui/react';

interface MobileSafeInputProps extends InputProps {
  androidOptimized?: boolean;
}

const MobileSafeInput = React.forwardRef<HTMLInputElement, MobileSafeInputProps>(
  ({ androidOptimized = true, classNames, className, ...props }, ref) => {
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      if (!androidOptimized) return;

      const container = containerRef.current;
      if (!container) return;

      const handleTouchStart = (e: TouchEvent) => {
        e.stopPropagation();
      };

      container.addEventListener('touchstart', handleTouchStart, { passive: true });

      return () => {
        container.removeEventListener('touchstart', handleTouchStart);
      };
    }, [androidOptimized]);

    const optimizedClassNames = {
      ...classNames,
      base: cn(
        "relative",
        classNames?.base
      ),
      inputWrapper: cn(
        "mobile-input-wrapper",
        "relative z-30",
        "touch-manipulation",
        "transform-gpu",
        classNames?.inputWrapper
      ),
      input: cn(
        "relative z-40",
        "autocomplete-off",
        classNames?.input
      ),
    };

    return (
      <div
        ref={containerRef}
        className={cn(
          "mobile-input-fix",
          "isolation-isolate",
          className
        )}
        style={{
          isolation: 'isolate',
          transform: 'translateZ(0)',
        }}
      >
        <Input
          ref={ref}
          {...props}
          classNames={optimizedClassNames}
          autoComplete="off"
          autoCorrect="off"
          autoCapitalize="off"
          spellCheck="false"
        />
      </div>
    );
  }
);

MobileSafeInput.displayName = "MobileSafeInput";

export default MobileSafeInput;
