import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  Select,
  SelectItem,
  ButtonGroup,
  useDisclosure,
} from "@heroui/react";
import { Icon } from "@iconify/react";

interface CryptoBalance {
  symbol: string;
  balance: string;
  icon: string;
}

interface WithdrawalModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const cryptoBalances: CryptoBalance[] = [
  { symbol: "ETH", balance: "0.5", icon: "cryptocurrency:eth" },
  { symbol: "BTC", balance: "0.025", icon: "cryptocurrency:btc" },
  { symbol: "USDT", balance: "1000", icon: "cryptocurrency:usdt" },
];

export const WithdrawalModal: React.FC<WithdrawalModalProps> = ({ isOpen, onClose }) => {
  const [selectedCrypto, setSelectedCrypto] = React.useState("");
  const [amount, setAmount] = React.useState("");
  const [address, setAddress] = React.useState("");
  const [withdrawalType, setWithdrawalType] = React.useState("crypto");
  const [processing, setProcessing] = React.useState(false);
  const [success, setSuccess] = React.useState(false);
  const [verificationCode, setVerificationCode] = React.useState("");

  const handleCryptoChange = (value: string) => {
    setSelectedCrypto(value);
    setAmount("");
  };

  const handleAmountPercentage = (percentage: number) => {
    const selected = cryptoBalances.find(crypto => crypto.symbol === selectedCrypto);
    if (selected) {
      const maxAmount = parseFloat(selected.balance);
      const calculatedAmount = (maxAmount * percentage / 100).toFixed(6);
      setAmount(calculatedAmount);
    }
  };

  const handleMaxAmount = () => {
    const selected = cryptoBalances.find(crypto => crypto.symbol === selectedCrypto);
    if (selected) {
      setAmount(selected.balance);
    }
  };

  const handleWithdraw = () => {
    setProcessing(true);
    setTimeout(() => {
      setProcessing(false);
      setSuccess(true);
    }, 2000);
  };

  const getFee = () => {
    return selectedCrypto === "ETH" ? "0.002" : "0.0001";
  };

  const getActualAmount = () => {
    const inputAmount = parseFloat(amount) || 0;
    const fee = parseFloat(getFee());
    return Math.max(0, inputAmount - fee).toFixed(6);
  };

  const resetModal = () => {
    setSelectedCrypto("");
    setAmount("");
    setAddress("");
    setVerificationCode("");
    setProcessing(false);
    setSuccess(false);
    onClose();
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={resetModal}
      size="sm"
      classNames={{
        base: "max-w-sm",
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-xl font-bold">Rút tiền từ ví</h2>
            </ModalHeader>
            <ModalBody>
              {!success ? (
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Chọn loại tiền
                    </label>
                    <Select
                      placeholder="Chọn loại tiền"
                      selectedKeys={selectedCrypto ? [selectedCrypto] : []}
                      onChange={(e) => handleCryptoChange(e.target.value)}
                    >
                      {cryptoBalances.map((crypto) => (
                        <SelectItem
                          key={crypto.symbol}
                          startContent={<Icon icon={crypto.icon} width={20} />}
                          description={`Số dư: ${crypto.balance} ${crypto.symbol}`}
                        >
                          {crypto.symbol}
                        </SelectItem>
                      ))}
                    </Select>
                  </div>

                  <div>
                    <div className="flex flex-col gap-2">
                      <div className="flex justify-between items-center">
                        <label className="block text-sm font-medium">
                          Số lượng
                        </label>
                      </div>
                      <Input
                        type="number"
                        placeholder="Nhập số lượng"
                        value={amount}
                        onValueChange={setAmount}
                        isDisabled={!selectedCrypto}
                      />
                      <ButtonGroup size="sm" variant="flat" className="w-full">
                        <Button
                          className="flex-1"
                          onPress={() => handleAmountPercentage(25)}
                          isDisabled={!selectedCrypto}
                        >
                          25%
                        </Button>
                        <Button
                          className="flex-1"
                          onPress={() => handleAmountPercentage(50)}
                          isDisabled={!selectedCrypto}
                        >
                          50%
                        </Button>
                        <Button
                          className="flex-1"
                          onPress={() => handleAmountPercentage(75)}
                          isDisabled={!selectedCrypto}
                        >
                          75%
                        </Button>
                        <Button
                          className="flex-1"
                          onPress={handleMaxAmount}
                          isDisabled={!selectedCrypto}
                        >
                          Max
                        </Button>
                      </ButtonGroup>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Địa chỉ đích
                    </label>
                    <Input
                      placeholder="Nhập địa chỉ ví"
                      value={address}
                      onValueChange={setAddress}
                      endContent={
                        <Button
                          isIconOnly
                          variant="light"
                          onPress={() => {}}
                        >
                          <Icon icon="lucide:qr-code" width={20} />
                        </Button>
                      }
                    />
                  </div>

                  {selectedCrypto && amount && (
                    <div className="bg-default-100 p-4 rounded-lg space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Phí giao dịch:</span>
                        <span>{getFee()} {selectedCrypto}</span>
                      </div>
                      <div className="flex justify-between text-sm font-medium">
                        <span>Thực nhận:</span>
                        <span>{getActualAmount()} {selectedCrypto}</span>
                      </div>
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Mã xác thực 2FA
                    </label>
                    <Input
                      type="text"
                      placeholder="Nhập mã 2FA"
                      value={verificationCode}
                      onValueChange={setVerificationCode}
                    />
                  </div>
                </div>
              ) : (
                <div className="text-center py-4 space-y-4">
                  <Icon
                    icon="lucide:check-circle"
                    className="text-success w-16 h-16 mx-auto"
                  />
                  <h3 className="text-xl font-medium">Rút tiền thành công!</h3>
                  <p className="text-sm text-default-500">
                    Hash giao dịch: 0x123...abc
                  </p>
                </div>
              )}
            </ModalBody>
            <ModalFooter>
              {!success ? (
                <>
                  <Button
                    color="danger"
                    variant="light"
                    onPress={resetModal}
                  >
                    Hủy
                  </Button>
                  <Button
                    color="primary"
                    onPress={handleWithdraw}
                    isLoading={processing}
                    isDisabled={!selectedCrypto || !amount || !address || !verificationCode}
                  >
                    {processing ? "Đang xử lý..." : "Xác nhận rút tiền"}
                  </Button>
                </>
              ) : (
                <Button
                  color="primary"
                  onPress={resetModal}
                  className="mx-auto"
                >
                  Đóng
                </Button>
              )}
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};