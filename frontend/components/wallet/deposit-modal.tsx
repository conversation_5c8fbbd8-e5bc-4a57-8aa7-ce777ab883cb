import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  Select,
  SelectItem,
} from "@heroui/react";
import { Icon } from "@iconify/react";

interface CryptoOption {
  symbol: string;
  icon: string;
  address: string;
}

interface DepositModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const cryptoOptions: CryptoOption[] = [
  { symbol: "ETH", icon: "cryptocurrency:eth", address: "******************************************" },
  { symbol: "BTC", icon: "cryptocurrency:btc", address: "**********************************" },
  { symbol: "USDT", icon: "cryptocurrency:usdt", address: "******************************************" },
];

export const DepositModal: React.FC<DepositModalProps> = ({ isOpen, onClose }) => {
  const [selectedCrypto, setSelectedCrypto] = React.useState("");
  
  const handleCopyAddress = () => {
    const selected = cryptoOptions.find(crypto => crypto.symbol === selectedCrypto);
    if (selected) {
      navigator.clipboard.writeText(selected.address);
    }
  };

  const resetModal = () => {
    setSelectedCrypto("");
    onClose();
  };

  const selectedCryptoData = cryptoOptions.find(crypto => crypto.symbol === selectedCrypto);

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={resetModal}
      size="sm"
      classNames={{
        base: "max-w-sm",
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-xl font-bold">Nạp tiền vào ví</h2>
            </ModalHeader>
            <ModalBody>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Chọn loại tiền
                  </label>
                  <Select
                    placeholder="Chọn loại tiền"
                    selectedKeys={selectedCrypto ? [selectedCrypto] : []}
                    onChange={(e) => setSelectedCrypto(e.target.value)}
                  >
                    {cryptoOptions.map((crypto) => (
                      <SelectItem
                        key={crypto.symbol}
                        startContent={<Icon icon={crypto.icon} width={20} />}
                      >
                        {crypto.symbol}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                {selectedCrypto && (
                  <div className="space-y-4">
                    <div className="flex justify-center">
                      <div className="w-40 h-40 bg-default-100 flex items-center justify-center rounded-lg">
                        <Icon icon="lucide:qr-code" width={80} className="text-default-400" />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Địa chỉ ví {selectedCrypto}
                      </label>
                      <Input
                        value={selectedCryptoData?.address}
                        isReadOnly
                        endContent={
                          <Button
                            isIconOnly
                            variant="light"
                            onPress={handleCopyAddress}
                          >
                            <Icon icon="lucide:copy" width={20} />
                          </Button>
                        }
                      />
                    </div>
                  </div>
                )}
              </div>
            </ModalBody>
            <ModalFooter>
              <Button
                color="danger"
                variant="light"
                onPress={resetModal}
              >
                Đóng
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};