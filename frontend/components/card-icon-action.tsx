'use client';

import type { CardProps } from '@heroui/react';

import React from 'react';
import { Card, CardBody } from '@heroui/react';
import { Icon } from '@iconify/react';
import { cn } from '@heroui/react';

export type CardIconActionProps = CardProps & {
  icon: string;
  title: string;
  color?: 'primary' | 'secondary' | 'warning' | 'danger';
};

const CardIconAction = React.forwardRef<HTMLDivElement, CardIconActionProps>(
  ({ icon, title, color = 'primary', className, ...props }, ref) => {
    const colors = React.useMemo(() => {
      switch (color) {
        case 'primary':
          return {
            iconWrapper: 'bg-gradient-to-r from-blue-500 to-green-500',
            icon: 'text-white',
          };
        case 'secondary':
          return {
            iconWrapper: 'bg-gradient-to-r from-purple-500 to-pink-500',
            icon: 'text-white',
          };
        case 'warning':
          return {
            iconWrapper: 'bg-gradient-to-r from-yellow-500 to-orange-500',
            icon: 'text-white',
          };
        case 'danger':
          return {
            iconWrapper: 'bg-gradient-to-r from-red-500 to-pink-500',
            icon: 'text-white',
          };
        default:
          return {
            iconWrapper: 'bg-gradient-to-r from-gray-500 to-gray-700',
            icon: 'text-white',
          };
      }
    }, [color]);

    return (
      <Card
        ref={ref}
        isPressable={true}
        className={cn('border-none shadow-none', className)}
        {...props}
      >
        <CardBody className="flex flex-col items-center text-center gap-1 p-2">
          <div
            className={cn(
              'flex items-center justify-center rounded-full p-3 w-12 h-12',
              colors?.iconWrapper,
            )}
          >
            <Icon className={colors?.icon} icon={icon} width={20} />
          </div>
          <p className="text-tiny">{title}</p>
        </CardBody>
      </Card>
    );
  },
);

CardIconAction.displayName = 'CardIconAction';

export default CardIconAction;