import { useSocketContext } from '@/providers/socket-provider';
import { useEffect } from 'react';
export const DepositModal = () => {
  const { socket } = useSocketContext();
  
  useEffect(() => {
    if (!socket) return;

    socket.on('order:status', (orderUpdate) => {
    });

    return () => {
      socket.off('order:status');
    };
  }, [socket]);

  const handleCreateOrder = async () => {
    try {
      socket.emit('order:created', {
        orderId: '123',
        userId: '123',
      });
    } catch (error) {
    }
  };
}; 