'use client';

import React from 'react';
import { <PERSON>, CardB<PERSON>, But<PERSON> } from '@heroui/react';
import { Icon } from '@iconify/react';

interface SocialLink {
  name: string;
  url: string;
  icon: string;
  color: string;
  description: string;
}

const socialLinks: SocialLink[] = [
    {
      name: 'Telegram Chat',
      url: 'https://t.me/worldmallchats',
      icon: 'ic:baseline-telegram',
      color: 'bg-[#229ED9]/10 text-[#229ED9] sm:hover:bg-[#229ED9]/20',
      description: 'Join our community chat'
    },
    {
      name: 'Telegram Channel',
      url: 'https://t.me/worldmallann',
      icon: 'ic:baseline-telegram',
      color: 'bg-[#229ED9]/10 text-[#229ED9] sm:hover:bg-[#229ED9]/20',
      description: 'Follow our announcements'
    },
    {
      name: 'YouTube',
      url: 'https://www.youtube.com/@worldmallOfficial',
      icon: 'ic:baseline-youtube',
      color: 'bg-[#FF0000]/10 text-[#FF0000] sm:hover:bg-[#FF0000]/20',
      description: 'Watch our latest videos'
    },
    {
      name: 'Website',
      url: 'https://worldmall.biz',
      icon: 'ic:baseline-web',
      color: 'bg-black/10 text-black sm:hover:bg-black/20 dark:bg-white/10 dark:text-white dark:sm:hover:bg-white/20',
      description: 'Visit our official website'
    },
    {
      name: 'X',
      url: 'https://x.com/worldmallweb3',
      icon: 'ic:tw-x',
      color: 'bg-black/10 text-black sm:hover:bg-black/20 dark:bg-white/10 dark:text-white dark:sm:hover:bg-white/20',
      description: 'Follow us on X'
    }
  ];

export default function CardSocial() {
  const handleSocialClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <Card className="w-full">
      <CardBody className="gap-3">
        <div className="flex items-center justify-center gap-2 sm:justify-start">
          <Icon icon="solar:share-circle-bold-duotone" className="text-gray-500" width={20} />
          <h3 className="text-base sm:text-lg font-semibold">Join Our Community</h3>
        </div>

        <p className="text-xs sm:text-sm text-center text-gray-500 mt-2">
          Stay updated with our latest news and announcements
        </p>

        <div className="grid grid-cols-1 gap-2 md:gap-3">
          {socialLinks.map((social) => (
            <Button
              key={social.name}
              className={`w-full ${social.color}`}
              variant="flat"
              onPress={() => handleSocialClick(social.url)}
            >
              <div className="flex items-center gap-3">
                <span className="font-medium text-base sm:text-md">{social.name}</span>
                <Icon 
                  icon="solar:arrow-right-up-linear" 
                  className="ml-auto" 
                  width={14} 
                />
              </div>
            </Button>
          ))}
        </div>
      </CardBody>
    </Card>
  );
}