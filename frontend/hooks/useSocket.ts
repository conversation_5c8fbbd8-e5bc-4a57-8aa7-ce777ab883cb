'use client';

import { useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useUserStore } from '@/store/userStore';

const SOCKET_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

export const useSocket = () => {
  const socketRef = useRef<Socket | null>(null);
  const { accessToken, user } = useUserStore();

  useEffect(() => {
    if (!accessToken || !user) return;

    socketRef.current = io(SOCKET_URL, {
      auth: {
        token: accessToken
      },
      transports: ['websocket'],
      autoConnect: true
    });

    const socket = socketRef.current;

    socket.on('connect', () => {
      socket.emit('join', { userId: user.id });
    });

    socket.on('disconnect', () => {
    });

    socket.on('error', (error) => {
    });

    socket.on('transaction:new', (transaction) => {
    });

    socket.on('order:status', (orderUpdate) => {
    });

    socket.on('mining:update', (miningUpdate) => {
    });

    return () => {
      if (socket) {
        socket.off('connect');
        socket.off('disconnect');
        socket.off('error');
        socket.off('transaction:new');
        socket.off('order:status');
        socket.off('mining:update');
        socket.disconnect();
      }
    };
  }, [accessToken, user]);

  const emit = (event: string, data: any) => {
    if (socketRef.current) {
      socketRef.current.emit(event, data);
    }
  };

  return {
    socket: socketRef.current,
    emit,
  };
}; 