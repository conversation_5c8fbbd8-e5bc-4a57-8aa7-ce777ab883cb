import { useState, useEffect } from 'react';
import { useApi } from './useApi';
import { TokenBalance, Token } from '@/types/models';
import { addToast } from '@heroui/react';

export interface TokenWithBalance extends Token {
  balance: {
    availableBalance: number;
    lockedBalance: number;
    totalBalance: number;
  };
  usdtValue: number;
}

export const useTokenBalances = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [tokens, setTokens] = useState<TokenWithBalance[]>([]);
  const [wmUsdRate, setWmUsdRate] = useState<number>(1.2);
  const { fetchApi } = useApi();

  const fetchWmUsdRate = async () => {
    try {
      const response = await fetchApi('/system/config/WM_USD_RATE');
      if (response.success && response.data) {
        const rate = parseFloat(response.data.value);
        if (!isNaN(rate) && rate > 0) {
          setWmUsdRate(rate);
        }
      }
    } catch (error) {
      console.error('Failed to fetch WM/USDT rate:', error);
    }
  };

  const fetchBalances = async () => {
    try {
      setIsLoading(true);

      await fetchWmUsdRate();

      const response = await fetchApi('/tokens/balances/me');

      if (response.success && response.data) {
        const transformedTokens = response.data.map((item: any) => ({
          ...item.token,
          balance: {
            availableBalance: parseFloat(item.availableBalance),
            lockedBalance: parseFloat(item.lockedBalance),
            totalBalance: parseFloat(item.totalBalance)
          },

          usdtValue: item.token.symbol === 'USDT'
            ? parseFloat(item.availableBalance)
            : item.token.symbol === 'WM'
              ? (parseFloat(item.availableBalance))
              : 0
        }));

        const sortedTokens = transformedTokens.sort((a: TokenWithBalance, b: TokenWithBalance) => {
          if (a.symbol === 'USDT') return -1;
          if (b.symbol === 'USDT') return 1;
          if (a.symbol === 'WM') return -1;
          if (b.symbol === 'WM') return 1;
          return 0;
        });

        setTokens(sortedTokens);
      } else {
        addToast({
          title: 'Error',
          description: 'Failed to load token balances',
          color: 'danger'
        });
      }
    } catch (error) {
      addToast({
        title: 'Error',
        description: 'Could not load token balances',
        color: 'danger'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBalances();
  }, []);

  const getTokenBalance = (symbol: string) => {
    return tokens.find(token => token.symbol === symbol);
  };

  const getTotalUsdtValue = () => {
    return tokens.reduce((total, token) => {
      const value = token.usdtValue || 0;
      const usdtValue = token.symbol === 'WM' ? value * wmUsdRate : value;
      return total + usdtValue;
    }, 0);
  };

  return {
    tokens,
    isLoading,
    getTokenBalance,
    getTotalUsdtValue,
    refreshBalances: fetchBalances,
    wmUsdRate
  };
};