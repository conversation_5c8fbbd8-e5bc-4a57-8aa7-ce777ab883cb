import { useUserStore } from '@/store/userStore';
import Cookies from 'js-cookie';
import { Language } from '@/providers/language-provider';
import { errorCodeMap } from '@/utils/error-codes';

const API_URL = '';  // Empty string means use the same domain
const API_PREFIX = 'api/v1';

// Define clear interfaces for API responses
interface ApiResponse<T = any> {
  statusCode: number;
  success: boolean;
  message: {
    en?: string;    // English
    vi?: string;    // Vietnamese
    zh?: string;    // Chinese
    ja?: string;    // Japanese
    kr?: string;    // Korean
    th?: string;    // Thai
    ru?: string;    // Russian
  };
  data: T;
  timestamp: string;
  errorCode?: string;
  errors?: Record<string, string>[];
}

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  body?: any;
  headers?: Record<string, string>;
  requireAuth?: boolean;
}

export const useApi = () => {
  const { accessToken } = useUserStore();
  const currentLanguage = Cookies.get('language') as Language || 'en';

  const fetchApi = async <T = any>(
    endpoint: string,
    options: ApiOptions = {}
  ): Promise<ApiResponse<T>> => {
    const { body, requireAuth = true, ...restOptions } = options;

    try {
      if (requireAuth && !accessToken) {
        return {
          success: false,
          message: {
            en: 'Authentication required',
            vi: 'Yêu cầu xác thực',
            kr: '인증이 필요합니다',
            zh: '需要认证',
            ja: '認証が必要です',
            th: 'ต้องการการยืนยันตัวตน',
            ru: 'Требуется аутентификация'
          },
          statusCode: 401,
          errorCode: 'AUTH_REQUIRED',
          data: null as T,
          timestamp: new Date().toISOString()
        };
      }

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        ...(requireAuth && accessToken && { Authorization: accessToken }),
        ...options.headers
      };

      const requestOptions = {
        ...restOptions,
        headers,
        credentials: 'include' as RequestCredentials,
        ...(body && {
          body: typeof body === 'string' ? body : JSON.stringify(body)
        })
      };

      const url = `${API_URL}/${API_PREFIX}${
        endpoint.startsWith('/') ? endpoint : `/${endpoint}`
      }`;

      const response = await fetch(url, requestOptions);
      const data = await response.json();

      if (!response.ok) {
        const errorResponse = {
          ...data,
          success: false,
          statusCode: response.status,
          message: normalizeMessage(data.message),
          timestamp: data.timestamp || new Date().toISOString(),
          errorCode: data.errorCode || `HTTP_${response.status}`,
          errors: data.errors || []
        };

        if (process.env.NODE_ENV === 'development') {
          console.error('API Error Response:', {
            endpoint,
            statusCode: response.status,
            errorCode: errorResponse.errorCode,
            message: errorResponse.message,
            errors: errorResponse.errors
          });
        }

        return errorResponse;
      }

      return {
        ...data,
        success: true,
        message: normalizeMessage(data.message)
      };

    } catch (error: any) {
      if (process.env.NODE_ENV === 'development') {
        console.error('API Error:', {
          endpoint,
          error,
          timestamp: new Date().toISOString()
        });
      }

      const unexpectedError = {
        success: false,
        message: {
          en: getErrorMessage(error),
          vi: 'Đã xảy ra lỗi không mong muốn',
          kr: '예상치 못한 오류가 발생했습니다',
          zh: '发生意外错误',
          ja: '予期しないエラーが発生しました',
          th: 'เกิดข้อผิดพลาดที่ไม่คาดคิด',
          ru: 'Произошла непредвиденная ошибка'
        },
        statusCode: 500,
        errorCode: 'UNEXPECTED_ERROR',
        data: null as T,
        timestamp: new Date().toISOString(),
        errors: []
      };

      return unexpectedError;
    }
  };

  return { fetchApi };
};

// Helper functions
function normalizeMessage(message: string | Record<string, string> | undefined): Record<string, string> {
  if (!message) return {
    en: 'No message provided',
    vi: 'Không có thông báo',
    kr: '메시지가 제공되지 않았습니다',
    zh: '未提供消息',
    ja: 'メッセージが提供されていません',
    th: 'ไม่มีข้อความที่ให้มา',
    ru: 'Сообщение не предоставлено'
  };
  if (typeof message === 'string') return {
    en: message,
    vi: message,
    kr: message,
    zh: message,
    ja: message,
    th: message,
    ru: message
  };
  return message;
}

function getLocalizedMessage(message: Record<string, string>): string {
  const currentLanguage = Cookies.get('language') as Language || 'en';

  if (message[currentLanguage]) {
    return message[currentLanguage];
  }

  if (message.en) {
    return message.en;
  }

  const firstAvailableLanguage = Object.keys(message)[0];
  if (firstAvailableLanguage) {
    return message[firstAvailableLanguage];
  }

  return 'No message available';
}

export function getErrorMessageFromCode(errorCode: string, t: (key: string) => string): string {
  if (errorCode && errorCodeMap[errorCode]) {
    return t(errorCodeMap[errorCode]);
  }

  return t('errors.unknown_error');
}

function getErrorMessage(error: any): string {
  if (error instanceof Error) return error.message;
  if (typeof error === 'string') return error;
  return 'An unexpected error occurred';
}