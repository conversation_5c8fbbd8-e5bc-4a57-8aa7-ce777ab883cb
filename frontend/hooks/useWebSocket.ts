import { useEffect, useRef } from 'react';
import io, { Socket } from 'socket.io-client';

export const useWebSocket = (url: string, onMessage: (data: any) => void) => {
  const socket = useRef<Socket | null>(null);

  useEffect(() => {
    socket.current = io(url, {
      transports: ['websocket'],
      autoConnect: true
    });

    socket.current.on('connect', () => {
    });

    socket.current.on('sessionUpdate', onMessage);

    return () => {
      if (socket.current) {
        socket.current.disconnect();
      }
    };
  }, [url, onMessage]);

  return socket.current;
};