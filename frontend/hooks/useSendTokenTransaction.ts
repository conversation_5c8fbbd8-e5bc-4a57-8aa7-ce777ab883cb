'use client';

import { useState, useCallback, useEffect } from "react";
import { useAccount, useWriteContract, useWaitForTransactionReceipt } from "wagmi";
import { bsc } from "wagmi/chains";
import { Abi } from "viem";

const useSendTokenTransaction = () => {
  const [isLoadingTransaction, setIsLoadingTransaction] = useState(false);
  const [error, setError] = useState(null);
  const [transactionResult, setTransactionResult] = useState(null);
  const [pendingTxHash, setPendingTxHash] = useState(null);

  const { address, isConnected } = useAccount();
  const { writeContractAsync } = useWriteContract();

  const [amount, setAmount] = useState(0);
  const [action, setAction] = useState("");

  const { data: receipt, isLoading: isConfirming, error: receiptError } = useWaitForTransactionReceipt({
    hash: pendingTxHash,
  });

  useEffect(() => {
    if (receipt && !isConfirming && !receiptError) {
      const isSuccess = receipt.status === "success";
      setTransactionResult({
        success: isSuccess,
        transactionHash: receipt.transactionHash,
        status: receipt.status,
      });
      setIsLoadingTransaction(false);

      if (isSuccess) {} else {
        setError("Transaction was reverted on the blockchain");
      }
    } else if (receiptError) {
      let errorMessage = "Transaction failed on the blockchain";
      if (receiptError.message.includes("BEP20: transfer from the zero address")) {
        errorMessage = "Cannot transfer from the zero address. Please ensure your wallet is connected.";
      } else if (receiptError.message.includes("execution reverted")) {
        errorMessage = "Transaction reverted: " + (receiptError.message.split("execution reverted: ")[1]?.split('.')[0] || "Unknown reason");
      }

      setTransactionResult({
        success: false,
        transactionHash: null,
        status: "failed",
        error: errorMessage
      });
      setError(errorMessage);
      setIsLoadingTransaction(false);
    }
  }, [receipt, isConfirming, receiptError, address]);

  const sendTokenTransaction = useCallback(
    async ({
      tokenContractAddress,
      recipientAddress,
      amount,
      decimals = 18,
      action = "Nạp",
    }) => {
      setIsLoadingTransaction(true);
      setError(null);
      setTransactionResult(null);
      setPendingTxHash(null);
      setAmount(amount);
      setAction(action);

      try {
        if (!isConnected) {
          throw new Error("Ví chưa được kết nối. Vui lòng kết nối ví trước!");
        }

        const tokenABI: Abi = [
          {
            name: "transfer",
            type: "function",
            inputs: [
              { name: "to", type: "address" },
              { name: "amount", type: "uint256" },
            ],
            outputs: [{ name: "", type: "bool" }],
            stateMutability: "nonpayable",
          },
        ];

        const amountInWei = BigInt(Math.round(parseFloat(amount) * 10 ** decimals));

        const txHash = await writeContractAsync({
          address: tokenContractAddress,
          abi: tokenABI,
          functionName: "transfer",
          args: [recipientAddress, amountInWei] as [string, bigint],
          chain: bsc,
          account: address,
          gas: BigInt(70000),
          gasPrice: BigInt(5_000_000_000),
        });

        setPendingTxHash(txHash);
        return { success: true, transactionHash: txHash, status: "pending" }; 
      } catch (err) {        
        let errorMessage = "An error occurred while sending the transaction";
        if (err.message.includes("User rejected the request") || err.message.includes("User denied transaction signature")) {
          errorMessage = "Transaction rejected by user";
        } else if (err.message.includes("insufficient funds")) {
          errorMessage = "Insufficient funds for transaction";
        } else if (err.message) {
          errorMessage = err.message.split('.')[0]; 
        }

        setError(errorMessage);
        setIsLoadingTransaction(false);
        return { success: false, message: errorMessage };
      }
    },
    [isConnected, address, writeContractAsync]
  );

  return {
    sendTokenTransaction,
    isLoadingTransaction,
    error,
    transactionResult,
    isConnected,
    address,
    isConfirming,
  };
};

export default useSendTokenTransaction;