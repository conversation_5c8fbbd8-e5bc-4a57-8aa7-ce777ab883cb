{"common": {"home": "Home", "team": "Team", "news": "News", "contact": "Contact", "account": "Account", "wallet": "Wallet", "settings": "Settings", "language": "Language", "language_code": "en", "theme": "Theme", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "save": "Save", "cancel": "Cancel", "submit": "Submit", "loading": "Loading...", "error": "Error", "success": "Success", "copy": "Copy", "copied": "<PERSON>pied", "connect_wallet": "Connect Wallet", "calculating": "Calculating...", "coming_soon": "Coming Soon", "did_you_know": "Did you know?", "notification_set": "Notification Set", "notify_when_available": "Notify Me When Available", "back_to_wallet": "Back to Wallet", "close": "Close"}, "auth": {"connect_wallet": "Connect your wallet to start using", "wallet_connected": "Wallet connected", "wallet_disconnected": "Wallet disconnected", "referral_code": "Referral Code", "referral_code_placeholder": "Enter referral code (optional)", "logging_in": "Logging In..."}, "account": {"profile": "Profile", "settings": "Settings", "security": "Security", "help_center": "Help center", "language": "Language", "theme": "Theme", "more_tools": "More tools", "edit_profile": "Edit my profile", "buy_sell_analytics": "Buy / Sell analytics", "credit_history": "Account credit history", "flash_buy_alarms": "Flash buy alarms listing", "global_mining": "Global Mining Co-Share", "get_started": "Get started", "complete_profile": "Complete your profile", "kyc_account": "KYC your account", "invite_friends": "Invite first member", "buy_first_nft": "Buy your first NFT", "buy_nft": "Buy NFT", "my_nfts": "My NFTs", "orders": "Orders", "credits": "Credits", "affiliate": "Affiliate", "sign_out": "SIGN OUT", "version": "Version"}, "wallet": {"balance": "Balance", "send": "Send", "receive": "Receive", "history": "History", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "transfer": "Transfer", "portfolio": "Portfolio", "exchange_rate": "Exchange Rate", "lightning_boost": "Lightning Boost", "priority_position": "Priority Position", "daily_mining": "Daily Mining", "base_rate": "Base rate", "total_mined": "Total mined", "mining_multiplier": "Mining multiplier", "referral_bonus": "Referral bonus", "mining": "Mining", "mining_success": "You've mined {{amount}} WM! New balance: {{balance}} WM", "deposit_crypto": "Deposit Crypto", "send_crypto_to_address": "Send your crypto to the following address", "network": "Network", "your_deposit_address": "Your Deposit Address", "important_notes": "Important Notes", "send_only_to_address": "Send only to this address", "ensure_network": "Ensure you're on the {{network}} network", "minimum_deposit": "Minimum deposit", "deposit_credited_after": "Deposits will be credited after network confirmation", "deposit_successful": "Deposit Successful", "deposit_credited": "Your deposit of {{amount}} {{token}} has been credited to your wallet.", "address_copied": "Wallet address copied to clipboard", "copy_failed": "Failed to copy address", "bnb_smart_chain": "BNB Smart Chain (BEP20)", "withdraw_usdt": "Withdraw USDT", "enter_amount_withdraw": "Enter the amount of USDT to withdraw", "amount_usdt": "Amount (USDT)", "available_balance": "Available balance", "minimum_withdrawal": "Minimum withdrawal", "ensure_sufficient_balance": "Ensure sufficient balance", "network_fees": "Network fees", "processing_time": "Processing may take up to 24 hours", "withdraw_now": "Withdraw now", "withdrawal_processed": "Withdrawal of {{amount}} {{token}} has been processed", "withdrawal_error": "Could not process withdrawal", "enter_valid_amount": "Please enter a valid amount", "transfer_wm": "Transfer WM", "enter_referral_code": "Please enter a referral code", "transfer_processed": "Transfer of {{amount}} {{token}} to referral {{referral}} has been processed", "transfer_error": "Could not process transfer", "select_wallet_type": "Select Wallet Type", "freeze_balance": "Freeze Balance", "amount_wm": "Amount (WM)", "referral_code": "Referral Code", "enter_referral_code_placeholder": "Enter referral code", "minimum_transfer": "Minimum transfer", "network_fees_may_apply": "Network fees may apply", "transfer_now": "Transfer Now", "enter_amount_and_referral": "Enter the amount of WM and referral code", "deposit_usdt": "Deposit USDT", "enter_amount_deposit": "Enter the amount of USDT to deposit", "ensure_wallet_balance": "Ensure sufficient balance in your wallet", "deposit_transaction_error": "Failed to process deposit transaction", "deposit_error": "Could not process deposit", "deposit_failed": "Deposit Failed", "transaction_error": "Transaction Error", "transaction_sent": "Transaction Sent", "waiting_confirmation": "Waiting for transaction confirmation...", "sending_transaction": "Sending Transaction...", "confirming_transaction": "Confirming Transaction...", "processing_deposit": "Processing Deposit...", "deposit_now": "Deposit now", "wallet_error": "<PERSON><PERSON>", "connect_wallet_first": "Please connect your wallet first"}, "errors": {"error": "Error", "something_went_wrong": "Something went wrong", "please_try_again": "Please try again", "not_found": "Not found", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "server_error": "Server error", "validation_error": "Validation error", "invalid_input": "Invalid input", "insufficient_balance": "Insufficient balance", "transaction_failed": "Transaction failed", "already_exists": "Already exists", "not_available": "Not available", "operation_failed": "Operation failed", "unknown_error": "Unknown error", "network_error": "Network error", "timeout": "Request timeout", "invalid_amount": "Invalid amount", "insufficient_usdt_balance": "Insufficient USDT balance", "insufficient_wm_balance": "Insufficient WM balance", "invalid_wallet_address": "Invalid wallet address", "invalid_transaction": "Invalid transaction", "invalid_signature": "Invalid signature", "account_locked": "Account locked", "invalid_referral_code": "Invalid referral code", "kyc_already_completed": "KYC already completed", "kyc_required": "KYC verification required", "create_staking_failed": "Failed to create staking", "maintenance_mode": "System is currently in maintenance mode. Please try again later.", "bad_request": "Bad request", "internal_server_error": "Internal server error", "invalid_credentials": "Invalid credentials", "account_disabled": "Account disabled", "account_not_found": "Account not found", "invalid_token": "Invalid token", "token_expired": "Token expired", "wallet_already_connected": "Wallet already connected", "transaction_not_found": "Transaction not found", "minimum_amount_required": "Minimum amount required", "maximum_amount_exceeded": "Maximum amount exceeded", "kyc_verification_failed": "KYC verification failed", "kyc_in_progress": "KYC verification is in progress", "self_referral_not_allowed": "Self referral is not allowed", "referral_already_exists": "Referral already exists", "already_purchased": "Already purchased", "purchase_failed": "Purchase failed", "rate_limit_exceeded": "Rate limit exceeded. Please try again later.", "daily_limit_exceeded": "Daily limit exceeded", "monthly_limit_exceeded": "Monthly limit exceeded", "data_integrity_error": "Data integrity error", "invalid_data_format": "Invalid data format", "user_not_found": "User not found", "trading_session_not_found": "Trading session not found", "nft_not_found": "NFT not found", "user_wallet_not_found": "User wallet not found", "invalid_wm_rate_configuration": "Invalid WM rate configuration", "insufficient_wm_balance_for_gas_fee": "Insufficient WM balance for gas fee", "insufficient_usdt_balance_for_purchase": "Insufficient USDT balance for purchase", "insufficient_staking_balance": "Insufficient global mining co-share balance for purchase", "failed_to_create_nft_order": "Failed to create NFT order"}, "lightning": {"title": "Lightning Boost", "buy": "Buy Lightning Boost", "description": "Get a permanent 2x mining boost by purchasing Lightning Boost.", "price": "Price", "payment_method": "Payment Method", "cost": "Lightning cost {{amount}}", "benefits": "Benefits", "benefit_mining_rate": "2x Mining Rate", "benefit_permanent": "Permanent Boost & +5WM", "benefit_stackable": "Stackable with Referral Bonus $0,5 x 10 level", "benefit_early_access": "Early Access to New Features", "transaction_error": "Transaction Error", "purchased": "Lightning Boost <PERSON>urchased", "purchase_success": "Your Lightning boost has been purchased successfully.", "purchase": "Lightning Boost Purchase", "process_error": "Could not process Lightning Boost", "purchase_failed": "Lightning Boost Purchase Failed", "purchase_error": "Failed to purchase Lightning Boost", "wallet_error": "<PERSON><PERSON>", "connect_wallet_first": "Please connect your wallet first", "transaction_sent": "Transaction Sent", "waiting_confirmation": "Waiting for transaction confirmation...", "sending_transaction": "Sending Transaction...", "confirming_transaction": "Confirming Transaction...", "processing_purchase": "Processing Purchase...", "already_purchased": "Already Purchased", "transaction_confirmed": "Transaction confirmed", "view_on_bscscan": "View on BscScan"}, "priority": {"title": "Priority Position", "buy": "Buy Priority Position", "description": "Get a priority position in the platform by making a one-time payment of {{amount}}.", "price": "Price", "payment_method": "Payment Method", "benefits": "Benefits", "benefit_ranking": "Higher ranking in user listings", "benefit_support": "Priority customer support", "benefit_early_access": "Early access to new features", "benefit_permanent": "Permanent account status upgrade", "transaction_error": "Transaction Error", "purchased": "Priority Position Purchased", "purchase_success": "Your Priority Position has been purchased successfully.", "purchase": "Priority Position Purchase", "process_error": "Could not process Priority Position", "purchase_failed": "Priority Position Purchase Failed", "purchase_error": "Failed to purchase Priority Position", "wallet_error": "<PERSON><PERSON>", "connect_wallet_first": "Please connect your wallet first", "transaction_sent": "Transaction Sent", "waiting_confirmation": "Waiting for transaction confirmation...", "sending_transaction": "Sending Transaction...", "confirming_transaction": "Confirming Transaction...", "processing_purchase": "Processing Purchase...", "already_purchased": "Already Purchased", "transaction_confirmed": "Transaction confirmed", "view_on_bscscan": "View on BscScan"}, "transactions": {"history": "Transaction History", "load_error": "Failed to load transaction history", "all": "All", "deposits": "Deposits", "withdrawals": "<PERSON><PERSON><PERSON><PERSON>", "transfers": "Transfers", "mining": "Mining", "referral_bonus": "Referral Bonus", "mining_referral": "Mining Referral", "task_rewards": "Task Rewards", "profile_bonus": "Profile Bonus", "kyc_bonus": "KYC Bonus", "kyc_commission": "KYC Commission", "kyc_purchase": "KYC Purchase", "lightning_bolt": "Lightning Bolt", "priority_position": "Priority Position", "nft_purchase": "NFT Purchase", "nft_withdrawal": "NFT Withdrawal", "nft_sale": "NFT Sale", "sale_bonus": "Sale Bonus", "direct_commission": "Direct Commission", "ranking_commission": "Ranking Commission", "co_shareholder_bonus": "Co-Shareholder Bonus", "gas_fee": "Gas Fee", "scheduling_fee": "Scheduling Fee", "system_commission": "System Commission", "global_mining_coshare": "Global Mining Co-Share", "coshare_interest": "Co-Share Interest", "coshare_bonus": "Co-Share Bonus", "coshare_direct_comm": "Co-Share Direct Comm", "coshare_matching_comm": "Co-Share Matching Comm", "coshare_profit_comm": "Co-Share Profit Comm", "coshare_rank_comm": "Co-Share Rank Comm", "coshare_same_level": "Co-Share Same Level", "coshare_milestone": "Co-Share Milestone", "coshare_shareholder": "Co-Share Shareholder", "no_transactions": "No transactions found", "amount": "Amount", "date": "Date", "date_time": "Date & Time", "note": "Note", "from": "From", "to": "To", "page": "Page", "of": "of", "transaction_hash": "Transaction Hash"}, "nfts": {"title": "NFTs", "notification": "NFTs notification", "fetch_error": "Failed to fetch {{type}} NFTs", "my": "my", "listed": "listed", "orders": "orders", "profits_withdrawn": "Profits withdrawn successfully", "action_failed": "Action failed", "listed_success": "NFT listed successfully", "list_error": "Failed to list NFT", "marketplace": "Marketplace", "no_listed_nfts": "No listed NFTs found", "my_nfts": "My NFTs", "no_nfts": "No NFTs found", "nft_not_found": "NFT not found", "nft_already_owned": "NFT already owned", "nft_not_owned": "You don't own this NFT", "nft_already_listed": "NFT already listed", "nft_not_listed": "NFT not listed", "purchase_failed": "Failed to purchase NFT", "buy_sell": "Buy/Sell", "no_orders": "No orders NFTs found", "price": "Price", "gas_fee": "Gas Fee", "quantity": "Quantity", "list_now": "List Now", "buy_with_flash_buy": "Buy with Flash Buy", "current_price": "Current Price", "initial_price": "Initial Price", "gas_fee_usdt": "Gas Fee (USDT)", "rarity_score": "Rarity Score", "description": "Description", "attributes": "Attributes", "list_nft": "List NFT", "buy_now": "Buy Now", "withdraw_profits": "Withdraw Profits", "list_for_sale": "List NFT for Sale", "confirm_list": "Are you sure you want to list {{type}} for sale?", "quantity_to_list": "Quantity to List", "quantity_min_error": "Quantity must be at least 1", "quantity_max_error": "Quantity exceeds available units", "units": "Units", "available_units": "Available: {{quantity}} Units", "current_quantity": "Current Quantity", "listing_quantity": "Listing Quantity", "confirm_listing": "Confirm Listing", "scheduled": "Scheduled", "buy": "Buy", "sell": "<PERSON>ll", "order": "Order", "buyer": "Buyer", "seller": "<PERSON><PERSON>", "nft_type": "NFT Type", "nft_id": "NFT ID", "session": "Session", "session_id": "Session ID", "session_time": "Session Time", "matched_at": "Matched At", "completed_at": "Completed At", "created_at": "Created At", "updated_at": "Updated At", "status": "Status"}, "affiliate": {"total_referrals": "Total Referrals", "direct_referrals": "Direct Referrals", "total_buy_volume": "Total Buy Volume", "direct_referrals_total_buy_volume": "Direct Referrals Total Buy Volume", "buy_volume_day": "Buy Volume Day", "direct_referrals_buy_volume_day": "Direct Referrals Buy Volume Day", "buy_volume_session": "Buy Volume Session", "direct_referrals_buy_volume_session": "Direct Referrals Buy Volume Session", "total_sell_volume": "Total Sell Volume", "direct_referrals_total_sell_volume": "Direct Referrals Total Sell Volume", "sell_volume_day": "Sell Volume Day", "direct_referrals_sell_volume_day": "Direct Referrals Sell Volume Day", "sell_volume_session": "Sell Volume Session", "direct_referrals_sell_volume_session": "Direct Referrals Sell Volume Session", "total_earnings": "Total Earnings", "direct_referrals_total_earnings": "Direct Referrals Total Earnings", "earnings_day": "Earnings Day", "direct_referrals_earnings_day": "Direct Referrals Earnings Day", "earnings_session": "Earnings Session", "direct_referrals_earnings_session": "Direct Referrals Earnings Session", "kyc": "kyc", "invite_link": "Invite link", "invite_code": "Invite code", "your_referral_code": "Your Referral Code", "share_code_rewards": "Share this code with others to earn rewards", "referral_code": "Referral Code", "code_copied": "Referral code copied to clipboard", "copy_code_error": "Failed to copy referral code", "your_invite_code": "Your invite code", "your_invite_link": "Your invite link", "link_copied": "Invite link copied to clipboard", "username": "Username", "joined": "Joined", "status": "Status", "active": "Active", "pending": "Pending", "rank": "Rank", "actions": "Actions", "no_direct_referrals": "No direct referrals found", "no_team_members": "No team members yet", "referrals": "referrals", "referral_network": "Referral Network", "no_referral_data": "No referral data available", "total_volume": "Total Volume", "volume_day": "Volume Day", "volume_session": "Volume Session", "copy_address": "Copy Address", "address_copied": "Address copied!", "address_copied_description": "{{address}} has been copied to clipboard.", "copy_failed": "Co<PERSON> failed", "copy_failed_description": "Could not copy address to clipboard."}, "credit_histories": {"title": "Credit histories", "bonus": "Bonus", "bonus_invite_friend": "Bonus for invite friend.", "bonus_new_account": "Bonus for new account.", "credits": "credits"}, "edit_profile": {"title": "Edit profile", "update_profile": "Update profile", "fill_required_fields": "Please fill in all required fields", "profile_updated": "Profile updated successfully", "update_failed": "Could not update profile", "update_error": "Failed to update profile. Please try again later.", "full_name": "Full name", "enter_full_name": "Enter your full name", "email": "Email", "enter_email": "Enter your email", "phone_number": "Phone number", "enter_phone": "Enter your phone number", "save_changes": "Save changes"}, "global_mining": {"title": "Global Mining Co-Share", "investment_packages": "Investment Packages", "my_investments": "My Investments", "fetch_packages_error": "Failed to fetch packages", "fetch_investments_error": "Failed to fetch your investments", "fetch_stats_error": "Failed to fetch statistics", "investment_created": "Investment created successfully", "create_investment_error": "Failed to create investment", "insufficient_usdt_balance": "Insufficient USDT balance for this investment", "insufficient_wm_balance": "Insufficient WM balance for this investment", "package_not_found": "Investment package not found", "invalid_duration": "Invalid investment duration", "max_stakings_reached": "Maximum number of active investments reached", "staking_not_found": "Investment not found", "staking_already_completed": "Investment already completed", "staking_already_active": "Investment already active", "no_packages": "No investment packages available", "no_investments": "No active investments found", "invest": "Invest", "select_duration": "Select Investment Duration", "days": "Days", "interest_rate": "Interest Rate", "interest_rate_month": "Interest Rate (Per month)", "bonus_rate": "Bonus Rate", "daily_interest": "Daily Interest", "daily_interest_rate": "Daily Interest Rate", "investment_summary": "Investment Summary", "package": "Package", "price": "Price", "duration": "Duration", "expected_return_usdt": "Expected Return (USDT)", "expected_return_wm": "Expected Return (WM)", "exchange_rate": "Exchange Rate", "confirm_investment": "Confirm Investment", "start_date": "Start Date", "end_date": "End Date", "total_interest": "Total Interest", "total_bonus": "Total Bonus", "progress": "Progress", "day": "Day", "days_left": "days left", "view_details": "View Details", "investment_details": "Investment Details", "amount": "Amount", "date": "Date", "max_out_status": "Max Out Status", "max_out_message": "You have reached 300% of your investment. Reinvest to continue earning.", "earnings_progress": "Earnings Progress", "interest_history": "Interest History", "no_interest_history": "No interest history available", "no_stats": "No statistics available", "investment_statistics": "Investment Statistics", "total": "Total", "active": "Active", "investment": "Investment", "interest": "Interest", "commission": "Commission", "total_volume": "Total Volume", "rank": "Rank", "max_out_progress": "Max-Out Progress", "todays_interest": "Today's Interest"}, "kyc": {"title": "Update KYC", "transaction_error": "Transaction Error", "updated_successfully": "KYC updated successfully", "update": "KYC Update", "update_failed": "Could not update KYC", "update_failed_title": "KYC Update Failed", "update_failed_message": "Failed to update KYC. Please try again later.", "wallet_error": "<PERSON><PERSON>", "connect_wallet_first": "Please connect your wallet first", "validation_error": "KYC Validation Error", "fill_required_fields": "Please fill in all required fields", "transaction_sent": "Transaction Sent", "waiting_confirmation": "Waiting for transaction confirmation...", "full_name": "Full name", "enter_full_name": "Enter your full name", "email": "Email", "enter_email": "Enter your email", "phone_number": "Phone number", "enter_phone": "Enter your phone number", "national_id": "National ID", "enter_national_id": "Enter your national ID", "verification_fee": "KYC Verification Fee", "fee_description": "A fee of 10 USDT is required to process your KYC verification. Please ensure your wallet has sufficient funds before submitting.", "sending_transaction": "Sending Transaction...", "confirming_transaction": "Confirming Transaction...", "submitting": "Submitting KYC...", "save_changes": "Save Changes", "transaction_confirmed": "Transaction confirmed", "view_on_bscscan": "View on BscScan", "transaction_failed": "Transaction Failed", "transaction_failed_desc": "Your transaction was not successful. Please try again.", "processing": "Processing...", "processing_kyc": "Processing KYC...", "please_wait": "Please wait, do not close this page..."}, "transfer": {"title": "Transfer USDT", "wallet_status": "Wallet Status", "connected": "Connected", "not_connected": "Not Connected", "network": "Network", "select_network": "Select a network", "to_address": "To Address", "no_address": "No address available", "amount_usdt": "Amount (USDT)", "enter_amount": "Enter USDT amount to transfer", "transfer_usdt": "Transfer USDT", "connect_wallet_failed": "Failed to connect wallet", "install_wallet": "Please install MetaMask or a compatible wallet", "check_connection_error": "Error checking wallet connection or network", "fetch_address_failed": "Failed to fetch wallet address", "fetch_address_error": "Error fetching wallet address", "network_not_added": "Network not added to wallet. Please add it manually.", "switch_network_failed": "Failed to switch network", "wallet_not_available": "Wallet not available to switch network", "validation_error": "Please enter an amount, ensure a valid address, connect wallet, and select correct network", "transfer_failed": "Transfer failed: {{error}}"}, "coming_soon": {"flash_buy_notify": "You'll be notified when Flash Buy is available!", "flash_buy_description": "Flash Buy will allow you to quickly purchase NFTs at the best available rates. We're working to bring you this feature soon!", "nfts_notify": "You'll be notified when NFTs are available!", "nfts_description": "We're working hard to bring NFT functionality to our platform. Stay tuned for exciting updates!"}, "flash_buy": {"title": "Flash Buy", "benefits": "Flash Buy Benefits", "benefit_instant": "Instant NFT purchases with minimal slippage", "benefit_rates": "Competitive rates across multiple exchanges", "benefit_fast": "Fast transaction processing", "benefit_secure": "Secure and transparent transactions", "did_you_know_content": "Flash Buy technology uses smart routing algorithms to find the best prices across multiple liquidity sources, ensuring you get the most tokens for your money with minimal price impact.", "fetch_nfts_failed": "Failed to fetch NFTs", "nft_purchased": "NFT purchased successfully", "purchase_failed": "Failed to purchase NFT", "action_failed": "Action failed", "no_nfts": "No NFTs found", "schedule_nft": "Schedule NFT", "nft_scheduled": "NFT scheduled successfully", "schedule_failed": "Failed to schedule NFT", "session_not_found": "Session not found", "session_already_started": "Session already started", "session_already_ended": "Session already ended", "session_not_active": "Session is not active", "session_full": "Session is full", "already_scheduled": "Already scheduled for this session", "insufficient_staking_balance": "Insufficient global mining co-share balance for purchase", "price": "Price", "gas_fee": "Gas Fee", "buy_now": "Buy Now", "schedule_now": "Schedule Now", "current_price": "Current Price", "initial_price": "Initial Price", "gas_fee_usdt": "Gas Fee (USDT)", "rarity_score": "Rarity Score", "description": "Description", "attributes": "Attributes", "list_nft": "List NFT", "withdraw_profits": "Withdraw Profits", "morning_session": "Morning Session", "afternoon_session": "Afternoon Session", "evening_session": "Evening Session", "high_liquidity": "High Liquidity", "hours": "Hours", "minutes": "Minutes", "seconds": "Seconds", "schedules": "Schedules", "orders": "Orders", "matched": "Matched", "volume": "Volume", "schedule": "Schedule", "join_now": "Join Now", "open_session": "Open Session", "close_session": "Close Session", "session_closed": "Session closed successfully", "close_session_error": "Failed to close session", "session_opened": "Session opened successfully", "open_session_error": "Failed to open session", "session_scheduled": "Session scheduled successfully", "schedule_session_error": "Failed to schedule session", "fetch_error": "Failed to fetch sessions", "fetch_sessions_error": "Failed to fetch trading sessions", "session_starts_in": "Session starts in", "session_ends_in": "Session ends in", "session_status": "Session status", "sold_out": "Sold Out", "remaining": "Remaining", "schedule_confirmation": "Schedule Confirmation", "confirm_schedule": "Are you sure you want to schedule the trading session at {{time}}?", "amount_usdt": "Amount (USDT)", "scheduling_fee": "Scheduling Fee", "fee_refund_note": "The fee will be refunded if you participate on time", "confirm_schedule_button": "Confirm Schedule"}, "my_nfts": {"title": "My NFTs", "coming_soon": "Coming Soon", "collection_message": "Your NFT collection will be displayed here. We're currently developing this feature to provide you with the best experience.", "explore_marketplace": "Explore NFT Marketplace", "back_to_wallet": "Back to Wallet", "future_features": "Future Features", "feature_view": "View all your owned NFTs in one place", "feature_track": "Track NFT value and history", "feature_transfer": "Transfer NFTs to other users", "feature_showcase": "Showcase your collection", "did_you_know": "Did you know?", "nft_explanation": "NFTs (Non-Fungible Tokens) are unique digital assets that represent ownership of a specific item or piece of content on the blockchain. Unlike cryptocurrencies, each NFT has a distinct value and cannot be exchanged on a one-to-one basis."}, "support": {"title": "Ticket Support", "contact_support": "Create Support Ticket", "ticket_id": "User ID", "wallet_address": "Wallet BNB Address", "content": "Content", "content_placeholder": "Describe your issue in detail...", "tx_hash": "Transaction Hash (if applicable)", "tx_hash_placeholder": "Enter transaction hash if related to a transaction", "send_message": "Submit Ticket", "validation_error": "Validation Error", "fill_required_fields": "Please fill in all required fields", "ticket_sent": "Ticket Submitted", "ticket_submitted": "Your support ticket has been submitted successfully. We'll get back to you soon.", "send_failed": "Failed to submit your ticket. Please try again later.", "fetch_failed": "Failed to fetch your tickets. Please try again later.", "ticket_history": "Ticket History", "invalid_content": "Invalid ticket content. Please provide more details.", "invalid_tx_hash": "Invalid transaction hash format.", "too_many_tickets": "You have too many open tickets. Please wait for responses.", "ticket_not_found": "Ticket not found", "ticket_already_resolved": "Ticket already resolved", "status": "Status", "status_pending": "Pending", "status_processing": "Processing", "status_resolved": "Resolved", "response": "Response", "no_response": "No response yet", "faq": "Frequently Asked Questions", "faq_wallet_title": "How do I connect my wallet?", "faq_wallet_content": "Click on the \"Connect Wallet\" button in the top right corner and select your preferred wallet provider. Follow the prompts to authorize the connection. Once connected, you'll be able to access all features of the platform.", "faq_referrals_title": "How do referrals work?", "faq_referrals_content": "Share your unique referral link with friends. When they join using your link, you'll receive referral rewards. You can find your referral link in the Referrals section of your account. The more people you refer, the more rewards you can earn.", "faq_nft_title": "When will NFT features be available?", "faq_nft_content": "We're actively developing NFT features and will announce their launch soon. Stay tuned for updates! You can enable notifications in your account settings to be alerted when new features are released.", "faq_security_title": "Is my data secure?", "faq_security_content": "Yes, we use industry-standard encryption and security practices to protect your data and transactions. We never store your private keys, and all sensitive information is encrypted. We also regularly audit our systems to ensure the highest level of security.", "faq_withdraw_title": "How do I withdraw my funds?", "faq_withdraw_content": "To withdraw funds, navigate to the Wallet section and click on \"Withdraw\". Select the token you wish to withdraw, enter the amount and the destination address. Confirm the transaction in your connected wallet. Processing times vary depending on network congestion."}, "admin": {"panel": "Admin Panel", "support_management": "Support Management", "user_management": "User Management", "ticket_management": "Support Ticket Management", "ticket_details": "Ticket Details", "admin_response": "Admin Response", "update_status": "Update Status", "update_ticket": "Update", "ticket_updated": "Ticket response updated successfully", "update_failed": "Failed to update ticket", "create_transaction_history": "Create transaction history", "balance_update_warning": "Note: This operation will change the balance immediately", "enter_valid_amount": "Please enter a valid amount (greater than 0)", "insufficient_balance_error": "Subtract amount cannot be greater than current balance", "balance_updated_successfully": "{{type}} balance updated successfully", "balance_update_error": "An error occurred while updating balance", "enter_amount_placeholder": "Enter {{type}} amount", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "set_balance": "Set Balance", "transaction_hash": "Transaction Hash", "transaction_hash_required": "Please enter transaction hash", "enter_transaction_hash": "Enter blockchain transaction hash", "deposit_successful": "Successfully deposited {{amount}} {{type}}", "deposit_failed": "Depo<PERSON><PERSON> failed", "deposit_warning": "Note: Transaction will be verified on blockchain", "process_deposit": "Process Deposit", "new_balance": "New Balance", "enter_new_balance_placeholder": "Enter new balance for {{type}}", "balance_change_preview": "Balance Change Preview", "no_change": "No change", "set_balance_warning": "Note: Balance will be adjusted to this value", "balance_set_successfully": "Successfully set {{type}} balance to {{amount}}", "enter_valid_amount_set": "Please enter a valid amount (>= 0)", "total_tickets": "Total", "refresh": "Refresh", "search_placeholder": "Search by ticket code, content or referral code...", "filter_by_status": "Filter by status", "all_status": "All", "user": "User", "wallet": "Wallet", "created_at": "Created At", "ticket_content": "Ticket Content", "current_status": "Current Status", "no_tickets": "No tickets found", "search_user": "Search User", "search_user_placeholder": "Enter wallet address or referral code...", "search": "Search", "basic_info": "Basic Information", "wallet_address": "Wallet Address", "referral_code": "Referral Code", "role": "Role", "rank": "Rank", "joined_date": "Joined Date", "first_deposit": "First Deposit", "account_balance": "Account <PERSON><PERSON>", "update_balance": "Update Balance", "current_balance": "Current Balance", "operation_type": "Operation Type", "add": "Add", "subtract": "Subtract", "set": "Set", "amount": "Amount", "new_balance_preview": "New Balance Preview", "update": "Update", "adjust_wm_successful": "Successfully {{type}} {{amount}} WM", "adjust_wm_failed": "Failed to adjust WM", "added": "added", "subtracted": "subtracted", "anonymous_user": "Anonymous User", "user_id": "User ID", "role_admin": "Administrator", "role_user": "User", "rank_bronze": "Bronze", "rank_silver": "Silver", "rank_gold": "Gold", "rank_platinum": "Platinum", "rank_diamond": "Diamond", "user_statistics": "User Statistics", "account_age": "Account Age", "days": "days", "kyc_status": "KYC Status", "verified": "Verified", "pending": "Pending", "total_value": "Total Value", "balance": "Balance", "available": "Available", "extended_info": "Extended Information", "contact_info": "Contact Information", "account_timeline": "Account Timeline", "joined": "Joined", "last_updated": "Last Updated", "referral_info": "Referral Information", "referred_by": "Referred By", "account_management": "Account Management", "financial_operations": "Financial Operations", "reports_analytics": "Reports & Analytics", "locked": "Locked", "lightning_bolt": "Lightning Bolt", "priority_buy": "Priority Buy", "shareholder": "Shareholder", "staking_active": "Staking Active", "account_status": "Account Status", "mining_status": "Mining Status", "multiplier": "Multiplier", "staking_status": "Staking Status", "earnings": "Earnings", "total_referrals": "Total Referrals", "special_features": "Special Features", "purchased_on": "Purchased on", "enabled": "Enabled", "min_deposit": "<PERSON>", "completed": "Completed", "completion_status": "Completion Status", "first_profile": "First Profile", "kyc_completed": "KYC Completed", "invite_completed": "Invite Completed", "nft_completed": "NFT Completed", "lock_account": "Lock Account", "unlock_account": "Unlock Account", "update_role": "Update Role", "update_rank": "Update Rank", "reset_password": "Reset Password", "user_analytics": "User Analytics", "view_referrals": "View Referrals", "view_staking": "View Staking", "send_notification": "Send Notification", "decimals": "decimals", "search_query_required": "Please enter wallet address or referral code", "authentication_required": "You are not logged in. Please log in again.", "admin_access_required": "You do not have admin access.", "authentication_error": "Authentication error. Please log in again or check admin permissions.", "user_not_found": "User not found with this information", "api_error": "An error occurred: {{message}}", "search_error": "An error occurred while searching", "email": "Email", "phone": "Phone", "admin_actions": "Admin Actions", "update_kyc": "Update KYC", "wallet_info": "Wallet Info", "transfer_usdt": "Transfer USDT", "transfer_wm": "Transfer WM", "withdraw_usdt": "Withdraw USDT", "adjust_wm": "Adjust WM", "transaction_history": "Transaction History", "user_info": "User Information", "current_kyc_status": "Current KYC Status", "kyc_updated_successfully": "KYC updated successfully", "kyc_update_failed": "Failed to update KYC", "kyc_update_warning": "This operation will update the user's KYC status. Please ensure the transaction code is correct.", "transfer_token": "Transfer {{type}}", "transfer_from": "Transfer From", "transfer_amount": "Transfer Amount", "recipient_referral_code": "Recipient Referral Code", "enter_referral_code": "Enter recipient referral code", "wallet_type": "Wallet Type", "available_wallet": "Available Wallet", "freeze_wallet": "Freeze Wallet", "transfer_warning": "This operation will transfer tokens from this account to another account. Please verify the information carefully.", "transfer_successful": "Successfully transferred {{amount}} {{type}} to {{referralCode}}", "transfer_failed": "Token transfer failed", "referral_code_required": "Please enter recipient referral code", "withdraw_from": "Withdraw From", "withdraw_amount": "Withdraw Amount", "recipient_address": "Recipient Address", "enter_recipient_address": "Enter recipient wallet address", "recipient_address_required": "Please enter recipient wallet address", "invalid_wallet_address": "Invalid wallet address", "withdraw_warning": "This operation will withdraw USDT from this account. Please verify the recipient wallet address carefully.", "withdraw_successful": "Successfully withdrew {{amount}} USDT to {{address}}", "withdraw_failed": "<PERSON><PERSON><PERSON> failed", "transaction_type": "Transaction Type", "all_types": "All Types", "withdrawal": "<PERSON><PERSON><PERSON>", "transfer": "Transfer", "mining": "Mining", "type": "Type", "token": "Token", "status": "Status", "date": "Date", "note": "Note", "no_transactions": "No transactions found", "fetch_transactions_failed": "Failed to fetch transaction history", "wallet_details": "Wallet Details", "wallet_id": "Wallet ID", "wallet_status": "Wallet Status", "active": "Active", "inactive": "Inactive", "updated_at": "Updated At", "token_balances": "Token Balances", "available_balance": "Available Balance", "freeze_balance": "Freeze Balance", "total_balance": "Total Balance", "no_wallet_info": "No wallet information found", "fetch_wallet_info_failed": "Failed to fetch wallet information", "adjust_amount": "Adjust Amount", "enter_note_optional": "Enter note (optional)", "adjust_wm_warning": "This operation will adjust the user's WM balance. Please verify the information carefully.", "wmanager_rank": "W-Manager Rank", "current_wmanager_rank": "Current W-Manager Rank", "new_wmanager_rank": "New W-Manager Rank", "select_wmanager_rank": "Select W-Manager Rank", "update_wmanager_rank": "Update W-Manager Rank", "wmanager_rank_updated_successfully": "Successfully updated W-Manager rank to {{rank}}", "wmanager_rank_update_failed": "Failed to update W-Manager rank", "wmanager_rank_update_warning": "This operation will update the user's W-Manager rank. Please verify the information carefully.", "no_rank": "No Rank"}}