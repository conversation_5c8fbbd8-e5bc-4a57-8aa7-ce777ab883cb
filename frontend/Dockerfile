FROM node:20-alpine AS base

# Cài đặt các phụ thuộc cần thiết
RUN apk add --no-cache libc6-compat

# Thiết lập thư mục làm việc
WORKDIR /app

# Bước cài đặt dependencies
FROM base AS deps
COPY package.json pnpm-lock.yaml* package-lock.json* yarn.lock* ./

# Cài đặt dependencies với pnpm (nếu có pnpm-lock.yaml)
RUN if [ -f pnpm-lock.yaml ]; then \
    npm install -g pnpm && \
    pnpm install --frozen-lockfile; \
  # Cài đặt dependencies với yarn (nếu có yarn.lock)
  elif [ -f yarn.lock ]; then \
    npm install -g yarn && \
    yarn install --frozen-lockfile; \
  # Cài đặt dependencies với npm
  else \
    npm install; \
  fi

# Bước xây dựng ứng dụng
FROM base AS builder
WORKDIR /app

# Sao chép node_modules từ bước trước
COPY --from=deps /app/node_modules ./node_modules

# Sao chép tất cả các file cấu hình và mã nguồn
COPY . .

# Bước chạy ứng dụng
FROM base AS runner
WORKDIR /app

# Thiết lập môi trường
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1

# Sao chép node_modules và các file cần thiết từ bước builder
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/next.config.js ./next.config.js
COPY --from=builder /app/public ./public
COPY --from=builder /app/app ./app
COPY --from=builder /app/components ./components
COPY --from=builder /app/styles ./styles
COPY --from=builder /app/types ./types
COPY --from=builder /app/config ./config
COPY --from=builder /app/.npmrc ./.npmrc
COPY --from=builder /app/tailwind.config.js ./tailwind.config.js
COPY --from=builder /app/postcss.config.js ./postcss.config.js
COPY --from=builder /app/tsconfig.json ./tsconfig.json

# Mở cổng 3000
EXPOSE 3000

# Khởi động ứng dụng
CMD ["npm", "run", "dev"] 