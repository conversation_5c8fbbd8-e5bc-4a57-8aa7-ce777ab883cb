import { errorCodeMap } from './error-codes';

/**
 * Handles API errors and returns appropriate error message in current language
 * @param error Error from API
 * @param t Translation function from useTranslation
 * @param defaultErrorKey Default key if no specific error code is found
 * @returns Translated error message
 */
export function handleApiError(
  error: any,
  t: (key: string) => string,
  defaultErrorKey: string = 'errors.unknown_error'
): string {

  if (!error) {
    return t(defaultErrorKey);
  }

  if (error.errorCode && errorCodeMap[error.errorCode]) {
    const translationKey = errorCodeMap[error.errorCode];
    const translatedMessage = t(translationKey);

    if (translatedMessage === translationKey) {
      if (error.message?.en) {
        return error.message.en;
      }
    }

    return translatedMessage;
  }

  if (error.message) {
    const currentLang = t('common.language_code') as keyof typeof error.message;

    if (typeof error.message === 'object') {
      if (error.message[currentLang]) {
        return error.message[currentLang];
      }

      if (error.message.en) {
        return error.message.en;
      }

      const firstAvailableLanguage = Object.keys(error.message)[0];
      if (firstAvailableLanguage) {
        return error.message[firstAvailableLanguage];
      }
    } else if (typeof error.message === 'string') {
      return error.message;
    }
  }

  return t(defaultErrorKey);
}
