/**
 * Maps backend error codes to frontend language keys
 * Each error code is mapped to a key in the language files
 */
export const errorCodeMap: Record<string, string> = {
  'UNKNOWN_ERROR': 'errors.unknown_error',
  'VALIDATION_ERROR': 'errors.validation_error',
  'UNAUTHORIZED': 'errors.unauthorized',
  'FORBIDDEN': 'errors.forbidden',
  'NOT_FOUND': 'errors.not_found',
  'MAINTENANCE_MODE': 'errors.maintenance_mode',
  'INTERNAL_SERVER_ERROR': 'errors.server_error',
  'BAD_REQUEST': 'errors.invalid_input',

  'INVALID_CREDENTIALS': 'errors.invalid_credentials',
  'ACCOUNT_LOCKED': 'errors.account_locked',
  'ACCOUNT_DISABLED': 'errors.account_disabled',
  'ACCOUNT_NOT_FOUND': 'errors.account_not_found',
  'INVALID_TOKEN': 'errors.invalid_token',
  'TOKEN_EXPIRED': 'errors.token_expired',
  'INVALID_SIGNATURE': 'errors.invalid_signature',
  'INVALID_WALLET_ADDRESS': 'errors.invalid_wallet_address',
  'WALLET_ALREADY_CONNECTED': 'errors.wallet_already_connected',

  'INSUFFICIENT_BALANCE': 'errors.insufficient_balance',
  'INSUFFICIENT_USDT_BALANCE': 'errors.insufficient_usdt_balance',
  'INSUFFICIENT_WM_BALANCE': 'errors.insufficient_wm_balance',
  'TRANSACTION_FAILED': 'errors.transaction_failed',
  'INVALID_TRANSACTION': 'errors.invalid_transaction',
  'TRANSACTION_NOT_FOUND': 'errors.transaction_not_found',
  'INVALID_AMOUNT': 'errors.invalid_amount',
  'MINIMUM_AMOUNT_REQUIRED': 'errors.minimum_amount_required',
  'MAXIMUM_AMOUNT_EXCEEDED': 'errors.maximum_amount_exceeded',

  'INVALID_WM_USD_RATE': 'errors.invalid_wm_rate_configuration',
  'INSUFFICIENT_STAKING_BALANCE': 'errors.insufficient_staking_balance',
  'CREATE_NFT_ORDER_FAILED': 'errors.failed_to_create_nft_order',

  'KYC_ALREADY_COMPLETED': 'errors.kyc_already_completed',
  'KYC_REQUIRED': 'errors.kyc_required',
  'KYC_VERIFICATION_FAILED': 'errors.kyc_verification_failed',
  'KYC_IN_PROGRESS': 'errors.kyc_in_progress',

  'INVALID_REFERRAL_CODE': 'errors.invalid_referral_code',
  'SELF_REFERRAL_NOT_ALLOWED': 'errors.self_referral_not_allowed',
  'REFERRAL_ALREADY_EXISTS': 'errors.referral_already_exists',

  'PACKAGE_NOT_FOUND': 'global_mining.package_not_found',
  'INVALID_DURATION': 'global_mining.invalid_duration',
  'MAX_STAKINGS_REACHED': 'global_mining.max_stakings_reached',
  'STAKING_NOT_FOUND': 'global_mining.staking_not_found',
  'STAKING_ALREADY_COMPLETED': 'global_mining.staking_already_completed',
  'STAKING_ALREADY_ACTIVE': 'global_mining.staking_already_active',
  'CREATE_STAKING_FAILED': 'global_mining.create_investment_error',

  'NFT_NOT_FOUND': 'nfts.nft_not_found',
  'NFT_ALREADY_OWNED': 'nfts.nft_already_owned',
  'NFT_NOT_OWNED': 'nfts.nft_not_owned',
  'NFT_ALREADY_LISTED': 'nfts.nft_already_listed',
  'NFT_NOT_LISTED': 'nfts.nft_not_listed',
  'NFT_PURCHASE_FAILED': 'nfts.purchase_failed',

  'SESSION_NOT_FOUND': 'flash_buy.session_not_found',
  'TRADING_SESSION_NOT_FOUND': 'flash_buy.session_not_found',
  'SESSION_ALREADY_STARTED': 'flash_buy.session_already_started',
  'SESSION_ALREADY_ENDED': 'flash_buy.session_already_ended',
  'SESSION_NOT_ACTIVE': 'flash_buy.session_not_active',
  'SESSION_FULL': 'flash_buy.session_full',
  'ALREADY_SCHEDULED': 'flash_buy.already_scheduled',

  'INVALID_CONTENT': 'support.invalid_content',
  'INVALID_TX_HASH': 'support.invalid_tx_hash',
  'TOO_MANY_TICKETS': 'support.too_many_tickets',
  'TICKET_NOT_FOUND': 'support.ticket_not_found',
  'TICKET_ALREADY_RESOLVED': 'support.ticket_already_resolved',

  'ALREADY_PURCHASED': 'errors.already_purchased',
  'PURCHASE_FAILED': 'errors.purchase_failed',

  'RATE_LIMIT_EXCEEDED': 'errors.rate_limit_exceeded',
  'DAILY_LIMIT_EXCEEDED': 'errors.daily_limit_exceeded',
  'MONTHLY_LIMIT_EXCEEDED': 'errors.monthly_limit_exceeded',

  'DUPLICATE_ENTRY': 'errors.already_exists',
  'DATA_INTEGRITY_ERROR': 'errors.data_integrity_error',
  'INVALID_DATA_FORMAT': 'errors.invalid_data_format',

  'USER_WALLET_NOT_FOUND': 'errors.user_wallet_not_found',
  'USER_NOT_FOUND': 'errors.user_not_found',
};
