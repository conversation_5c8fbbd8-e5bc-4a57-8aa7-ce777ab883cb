{"name": "worldmall-crypto-platform-frontend", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --no-lint", "start": "next start", "lint": "eslint . --ext .ts,.tsx -c .eslintrc.json --fix"}, "dependencies": {"@heroui/button": "2.2.13", "@heroui/code": "2.2.10", "@heroui/input": "2.4.13", "@heroui/kbd": "2.2.10", "@heroui/link": "2.2.11", "@heroui/listbox": "2.3.13", "@heroui/navbar": "2.2.12", "@heroui/react": "^2.7.4", "@heroui/snippet": "2.2.14", "@heroui/switch": "2.2.12", "@heroui/system": "2.4.10", "@heroui/theme": "2.4.9", "@iconify/react": "^5.2.0", "@internationalized/date": "^3.7.0", "@rainbow-me/rainbowkit": "^2.2.4", "@react-aria/ssr": "3.9.7", "@react-aria/visually-hidden": "3.8.19", "@tanstack/react-query": "^5.67.2", "@types/js-cookie": "^3.0.6", "axios": "^1.8.1", "bignumber.js": "^9.1.2", "clsx": "2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "ethers": "^6.13.5", "framer-motion": "11.13.1", "intl-messageformat": "^10.5.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment-timezone": "^0.5.47", "next": "15.0.4", "next-themes": "^0.4.4", "qrcode.react": "^3.0.2", "react": "18.3.1", "react-d3-tree": "^3.6.6", "react-dom": "18.3.1", "react-organizational-chart": "^2.2.1", "siwe": "^3.0.0", "socket.io-client": "^4.8.1", "swr": "2.3.2", "viem": "^2.23.11", "wagmi": "^2.14.12", "zustand": "^5.0.3"}, "devDependencies": {"@next/eslint-plugin-next": "15.0.4", "@react-types/shared": "3.25.0", "@types/lodash": "^4.17.16", "@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.11.0", "@typescript-eslint/parser": "8.11.0", "autoprefixer": "10.4.19", "eslint": "^8.57.0", "eslint-config-next": "15.0.4", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "4.1.4", "postcss": "8.4.49", "prettier": "3.3.3", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "typescript": "5.6.3"}}