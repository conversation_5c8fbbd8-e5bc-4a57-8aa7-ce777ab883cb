import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { User } from '@/types/models';
import { TreeResponseDto } from '@/app/(_authenticated)/affiliate/types/type';

interface AuthState {
  user: User | null;
  accessToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  refAddress: string | null;
  isHydrated: boolean;
  treeData: TreeResponseDto | null;
  isTreeLoading: boolean;
  setAuth: (accessToken: string, user: User) => void;
  updateUser: (user: User) => void;
  clearAuth: () => void;
  logout: () => void;
  hydrate: () => void;
  fetchTreeData: () => Promise<void>;
}

const STORAGE_KEY = 'user-storage';
const API_URL = '';
const API_PREFIX = 'api/v1';

const storage = {
  getItem: (key: string): string | null => {
    if (typeof window === 'undefined') return null;
    try {
      const item = localStorage.getItem(key);
      return item;
    } catch {
      return null;
    }
  },
  setItem: (key: string, value: string) => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(key, value);
    } catch (error) {
    }
  },
  removeItem: (key: string) => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.removeItem(key);
    } catch (error) {
    }
  },
};

export const useUserStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      accessToken: null,
      isAuthenticated: false,
      isLoading: false,
      refAddress: null,
      isHydrated: false,
      treeData: null,
      isTreeLoading: false,
      
      hydrate: () => {
        if (typeof window === 'undefined') return;
        
        try {
          const storedData = localStorage.getItem(STORAGE_KEY);
          if (!storedData) return;

          const { state } = JSON.parse(storedData);
          if (!state) return;

          const { user, accessToken, isAuthenticated, refAddress } = state;
          
          if (user && accessToken) {
            set({
              user,
              accessToken,
              isAuthenticated: true,
              refAddress,
              isHydrated: true,
            });
          }
        } catch (error) {
        }
      },
      
      setAuth: (accessToken: string, user: User) => {
        if (!accessToken) {
          return;
        }
        
        if (!user) {
          return;
        }
        
        const normalizedToken = accessToken.startsWith('Bearer ')
          ? accessToken
          : `Bearer ${accessToken}`;
        
        if (!user.id || !user.wallet) {
          return;
        }

        const newState = {
          user,
          accessToken: normalizedToken,
          isAuthenticated: true,
          isLoading: false,
          refAddress: get().refAddress,
          isHydrated: true,
        };

        set(newState);
        
        const currentState = get();
        
        return currentState;
      },
      
      updateUser: (user: User) => {
        if (!user || !user.id) {
          return;
        }

        const currentState = get();
        if (!currentState.isAuthenticated) {
          return;
        }

        set({ user });
      },
      
      clearAuth: () => {
        const newState = {
          user: null,
          accessToken: null,
          isAuthenticated: false,
          isLoading: false,
          refAddress: null,
          isHydrated: true,
          treeData: null,
          isTreeLoading: false,
        };
        
        set(newState);
      },
      
      logout: () => {
        get().clearAuth();
      },
      
      fetchTreeData: async () => {
        const state = get();
        if (!state.isAuthenticated || !state.accessToken) return;

        set({ isTreeLoading: true });
        try {
          const response = await fetch(`${API_URL}/${API_PREFIX}/users/tree`, {
            method: 'GET',
            headers: {
              Authorization: state.accessToken,
            },
          });
          const data = await response.json();
          if (data.success) {
            set({ treeData: data.data });
          }
        } catch (error) {
        } finally {
          set({ isTreeLoading: false });
        }
      },
    }),
    {
      name: STORAGE_KEY,
      storage: createJSONStorage(() => storage),
      skipHydration: true,
      partialize: (state) => ({
        user: state.user,
        accessToken: state.accessToken,
        isAuthenticated: state.isAuthenticated,
        refAddress: state.refAddress,
        isHydrated: state.isHydrated,
      }),
    }
  )
);