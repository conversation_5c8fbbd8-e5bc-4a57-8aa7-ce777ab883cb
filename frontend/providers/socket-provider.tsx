'use client';

import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { useUserStore } from '@/store/userStore';

interface SocketAuth {
  token: string;
}

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  reconnect: () => void;
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
  reconnect: () => {},
});

export const useSocketContext = () => useContext(SocketContext);

export function SocketProvider({ children }: { children: React.ReactNode }) {
  const [isConnected, setIsConnected] = useState(false);
  const socketRef = useRef<Socket | null>(null);
  const { accessToken } = useUserStore();

  const initializeSocket = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
    }

    const SOCKET_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
    socketRef.current = io(SOCKET_URL, {
      transports: ['websocket'],
      reconnection: true,
      reconnectionAttempts: Infinity,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      timeout: 20000,
      auth: { token: accessToken },
    });

    socketRef.current.on('connect', () => {
      setIsConnected(true);
    });

    socketRef.current.on('disconnect', (reason) => {
      setIsConnected(false);
    });

    socketRef.current.on('connect_error', (error) => {
    });
  };

  const reconnect = () => {
    if (!accessToken) {
      return;
    }
    if (!socketRef.current || !socketRef.current.connected) {
      initializeSocket();
    }
  };

  useEffect(() => {
    if (!accessToken) {
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
      setIsConnected(false);
      return;
    }

    if (!socketRef.current || (socketRef.current.auth as SocketAuth)?.token !== accessToken) {
      initializeSocket();
    }

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
    };
  }, [accessToken]);

  return (
    <SocketContext.Provider value={{ socket: socketRef.current, isConnected, reconnect }}>
      {children}
    </SocketContext.Provider>
  );
}