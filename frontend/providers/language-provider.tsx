'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import Cookies from 'js-cookie';

export type Language = 'en' | 'vi' | 'kr' | 'zh' | 'ja' | 'th' | 'ru';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string, params?: Record<string, any>) => string;
}

export const LanguageContext = createContext<LanguageContextType>({
  language: 'en',
  setLanguage: () => {},
  t: (key) => key,
});

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('en');
  const [translations, setTranslations] = useState<Record<string, any>>({});
  const [isLoaded, setIsLoaded] = useState(false);

  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage);
    Cookies.set('language', newLanguage, { expires: 365 });
    loadTranslations(newLanguage);
  };

  const loadTranslations = async (lang: Language) => {
    try {
      const translations = await import(`../locales/${lang}.json`);
      setTranslations(translations);
      setIsLoaded(true);
    } catch (error) {
      console.error(`Failed to load translations for ${lang}:`, error);
      if (lang !== 'en') {
        const defaultTranslations = await import('../locales/en.json');
        setTranslations(defaultTranslations);
      }
      setIsLoaded(true);
    }
  };

  const t = (key: string, params?: Record<string, any>): string => {
    if (!isLoaded) return key;

    const keys = key.split('.');
    let value = translations;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key;
      }
    }

    let result = typeof value === 'string' ? value : key;

    if (params && typeof result === 'string') {
      Object.keys(params).forEach(paramKey => {
        result = result.replace(new RegExp(`{{${paramKey}}}`, 'g'), params[paramKey]);
      });
    }

    return result;
  };

  useEffect(() => {
    const savedLanguage = Cookies.get('language') as Language;
    const initialLanguage = savedLanguage || 'en';
    setLanguageState(initialLanguage);
    loadTranslations(initialLanguage);
  }, []);

  const contextValue: LanguageContextType = {
    language,
    setLanguage,
    t,
  };

  if (!isLoaded) {
    return <div>Loading...</div>;
  }

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};
