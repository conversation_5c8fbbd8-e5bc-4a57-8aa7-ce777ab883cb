<PERSON><PERSON><PERSON> sẽ liệt kê chi tiết các API endpoints theo từng luồng chức năng:

### 1. Authentication
**Wallet Login**
```
POST /api/v1/auth/wallet-login
Request:
{
  "wallet": "******************************************",
  "signature": "0xa8159e3e...",
  "message": "Login to WorldMall Crypto Platform",
  "ref": "F4A0D480" // Optional referral code
}
Response:
{
  "statusCode": 200,
  "success": true,
  "message": "Wallet login successful",
  "data": {
    "accessToken": "eyJhbGciOiJ...",
    "id": "76f1dcdd-...",
    "username": "0x1e588c",
    "wallet": "******************************************",
    "role": "USER",
    "email": null,
    "referralCode": "08B86E42"
  }
}
```

### 2. User Profile
**Update Profile**
```
PATCH /api/v1/users/profile
Headers: Authorization: Bearer {token}
Request:
{
  "email": "<EMAIL>",
  "name": "Test User",
  "phone": "+84123456789"
}
Response:
{
  "statusCode": 200,
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "id": "76f1dcdd-...",
    "username": "0x1e588c",
    "wallet": "******************************************",
    "email": "<EMAIL>",
    "name": "Test User",
    "phone": "+84123456789",
    "role": "USER",
    "rank": "BRONZE",
    "miningMultiplier": "1.00",
    "totalMined": "1.00000000",
    ...
  }
}
```

### 3. Mining
**Mine WM Token**
```
POST /api/v1/wallet/mine
Headers: Authorization: Bearer {token}
Response:
{
  "statusCode": 200,
  "success": true,
  "message": "Successfully mined 1 WM tokens",
  "data": {
    "success": true,
    "amount": 1,
    "newBalance": "1.00000000",
    "totalMined": 1,
    "rank": "BRONZE",
    "difficulty": 1,
    "nextDifficulty": 1,
    "referralBonus": 0.25,
    "message": "Successfully mined 1 WM tokens"
  }
}
```

### 4. Lightning Bolt
**Purchase Lightning Bolt**
```
POST /api/v1/wallet/lightning-bolt
Headers: Authorization: Bearer {token}
Response:
{
  "statusCode": 200,
  "success": true,
  "message": "Successfully purchased lightning bolt",
  "data": {
    "success": true,
    "price": 2,
    "newUsdtBalance": 8,
    "miningMultiplier": 2,
    "currentTier": 1,
    "nextPrice": 4,
    "message": "Successfully purchased lightning bolt for 2 USDT"
  }
}
```

### 5. Wallet & Transactions
**Get Transaction History**
```
GET /api/v1/wallet/transactions/history?page=1&limit=10
Headers: Authorization: Bearer {token}
Response:
{
  "statusCode": 200,
  "success": true,
  "message": "Transaction history retrieved successfully",
  "data": {
    "transactions": [
      {
        "id": "853cd42a-...",
        "type": "MINING",
        "amount": 1,
        "status": "COMPLETED",
        "note": "Mined 1 WM tokens",
        "createdAt": "2025-03-07T20:21:26.943Z",
        ...
      }
    ],
    "total": 10,
    "page": 1,
    "limit": 10
  }
}
```

**Deposit USDT**
```
POST /api/v1/wallet/deposit/{walletId}
Headers: Authorization: Bearer {token}
Request:
{
  "amount": 10,
  "token": "USDT"
}
Response:
{
  "statusCode": 200,
  "success": true,
  "message": "Deposit completed successfully",
  "data": {
    "id": "transaction_id",
    "type": "DEPOSIT",
    "amount": 10,
    "status": "COMPLETED",
    ...
  }
}
```

### 6. Referral System
**Get Referral Tree**
```
GET /api/v1/users/tree
Headers: Authorization: Bearer {token}
Response:
{
  "statusCode": 200,
  "success": true,
  "message": "Tree data retrieved successfully",
  "data": {
    "stats": {
      "totalReferrals": 3,
      "totalDirectReferrals": 3,
      "totalVolume": "0.00",
      "totalEarnings": "0.25"
    },
    "tree": {
      "id": "user_id",
      "username": "0x47f32c",
      "wallet": "0x47f32c...",
      "rank": "BRONZE",
      "children": [...]
    },
    "directReferrals": [...]
  }
}
```

### 7. Tasks
**Complete Task**
```
POST /api/v1/wallet/task/complete
Headers: Authorization: Bearer {token}
Request:
{
  "taskId": "DAILY_CHECK_IN",
  "rewardAmount": 0.1
}
Response:
{
  "statusCode": 200,
  "success": true,
  "message": "Task completed successfully. Received 0.1 WM tokens",
  "data": {
    "id": "transaction_id",
    "type": "TASK_REWARD",
    "amount": 0.1,
    "status": "COMPLETED",
    ...
  }
}
```

### 8. System Configuration
**Get System Config**
```
GET /api/v1/system/config
Response:
{
  "statusCode": 200,
  "success": true,
  "message": "System configs retrieved successfully",
  "data": [
    {
      "key": "MINING_BASE_REWARD",
      "value": "0.1",
      "description": "Base reward for mining (in WM tokens)"
    },
    {
      "key": "MINING_COOLDOWN",
      "value": "3600",
      "description": "Cooldown period for mining (in seconds)"
    },
    ...
  ]
}
```

**Lưu ý quan trọng:**
1. Tất cả các API (trừ wallet-login) đều yêu cầu JWT token trong header
2. Format token: `Authorization: Bearer {token}`
3. Tất cả response đều có format chung:
```typescript
{
  statusCode: number;
  success: boolean;
  message: string;
  data: any;
  timestamp: string;
}
```
4. Mã lỗi:
   - 200: Thành công
   - 400: Bad Request (tham số không hợp lệ)
   - 401: Unauthorized (token không hợp lệ/hết hạn)
   - 404: Not Found
   - 500: Internal Server Error

Bạn cần thêm thông tin về API nào không?
