export interface User {
  id: string;
  username: string;
  wallet: string;
  email?: string;
  name?: string;
  phone?: string;
  nationalId?: string;
  role: UserRole;
  rank: UserRank;
  wManagerRank: WManagerRank;
  miningMultiplier: number;
  totalMined: number;
  lastMiningTime?: Date;
  hasLightningBolt: boolean;
  lightningBoltPrice: number;
  lightningBoltPurchaseDate?: Date;
  hasMinDeposit: boolean;
  credits: number;
  referralCode: string;
  referredBy?: string;
  referrer?: User;
  referrals?: User[];
  path?: string;
  web3WalletId?: string;
  web3Wallet?: Wallet;
  dailyMiningRate: number;
  referralMiningBonus: number;
  completedTasks: number;
  totalEarnings: number;
  totalVolume: number;
  firstDepositTime?: Date;
  isFirstProfileCompleted?: boolean;
  isKycCompleted?: boolean;
  isInviteCompleted?: boolean;
  isNftCompleted?: boolean;
  isLocked?: boolean;
  isPriorityBuy?: boolean;
  isShareholder?: boolean;
  stakingActive?: boolean;
  stakingMaxedOut?: boolean;
  stakingTotalInvestment?: number;
  stakingTotalEarnings?: number;
  tokenBalances?: TokenBalance[];
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER'
}

export enum UserRank {
  BRONZE = 'BRONZE',
  SILVER = 'SILVER',
  GOLD = 'GOLD',
  PLATINUM = 'PLATINUM',
  DIAMOND = 'DIAMOND'
}

export enum WManagerRank {
  WM1 = 'WM1',
  WM2 = 'WM2',
  WM3 = 'WM3',
  WM4 = 'WM4',
  WM5 = 'WM5'
}

export interface Wallet {
  id: string;
  userId: string;
  user: User;
  address: string;
  type: string;
  wmBalance: number;
  wmLockedBalance: number;
  usdtBalance: number;
  isActive: boolean;
  encryptedPrivateKey?: string;
  transactions: Transaction[];
  createdAt: Date;
  updatedAt: Date;
}

export enum TransactionType {
  DEPOSIT = 'DEPOSIT',
  WITHDRAWAL = 'WITHDRAWAL',
  TRANSFER = 'TRANSFER',
  MINING = 'MINING',
  REFERRAL = 'REFERRAL',
  PURCHASE = 'PURCHASE',
  LIGHTNING_BOLT_PURCHASE = 'LIGHTNING_BOLT_PURCHASE',
  PRIORITY_POSITION_PURCHASE = 'PRIORITY_POSITION_PURCHASE',
  KYC_PURCHASE = 'KYC_PURCHASE',
  SYSTEM_COMMISSION = 'SYSTEM_COMMISSION'
}

export enum TransactionStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

export interface Transaction {
  id: string;
  walletId: string;
  wallet: Wallet;
  userId: string;
  user: User;
  tokenId: string;
  token: Token;
  type: TransactionType;
  amount: number;
  txHash?: string;
  status: TransactionStatus;
  reference?: string;
  note?: string;
  recipientAddress?: string;
  senderAddress?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum TokenType {
  NATIVE = 'NATIVE',
  STABLECOIN = 'STABLECOIN',
  UTILITY = 'UTILITY',
  GOVERNANCE = 'GOVERNANCE',
  REWARD = 'REWARD'
}

export interface Token {
  id: string;
  symbol: string;
  name: string;
  type: TokenType;
  totalSupply: number;
  circulatingSupply: number;
  contractAddress?: string;
  decimals?: number;
  isActive: boolean;
  logoUrl?: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TokenBalance {
  id: string;
  userId: string;
  user: User;
  tokenId: string;
  token: Token;
  availableBalance: number;
  lockedBalance: number;
  totalBalance: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserShowModal {
  wallet: string;
  username: string;
  rank: UserRank;
  referrals: User[] | null;
  hasMinDeposit: boolean;
  createdAt: string;
  avatar?: string;
}

export enum NFTSessionType {
  MORNING = 'MORNING',
  EVENING = 'EVENING',
  MIDDLE = 'MIDDLE'
}

export enum NFTSessionStatus {
  PENDING = 'PENDING',
  OPEN = 'OPEN',
  CLOSED = 'CLOSED'
}

export interface NFTTradingSession {
  id: string;
  type: NFTSessionType;
  date: Date;
  status: NFTSessionStatus;
  openTime: Date;
  closeTime: Date;
  totalOrders: number;
  matchedOrders: number;
  totalVolume: number;
  totalSchedules: number;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface GroupedSessions {
  [date: string]: NFTTradingSession[];
}

export enum NFTType {
  PHOENIX = 'PHOENIX',
  SPIRIT_TURTLE = 'SPIRIT_TURTLE',
  UNICORN = 'UNICORN',
  DRAGON = 'DRAGON'
}

export enum NFTStatus {
  AVAILABLE = 'AVAILABLE',
  LISTED = 'LISTED',
  SOLD = 'SOLD'
}

export interface NFT {
  id: string;
  type: NFTType;
  originalNftId?: string;
  splitNumber?: number;
  totalSplits?: number;
  currentPrice: number;
  initialPrice: number;
  gasFee: number;
  quantity: string;
  status: NFTStatus;
  ownerId?: string;
  owner?: User;
  createdAt: Date;
  updatedAt: Date;
}

export enum OrderStatus {
  PENDING = 'PENDING',
  MATCHED = 'MATCHED',
  CANCELLED = 'CANCELLED',
}

export enum OrderSession {
  MORNING = 'MORNING',
  EVENING = 'EVENING',
}

export enum OrderType {
  BUY = 'BUY',
  SELL = 'SELL'
}

export interface NFTOrder {
  id: string;
  traderId: string;
  trader: User;
  orderType: OrderType;
  nftType: NFTType;
  nftId?: string;
  nft?: NFT;
  nftPrice: number;
  status: OrderStatus;
  sessionType: OrderSession;
  sessionId: string;
  session: NFTTradingSession;
  gasFee: number;
  gasFeePercentage: number;
  sessionTime: Date;
  quantity: string;
  soldQuantity: string;
  remainingQuantity: string;
  tradeSessions: number;
  matchedAt?: Date;
  completedAt?: Date;
  sellerPaid: boolean;
  createdAt: Date;
  updatedAt: Date;
  isScheduled: boolean;
}

export enum StakingPackageCode {
  PHOENIX = 'PHOENIX',
  SPIRIT_TURTLE = 'SPIRIT_TURTLE',
  UNICORN = 'UNICORN',
  DRAGON = 'DRAGON',
  DRAGON_LORD = 'DRAGON_LORD',
  ETERNAL_DRAGON = 'ETERNAL_DRAGON',
  SOVEREIGN_DRAGON = 'SOVEREIGN_DRAGON'
}

export enum StakingStatus {
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export interface StakingPackage {
  code: string;
  name: string;
  amount: number;
  tokenSymbol: string;
  durations: StakingDuration[];
}

export interface StakingDuration {
  days: number;
  interestRate: number;
  bonusRate: number;
  dailyInterestRate: number;
  totalReturn: number;
}

export interface Staking {
  id: string;
  packageCode: string;
  packageName: string;
  amount: number;
  durationDays: number;
  interestRate: number;
  bonusRate: number;
  dailyInterestRate: number;
  startDate: Date;
  endDate: Date;
  status: StakingStatus;
  totalInterest: number;
  totalBonus: number;
  wmBonusAmount: number;
  wmUsdRate: number;
  lastInterestDate: Date;
  createdAt: Date;
  elapsedDays: number;
  daysRemaining: number;
  progressPercentage: number;
}

export interface StakingStats {
  totalStakings: number;
  activeStakings: number;
  totalInvestment: number;
  totalInterest: number;
  todayInterest: number;
  totalEarnings?: number;
  maxEarnings?: number;
  earningsPercentage?: number;
  isMaxedOut?: boolean;
  totalCommission?: number;
  directCommission?: number;
  matchingCommission?: number;
  rankCommission?: number;
  rank?: string;
  wmUsdRate?: number;
  totalVolume?: number;
}